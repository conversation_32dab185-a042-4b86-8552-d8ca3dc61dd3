version: '1.0'
name: pipeline-20230920
displayName: pipeline-diguo
triggers:
  trigger: auto
  push:
    branches:
      prefix:
        - diguo
stages:
  - name: stage-de248c78
    displayName: 编译
    strategy: naturally
    trigger: auto
    executor: []
    steps:
      - step: build@gradle
        name: build_gradle
        displayName: Gradle 构建
        jdkVersion: '8'
        gradleVersion: '4.4'
        commands:
          - '# Gradle默认构建命令'
          - chmod +x ./gradlew
          - ./gradlew clean :game:assembleTaptapLiteChinaRelease
        artifacts:
          - name: BUILD_ARTIFACT
            path:
              - ./target
        caches:
          - ~/.gradle/caches
        notify: []
        strategy:
          retry: '0'
