version: '1.0'
name: pr-pipeline
displayName: PRPipeline
stages:
  - stage:
    name: compile
    displayName: 编译
    steps:
      - step: build@gradle
        name: build_gradle
        displayName: Gradle 构建
        # 支持6、7、8、9、10、11六个版本
        jdkVersion: 8
        # 支持4.1、4.2、4.3、4.4四个版本
        gradleVersion: 4.4
        # 构建命令
        commands:
          - chmod +x ./gradlew
          - ./gradlew build
        # 非必填字段，开启后表示将构建产物暂存，但不会上传到制品库中，7天后自动清除
        artifacts:
            # 构建产物名字，作为产物的唯一标识可向下传递，支持自定义，默认为BUILD_ARTIFACT。在下游可以通过${BUILD_ARTIFACT}方式引用来获取构建物地址
          - name: BUILD_ARTIFACT
            # 构建产物获取路径，是指代码编译完毕之后构建物的所在路径，如通常jar包在target目录下。当前目录为代码库根目录
            path:
              - ./build
      - step: publish@general_artifacts
        name: publish_general_artifacts
        displayName: 上传制品
        # 上游构建任务定义的产物名，默认BUILD_ARTIFACT
        dependArtifact: BUILD_ARTIFACT
        # 上传到制品库时的制品命名，默认output
        artifactName: output  
        dependsOn: build_gradle
triggers:
  pr:
    branches:
      include:
        - master
