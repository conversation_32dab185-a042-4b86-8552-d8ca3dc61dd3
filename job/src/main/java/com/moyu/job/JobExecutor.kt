package com.moyu.job

import android.content.Context

class JobExecutor<T : Context>(private val context: T) {

    companion object {
        fun <T : Context> getExecutor(context: T): JobExecutor<T> {
            return JobExecutor(context)
        }
    }

    private val commitList = mutableListOf<JobContent<T>>()

    fun add(jobContent: JobContent<T>): JobExecutor<T> {
        commitList.add(jobContent)
        return this
    }
}