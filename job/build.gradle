plugins {
    id 'com.android.library'
    id 'kotlin-android'
}

android {
    compileSdk 35

    defaultConfig {
        minSdk 23
        targetSdk 35

        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
    }
    namespace 'com.moyu.job'
}

dependencies {
}