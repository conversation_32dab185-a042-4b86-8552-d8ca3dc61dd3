import os
import subprocess
import sys

def main():
    scripts = [
        "download_english_excel.py",
        "download_excel.py"
    ]

    for script in scripts:
        print(f"\n===== 开始执行：{script} =====")
        # 1) 使用 os.system
        # os.system(f"python {script}")

        # 2) 或者使用 subprocess.run（推荐）
        result = subprocess.run([sys.executable, script], check=True)
        if result.returncode == 0:
            print(f"[OK] 脚本 {script} 执行成功")
        else:
            print(f"[ERROR] 脚本 {script} 执行失败，退出码：{result.returncode}")

if __name__ == "__main__":
    main()
