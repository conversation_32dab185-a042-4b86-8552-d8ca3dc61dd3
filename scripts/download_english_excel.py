import http.client
import json
import os
import string

# user-access-token
ACCESS_TOKEN = "u-fsOswct0xcnqsPqaHPhR.lg03ANl4kEVXq20llWay4ce"  # 调用get_access_token重新赋值
#
USE_Tenant_access_token = True  # 使用应用的TOKEN，通过APPSECRET 动态获取 。fasle使用USER_ACCESS_TOKEN，需要手动配置ACCESS_TOKEN
DIR_PATH = "../game/src/lite/english/"
# 压缩后的文件名
COMPRESS_FILE = "../game/src/main/assets/en_impossible.mp3"

APP_ID = "cli_a465fa21177a100e"
APP_SECRET = "YV1kSvzjwaOtvhw1mlwkycPMH5wJdpod"

def get_wiki_spaces():
    """
       获取知识空间列表
    :return:
    """
    conn = http.client.HTTPSConnection("open.feishu.cn")

    payload = ''

    headers = {'Authorization': f'Bearer {ACCESS_TOKEN}'}
    query_url = f"/open-apis/wiki/v2/spaces"

    conn.request("GET", query_url, payload, headers)
    res = conn.getresponse()
    if res.status != 200:
        print("[-]Error", res.read())
    else:
        data = res.read()
        data_in_sheet = json.loads(data.decode("utf-8"))['data']['items']
        # print(data_in_sheet)
        print("知识空间列表:")
        for item in data_in_sheet:
            print(f"name ={item['name']},space_id={item['space_id']}")
    conn.close()


def get_space_nodes(space_id: str, parent_node_token: str = None):
    """
    获取知识空间子节点列表  /open-apis/wiki/v2/spaces/:space_id/nodes
    :return:
    """
    conn = http.client.HTTPSConnection("open.feishu.cn")
    payload = ''
    headers = {'Authorization': f'Bearer {ACCESS_TOKEN}'}
    query_url = f"/open-apis/wiki/v2/spaces/{space_id}/nodes?page_size=50"

    if parent_node_token is not None:
        query_url += f"&parent_node_token={parent_node_token}"

    conn.request("GET", query_url, payload, headers)

    res = conn.getresponse()
    if res.status != 200:
        print("[-]Error", res.read())
    else:
        data = res.read()
        data_in_sheet = json.loads(data.decode("utf-8"))['data']['items']
        print("知识空间子节点列表:")
        for item in data_in_sheet:
            print(f"node_token={item['node_token']},obj_token={item['obj_token']}"
                  f",obj_type={item['obj_type']},space_id={item['space_id']},title={item['title']}")
        return data_in_sheet
    conn.close()

class SimpleSheetInfo:

    def __init__(self, row_count: str, column_count: str, sheet_id: str, title: str):
        """
        :param row_count: 行数
        :param column_count: 列数
        :param sheet_id: sheet_id
        :param title: sheet的title
        """
        self.column_count = column_count
        self.row_count = row_count
        self.sheet_id = sheet_id
        self.title = title


class Spreadsheets:
    simple_sheet_info: SimpleSheetInfo = None

    def __init__(self, spreadsheet_token: str, title: str, saved_file_name: str):
        """
        :param title 表格文件名称
        :param spreadsheet_token:  飞书表格的标识 例如：R0PMsS9TRhFYhSt0iQ5ciz0xnGc
        :param saved_file_name: 保存后的文件名，例如test.txt
        """
        self.title = title
        self.spreadsheet_token = spreadsheet_token
        self.saved_file_name = saved_file_name


def get_access_token():
    """
        用于获取应用得tenant_access_token
    :return:
    """
    url = "/open-apis/auth/v3/app_access_token/internal/"
    headers = {
        'Content-Type': 'application/json; charset=utf-8'
    }
    payload = {
        'app_id': APP_ID,
        'app_secret': APP_SECRET
    }
    conn = http.client.HTTPSConnection("open.feishu.cn")

    # payload = ''
    conn.request("POST", url, json.dumps(payload), headers)
    res = conn.getresponse()
    data = res.read()
    global ACCESS_TOKEN
    ACCESS_TOKEN = json.loads(data)['tenant_access_token']
    print(ACCESS_TOKEN)


# https://open.feishu.cn/document/server-docs/docs/faq  7.2节获取知识空间中的所有sheet文档。

def get_wiki_spaces():
    """
       获取知识空间列表
    :return:
    """
    conn = http.client.HTTPSConnection("open.feishu.cn")

    payload = ''

    headers = {'Authorization': f'Bearer {ACCESS_TOKEN}'}
    query_url = f"/open-apis/wiki/v2/spaces"

    conn.request("GET", query_url, payload, headers)
    res = conn.getresponse()
    if res.status != 200:
        print("[-]Error", res.read())
    else:
        data = res.read()
        data_in_sheet = json.loads(data.decode("utf-8"))['data']['items']
        # print(data_in_sheet)
        print("知识空间列表:")
        for item in data_in_sheet:
            print(f"name ={item['name']},space_id={item['space_id']}")
    conn.close()


def get_space_nodes(space_id: str, parent_node_token: str = None):
    """
    获取知识空间子节点列表  /open-apis/wiki/v2/spaces/:space_id/nodes
    :return:
    """
    conn = http.client.HTTPSConnection("open.feishu.cn")
    payload = ''
    headers = {'Authorization': f'Bearer {ACCESS_TOKEN}'}
    query_url = f"/open-apis/wiki/v2/spaces/{space_id}/nodes?page_size=50"

    if parent_node_token is not None:
        query_url += f"&parent_node_token={parent_node_token}"

    conn.request("GET", query_url, payload, headers)

    res = conn.getresponse()
    if res.status != 200:
        print("[-]Error", res.read())
    else:
        data = res.read()
        data_in_sheet = json.loads(data.decode("utf-8"))['data']['items']
        print("知识空间子节点列表:")
        for item in data_in_sheet:
            print(f"node_token={item['node_token']},obj_token={item['obj_token']}"
                  f",obj_type={item['obj_type']},space_id={item['space_id']},title={item['title']}")
        return data_in_sheet
    conn.close()


def get_first_sheet_info(spreadsheet_token: str) -> SimpleSheetInfo:
    # /open-apis/sheets/v3/spreadsheets/:spreadsheet_token/sheets/query
    conn = http.client.HTTPSConnection("open.feishu.cn")
    payload = ''
    headers = {'Authorization': f'Bearer {ACCESS_TOKEN}'}
    query_url = f"/open-apis/sheets/v3/spreadsheets/{spreadsheet_token}/sheets/query"

    conn.request("GET", query_url, payload, headers)

    res = conn.getresponse()
    if res.status != 200:
        print("[-]Error", res.read())
    else:
        data = res.read()
        data_in_sheet = json.loads(data.decode("utf-8"))['data']['sheets'][0]
        sheet_id = data_in_sheet['sheet_id']
        title = data_in_sheet['title']
        row_count = data_in_sheet['grid_properties']['row_count']
        column_count = data_in_sheet['grid_properties']['column_count']
        simple_sheet_info = SimpleSheetInfo(
            column_count=column_count, row_count=row_count, sheet_id=sheet_id, title=title)

        conn.close()
        return simple_sheet_info


def get_all_sheet_and_print_class_define_str():
    """
    工具函数，打印所有需要读取的在线excel的类定义，需要手动拷贝，核对需要保存的文件名等。
    7251081850708328449 space_id 帝国人生
    YTH6w8aS6iK4ZTkTv9EcG9eOnkf parent_node_token 配置表节点
    :return:
    """
    # get_space_nodes("7251081850708328449", "YTH6w8aS6iK4ZTkTv9EcG9eOnkf")  # 获取知识空间的子节点 配置表节点
    # 示例结果：
    # node_token=H8v4wQUaYiudYNkCNTVcNFBPnuc,obj_token=IGoXsXG8Vh90dNtjq3hcdWt1nag,obj_type=sheet,space_id=7251081850708328449,title=skill军团卡

    items = get_space_nodes("7251081850708328449", "BjH7wIVg2is98CkWYdkc6DrSnhc")
    for item in items:
        simple_sheet_info = get_first_sheet_info(item['obj_token'])
        s = f"""Spreadsheets(title="{item['title']}",spreadsheet_token="{item['obj_token']}", saved_file_name="{item['title']}.txt"),"""
        print(s)

# 对读取到的行进行数据类型转换等。
def transform(x):
    if x is None:  # 空列
        return str(None)
    if type(x) == list:  # url 链接的情况
        print("发现特殊数据，正在处理... >>>", x)
        if len(x) > 0 and type(x[0]) == dict and 'text' in x[0]:
            return x[0]['text']
        else:
            raise Exception('发现特殊数据.. 请制定处置规则')
    return str(x)

def get_content_write_to_file(sheet: Spreadsheets):
    """以下是curl示例 https://open.feishu.cn/document/server-docs/docs/sheets-v3/data-operation/reading-multiple-ranges
    curl --location --request GET 'https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/shtcngNygNfuqhxTBf588jwgWbJ/values_batch_get?ranges=Q7PlXT!A2:B6,0b6377!B1:C8&valueRenderOption=ToString&dateTimeRenderOption=FormattedString' \
--header 'Authorization: Bearer t-e346617a4acfc3a11d4ed24dca0d0c0fc8e0067e' \
    :param sheet:
    :return:
    """
    conn = http.client.HTTPSConnection("open.feishu.cn")

    payload = ''

    headers = {
        'Authorization': f'Bearer {ACCESS_TOKEN}'
    }

    # 读取区域 示例： A1:AA11
    sheet_end = int(sheet.simple_sheet_info.column_count)
    if sheet_end <= 26:
        sheet_end = string.ascii_uppercase[sheet_end - 1]
    else:
        sheet_end = "A" + string.ascii_uppercase[((sheet_end - 1) % 26)]

    sheet_range = f"A1:{sheet_end}{sheet.simple_sheet_info.row_count}"

    query_url = f"/open-apis/sheets/v2/spreadsheets/{sheet.spreadsheet_token}/values_batch_get" \
                f"?valueRenderOption=FormattedValue&ranges={sheet.simple_sheet_info.sheet_id}!{sheet_range}"
    # print(query_url)
    conn.request("GET", query_url,
                 payload, headers)

    res = conn.getresponse()
    if res.status != 200:
        print(f"{sheet.title} - 获取内容失败", res.read())
    else:
        data = res.read()
        # print(data )
        data_in_sheet = json.loads(data.decode("utf-8"))['data']['valueRanges'][0]['values']
        # print(data_in_sheet)
        # 组装数据，写入文件到对应位置。
        with open(DIR_PATH + sheet.saved_file_name, "w", encoding='utf-8') as f:
            result = []
            for item in data_in_sheet[3:]:  # 跳过前三行
                if item[0] is None: continue
                item_temp = map(transform, item)
                result.append("\t".join(item_temp))
            f.write("\n".join(result))
    conn.close()


sheet_list = [
   Spreadsheets(title="skill军团卡",spreadsheet_token="AGxisnMnrhpKrntXXY1cI3zPnBb", saved_file_name="special_skill.txt"),
   Spreadsheets(title="skill战术卡",spreadsheet_token="NaBusMloZh0OCjtoUpRceI7fn4f", saved_file_name="skill.txt"),
   Spreadsheets(title="skill-诅咒附魔",spreadsheet_token="SUJusxyk3hOwCltDAZCcmRnBnPc", saved_file_name="halo_skill.txt"),
   Spreadsheets(title="skill史诗人物",spreadsheet_token="BwTIs4zK6h65EZtNaD1cMceUn3e", saved_file_name="hero_skill.txt"),
   Spreadsheets(title="skill冒险卡",spreadsheet_token="TwZAsZrnsheQhNtaS5zc0jjDnkf", saved_file_name="adventure_skill.txt"),
   #Spreadsheets(title="通用skill冒险枚举值表",spreadsheet_token="RzsIsAPj8hwb1qtyAeBcFkT1nDb", saved_file_name="通用skill冒险枚举值表.txt"),
   #Spreadsheets(title="帝国专用skill冒险枚举值表",spreadsheet_token="KjRKsCDHWhOrFutRPAtcBFKXnkh", saved_file_name="帝国专用skill冒险枚举值表.txt"),
   Spreadsheets(title="skill天赋树",spreadsheet_token="M401sHMsAhTvbIthDmtcdhPJnue", saved_file_name="talent_skill.txt"),
   Spreadsheets(title="buff",spreadsheet_token="GSHnsVcUQhF8jft2kKhcF8fknWd", saved_file_name="buff.txt"),
   Spreadsheets(title="combinedbuff",spreadsheet_token="R5wcsMQ46h2kc0te3P3cKZmSnBJ", saved_file_name="combinedbuff.txt"),
   Spreadsheets(title="race",spreadsheet_token="LEuPssLSbhLt66tFJeOc5Lflnwe", saved_file_name="race.txt"),
   Spreadsheets(title="event",spreadsheet_token="WgEBsLtdDhDAvEtkYN7c3Bqcnug", saved_file_name="event.txt"),
   Spreadsheets(title="pool",spreadsheet_token="Gv98sNzLJhyDtCt8RZ3cm2fhnwf", saved_file_name="pool.txt"),
   Spreadsheets(title="ally",spreadsheet_token="UlwZsEorThMPiTt1AmYcJdKYnFS", saved_file_name="ally.txt"),
   Spreadsheets(title="hero",spreadsheet_token="LljNs1I2ehQoPHtOYk8cIOW2nEe", saved_file_name="hero.txt"),
   Spreadsheets(title="scroll",spreadsheet_token="S2fLsXHCuh258LtDxPVchXJmnEU", saved_file_name="scroll.txt"),
   Spreadsheets(title="prestige",spreadsheet_token="ZkkuspksjhMtOytqq4FcoKMonwT", saved_file_name="reputation_level.txt"),
   Spreadsheets(title="population",spreadsheet_token="OfWOsWfJphz49jtVYlOcIg33nbd", saved_file_name="population_level.txt"),
   Spreadsheets(title="story",spreadsheet_token="JmpWsjAlAhJT1CtwdrRcJ5uynod", saved_file_name="story.txt"),
   Spreadsheets(title="talent",spreadsheet_token="PvWGsF1GOhVN8YtA6X7cVSThnlf", saved_file_name="talent.txt"),
   Spreadsheets(title="task",spreadsheet_token="NR06steuOhgi1gtcNmFc6JdgnUd", saved_file_name="task.txt"),
   Spreadsheets(title="common",spreadsheet_token="B3fZsD2AZhTVHztAjpNcKSibnSd", saved_file_name="common.txt"),
   Spreadsheets(title="unlock",spreadsheet_token="FJXWsFptshQPw0tkKp1cxycbnLb", saved_file_name="unlock.txt"),
   Spreadsheets(title="tcg",spreadsheet_token="RJIDsENs2hoSMmtMPHgcwiIfnVc", saved_file_name="tcg_card_type.txt"),
   Spreadsheets(title="tcgname",spreadsheet_token="N6C9sgsdohpyIdtHKEDcgvnQnSg", saved_file_name="tcg_card.txt"),
   Spreadsheets(title="tcgreward",spreadsheet_token="TSxjsnGxJhQ2DhtuOMmcqdRqnsI", saved_file_name="tcg_award.txt"),
   #Spreadsheets(title="gather",spreadsheet_token="BK9Ps87AqhAdw8tmkWNcHIIqnce", saved_file_name="gather.txt"),
   Spreadsheets(title="dialog",spreadsheet_token="HwOMsd8bshcFs1t9BP2cC4HrnXd", saved_file_name="dialog.txt"),
   Spreadsheets(title="sell",spreadsheet_token="CRces28iphETrftmmPCcHtUDnye", saved_file_name="sell.txt"),
   Spreadsheets(title="gift",spreadsheet_token="N2iPslg2NhePPutZgdOcjn6inQg", saved_file_name="gift.txt"),
   Spreadsheets(title="vip",spreadsheet_token="M9fMsip6jhwcSYtbCWBckKkSnCb", saved_file_name="vip.txt"),
   Spreadsheets(title="battlepass",spreadsheet_token="Jv1DsP20JhmxP9t3LufchX6qnsd", saved_file_name="warpass.txt"),
   Spreadsheets(title="battlepass2",spreadsheet_token="L5w9scNwFhFdrhtcCgVcfdHonIe", saved_file_name="warpass2.txt"),
   Spreadsheets(title="pvp",spreadsheet_token="BNwEsNls6hasmHtG7xHc2UM7nwe", saved_file_name="pvp.txt"),
   Spreadsheets(title="badge",spreadsheet_token="GPMwsnaLihmdIOt2koMcNA60nib", saved_file_name="badge.txt"),
   Spreadsheets(title="sign",spreadsheet_token="W6GNscZrqhNFnLtbEPQc7wznnAh", saved_file_name="sign.txt"),
   Spreadsheets(title="dayreward",spreadsheet_token="Ngx1sCpHUhj9pbt7ap2cuXgZnOe", saved_file_name="dayreward.txt"),
   Spreadsheets(title="lucky",spreadsheet_token="ZQ7gskwd6hcVQjtotb5cRbsmnAg", saved_file_name="lucky.txt"),
   Spreadsheets(title="turntable",spreadsheet_token="R904sgtxlhe0FtttA9jcgVJ3nbb", saved_file_name="turntable.txt"),
   Spreadsheets(title="draw",spreadsheet_token="XKhMsiiSXhalg2tAUxMcYFPunje", saved_file_name="draw.txt"),
   Spreadsheets(title="tower",spreadsheet_token="FTSasvrVPhsMvdtbm5Xcmz6PnEc", saved_file_name="tower.txt"),
]

def compress_dir():
    p = "58598744820488376"
    try:
        import pyminizip
    except ImportError:
        print("[-]未发现依赖项pyminizip，正在自动安装........")
        os.system("pip install pyminizip")
        import pyminizip
        print("[+]完成安装依赖项pyminizip\n")
    file_path = []
    for item in sheet_list:
        file_path.append(os.path.join(DIR_PATH, item.saved_file_name))
    pyminizip.compress_multiple(file_path, [], COMPRESS_FILE, p, 1)
    print(f"[2]打包压缩文件成功:{COMPRESS_FILE}")
    pass


def main():
    """
        1、获取access_token  get_access_token()
        2、遍历sheet_list 获取最新数据，写入文件
    """
    # get_all_sheet_and_print_class_define_str()

    if USE_Tenant_access_token:
        get_access_token()

    count = 0
    for item in sheet_list:

        print(f"bein {item.title}")
        item.simple_sheet_info = get_first_sheet_info(item.spreadsheet_token)
        # pool 只获取前6列
        if item.title == 'pool':
            item.simple_sheet_info.column_count = 6
        # end pool 只获取前6列
        get_content_write_to_file(item)
        count += 1
    print(f"[1]共计{len(sheet_list)}在线配置文件,完成下载更新: {count}个,失败{len(sheet_list) - count}")

    # 打包
    if count == len(sheet_list):
        compress_dir()
    else:
        print("[2]存在失败项，停止打包环节...")


if __name__ == "__main__":
    #get_wiki_spaces()
    #get_space_nodes("7251081850708328449")
    #get_all_sheet_and_print_class_define_str()
    main()
