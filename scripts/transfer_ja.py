# -*- coding: utf-8 -*-
"""
Spyder Editor

This is a temporary script file.
"""
import os
from opencc import OpenCC
import pathlib
from google.cloud import translate_v2 as translate
from collections import defaultdict
from xml.etree import ElementTree as ET
import re

CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(__file__))
# 目标文件放置文件夹
DIR_PATH = pathlib.Path(CURRENT_DIRECTORY).joinpath("../game/src/lite/assets/")
#DIR_PATH = pathlib.Path(CURRENT_DIRECTORY).joinpath("/Users/<USER>/Desktop/diguofanyi/")

STRING_PATH = pathlib.Path(CURRENT_DIRECTORY).joinpath("../game/src/main/res/values/")
STRING_PATH3 = pathlib.Path(CURRENT_DIRECTORY).joinpath("../game/src/oversea/res/values/")
STRING_PATH2 = pathlib.Path(CURRENT_DIRECTORY).joinpath("../core/src/main/res/values/")
# 压缩后的文件名
COMPRESS_FILE = pathlib.Path(CURRENT_DIRECTORY).joinpath("../game/src/main/assets/impossible.mp3")
#COMPRESS_FILE = pathlib.Path(CURRENT_DIRECTORY).joinpath("/Users/<USER>/Desktop/diguofanyi/impossible.mp3")


key = 'AIzaSyDolaH0fBrHHqyco6HsjNuJHWSvOfwwu54'
count = 0


def is_translatable(part):
    # 使用正则表达式检查是否字段仅包含英文字母、数字和特定的英文标点符号
    if re.fullmatch(r'[A-Za-z0-9 ,.;:\-_/?]+', part):
        return False  # 如果字段符合模式，则不需要翻译

    # 如果不符合上述模式，认为需要翻译
    return True

def translate_text(client, text_parts, dest):
    global count  # 声明 count 是全局变量
    # 去重并分批处理翻译
    unique_texts = list(set(text_parts))  # 去重
    max_parts = 128
    translations = {}
    for i in range(0, len(unique_texts), max_parts):
        batch = unique_texts[i:i + max_parts]
        count = count + 1
        print(f"Sending batch {count} {batch}.")  # 打印发送的批次大小
        translated_batch = client.translate(batch, target_language=dest, format_='text')
        translations.update({batch[j]: translated_batch[j]['translatedText'] for j in range(len(batch))})
    return translations

def process_files_in_directory(directory, dest, max_length=51000):
    client = translate.Client(key)
    to_translate = []
    file_structure = defaultdict(list)  # 记录位置信息

    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith((".DS_Store", "pool.txt", "challenge.txt", "common.txt", "level.txt", "pvp.txt", "sign.txt", "tcg_award.txt")):
                continue
            file_path = os.path.join(root, file)
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            for line_index, line in enumerate(lines):
                parts = line.split('\t')
                for part_index, part in enumerate(parts):
                    if is_translatable(part):
                        to_translate.append(part)
                        file_structure[part].append((file_path, line_index, part_index))

    # 翻译去重后的列表
    translations = translate_text(client, to_translate, dest)

    # 写回文件
    for text, locations in file_structure.items():
        translated_text = translations[text]
        for file_path, line_index, part_index in locations:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            lines[line_index] = lines[line_index].split('\t')
            lines[line_index][part_index] = translated_text
            lines[line_index] = '\t'.join(lines[line_index])
            with open(file_path, 'w', encoding='utf-8') as f:
                f.writelines(lines)

def compress_dir():
    p = "58598744820488376"
    try:
        import pyminizip
    except ImportError:
        print("[-]未发现依赖项pyminizip，正在自动安装........")
        os.system("pip install pyminizip")
        import pyminizip
        print("[+]完成安装依赖项pyminizip\n")

    file_path = []
    all_files = os.listdir(DIR_PATH)
    for item in all_files:
        file_path.append(DIR_PATH.joinpath(item).resolve().as_posix())
    pyminizip.compress_multiple(file_path, [], COMPRESS_FILE.resolve().as_posix(), p, 1)
    print(f"[2]打包压缩文件成功:{COMPRESS_FILE.resolve()}")
    pass

def process_file(file_path, dest):
    client = translate.Client(key)
    tree = ET.parse(file_path)
    root = tree.getroot()
    texts_to_translate = []
    translations_map = {}

    # 收集需要翻译的文本
    for string in root.findall('string'):
        text = string.text
        if text and is_translatable(text):
            texts_to_translate.append(text)

    # 翻译文本
    translations = translate_text(client, texts_to_translate, dest)

    # 更新 XML 树结构
    for string in root.findall('string'):
        if string.text in translations:
            string.text = translations[string.text]

    # 写回到文件
    tree.write(file_path, encoding='utf-8', xml_declaration=True)



#dest = 'zh-tw'
dest = 'ko'
#dest = 'ja'
#dest = 'en'
# 处理目录下的所有文件
process_files_in_directory(DIR_PATH, dest)
#compress_dir()
#process_files_in_directory(STRING_PATH2, dest)
#process_files_in_directory(STRING_PATH, dest)