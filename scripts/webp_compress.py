# -*- coding: utf-8 -*-
"""
Optimized script to compress WebP images with the condition to only overwrite
if the compressed file is smaller than the original, and prints statistics.
"""
import os
import subprocess
import pathlib

CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(__file__))

STRING_PATH = pathlib.Path(CURRENT_DIRECTORY).joinpath("/Users/<USER>/Desktop/test/")

def compress_webp(input_dir, output_dir, quality=50, effort=6):
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    total_files = 0
    skipped_files = 0
    original_total_size = 0
    optimized_total_size = 0

    for root, _, files in os.walk(input_dir):
        for file in files:
            if file.lower().endswith('.webp'):
                input_path = os.path.join(root, file)
                output_path = os.path.join(output_dir, file)

                try:
                    # 临时输出路径
                    temp_output_path = output_path + ".temp"

                    # 调用 cwebp 进行有损压缩
                    subprocess.run([
                        'cwebp',
                        '-q', str(quality),
                        '-m', str(effort),
                        input_path,
                        '-o', temp_output_path
                    ], check=True)

                    # 比较文件大小
                    original_size = os.path.getsize(input_path)
                    compressed_size = os.path.getsize(temp_output_path)

                    # 更新统计数据
                    original_total_size += original_size
                    total_files += 1

                    if compressed_size < original_size:
                        optimized_total_size += compressed_size
                        os.replace(temp_output_path, output_path)  # 覆盖原始文件
                        print(f"Compressed {file} and saved to {output_path}")
                    else:
                        optimized_total_size += original_size  # 保持原始大小
                        os.remove(temp_output_path)  # 删除临时文件
                        skipped_files += 1
                        print(f"Compression for {file} was skipped due to larger size.")
                except Exception as e:
                    print(f"Failed to compress {file}: {e}")

    # 计算总减少的体积
    total_reduction = original_total_size - optimized_total_size

    # 打印统计数据
    print("\n--- Compression Statistics ---")
    print(f"Total files processed: {total_files}")
    print(f"Files skipped: {skipped_files}")
    print(f"Original total size: {original_total_size / 1024:.2f} KB")
    print(f"Optimized total size: {optimized_total_size / 1024:.2f} KB")
    print(f"Total size reduced: {total_reduction / 1024:.2f} KB")

if __name__ == "__main__":
    input_directory = STRING_PATH
    output_directory = STRING_PATH
    compress_webp(input_directory, output_directory, quality=65, effort=6)
