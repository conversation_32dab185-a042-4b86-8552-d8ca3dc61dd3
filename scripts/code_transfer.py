# -*- coding: utf-8 -*-
"""
Spyder Editor

This is a temporary script file.
"""
import os 
from os import path
from PIL import Image

imagePath = r"/Users/<USER>/Desktop/test"

mapping_table = {
    'A': 'X',
    'B': 'H',
    'C': 'Q',
    'D': 'N',
    'E': 'Y',
    'F': 'U',
    'G': 'Z',
    'H': 'O',
    'I': 'E',
    'J': 'P',
    'K': 'A',
    'L': 'T',
    'M': 'F',
    'N': 'W',
    'O': 'V',
    'P': 'G',
    'Q': 'K',
    'R': 'C',
    'S': 'M',
    'T': 'I',
    'U': 'J',
    'V': 'R',
    'W': 'L',
    'X': 'S',
    'Y': 'D',
    'Z': 'B',
    '0': '4',
    '1': '7',
    '2': '2',
    '3': '8',
    '4': '6',
    '5': '0',
    '6': '5',
    '7': '9',
    '8': '3',
    '9': '1'
}

def map_string(input_string):
    mapped_string = ""
    for char in input_string:
        if char in mapping_table:
            mapped_string += mapping_table[char]
        else:
            mapped_string += char
    return mapped_string

def process_line(line):
    if line.strip(' ').startswith('"') and len(line) >= 3:
        processed_line = map_string(line.strip(' '))[:28] + '",'
        return processed_line
    else:
        return line
    
    
for parent, _, fileNames in os.walk(imagePath):
   for fileName in fileNames:
        realFilePath = path.join(imagePath, fileName)
        if (realFilePath.endswith(".DS_Store") == False and realFilePath.endswith("_out.kt") == False):
            output_file = path.join(imagePath, fileName.split('.')[0] + "_out.kt")
            with open(realFilePath, 'r') as infile, open(output_file, 'w') as outfile:
                for line in infile:
                    processed_line = process_line(line)
                    print(processed_line)
                    outfile.write(processed_line)
            