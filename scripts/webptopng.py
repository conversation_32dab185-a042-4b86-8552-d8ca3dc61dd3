# -*- coding: utf-8 -*-
"""
Spyder Editor

This is a temporary script file.
"""
import os 
from os import path
from PIL import Image

imagePath = r"/Users/<USER>/Desktop/test"

for parent, _, fileNames in os.walk(imagePath):
   for fileName in fileNames:
        print(fileName)
        realFilePath = path.join(imagePath, fileName)
        # im = Image.open(realFilePath)
        # im.load()
        # targetFilePath = path.join(imagePath, fileName.split('.')[0] + ".png")
        # print(targetFilePath)
        # im.save(targetFilePath)
        
        
        
        # image = Image.open(realFilePath).convert('RGBA')
        # data = image.load()
        # width, height = image.size
        # targetFilePath = path.join(imagePath, fileName.split('.')[0] + ".png")
        # out = open(targetFilePath, "w")
        # out.write('<?xml version="1.0" encoding="UTF-8" standalone="no"?>\n')
        # out.write('<svg id="svg2" xmlns="http://www.w3.org/2000/svg" version="1.1" \
        #             width="%(x)i" height="%(y)i" viewBox="0 0 %(x)i %(y)i">\n' % \
        #           {'x': width, 'y': height})
        
        # for y in range(height):
        #     for x in range(width):
        #         rgba = data[x, y]
        #         rgb = '#%02x%02x%02x' % rgba[:3]
        #         if rgba[3] > 0:
        #             out.write('<rect width="1" height="1" x="%i" y="%i" fill="%s" \
        #                 fill-opacity="%.2f" />\n' % (x, y, rgb, rgba[3]/255.0))
        # out.write('</svg>\n')
        # out.close()
        
        if (realFilePath.endswith(".DS_Store") == False):
            im = Image.open(realFilePath)
            im.load()
            targetFilePath = path.join(imagePath, fileName.split('.')[0] + ".jpeg")
            print(targetFilePath)
            im.save(targetFilePath)
            