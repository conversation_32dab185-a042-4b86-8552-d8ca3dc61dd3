package com.moyu.core.logic.role

import com.moyu.core.logic.identifier.RoleIdentifier
import com.moyu.core.model.role.Role
import com.moyu.core.util.RANDOM

class OneVsOneRoleHolder(private val roles: MutableList<Role>) : GameRoleHolder {
    override fun getRole(roleIdentifier: RoleIdentifier): Role? {
        return getAllRoles().filterNot { it.isOver() }
            .firstOrNull { it.playerId() == roleIdentifier.playerId() }
    }

    override fun getAllRoles(): List<Role> {
        return roles.filterNot { it.isOver() }
    }

    override fun randomRole(): Role {
        return roles.filterNot { it.isOver() }.shuffled(RANDOM).first()
    }

    override fun getEnemies(): List<Role> {
        return roles.filterNot { it.isOver() }.filterNot { it.isPlayerSide() }
    }

    override fun getPlayers(): List<Role> {
        return roles.filterNot { it.isOver() }.filter { it.isPlayerSide() }
    }

    override fun getAllPlayers(): List<Role> {
        return roles.filter { it.isPlayerSide() }
    }

    override fun getAllEnemies(): List<Role> {
        return roles.filterNot { it.isPlayerSide() }
    }

    override fun getMyTeamRoles(attacker: RoleIdentifier): List<Role> {
        return if (attacker.isPlayerSide()) getPlayers() else getEnemies()
    }

    override fun getPeerTeamRoles(attacker: RoleIdentifier): List<Role> {
        return if (attacker.isPlayerSide()) getEnemies() else getPlayers()
    }

    override fun getMinion(role: Role): Role? {
        return getAllRoles().filterNot { it.isOver() }
            .firstOrNull { it.masterId() == role.playerId() }
    }

    override fun addMinion(role: Role) {
        roles.add(role)
    }

    override fun getDirectPeer(role: Role): Role? {
        val team =
            roles.filter { it.isPlayerSide() == role.isPlayerSide() }.filter { !it.isMinion() }
        val indexOfYou = if (role.isMinion()) {
            team.indexOfFirst { role.masterId() == it.playerId() }
        } else {
            team.indexOfFirst { role.playerId() == it.playerId() }
        }
        val enemies =
            roles.filter { it.isPlayerSide() != role.isPlayerSide() }.filter { !it.isMinion() }
        val enemyMinions =
            roles.filter { it.isPlayerSide() != role.isPlayerSide() }.filter { it.isMinion() }
        val otherWiseTarget = enemyMinions.firstOrNull { !it.isOver() } ?: enemies.firstOrNull { !it.isOver() }
        val peer = enemies.filter { !it.isMinion() }.getOrNull(indexOfYou)

        return if (peer == null) {
            // 不存在对面
            otherWiseTarget
        } else {
            // 存在
            if (peer.isOver()) {
                // 但是已经死了
                otherWiseTarget
            } else {
                getMyMaster(peer) ?: peer
            }
        }
    }

    override fun getMyMaster(role: Role): Role? {
        return getAllRoles().firstOrNull { it.playerId() == role.masterId() }
    }

    override fun getMyMinion(role: Role): Role? {
        return getAllRoles().firstOrNull { it.masterId() == role.playerId() }
    }
}