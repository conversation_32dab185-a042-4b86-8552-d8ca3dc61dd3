package com.moyu.core.logic.skill

import com.moyu.core.AppWrapper
import com.moyu.core.R
import com.moyu.core.model.skill.Skill
import com.moyu.core.util.percentValueToDot
import com.moyu.core.util.realValueToDotWithNoDigits
import com.moyu.core.util.realValueToDotWithOneDigits

fun Skill.getNameInfo() = "【$name】"

fun Skill.getDisplayName() = if (level > 0) name + " " + level + AppWrapper.getString(R.string.level) else name

fun Skill.getLevelName() = if (level > 0) level.toString() + AppWrapper.getString(R.string.level1) else ""

fun Skill.getRealDesc(): String {
    return desc.replace("\\n", "\n").replace("%r", rate.toString())
        .replace("%t", buffContinue.toString()).let {
            var result = it
            (15 downTo 0).forEach { index ->
                result = replaceEffectValue(result, index)
            }
            result
        }
}

private fun Skill.replaceEffectValue(desc: String, index: Int): String {
    return if (desc.contains("%d${index + 1}") && effectNum.getOrNull(index) != null) {
        desc.replace("%d${index + 1}", effectNum[index].realValueToDotWithNoDigits())
    } else if (desc.contains("%p${index + 1}") && effectNum.getOrNull(index) != null) {
        desc.replace("%p${index + 1}", effectNum[index].percentValueToDot())
    }else desc
}