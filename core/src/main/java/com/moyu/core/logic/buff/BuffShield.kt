package com.moyu.core.logic.buff

import com.moyu.core.model.buff.Buff
import com.moyu.core.model.damage.DamageType
import com.moyu.core.util.forEachReversedWithIndex
import kotlin.math.roundToInt

const val DAMAGE_TYPE_SHIELD_BASE = 5100
const val ALL_SHIELD = 5111
const val HOLY_SHIELD = 5112

const val SHIELD_INC = 5403
const val SHIELD_DEC = 5404

fun Buff.isShield(): Boolean {
    return this.id in DAMAGE_TYPE_SHIELD_BASE + 1..DAMAGE_TYPE_SHIELD_BASE + 11
}

fun getShieldBuffIdByDamageType(damageType: DamageType): Int {
    return DAMAGE_TYPE_SHIELD_BASE + damageType.value
}

fun DefaultBuffCarrier.updateShieldById(leftShield: Int, shieldId: Int, isHoly: Boolean = true) {
    var leftShieldTotal = leftShield
    var beginToRemove = false
    val newList = getBuffList().toMutableList().apply {
        forEachReversedWithIndex { index, buff ->
            if (buff.id == shieldId) {
                if (beginToRemove) {
                    remove(buff)
                } else {
                    // 注意立场原始数值，可能被5037/5038增强过，所以这里比较复杂
                    // 一开始要除shieldIncrease，因为leftShieldTotal是增强过后的数值
                    val shieldIncrease = if (isHoly) 1.0 else 1 + getSumValuePercentById({ it.id == SHIELD_INC }) - getSumValuePercentById({ it.id == SHIELD_DEC })
                    if (buff.buffValue >= leftShieldTotal / shieldIncrease) {
                        // 倒着找，如果buff里的立场值大于剩余立场值，仅需要保留这一个立场，其他都删除即可
                        beginToRemove = true
                        this[index] = buff.copy(buffValue = (leftShieldTotal / shieldIncrease).roundToInt().toDouble())
                    } else {
                        // 否则，这个buff里的立场值全保留，继续找
                        leftShieldTotal -= (buff.buffValue * shieldIncrease).roundToInt()
                    }
                }
            }
        }
    }
    setBuffList(newList)
}