package com.moyu.core.logic.buff




const val HOT = 1101
val DOT = (1001..1010).toList()
const val DAMAGE_CHANGE_BASE = 5500

const val FROZEN = 4001 // 沉默
const val HEAL_FORBID = 4002 // 禁疗
const val FRENZY = 4003 // 狂暴
const val PALSY = 4004 // 麻痹
const val DISARM = 4005 // 缴械
const val BAN_SKILL_BASE = 4100 // 禁用技能系 (4101-4110为1系到10系)

const val FROZEN_IMMUNE = 4201 // 沉默
const val HEAL_FORBID_IMMUNE = 4202 // 禁疗
const val FRENZY_IMMUNE = 4203 // 狂暴
const val PALSY_IMMUNE = 4204 // 麻痹
const val DISARM_IMMUNE = 4205 // 缴械
const val CONTROL_IMMUNE = 4206


const val IMMUNE = 5001
const val SHIELD_IGNORE = 5002
const val DODGE_IMMUNE = 5003
const val TAUNT_IMMUNE = 5004
const val AVOID_DEATH_IMMUNE = 5005
const val BUFF_DISPEL_IMMUNE = 5006
const val AVOID_DEATH = 5007
const val DOUBLE_SKILL = 5008
const val DEBUFF_DISPEL_IMMUNE = 5009
const val SINGLE_DAMAGE_LIMIT = 5010
const val TAUNT = 5011
const val POISON_HEAL = 5012
const val DIVIDE_ATTACK = 5013
const val SUCK_BLOOD_IMMUNE = 5014

const val DAMAGE_IMMUNE_BASE = 5200
const val DAMAGE_PIERCE_BASE = 5301
