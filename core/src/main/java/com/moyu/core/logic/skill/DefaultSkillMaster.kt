package com.moyu.core.logic.skill

import com.moyu.core.GameCore
import com.moyu.core.logic.battle.BattleField
import com.moyu.core.logic.battle.skillChainHandler
import com.moyu.core.logic.identifier.RoleIdentifier
import com.moyu.core.logic.info.addDoubleSkillInfo
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.SkillEnhancement
import com.moyu.core.model.skill.SkillEnhancementType
import com.moyu.core.model.skill.enhancement16SkillTurnLimitInc
import com.moyu.core.model.skill.enhancement17SkillTurnLimitDec
import com.moyu.core.model.skill.enhancement18SkillAllLimitInc
import com.moyu.core.model.skill.enhancement19SkillAllLimitDec
import com.moyu.core.model.skill.isAdventure
import com.moyu.core.model.skill.isBattleSkill
import com.moyu.core.model.timing.Timing
import java.util.concurrent.CopyOnWriteArrayList

class DefaultSkillMaster : SkillMaster {
    private val skillList = CopyOnWriteArrayList<Skill>()
    private val skillGraveList = CopyOnWriteArrayList<Skill>()

    override fun getSkills(): List<Skill> {
        return skillList.toList()
    }

    override fun setSkills(skills: List<Skill>) {
        skillList.clear()
        skillList.addAll(skills)
    }

    override fun setGraveSkills(skills: List<Skill>) {
        skillGraveList.clear()
        skillGraveList.addAll(skills)
    }

    override fun forgetSkill(skill: Skill) {
        skillList.removeAll { it.id == skill.id }
    }

    override fun oneYearPass() {
        skillList.map {
            it.nextYear()
        }.let {
            skillList.clear()
            skillList.addAll(it)
        }
    }

    override fun getGraveSkills(): List<Skill> {
        return skillGraveList.toList()
    }

    override suspend fun triggerSkillByType(
        field: BattleField,
        triggerType: List<Int>,
        skillOwner: Role,
        triggerSkill: Skill?,
        triggerChainEnabled: Boolean
    ) {
        val tobeTrigger = skillList.filter { !it.isAdventure() }.filter { skill -> // 移除不可再生效，即skillGrave
            triggerType.any { skill.triggerType == it } // 筛选对应的triggerType的技能list
        }.sortedBy { it.priority /*按照优先级排序*/ }
        tobeTrigger.forEach { originSkill ->
            var skill = originSkill
            val trigger = DefaultSkillTrigger.trigger(skill, field, skillOwner, triggerSkill)
            if (!trigger) {
                return@forEach
            }
            val inGrave = skillOwner.getGraveSkills().find { it.id == skill.id && skill.uuid == it.uuid } != null
            if (inGrave) {
                return@forEach
            }
            val needCoolDown = skill.needCoolDown(skillOwner.getRealCoolDown(skill))
            if (needCoolDown) {
                return@forEach
            }
            repeat(skillOwner.skillTimes(skill) + 1) {
                // 生效后，判定是否临时移除
                // 这里会改变skill，所以后续要用新的skill
                skill = checkIfAddGraveSkill(skill, skillOwner)
                skill = markReCoolDown(skill, skillOwner)
                // 多重施法仅对3有效
                if (it > 0) {
                    GameCore.instance.addDoubleSkillInfo(skillOwner)
                }
                if (it == 0 || skill.triggerType.canDoubleSkill()) {
                    skill = skill.doEffect(field, skillOwner, triggerSkill)
                }
                field.checkDeathProcess()
                if (field.gameFinished()) { // 任意角色死亡，中断技能处理流程
                    return
                }
                // 只要设置了回调的，需要回调出去，判定其他逻辑，之类是回调处理
                if (triggerChainEnabled) skillChainHandler.invoke(skill, field)
                field.checkDeathProcess()
                if (field.gameFinished()) { // 任意角色死亡，中断技能处理流程
                    return
                }
            }
        }
    }

    override fun learnSkill(skill: Skill, ownerIdentifier: RoleIdentifier) {
        skillList.add(skill.copy(ownerIdentifier = ownerIdentifier))
    }

    override fun addGraveSkill(skill: Skill) {
        skillGraveList.add(skill)
    }

    override fun clearGrave(timing: Timing) {
        when (timing) {
            Timing.TurnBegin -> {
                // 这里有判定只有grave > 0 的才清理，所以enhancement20这种计数不会被清理，可以整场实现效果
                skillGraveList.filter { it.grave > 0 }.forEach { targetSkill ->
                    resetGraveCount(targetSkill)
                }
                // special > 0 说明每回合结束要清理
                skillGraveList.removeAll {
                    it.grave > 0
                }
            }
            else -> {
                skillGraveList.forEach { targetSkill ->
                    resetGraveCount(targetSkill)
                }
                // 其他则全部清理，比如special == 2，正常战斗只能生效一次的技能，在这里清理
                skillGraveList.clear()
            }
        }
    }

    private fun resetGraveCount(targetSkill: Skill) {
        skillList.indexOfFirst { it.id == targetSkill.id }.takeIf { it != -1 }?.let { index ->
            skillList[index] = skillList[index].copy(graveCount = 0)
        }
    }

    override fun getOnlyNormalSkills(): List<Skill> {
        return skillList.filter { it.isBattleSkill() }
    }

    override fun hasLearnedSkill(skill: Skill): Boolean {
        return skillList.find {
            it.mainId == skill.mainId
        } != null
    }

    override fun enhanceSkill(
        skill: Skill,
        enhancementId: SkillEnhancementType,
        value: Double,
    ) {
        skillList.indexOfFirst { it.id == skill.id }.takeIf { it >= 0 }?.let { index ->
            val current = skillList[index].enhancementOneGame
            val enhancement = SkillEnhancement(
                enhancementId = enhancementId, enhancementValue = value
            )
            val newList =
                if (current.isEmpty()) mutableListOf(enhancement) else current.toMutableList()
                    .apply {
                        add(enhancement)
                    }
            skillList[index] = skillList[index].copy(enhancementOneGame = newList)
        }
    }

    override fun markSkillNewTurn(role: Role) {
        skillList.forEachIndexed { index, skill ->
            if (role.getRealCoolDown(skill) > 0) {
                skillList[index] = skill.copy(currentCoolDown = skill.currentCoolDown + 1)
            }
        }
    }

    override fun checkIfAddGraveSkill(skill: Skill, skillOwner: Role): Skill {
        val realGrave = if (skill.grave == 0) {
            0
        } else if (skill.grave > 0) {
            maxOf(1,
                skill.grave + (skill.getAllEnhancements()
                    .filter { it.enhancementId == enhancement16SkillTurnLimitInc }
                    .sumOf { it.enhancementValue } - skill.getAllEnhancements()
                    .filter { it.enhancementId == enhancement17SkillTurnLimitDec }
                    .sumOf { it.enhancementValue }).toInt()
            )
        } else {
            // 这里是-1，整场战斗1次
            minOf(-1, (skill.grave - skill.getAllEnhancements()
                .filter { it.enhancementId == enhancement18SkillAllLimitInc }
                .sumOf { it.enhancementValue } + skill.getAllEnhancements()
                .filter { it.enhancementId == enhancement19SkillAllLimitDec }
                .sumOf { it.enhancementValue }).toInt())
        }
        when (realGrave) {
            in -99..-1 -> {
                if (skill.graveCount - 1 <= realGrave) addGraveSkill(skill)
                else {
                    skillList.indexOfFirst { it.id == skill.id }.takeIf { it >= 0 }?.let { index ->
                        skillList[index] = skillList[index].copy(
                            graveCount = skill.graveCount - 1
                        )
                    }
                }
            }
            in 1..99 -> {
                if (skill.graveCount + 1 >= realGrave) addGraveSkill(skill)
                else {
                    skillList.indexOfFirst { it.id == skill.id }.takeIf { it >= 0 }?.let { index ->
                        skillList[index] = skillList[index].copy(
                            graveCount = skill.graveCount + 1
                        )
                    }
                }
            }
        }
        return skill.refreshSkill(skillOwner)
    }

    override fun markReCoolDown(skill: Skill, skillOwner: Role): Skill {
        skillList.indexOfFirst { it.id == skill.id }.takeIf { it >= 0 }?.let { index ->
            skillList[index] = skillList[index].copy(
                // 为什么是-1，因为技能使用后，下一回合开始，会+1，那么就到了0，这个时候，如果coolDown是1
                // ，则要再等一回合，符合预期，如果coolDown被buff-1了，则又可以使用
                currentCoolDown = -1
            )
        }
        return skill.refreshSkill(skillOwner)
    }

    override fun clearCoolDown() {
        skillList.forEachIndexed { index, skill ->
            skillList[index] = skill.copy(
                // 让所有技能冷却完成。
                currentCoolDown = 100
            )
        }
    }

    override fun replaceSkillsFromPool(role: Role) {
        val newList = getSkills().map { skill ->
            skill.copy(ownerIdentifier = role, enhancementOneGame = skill.enhancementOneGame)
        }
        skillList.clear()
        skillList.addAll(newList)
    }

    override fun toPersistData(): SkillData {
        return SkillData(
            skills = skillList,
            graveSkills = skillGraveList
        )
    }
}