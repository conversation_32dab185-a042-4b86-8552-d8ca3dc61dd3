package com.moyu.core.logic.enemy

import com.moyu.core.GameCore
import com.moyu.core.logic.identifier.Identifier
import com.moyu.core.logic.level.DefaultLevelController
import com.moyu.core.model.property.Property
import com.moyu.core.model.race.Race
import com.moyu.core.model.role.Role
import com.moyu.core.util.RANDOM

interface RoleCreator {
    fun create(race: Race, diffProperty: Property, extraSkills: List<Int> = emptyList()): Role
    fun createDetailed(
        race: Race,
        diffProperty: Property,
        roleIdentifier: Identifier,
        extraSkills: List<Int>,
        needRandomSkill: Boolean = !roleIdentifier.isPlayer()
    ): Role {
        return Role(
            roleIdentifier = roleIdentifier,
            updateId = RANDOM.nextLong(),
            roleLevel = DefaultLevelController().apply {
                setLevel(1)
            }).apply {
            this.setRace(race)
            this.createSolidSkills(race)
            if (needRandomSkill) {
                this.createRandomSkills(race)
            }
            this.createExtraSkills(extraSkills)
            this.setProperty(race, diffProperty)
        }
    }
}

fun Role.setProperty(race: Race, diffProperty: Property) {
    val targetProperty = (race.getProperty() + diffProperty).ensureNotNegative()
    setInitProperty(targetProperty)
    setPropertyToDefault()
}

fun Role.createSolidSkills(race: Race) {
    race.skillId.filter { it != 0 }.forEach { id ->
        GameCore.instance.getSkillPool().firstOrNull { it.id == id }?.let { skill ->
            this.learnSkill(skill, this.roleIdentifier)
        }
    }
}

fun Role.createRandomSkills(race: Race) {
    race.randomSkillId.shuffled(RANDOM).take(race.randomSkillNum.first()).forEach { skillId ->
        GameCore.instance.getSkillPool().firstOrNull { it.id == skillId }?.let { skill ->
            this.learnSkill(skill, this.roleIdentifier)
        }
    }
}

fun Role.createExtraSkills(extraSkills: List<Int>) {
    extraSkills.forEach { skillId ->
        GameCore.instance.getSkillPool().firstOrNull { it.id == skillId }?.let { skill ->
            this.learnSkill(skill, this.roleIdentifier)
        }
    }
}