package com.moyu.core.logic.damage.processor

import com.moyu.core.model.damage.DamageStatus
import com.moyu.core.model.damage.DamageType
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.Skill

fun Int.subTypeToDamageType(): DamageType {
    return DamageType.fromTypeValue(this)!!
}

// 详情参考buff.txt
fun Int.buffEffectTypeToDamageType(): DamageType {
    return (this % 1000).subTypeToDamageType()
}

object DamageProcessorFactory {
    fun getDamageProcessor(
        type: DamageType,
        attacker: Role,
        victim: Role,
        skill: Skill,
        initDamage: Int = 0,
        damageStatus: DamageStatus = DamageStatus()
    ): DamageProcessor {
        return when (type.value) {
            1 -> PhysicDamageProcessor(
                damageType = type,
                attacker = attacker,
                victim = victim,
                initDamage = initDamage,
                skill = skill,
                initDamageStatus = damageStatus
            )
            in 2..5 -> ElementDamageProcessor(
                damageType = type,
                attacker = attacker,
                victim = victim,
                initDamage = initDamage,
                skill = skill
            )
            else -> RealDamageProcessor(
                damageType = type,
                attacker = attacker,
                victim = victim,
                initDamage = initDamage,
                skill = skill
            )
        }
    }
}