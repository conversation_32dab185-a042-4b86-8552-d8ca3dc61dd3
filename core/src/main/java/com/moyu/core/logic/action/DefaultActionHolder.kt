package com.moyu.core.logic.action

import com.moyu.core.GameCore
import com.moyu.core.logic.buff.AVOID_DEATH
import com.moyu.core.model.action.ActionState
import com.moyu.core.model.action.ActionStateType
import com.moyu.core.model.buff.Buff
import com.moyu.core.model.damage.DamageResult
import com.moyu.core.model.damage.DamageType
import com.moyu.core.model.heal.HealResult
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.SkillEffectType
import com.moyu.core.music.SoundEffect
import java.util.concurrent.CopyOnWriteArrayList

fun getEffectAudioByDamageType(damageType: DamageType): SoundEffect {
    return when (damageType) {
        DamageType.DamageType1 -> SoundEffect.Damage1
        DamageType.DamageType2 -> SoundEffect.Damage2
        DamageType.DamageType3 -> SoundEffect.Damage3
        DamageType.DamageType4 -> SoundEffect.Damage4
        else -> SoundEffect.Damage5
    }
}

class DefaultActionHolder : ActionHolder {
    private var stateList = CopyOnWriteArrayList<ActionState>()

    override fun getStateList(): List<ActionState> {
        return stateList
    }

    override fun setBeingAttackState(skill: Skill, damage: DamageResult) {
        GameCore.instance.onBattleEffect(getEffectAudioByDamageType(damage.type))
        stateList.add(
            ActionState(
                ActionStateType.BeingAttack,
                skill,
                damage,
                damage.damageValue.finalDamage
            )
        )
    }

    override fun setBeingBuffState(buff: Buff) {
        val sound =
            if (buff.id == AVOID_DEATH) SoundEffect.AvoidDeath
            else if (buff.isControl()) SoundEffect.Control
            else if (buff.buffType == 1) SoundEffect.GetBuff else SoundEffect.GetDebuff
        GameCore.instance.onBattleEffect(sound)
        if (hasState(ActionStateType.BeingBuff)) return
        stateList.add(
            ActionState(
                ActionStateType.BeingBuff,
                buff = buff
            )
        )
    }

    override fun setDispelState() {
        GameCore.instance.onBattleEffect(SoundEffect.Dispel)
        stateList.add(
            ActionState(
                ActionStateType.BeingDispel
            )
        )
    }

    override fun setState(type: ActionStateType) {
        if (type == ActionStateType.Summoning) {
            GameCore.instance.onBattleEffect(SoundEffect.Summon)
        }
        stateList.add(ActionState(type))
    }

    override fun setBeingHeal(skill: Skill, realHealPoint: HealResult) {
        GameCore.instance.onBattleEffect(SoundEffect.Heal)
        stateList.add(ActionState(ActionStateType.BeingHeal, skill, healResult = realHealPoint))
    }

    override fun clearAnimationState() {
        stateList = CopyOnWriteArrayList<ActionState>()
    }

    override fun setDoAttackState(skill: Skill, targets: List<String>) {
        stateList.add(
            ActionState(
                ActionStateType.DoAttack,
                skill = skill,
                targets = targets
            )
        )
    }

    override fun doSkillState(skill: Skill, skillType: SkillEffectType) {
        stateList.add(ActionState(ActionStateType.DoSkill, skill))
        stateList.add(ActionState(ActionStateType.Jump))
    }

    override fun hasState(beingAttack: ActionStateType): Boolean {
        return stateList.find { it.type == beingAttack } != null
    }
}
