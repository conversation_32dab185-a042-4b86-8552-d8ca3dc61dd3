package com.moyu.core.logic.action

import com.moyu.core.model.action.ActionState
import com.moyu.core.model.action.ActionStateType
import com.moyu.core.model.buff.Buff
import com.moyu.core.model.damage.DamageResult
import com.moyu.core.model.heal.HealResult
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.SkillEffectType

interface ActionHolder {
    fun getStateList(): List<ActionState>
    fun setBeingAttackState(skill: Skill, damage: DamageResult)
    fun setBeingBuffState(buff: Buff)
    fun setDispelState()
    fun setState(type: ActionStateType)
    fun setBeingHeal(skill: Skill, realHealPoint: HealResult)
    fun clearAnimationState()
    fun setDoAttackState(skill: Skill, targets: List<String>)
    fun doSkillState(skill: Skill, skillType: SkillEffectType)
    fun hasState(beingAttack: ActionStateType): Boolean
}