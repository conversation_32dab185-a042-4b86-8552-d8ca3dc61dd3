package com.moyu.core.logic.battle

import com.moyu.core.GameCore
import com.moyu.core.logic.gameover.GameJudge
import com.moyu.core.logic.info.addFailMaxTurn
import com.moyu.core.logic.info.addTurnBeginInfo
import com.moyu.core.logic.recorder.GameDataRecorder
import com.moyu.core.logic.role.GameRoleHolder
import com.moyu.core.logic.role.OneVsOneRoleHolder
import com.moyu.core.logic.skill.TurnBegin
import com.moyu.core.logic.skill.TurnEnd
import com.moyu.core.logic.turn.GameTurnHolder
import com.moyu.core.model.role.Role
import com.moyu.core.model.timing.Timing
import com.moyu.core.music.SoundEffect

const val BATTLE_MAX_TURNS = 99

class BattleController(
    private val roles: MutableList<Role>,
    private val battleField: BattleField = BattleField(OneVsOneRoleHolder(roles)),
) : BattleHolder, GameJudge by battleField, GameDataRecorder by battleField,
    GameRoleHolder by battleField, GameTurnHolder by battleField {

    override suspend fun startBattle() {
        GameCore.instance.onTurnBegin()
        battleField.clearAllAndUpdate()
        battleField.doUpdateSequence()
        doPassiveSkill()
        battleField.doUpdateSequence()

        if (!GameCore.instance.callback.getDebugConfig().singleStep) {
            while (!terminated() && !isBattleOver()) {
                doOneTurn()
            }
        }
    }

    override suspend fun nextStep() {
        if (!terminated() && !isBattleOver()) {
            doOneTurn()
        }
    }

    private suspend fun iterateRolesAction(action: suspend (Role) -> Unit) {
        var exit = false
        while (!exit && battleField.getAllRoles().any { !it.isActioned() }) {
            battleField.getAllRoles().filterNot { it.isActioned() }
                .maxByOrNull { it.getCurrentProperty().speed }?.let {
                    battleField.checkDeathProcess()
                    if (battleField.gameFinished()) {
                        exit = true
                    } else {
                        it.setActioned(true)
                        action(it)
                    }
                }
        }
        getAllRoles().forEach {
            it.setActioned(false)
        }
    }

    override fun getBattleField(): BattleField {
        return battleField
    }

    private suspend fun doOneTurn() {
        oneTurnProcess()
        oneTurnEndProcess()
        GameCore.instance.onTurnBegin()
    }

    private suspend fun oneTurnEndProcess() {
        battleField.clearHeal()
        battleField.nextTurn()
        battleField.clearSkillGrave(Timing.TurnBegin)
        battleField.doUpdateSequence()
    }

    private suspend fun cleanBattleField(gameOver: Boolean) {
        // 战斗结束，清理战场
        battleField.clearAllAndUpdate()
        if (!gameOver) battleField.dealAward()
        battleField.setGameOver()
        GameCore.instance.onBattleEnd(
            gameOver, battleField.getTurn(), battleField.getAllPlayers(), battleField.getAllEnemies()
        )
    }

    private suspend fun doRoleSkillProcess() {
        // 这个角色的所有buff生效一次，主要是dot和hot
        battleField.attacker()?.let {
            battleField.effectRoleBuffs(it)
        }
        // 这个角色的所有buff更新下回合数/其实其他人的buff也会检查，因为可能达到了最大回合数
        battleField.updateAllBuffTurn()

        // 这里还要加一个检查，防止buff烧死了，被回合初回血技能救活
        battleField.checkDeathProcess()
        if (battleField.gameFinished()) return
        // TODO 回合数逻辑需要优化，现在只能把这个放这里，不然1回合直接过去了
        // 意思是，如果通过TurnBegin触发了buff，如果这个在doAttackerBuff之前，则buff会直接扣除一回合。如果buff只持续1回合，则直接消失了
        attackerTurnBegin()

        battleField.checkDeathProcess()
        if (battleField.gameFinished()) return

        doSkillSequence()

        battleField.checkDeathProcess()
        if (battleField.gameFinished()) return
        attackerTurnEnd()
    }


    private suspend fun oneTurnProcess() {
        // 根据速度来执行流程
        iterateRolesAction {
            setOnesTurn(it)
            onesTurnProcess()
        }
    }
    
    private suspend fun onesTurnProcess() {
        GameCore.instance.onBattleEffect(SoundEffect.TurnBegin)
        // 角色和他的仆从，都要执行完整流程
        battleField.attacker()?.let { role ->
            GameCore.instance.addTurnBeginInfo(role, battleField.getTurn())
            battleField.checkBuffCondition()
            doRoleSkillProcess()
            battleField.checkBuffCondition()
        }
    }
    
    private suspend fun attackerTurnBegin() {
        battleField.getAllRoles().forEach {
            battleField.attacker()?.let { attacker ->
                it.triggerSkillByType(
                    field = battleField,
                    triggerType = listOf(1, 2),
                    skillOwner = it,
                    triggerSkill = TurnBegin.copy(ownerIdentifier = attacker),
                )
            }
        }
    }

    private suspend fun attackerTurnEnd() {
        battleField.getAllRoles().forEach {
            battleField.attacker()?.let { attacker ->
                it.triggerSkillByType(
                    field = battleField,
                    triggerType = listOf(1, 2),
                    skillOwner = it,
                    triggerSkill = TurnEnd.copy(ownerIdentifier = attacker),
                )
            }
        }
    }
    private suspend fun doPassiveSkill() {
        iterateRolesAction {
            battleField.setOnesTurn(it)
            it.triggerSkillByType(
                field = battleField,
                triggerType = listOf(1),
                skillOwner = it,
                triggerChainEnabled = false
            )
        }
        battleField.setPassiveTurn(false)
    }

    private suspend fun doSkillSequence() {
        battleField.checkDeathProcess()
        if (battleField.gameFinished()) return
        // 显示被控制战报
        battleField.attacker()?.checkStatusCanActiveSkill()
        battleField.attacker()?.checkStatusCanTriggerSkill()
        // 1. 检查进攻方trigger type == 2(被动条件生效)的技能，直接释放并生效，包括buff，也是直接生效
        battleField.attacker()?.let {
            it.triggerSkillByType(
                field = battleField,
                triggerType = listOf(1, 2),
                skillOwner = it,
            )
        }
        battleField.checkDeathProcess()
        if (battleField.gameFinished()) return

        battleField.attacker()?.let {
            it.triggerSkillByType(
                field = battleField,
                triggerType = listOf(3, 4),
                skillOwner = it,
            )
        }
    }

    private suspend fun isBattleOver(): Boolean {
        if (battleField.gameFinished()) {
            if (battleField.getWinFlag()) {
                // 如果是直接胜利，则设置对方都为-1血，然后正常判定胜负
                val winFlagPlayer = battleField.getWinFlagPlayer()
                battleField.getPeerTeamRoles(winFlagPlayer).forEach {
                    it.setCurrentHp(-1)
                }
            }
            cleanBattleField(!battleField.getEnemies().all { it.isDeath() })
            return true
        }
        if (battleField.getTurn() > BATTLE_MAX_TURNS) {
            GameCore.instance.addFailMaxTurn()
            cleanBattleField(true)
            return true
        }
        return false
    }
}