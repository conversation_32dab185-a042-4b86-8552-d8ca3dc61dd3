package com.moyu.core.logic.damage.processor

import com.moyu.core.AppWrapper
import com.moyu.core.GameCore
import com.moyu.core.R
import com.moyu.core.logic.skill.getNameInfo
import com.moyu.core.model.damage.DamageResult
import com.moyu.core.model.damage.DamageStatus
import com.moyu.core.model.damage.DamageType
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.Skill
import com.moyu.core.util.percentValueToDotWithNoDigits
import com.moyu.core.util.percentValueToDotWithNoDigits
import com.moyu.core.util.realValueToDotWithNoDigits
import kotlin.math.max
import kotlin.math.min
import kotlin.math.roundToInt

interface DamageProcessor {
    val damageType: DamageType
    val attacker: Role
    val victim: Role
    val skill: Skill
    val initDamage: Int
    val initDamageStatus: DamageStatus
    fun process(): DamageResult
    fun getCommonValue(damageType: DamageType, direction: Int): Double {
        return (if (damageType == DamageType.DamageType1) {
            GameCore.instance.getConstA()
        } else {
            GameCore.instance.getConstB()
        }) * direction
    }

    fun innerProcess(damageStatus: DamageStatus): DamageResult {
        val fatalDamage =
            if (damageStatus.isFatal) attacker.getCurrentProperty().getRealFatalDamage() else 1.0
        val skillDamageRate = attacker.skillDamageInc(skill, damageType)
        val damageRate = attacker.getCurrentProperty().getDamageIncreaseByType(damageType)
        val raceRate = attacker.raceDamageInc(skill, victim.getRace())
        val normalAttackRate = if (skill.isNormalAttackType()) attacker.getCurrentProperty().getNormalAttackRate() else 0.0
        val allRate = attacker.getCurrentProperty().getAllDamageRate()
        val defense = victim.getCurrentProperty().getDefenseByType(damageType)
        val pierce = attacker.getPierceByType(skill, damageType)
        val realDefense = max (0.0, defense - defense * pierce)
        val direction = 1 // if (realDefense > 0) 1 else -1 // 支持负数的护甲和抗性 // 该游戏不支持负数护甲
        val defenseEffect =
            realDefense / (realDefense + getCommonValue(
                damageType,
                direction
            ) /** * victim.getLevel()**/) * direction // 等级恒等于1
        val beingDamageRate =
            victim.getCurrentProperty().getReduceDamageIncreaseByType(damageType)
        val raceBeingRate = victim.getCurrentProperty()
            .getReduceDamageIncreaseByRaceType(attacker.getRace().raceType)
        val allBeingDamageReduce = victim.getCurrentProperty().getAllReduceDamageRate()

        val damage =
            initDamage.toDouble() * (1.0 + skillDamageRate + normalAttackRate) * (1.0 + damageRate) * (1.0 + raceRate) * (1.0 + allRate) * fatalDamage
        val finalDamage =
            if (damageStatus.isDodge || damageStatus.isImmune || damageStatus.isHolyShield) 0
            else {
                max(
                    1,
                    (damage * (1.0 - defenseEffect) * (1.0 - beingDamageRate) * (1.0 - raceBeingRate) * (1.0 - allBeingDamageReduce)).roundToInt()
                )
            }

        val singleLimit = victim.singleDamageLimit() // 返回比例，50就是50%最大生命值的意思，-1表示无限制
        val terminalDamage = if (GameCore.instance.getDebugConfig().unbreakable) {
            if (victim.isPlayer()) 0 else 999999999
        } else {
            if (singleLimit == -1) finalDamage else {
                min(singleLimit, finalDamage)
            }
        }

        val processInfo =
            buildString {
                append("\n" + damageType.display + AppWrapper.getString(R.string.damage_record) + skill.getNameInfo())
                append("\n" + AppWrapper.getString(R.string.attacker_with_comma))
                append(attacker.getSideName())
                append("\n" + AppWrapper.getString(R.string.defender_with_comma))
                append(victim.getSideName())
                append("\n" + AppWrapper.getString(R.string.initial_damage_with_comma))
                append(initDamage)
                append("\n" + AppWrapper.getString(R.string.fatal_with_comma))
                append(damageStatus.isFatal.display())
                append((if (damageStatus.isFatal) "\n" + AppWrapper.getString(R.string.fatal_damage_with_comma) + fatalDamage.percentValueToDotWithNoDigits() else ""))
                append("\n" + AppWrapper.getString(R.string.race_damage_with_comma))
                append(raceRate.percentValueToDotWithNoDigits())
                append("\n" + AppWrapper.getString(R.string.normal_attack_with_comma))
                append(normalAttackRate.percentValueToDotWithNoDigits())
                append("\n" + AppWrapper.getString(R.string.skill_damage_with_comma))
                append(skillDamageRate.percentValueToDotWithNoDigits())
                append("\n" + (damageType.display + AppWrapper.getString(R.string.attacker_damage_inc)))
                append(damageRate.percentValueToDotWithNoDigits())
                append("\n" + AppWrapper.getString(R.string.attacker_damage_inc_all))
                append(allRate.percentValueToDotWithNoDigits())
                append("\n" + AppWrapper.getString(R.string.damage_value))
                append(damage.realValueToDotWithNoDigits())
                append("\n" + "=========================================")
                append("\n" + (AppWrapper.getString(R.string.defender_init_defense)  + ":"))
                append(defense)
                append("\n" + (damageType.display + AppWrapper.getString(R.string.pierce_attacker) + pierce.percentValueToDotWithNoDigits()))
                append("\n" + (AppWrapper.getString(R.string.defender_real_defense)  + ":"))
                append(realDefense)
                append(
                    "\n" + AppWrapper.getString(
                        R.string.defender_reduce_damage
                    )
                )
                append(defenseEffect.percentValueToDotWithNoDigits())
                append("\n" + (damageType.display + AppWrapper.getString(R.string.defender_reduce_value)))
                append(beingDamageRate.percentValueToDotWithNoDigits())
                append("\n" + AppWrapper.getString(R.string.defender_race_reduce))
                append(raceBeingRate.percentValueToDotWithNoDigits())
                append("\n" + AppWrapper.getString(R.string.defender_all_reduce))
                append(allBeingDamageReduce.percentValueToDotWithNoDigits())
                append("\n" + AppWrapper.getString(R.string.defender_immune))
                append(damageStatus.isImmune.display())
                append("\n" + AppWrapper.getString(R.string.defender_holy_shield))
                append(damageStatus.isHolyShield.display())
                append("\n" + AppWrapper.getString(R.string.defender_dodge))
                append(damageStatus.isDodge.display())
                append(
                    (if (singleLimit != -1) {
                        "\n" + AppWrapper.getString(R.string.single_damage) + "$singleLimit"
                    } else "")
                )
                append("\n" + AppWrapper.getString(R.string.final_damage))
                append(terminalDamage)
                append("\n" + "=========================================")
                append(if (terminalDamage != finalDamage) "\n" + AppWrapper.getString(R.string.single_damage_protect) + "$singleLimit" else "")
            }
        return DamageResult(
            type = damageType,
            damageStatus = damageStatus.copy(isSelfHarm = attacker.roleIdentifier == victim.roleIdentifier),
            damageInfo = processInfo,
            rawDamage = terminalDamage,
            damageSkill = skill,
            attacker = attacker.roleIdentifier,
            victim = victim.roleIdentifier
        )
    }
}

private fun Boolean.display(): String {
    return if (this) AppWrapper.getString(R.string.yes) else AppWrapper.getString(R.string.no)
}
