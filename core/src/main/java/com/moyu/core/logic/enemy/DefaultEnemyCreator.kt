package com.moyu.core.logic.enemy

import com.moyu.core.logic.identifier.Identifier
import com.moyu.core.model.property.Property
import com.moyu.core.model.race.Race
import com.moyu.core.model.role.Role

object DefaultEnemyCreator : RoleCreator {
    override fun create(race: Race, diffProperty: Property, extraSkills: List<Int>): Role {
        return createDetailed(race, diffProperty, Identifier.enemy(name = race.name), extraSkills)
    }
}