package com.moyu.core.config

import com.moyu.core.model.pool.Pool

class PoolConfigParser : ConfigParser<Pool> {
    override fun parse(line: String): Pool {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val type = words[i++].trim().split(",").map { it.toInt() }
        val pool = words[i++].trim().split(",").map { it.toInt() }
        val num = words[i++].trim().split(",").map { it.toInt() }
        val rate = words[i++].trim().split(",").map { it.toInt() }
        val quality = words[i].trim().split(",").map { it.toInt() }

//        type.forEachIndexed { index, i ->
//            if (type[index] in listOf(5, 10)) {
//                if (pool[index] != 0) {
//                    if (pool[index] > 5) {
//                        Log.e("", "pool配置错误，资源类型大于5 $line")
//                    }
//                }
//            }
//        }
//        if (type.size != num.size) {
//            Log.e("", "pool配置错误，num数量不对 $line")
//        }
        return Pool(
            id,
            type,
            pool,
            num,
            rate,
            quality
        )
    }
}