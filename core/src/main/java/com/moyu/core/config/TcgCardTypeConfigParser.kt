package com.moyu.core.config

import com.moyu.core.model.tcg.TcgCardType


class TcgCardTypeConfigParser : ConfigParser<TcgCardType> {
    override fun parse(line: String): TcgCardType {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val type = words[i++].trim().toInt()
        val name = words[i++].trim()
        val total = words[i].trim().toInt()

        return TcgCardType(
            id, type, name, total
        )
    }
}