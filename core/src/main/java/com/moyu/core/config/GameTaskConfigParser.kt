package com.moyu.core.config

import com.moyu.core.model.task.GameTask

class GameTaskConfigParser : ConfigParser<GameTask> {
    override fun parse(line: String): GameTask {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val taskType = words[i++].trim().toInt()
        val name = words[i++].trim()
        val desc = words[i++].trim()
        val type = words[i++].trim().toInt()
        val subType = words[i++].trim().split(",").map { it.toInt() }
        val num = words[i++].trim().toInt()
        val reward = words[i++].trim().toInt()
        val order = words[i++].trim().toInt()
        val talent = words[i].trim().split(",").map { it.toInt() }
        return GameTask(
            id,
            taskType,
            name,
            desc,
            type,
            subType,
            num,
            reward,
            order,
            talent,
        )
    }
}