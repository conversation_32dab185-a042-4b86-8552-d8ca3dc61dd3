package com.moyu.core.config

import com.moyu.core.model.card.Card

class CardConfigParser : ConfigParser<Card> {
    override fun parse(line: String): Card {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val type = words[i++].trim().toInt()
        val rate = words[i++].trim().split(",").map { it.toInt() }
        val name = words[i].trim()
        return Card(
            id,
            type,
            rate,
            name,
        )
    }
}