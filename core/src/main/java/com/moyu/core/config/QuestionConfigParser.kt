package com.moyu.core.config

import com.moyu.core.model.question.Question

class QuestionConfigParser : ConfigParser<Question> {
    override fun parse(line: String): Question {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val mainId = words[i++].trim().toInt()
        val question = words[i++].trim()
        val answer1 = words[i++].trim()
        val answer2 = words[i++].trim()
        val answer3 = words[i++].trim()
        val npcPic = words[i++].trim()
        val npcName = words[i++].trim()
        val correct = words[i++].trim().toInt()
        val name = words[i].trim()
        return Question(
            id,
            mainId,
            question,
            answer1,
            answer2,
            answer3,
            npcPic,
            npcName,
            correct,
            name,
        )
    }
}