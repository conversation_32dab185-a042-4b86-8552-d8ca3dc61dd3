package com.moyu.core.config

import com.moyu.core.model.unlock.Unlock

class UnlockConfigParser : ConfigParser<Unlock> {
    override fun parse(line: String): Unlock {
        var i = 0
        val words = line.split("\t")
        val id = words[i++].trim().toInt()
        val name = words[i++].trim()
        val initialLock = words[i++].trim().toInt()
        val conditionType = words[i++].trim().split(",").map { it.toInt() }
        val conditionNum = words[i++].trim().split(",").map { it.toInt() }
        val show = words[i++].trim().split(",").map { it.toInt() }
        val desc = words[i++].trim()
        val url = words[i++].trim()
        val icon = words[i++].trim()
        val googleItemId = words[i].trim()

        return Unlock(
            id, name, initialLock, conditionType, conditionNum, show, desc, url, icon, googleItemId
        )
    }
}