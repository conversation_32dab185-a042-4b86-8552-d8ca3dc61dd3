package com.moyu.core.config

import com.moyu.core.model.tcg.TcgAward

class TcgAwardConfigParser : ConfigParser<TcgAward> {
    override fun parse(line: String): TcgAward {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val num = words[i++].trim().toInt()
        val type = words[i++].trim().toInt()
        val reward = words[i].trim().toInt()
        return TcgAward(
            id,
            type,
            num,
            reward,
        )
    }
}