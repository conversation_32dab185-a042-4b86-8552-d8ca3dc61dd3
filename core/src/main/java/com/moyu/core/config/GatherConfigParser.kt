package com.moyu.core.config

import com.moyu.core.model.Gather

class GatherConfigParser : ConfigParser<Gather> {
    override fun parse(line: String): Gather {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val heroLevel = words[i++].trim().split(",").map { it.toInt() }
        val level = words[i++].trim().toInt()
        val conditionNum = words[i++].trim().toInt()
        val heroRate = words[i++].trim().toInt()
        val conditionRate = words[i++].trim().toInt()
        val attribute1 = words[i++].trim().toInt()
        val attribute2 = words[i++].trim().toInt()
        val attribute3 = words[i++].trim().toInt()
        val attribute4 = words[i++].trim().toDouble()
        val attribute5 = words[i++].trim().toDouble()
        val attribute6 = words[i++].trim().toDouble()
        val attribute7 = words[i++].trim().toDouble()
        val attribute8 = words[i++].trim().toInt()
        val reward = words[i++].trim().toInt()
        val time = words[i].trim().toInt()
        return Gather(
            id,
            heroLevel,
            level,
            conditionNum,
            heroRate,
            conditionRate,
            attribute1,
            attribute2,
            attribute3,
            attribute4,
            attribute5,
            attribute6,
            attribute7,
            attribute8,
            reward,
            time,
        )
    }
}