package com.moyu.core.config

import com.moyu.core.model.level.RoleLevel

class LevelConfigParser: ConfigParser<RoleLevel> {
    override fun parse(line: String): RoleLevel {
        var i = 0
        val words = line.split("\t")
        val id = words[i++].trim().toInt()
        val nextExp = words[i++].trim().toLong()
        val totalExp = words[i].trim().toLong()
        return RoleLevel(
            id = id,
            nextExp = nextExp,
            totalExp = totalExp,
        )
    }
}