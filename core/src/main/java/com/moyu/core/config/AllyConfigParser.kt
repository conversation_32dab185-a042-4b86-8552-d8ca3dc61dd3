package com.moyu.core.config

import com.moyu.core.model.ally.Ally

class AllyConfigParser : ConfigParser<Ally> {
    override fun parse(line: String): Ally {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val mainId = words[i++].trim().toInt()
        val name = words[i++].trim()
        val star = words[i++].trim().toInt()
        val starLimit = words[i++].trim().toInt()
        val quality = words[i++].trim().toInt()
        val type = words[i++].trim().toInt()
        val skillNum = words[i++].trim().toInt()
        val starUpNum = words[i++].trim().toInt()
        val starUpResourceNum = words[i++].trim().toInt()
        val dropLimit = words[i++].trim().toInt()
        val story = words[i++].trim()
        val empire = words[i].trim()
        return Ally(
            id,
            mainId,
            name,
            star,
            starLimit,
            quality,
            type,
            skillNum,
            starUpNum,
            starUpResourceNum,
            dropLimit,
            story,
            empire
        )
    }
}