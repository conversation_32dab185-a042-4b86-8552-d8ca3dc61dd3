package com.moyu.core.config

import com.moyu.core.model.dialog.Dialog

class DialogConfigParser : ConfigParser<Dialog> {
    override fun parse(line: String): Dialog {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val mainId = words[i++].trim().toInt()
        val type = words[i++].trim().toInt()
        val desc = words[i++].trim()
        val answer1 = words[i++].trim()
        val answer2 = words[i++].trim()
        val answer3 = words[i++].trim()
        val correct = words[i++].trim().toInt()
        val npcPic = words[i++].trim()
        val npcName = words[i++].trim()
        val condition = words[i].trim().split(",").map { it.toInt() }
        return Dialog(
            id,
            mainId,
            type,
            desc,
            answer1,
            answer2,
            answer3,
            correct,
            npcPic,
            npcName,
            condition
        )
    }
}