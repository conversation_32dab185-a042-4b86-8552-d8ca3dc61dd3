package com.moyu.core.config

import com.moyu.core.model.Challenge

class ChallengeConfigParser : ConfigParser<Challenge> {
    override fun parse(line: String): Challenge {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val effectAdventure = words[i++].trim().split(",").map { it.toInt() }
        val effectBattle = words[i].trim().split(",").map { it.toInt() }
        return Challenge(
            id,
            effectAdventure,
            effectBattle,
        )
    }
}