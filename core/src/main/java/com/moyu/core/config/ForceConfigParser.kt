package com.moyu.core.config

import com.moyu.core.model.Force

class ForceConfigParser : ConfigParser<Force> {
    override fun parse(line: String): Force {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val name = words[i++].trim()
        val desc = words[i++].trim()
        val showCard = words[i].trim().split(",").map { it.toInt() }
        return Force(
            id,
            name,
            desc,
            showCard,
        )
    }
}