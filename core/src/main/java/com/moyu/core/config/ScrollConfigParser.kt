package com.moyu.core.config

import com.moyu.core.model.skill.Scroll

class ScrollConfigParser : ConfigParser<Scroll> {
    override fun parse(line: String): Scroll {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val mainId = words[i++].trim().toInt()
        val name = words[i++].trim()
        val star = words[i++].trim().toInt()
        val starLimit = words[i++].trim().toInt()
        val quality = words[i++].trim().toInt()
        val type = words[i++].trim().toInt()
        val starUpNum = words[i++].trim().toInt()
        val dropLimit = words[i++].trim().toInt()
        val empire = words[i++].trim()
        val story = words[i].trim()
        return Scroll(
            id,
            mainId,
            name,
            star,
            starLimit,
            quality,
            type,
            starUpNum,
            dropLimit,
            story,
            0,
            empire
        )
    }
}