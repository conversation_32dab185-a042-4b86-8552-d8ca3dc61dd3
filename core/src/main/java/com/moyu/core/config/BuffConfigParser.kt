package com.moyu.core.config

import com.moyu.core.model.buff.Buff

class BuffConfigParser: ConfigParser<Buff> {
    override fun parse(line: String): Buff {
        var i = 0
        val words = line.split("\t")
        val id = words[i++].trim().toInt()
        val name = words[i++].trim()
        val desc = words[i++].trim()
        val buffType = words[i++].trim().toInt()
        val buffEffect = words[i++].trim()
        val buffEffectNum = words[i].trim().toInt()
        return Buff(
            id = id,
            name = name,
            desc = desc,
            buffType = buffType,
            buffEffect,
            buffEffectNum
        )
    }
}