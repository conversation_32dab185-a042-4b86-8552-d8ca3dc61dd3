package com.moyu.core.config

import com.moyu.core.model.level.ReputationLevel

class ReputationLevelConfigParser : ConfigParser<ReputationLevel> {
    override fun parse(line: String): ReputationLevel {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val name = words[i++].trim()
        val level = words[i++].trim().toInt()
        val exp = words[i++].trim().toInt()
        val expTotal = words[i].trim().toInt()
        return ReputationLevel(
            id,
            name,
            level,
            exp,
            expTotal,
        )
    }
}