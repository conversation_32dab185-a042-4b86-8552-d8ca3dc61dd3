package com.moyu.core.config

import com.moyu.core.model.BattlePass
import com.moyu.core.model.DayReward
import com.moyu.core.model.DrawItem
import com.moyu.core.model.Lucky
import com.moyu.core.model.Pvp
import com.moyu.core.model.Sign
import com.moyu.core.model.Tower
import com.moyu.core.model.TurnTable
import com.moyu.core.model.Vip
import com.moyu.core.model.ally.Ally
import com.moyu.core.model.badge.Badge
import com.moyu.core.model.buff.Buff
import com.moyu.core.model.dialog.Dialog
import com.moyu.core.model.event.Event
import com.moyu.core.model.level.ReputationLevel
import com.moyu.core.model.pool.Pool
import com.moyu.core.model.property.Property
import com.moyu.core.model.race.Race
import com.moyu.core.model.sell.Gift
import com.moyu.core.model.sell.Sell
import com.moyu.core.model.skill.Scroll
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.story.Story
import com.moyu.core.model.talent.Talent
import com.moyu.core.model.task.GameTask
import com.moyu.core.model.tcg.TcgAward
import com.moyu.core.model.tcg.TcgCard
import com.moyu.core.model.tcg.TcgCardType
import com.moyu.core.model.unlock.Unlock

const val BUFF_FILE_NAME = "buff.txt"
const val ALLY_FILE_NAME = "ally.txt"
const val COMMON_FILE_NAME = "common.txt"
const val SELL_FILE_NAME = "sell.txt"
const val REPUTATION_LEVEL_FILE_NAME = "reputation_level.txt"
const val POPULATION_LEVEL_FILE_NAME = "population_level.txt"
const val RACE_FILE_NAME = "race.txt"
const val SKILL_FILE_NAME = "skill.txt"
const val SPECIAL_SKILL_FILE_NAME = "special_skill.txt"
const val HALO_SKILL_FILE_NAME = "halo_skill.txt"
const val TALENT_SKILL_FILE_NAME = "talent_skill.txt"
const val HERO_SKILL_FILE_NAME = "hero_skill.txt"
const val ADVENTURE_SKILL_FILE_NAME = "adventure_skill.txt"
const val TASK_FILE_NAME = "task.txt"
const val UNLOCK_FILE_NAME = "unlock.txt"
const val DIALOG_FILE_NAME = "dialog.txt"
const val GIFT_FILE_NAME = "gift.txt"
const val TALENT_FILE_NAME = "talent.txt"
const val TCG_CARD_FILE_NAME = "tcg_card.txt"
const val TCG_AWARD_FILE_NAME = "tcg_award.txt"
const val TCG_CARD_TYPE_FILE_NAME = "tcg_card_type.txt"
const val EVENT_FILE_NAME = "event.txt"
const val SCROLL_FILE_NAME = "scroll.txt"
const val POOL_FILE_NAME = "pool.txt"
const val BADGE_FILE_NAME = "badge.txt"
const val VIP_FILE_NAME = "vip.txt"
const val COMBINEDBUFF_FILE_NAME = "combinedbuff.txt"
const val HERO_FILE_NAME = "hero.txt"
const val STORY_FILE_NAME = "story.txt"
const val WAR_PASS_FILE_NAME = "warpass.txt"
const val WAR_PASS2_FILE_NAME = "warpass2.txt"
const val SIGN_FILE_NAME = "sign.txt"
const val DAY_REWARD_FILE_NAME = "dayreward.txt"
const val LUCKY_FILE_NAME = "lucky.txt"
const val TURNTABLE_FILE_NAME = "turntable.txt"
const val PVP_FILE_NAME = "pvp.txt"
const val DRAW_FILE_NAME = "draw.txt"
const val TOWER_FILE_NAME = "tower.txt"

interface ConfigHolder {
    fun setGameConfig(key: String, pool: List<Any>)

    // basic
    fun getSkillPool(): List<Skill>
    fun getBuffPool(): List<Buff>
    fun getReputationLevelPool(): List<ReputationLevel>
    fun getPopulationLevelPool(): List<ReputationLevel>
    fun getRacePool(): List<Race>
    fun getEventPool(): List<Event>
    fun getAllyPool(): List<Ally>
    fun getDialogPool(): List<Dialog>
    fun getTalentPool(): List<Talent>
    fun getGiftPool(): List<Gift>
    fun getSellPool(): List<Sell>
    fun getGameTaskPool(): List<GameTask>
    fun getUnlockPool(): List<Unlock>
    fun getTcgCardTypePool(): List<TcgCardType>
    fun getTcgCardPool(): List<TcgCard>
    fun getTcgAwardPool(): List<TcgAward>
    fun getScrollPool(): List<Scroll>
    fun getPoolPool(): List<Pool>
    fun getBadgePool(): List<Badge>
    fun getVipPool(): List<Vip>
    fun getCombinedBuffPool(): List<Buff>
    fun getHeroPool(): List<Scroll>
    fun getStoryPool(): List<Story>
    fun getBattlePassPool(): List<BattlePass>
    fun getBattlePass2Pool(): List<BattlePass>
    fun getSignPool(): List<Sign>
    fun getDayRewardPool(): List<DayReward>
    fun getTurnTablePool(): List<TurnTable>
    fun getLuckyPool(): List<Lucky>
    fun getPvpPool(): List<Pvp>
    fun getDrawPool(): List<DrawItem>
    fun getTowerPool(): List<Tower>

    // common
    fun getConstA(): Double
    fun getConstB(): Double
    fun getLimitedChestRefreshGapInHour(): Int
    fun getDoubleGuajiTime(): Int
    fun getSellSkillCountByStage(stage: Int): Int
    fun getInitProperty(): Property
    fun getInitAllySlot(): Int
    fun getInitSkillSlot(): Int
    fun getEndingPoolIdByAge(age: Int): Int
    fun getEndingDiamondByAge(age: Int): Int
    fun getFreeShopItemNum(): Int
    fun getSkinShopItemNum(): Int
    fun getAllyShopItemNum(): Int
    fun getSkillShopItemNum(): Int
    fun getEquipShopItemNum(): Int
    fun getRefreshShopCost(): Int
    fun getKeyToDiamondRate(): Int
    fun getBlueDamage(): Int
    fun getPurpleDamage(): Int
    fun getOrangeDamage(): Int
    fun getChallengeQuestNum(): Int
    fun getDailyShopRefreshCount(): Int
    fun getInitAdvProps(): List<Int>
    fun getShareKeys(): Int

    // extra
    fun getSkillById(skillId: Int): Skill
    fun getHeroById(skillId: Int): Scroll
    fun getGameTaskById(id: Int): GameTask
    fun getAllyById(id: Int): Ally
    fun getScrollById(id: Int): Scroll
    fun getBuffById(buffId: Int): Buff
    fun getRaceById(raceId: Int): Race
    fun getTalentById(id: Int): Talent
    fun getUnlockById(id: Int): Unlock
    fun getTcgCardById(cardId: Int): TcgCard
    fun getFirstSkillIds(): List<Int>
    fun getFirstAllyIds(): List<Int>
    fun getPoolById(id: Int): Pool
    fun getPoolByKeyAny(key: String): List<Any>
    fun getInitResources(): List<Int>
    fun getInitExtraResources(): List<Int>
    fun getVipRaceCard(): Int
    fun getWarPassQuestCount(): Int
    fun getNewQuestCount(): Int
    fun getDailyQuestCount(): Int
    fun getFamousDiamond(): Int
    fun getGetKeyUrls(): List<String>
    fun getFirstChargeAllyId(): Int
    fun getGiftById(value: Int): Gift?
    fun getLuckyWeights(): List<Int>
    fun getLuckyOutputs(): List<Int>
    fun getCheapLotteryCosts(): List<Int>
    fun getExpensiveLotteryCosts(): List<Int>
    fun getAllyCouponRate(): Int
    fun getSkillCouponRate(): Int
    fun getHeroCouponRate(): Int
    fun getDrawEnsurePool(): Int
    fun getHolidayLotteryCosts(): List<Int>
    fun getTowerAwardKey(towerLevel: Int): Int
}