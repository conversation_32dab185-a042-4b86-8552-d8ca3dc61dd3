package com.moyu.core.config

import com.moyu.core.model.badge.Badge

class BadgeConfigParser : ConfigParser<Badge> {
    override fun parse(line: String): Badge {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val name = words[i++].trim()
        val pic = words[i++].trim()
        val tips = words[i].trim()
        return Badge(
            id,
            name,
            pic,
            tips
        )
    }
}