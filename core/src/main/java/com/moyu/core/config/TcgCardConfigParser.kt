package com.moyu.core.config

import com.moyu.core.model.tcg.TcgCard


class TcgCardConfigParser : ConfigParser<TcgCard> {
    override fun parse(line: String): TcgCard {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val type = words[i++].trim().toInt()
        val name = words[i++].trim()
        val pic = words[i++].trim()
        val quality = words[i].trim().toInt()
        return TcgCard(
            id, type, name, pic, quality
        )
    }
}