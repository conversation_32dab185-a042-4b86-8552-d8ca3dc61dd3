package com.moyu.core.config

import com.moyu.core.model.encounter.Encounter

class EncounterConfigParser : ConfigParser<Encounter> {
    override fun parse(line: String): Encounter {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val name = words[i++].trim()
        val desc1 = words[i++].trim()
        val answer1 = words[i++].trim()
        val answer2 = words[i++].trim()
        val result1 = words[i++].trim()
        val result2 = words[i++].trim()
        val rate1 = words[i++].trim().split(",").map { it.toInt() }
        val rate2 = words[i++].trim().split(",").map { it.toInt() }
        val reward1 = words[i++].trim().split(",").map { it.toInt() }
        val reward2 = words[i++].trim().split(",").map { it.toInt() }
        val rewardSubType1 = words[i++].trim().split(",").map { it.toInt() }
        val rewardSubType2 = words[i++].trim().split(",").map { it.toInt() }
        val rewardNum1 = words[i++].trim().split(",").map { it.toInt() }
        val rewardNum2 = words[i++].trim().split(",").map { it.toInt() }
        val npcPic = words[i++].trim()
        val npcName = words[i++].trim()
        val fail1 = words[i++].trim().toInt()
        val fail2 = words[i].trim().toInt()
        return Encounter(
            id,
            name,
            desc1,
            answer1,
            answer2,
            result1,
            result2,
            rate1,
            rate2,
            reward1,
            reward2,
            rewardSubType1,
            rewardSubType2,
            rewardNum1,
            rewardNum2,
            npcPic,
            npcName,
            fail1,
            fail2,
        )
    }
}