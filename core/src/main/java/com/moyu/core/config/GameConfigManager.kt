package com.moyu.core.config

import com.moyu.core.exception.configError
import com.moyu.core.model.BattlePass
import com.moyu.core.model.DayReward
import com.moyu.core.model.DrawItem
import com.moyu.core.model.Lucky
import com.moyu.core.model.Pvp
import com.moyu.core.model.Sign
import com.moyu.core.model.Tower
import com.moyu.core.model.TurnTable
import com.moyu.core.model.Vip
import com.moyu.core.model.ally.Ally
import com.moyu.core.model.badge.Badge
import com.moyu.core.model.buff.Buff
import com.moyu.core.model.common.Common
import com.moyu.core.model.dialog.Dialog
import com.moyu.core.model.event.Event
import com.moyu.core.model.level.ReputationLevel
import com.moyu.core.model.pool.Pool
import com.moyu.core.model.property.Property
import com.moyu.core.model.race.Race
import com.moyu.core.model.sell.Gift
import com.moyu.core.model.sell.Sell
import com.moyu.core.model.skill.Scroll
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.story.Story
import com.moyu.core.model.talent.Talent
import com.moyu.core.model.task.GameTask
import com.moyu.core.model.tcg.TcgAward
import com.moyu.core.model.tcg.TcgCard
import com.moyu.core.model.tcg.TcgCardType
import com.moyu.core.model.unlock.Unlock
import java.util.concurrent.ConcurrentHashMap

class GameConfigManager : ConfigHolder {
    private val configLoaders = ConcurrentHashMap<String, List<Any>>()
    private val skillsPool by lazy { // 性能优化
        mutableListOf<Skill>().apply {
            addAll(getNormalSkillPool())
            addAll(getAdventureSkillPool())
            addAll(getSpecialSkillPool())
            addAll(getHaloSkillPool())
            addAll(getHeroSkillPool())
            addAll(getTalentSkillPool())
        }
    }

    private inline fun <reified T> getPoolByKey(key: String): List<T> {
        return configLoaders[key]!! as List<T>
    }

    override fun setGameConfig(key: String, pool: List<Any>) {
        configLoaders[key] = pool
    }

    override fun getSkillPool(): List<Skill> {
        return skillsPool
    }

    private fun getNormalSkillPool(): List<Skill> {
        return getPoolByKey(SKILL_FILE_NAME)
    }

    private fun getSpecialSkillPool(): List<Skill> {
        return getPoolByKey(SPECIAL_SKILL_FILE_NAME)
    }

    private fun getAdventureSkillPool(): List<Skill> {
        return getPoolByKey(ADVENTURE_SKILL_FILE_NAME)
    }

    private fun getHaloSkillPool(): List<Skill> {
        return getPoolByKey(HALO_SKILL_FILE_NAME)
    }

    private fun getTalentSkillPool(): List<Skill> {
        return getPoolByKey(TALENT_SKILL_FILE_NAME)
    }

    private fun getHeroSkillPool(): List<Skill> {
        return getPoolByKey(HERO_SKILL_FILE_NAME)
    }

    override fun getBuffPool(): List<Buff> {
        return getPoolByKey(BUFF_FILE_NAME)
    }

    override fun getReputationLevelPool(): List<ReputationLevel> {
        return getPoolByKey(REPUTATION_LEVEL_FILE_NAME)
    }

    override fun getPopulationLevelPool(): List<ReputationLevel> {
        return getPoolByKey(POPULATION_LEVEL_FILE_NAME)
    }

    override fun getRacePool(): List<Race> {
        return getPoolByKey(RACE_FILE_NAME)
    }

    override fun getAllyPool(): List<Ally> {
        return getPoolByKey(ALLY_FILE_NAME)
    }

    override fun getDialogPool(): List<Dialog> {
        return getPoolByKey(DIALOG_FILE_NAME)
    }

    override fun getEventPool(): List<Event> {
        return getPoolByKey(EVENT_FILE_NAME)
    }

    override fun getTalentPool(): List<Talent> {
        return getPoolByKey(TALENT_FILE_NAME)
    }

    override fun getGiftPool(): List<Gift> {
        return getPoolByKey(GIFT_FILE_NAME)
    }

    override fun getSellPool(): List<Sell> {
        return getPoolByKey(SELL_FILE_NAME)
    }

    override fun getDayRewardPool(): List<DayReward> {
        return getPoolByKey(DAY_REWARD_FILE_NAME)
    }

    override fun getTurnTablePool(): List<TurnTable> {
        return getPoolByKey(TURNTABLE_FILE_NAME)
    }

    override fun getLuckyPool(): List<Lucky> {
        return getPoolByKey(LUCKY_FILE_NAME)
    }

    override fun getPvpPool(): List<Pvp> {
        return getPoolByKey(PVP_FILE_NAME)
    }

    override fun getDrawPool(): List<DrawItem> {
        return getPoolByKey(DRAW_FILE_NAME)
    }

    override fun getGameTaskPool(): List<GameTask> {
        return getPoolByKey(TASK_FILE_NAME)
    }

    override fun getUnlockPool(): List<Unlock> {
        return getPoolByKey(UNLOCK_FILE_NAME)
    }

    override fun getTcgCardTypePool(): List<TcgCardType> {
        return getPoolByKey(TCG_CARD_TYPE_FILE_NAME)
    }

    override fun getTcgCardPool(): List<TcgCard> {
        return getPoolByKey(TCG_CARD_FILE_NAME)
    }

    override fun getTcgAwardPool(): List<TcgAward> {
        return getPoolByKey(TCG_AWARD_FILE_NAME)
    }

    override fun getScrollPool(): List<Scroll> {
        return getPoolByKey(SCROLL_FILE_NAME)
    }

    override fun getPoolPool(): List<Pool> {
        return getPoolByKey(POOL_FILE_NAME)
    }

    override fun getBadgePool(): List<Badge> {
        return getPoolByKey(BADGE_FILE_NAME)
    }

    override fun getVipPool(): List<Vip> {
        return getPoolByKey(VIP_FILE_NAME)
    }

    override fun getCombinedBuffPool(): List<Buff> {
        return getPoolByKey(COMBINEDBUFF_FILE_NAME)
    }

    override fun getHeroPool(): List<Scroll> {
        return getPoolByKey(HERO_FILE_NAME)
    }

    override fun getStoryPool(): List<Story> {
        return getPoolByKey(STORY_FILE_NAME)
    }

    override fun getBattlePassPool(): List<BattlePass> {
        return getPoolByKey(WAR_PASS_FILE_NAME)
    }

    override fun getBattlePass2Pool(): List<BattlePass> {
        return getPoolByKey(WAR_PASS2_FILE_NAME)
    }

    override fun getSignPool(): List<Sign> {
        return getPoolByKey(SIGN_FILE_NAME)
    }

    override fun getTowerPool(): List<Tower> {
        return getPoolByKey(TOWER_FILE_NAME)
    }



    override fun getSkillById(skillId: Int): Skill {
        return getSkillPool().find { it.id == skillId } ?: configError(skillId)
    }

    override fun getHeroById(skillId: Int): Scroll {
        return getHeroPool().find { it.id == skillId } ?: configError(skillId)
    }

    override fun getGameTaskById(id: Int): GameTask {
        return getGameTaskPool().find { it.id == id } ?: configError(id)
    }

    override fun getAllyById(id: Int): Ally {
        return getAllyPool().find { it.id == id } ?: configError(id)
    }

    override fun getScrollById(id: Int): Scroll {
        return getScrollPool().find { it.id == id } ?: configError(id)
    }

    override fun getBuffById(buffId: Int): Buff {
        return getBuffPool().find { it.id == buffId } ?: configError(buffId)
    }

    override fun getRaceById(raceId: Int): Race {
        return getRacePool().find { it.id == raceId } ?: configError(raceId)
    }

    override fun getTalentById(id: Int): Talent {
        return getTalentPool().find { it.id == id } ?: configError(id)
    }

    override fun getUnlockById(id: Int): Unlock {
        return getUnlockPool().find { it.id == id } ?: configError(id)
    }

    override fun getTcgCardById(cardId: Int): TcgCard {
        return getTcgCardPool().find { it.id == cardId } ?: configError(cardId)
    }

    override fun getPoolById(id: Int): Pool {
        return getPoolPool().firstOrNull { it.id == id } ?: configError(id)
    }

    override fun getPoolByKeyAny(key: String): List<Any> {
        return configLoaders[key]!!
    }

    override fun getConstA(): Double {
        return 5.0
    }

    override fun getConstB(): Double {
        return 5.0
    }

    override fun getLimitedChestRefreshGapInHour(): Int {
        return 3
    }

    override fun getDoubleGuajiTime(): Int {
        return 4
    }

    override fun getFirstSkillIds(): List<Int> {
        return getPoolByKey<Common>(COMMON_FILE_NAME)[1].num.split(",").map { it.toInt() }
    }

    override fun getFirstAllyIds(): List<Int> {
        return getPoolByKey<Common>(COMMON_FILE_NAME)[0].num.split(",").map { it.toInt() }
    }

    override fun getSellSkillCountByStage(stage: Int): Int {
        return 0 // getPoolByKey<Common>(COMMON_FILE_NAME)[4 + stage].num.toInt()
    }

    override fun getInitProperty(): Property {
//        val list = getPoolByKey<Common>(COMMON_FILE_NAME)[9].num.split(",").map { it.toInt() }
//        return Property(strength = list[0], agile = list[1], smart = list[2], lucky = list[3], carry = list[4], age = list[5])
        return Property()
    }

    override fun getInitAllySlot(): Int {
        return 6//getPoolByKey<Common>(COMMON_FILE_NAME)[10].num.toInt()
    }

    override fun getInitSkillSlot(): Int {
        return 6//getPoolByKey<Common>(COMMON_FILE_NAME)[11].num.toInt()
    }

    override fun getEndingPoolIdByAge(age: Int): Int {
        val list = getPoolByKey<Common>(COMMON_FILE_NAME)[14].num.split(",").map { it.toInt() }
        val index = (age / 30) - 1
        return if (index < 0) -1 else if (index >= list.size) list.last() else list[index]
    }

    override fun getEndingDiamondByAge(age: Int): Int {
        val list = getPoolByKey<Common>(COMMON_FILE_NAME)[7].num.split(",").map { it.toInt() }
        return (age / 30).let {
            if (it >= list.size) list.last() else if (it > 0) list[it - 1] else 0
        }
    }

    override fun getFreeShopItemNum(): Int {
        return 9 // getPoolByKey<Common>(COMMON_FILE_NAME)[13].num.toInt()
    }

    override fun getSkinShopItemNum(): Int {
        return 3 // getPoolByKey<Common>(COMMON_FILE_NAME)[14].num.toInt()
    }

    override fun getAllyShopItemNum(): Int {
        return 3 // getPoolByKey<Common>(COMMON_FILE_NAME)[15].num.toInt()
    }

    override fun getSkillShopItemNum(): Int {
        return 3 // getPoolByKey<Common>(COMMON_FILE_NAME)[16].num.toInt()
    }

    override fun getEquipShopItemNum(): Int {
        return 3 // getPoolByKey<Common>(COMMON_FILE_NAME)[17].num.toInt()
    }

    override fun getRefreshShopCost(): Int {
        return getPoolByKey<Common>(COMMON_FILE_NAME)[4].num.toInt()
    }

    override fun getKeyToDiamondRate(): Int {
        return getPoolByKey<Common>(COMMON_FILE_NAME)[5].num.toInt()
    }

    override fun getBlueDamage(): Int {
        return 0 // getPoolByKey<Common>(COMMON_FILE_NAME)[0].num.toInt()
    }

    override fun getPurpleDamage(): Int {
        return 0 // getPoolByKey<Common>(COMMON_FILE_NAME)[1].num.toInt()
    }

    override fun getOrangeDamage(): Int {
        return 0 //getPoolByKey<Common>(COMMON_FILE_NAME)[2].num.toInt()
    }

    override fun getChallengeQuestNum(): Int {
        return 9 // getPoolByKey<Common>(COMMON_FILE_NAME)[26].num.toInt()
    }

    override fun getDailyShopRefreshCount(): Int {
        return getPoolByKey<Common>(COMMON_FILE_NAME)[3].num.toInt()
    }

    override fun getInitAdvProps(): List<Int> {
        return getPoolByKey<Common>(COMMON_FILE_NAME)[8].num.split(",").map { it.toInt() }
    }

    override fun getShareKeys(): Int {
        return getPoolByKey<Common>(COMMON_FILE_NAME)[13].num.toInt()
    }

    override fun getInitResources(): List<Int> {
        return getPoolByKey<Common>(COMMON_FILE_NAME)[9].num.split(",").map { it.toInt() }
    }

    override fun getInitExtraResources(): List<Int> {
        return getPoolByKey<Common>(COMMON_FILE_NAME)[10].num.split(",").map { it.toInt() }
    }

    override fun getVipRaceCard(): Int {
        return 40855
    }

    override fun getWarPassQuestCount(): Int {
        return getPoolByKey<Common>(COMMON_FILE_NAME)[11].num.toInt()
    }

    override fun getNewQuestCount(): Int {
        return getPoolByKey<Common>(COMMON_FILE_NAME)[12].num.toInt()
    }

    override fun getDailyQuestCount(): Int {
        return getPoolByKey<Common>(COMMON_FILE_NAME)[2].num.toInt()
    }

    override fun getFamousDiamond(): Int {
        return getPoolByKey<Common>(COMMON_FILE_NAME)[6].num.toInt()
    }

    override fun getFirstChargeAllyId(): Int {
        return getPoolByKey<Common>(COMMON_FILE_NAME).first { it.id == 23 }.num.toInt()
    }

    override fun getGetKeyUrls(): List<String> {
        return List(6) { index ->
            getPoolByKey<Common>(COMMON_FILE_NAME).first { it.id == 17 + index }.num
        }
    }

    override fun getLuckyWeights(): List<Int> {
        return getValueById(2001).split(",").map { it.toInt() }
    }

    override fun getLuckyOutputs(): List<Int> {
        return getValueById(2002).split(",").map { it.toInt() }
    }

    override fun getCheapLotteryCosts(): List<Int> {
        return getValueById(2101).split(",").map { it.toInt() }
    }

    override fun getExpensiveLotteryCosts(): List<Int> {
        return getValueById(2102).split(",").map { it.toInt() }
    }

    override fun getAllyCouponRate(): Int {
        return getValueById(90).toInt()
    }

    override fun getSkillCouponRate(): Int {
        return getValueById(90).toInt()
    }

    override fun getHeroCouponRate(): Int {
        return getValueById(90).toInt()
    }

    override fun getDrawEnsurePool(): Int {
        return getValueById(2004).toInt()
    }

    override fun getHolidayLotteryCosts(): List<Int> {
        return getValueById(2103).split(",").map { it.toInt() }
    }

    override fun getTowerAwardKey(towerLevel: Int): Int {
        val index = if (towerLevel <= 500) 0 else if (towerLevel <= 1000) 1 else 2
        return getValueById(86).split(",").map { it.toInt() }[index]
    }

    override fun getGiftById(value: Int): Gift? {
        return getGiftPool().find { it.id == value }
    }

    private fun getValueById(id: Int) = getPoolByKey<Common>(COMMON_FILE_NAME).first { it.id == id }.num
}