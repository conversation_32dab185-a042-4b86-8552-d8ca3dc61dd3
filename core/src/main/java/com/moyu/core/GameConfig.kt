package com.moyu.core

// taptap
const val tapClientId = "rs7gqhltn1v7bpajsb"
const val tapClientToken = "4h25MulksJRoPbqc1zoOFirTNy3jgBkiKUMUZLnN"
const val tapServerUrl = "https://mk6zyyun.cloud.tds1.tapapis.cn"

// 好游快爆
const val gameId = "31775"

// bugly 需要更新android manifest
const val buglyId = "f49d0cb14f"


// 存档
const val DS_NAME = "_exceptions"
const val DS_NAME2 = "_crash_fatal2"

// 加密密钥
const val INT_ENCRYPT = "kilD?&*%21"

// 正式上线还要注意修改云存档/排行榜的api


// 隐私
const val privacyLink = "https://note.youdao.com/s/R23NCKBE"

// 许可
const val licenseLink = "https://note.youdao.com/ynoteshare/index.html?id=b1121c2adc048772a46dde7436d3359e&type=note&_time=1661097900185"


const val AD_UNIT_ID_TEST = "ca-app-pub-3940256099942544/**********"
const val AD_UNIT_ID_T1 = "ca-app-pub-5058022002121914/**********"


const val appFlyerDevKey = "BerpMdszVXZZsKMmBgkLYm"

const val configPath = "impossible.mp3"