package com.moyu.core.model.skill


fun Skill.isBattle() = isBattleSkill()

fun Skill.isAdventure() = skillType == SkillType.AdventureSkill.id
fun Skill.isHalo() = skillType == SkillType.HaloSkill.id
fun Skill.isBattleSkill() = skillType == SkillType.NormalSkill.id
fun Skill.isAllySkill() = skillType == SkillType.SpecialSkill.id
fun Skill.isHeroSkill() = skillType == SkillType.HeroSkill.id
fun Skill.isTalentSkill() = skillType == SkillType.TalentSkill.id

/**
1=军团卡技能
2=战术卡
3=史诗人物
4=冒险卡
5=天赋
6=天气卡
 */
enum class SkillType(val id: Int) {
    SpecialSkill(1),
    NormalSkill(2),
    HeroSkill(3),
    AdventureSkill(4),
    TalentSkill(5),
    HaloSkill(6),
}
