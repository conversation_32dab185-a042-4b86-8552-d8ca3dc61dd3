package com.moyu.core.model.skin

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient
import com.moyu.core.GameCore
import com.moyu.core.model.ConfigData

@Serializable
data class Skin(
    @SerialName("x")
   override val id: Int = 0,
    @Transient
    val name: String = "",
    @Transient
    val type: Int = 0,
    @Transient
    val effectType: List<Int> = emptyList(),
    @Transient
    val effectNum: List<Int> = emptyList(),
    @Transient
    val pic: String = "",
): ConfigData {
    fun create(): Skin {
        return this
    }
}