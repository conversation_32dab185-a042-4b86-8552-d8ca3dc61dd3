package com.moyu.core.model.buff

import com.moyu.core.AppWrapper
import com.moyu.core.R
import com.moyu.core.logic.battle.BattleField
import com.moyu.core.logic.buff.*
import com.moyu.core.logic.identifier.NoneRoleIdentifier
import com.moyu.core.logic.identifier.RoleIdentifier
import com.moyu.core.model.ConfigData
import com.moyu.core.model.property.EMPTY_PROPERTY
import com.moyu.core.model.property.Property
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.enhancement7DispelImmune
import kotlin.math.roundToInt

// continueMaxTurn=99的不会中途检查条件移除，continueMaxTurn=0的会检查
const val ETERNAL_BUFF_TURN = 99

data class Buff(
    override val id: Int,
    val name: String,
    val desc: String,
    val buffType: Int,
    val targetEffect: String,
    val targetEffectNum: Int,
    val skill: Skill? = null,
    val continueMaxTurn: Int = 0,
    val continueCurrentTurn: Int = 0,
    val buffCurrentLayer: Int = 1,
    val buffMaxLayer: Int = 0,
    val buffValue: Double = 0.0,
    val addOnWhosTurn: RoleIdentifier = NoneRoleIdentifier,
    val disposable: Boolean = true,
    // 为了实现四抗逻辑加的
    var buffStoreProperty: Property = EMPTY_PROPERTY,
    val combinedId: Int = 0,
): ConfigData {
    fun getNameInfo(): String {
        return "【$name】"
    }

    fun isEternal(): Boolean {
        return continueMaxTurn == ETERNAL_BUFF_TURN || continueMaxTurn == 0
    }

    fun isControl() = id in FROZEN..DISARM || id in BAN_SKILL_BASE..BAN_SKILL_BASE + 10
    fun isChangeDamage() = id in 5501..5510
    fun isPalsy() = id == PALSY
    fun isHealForbidden() = id == HEAL_FORBID
    fun isFrenzy() = id == FRENZY
    fun isDisarm() = id == DISARM
    fun isSilent() = id == FROZEN
    fun setValue(
        field: BattleField,
        roleIdentifier: RoleIdentifier,
        skill: Skill,
        index: Int,
        skillTrigger: Skill?
    ): Buff {
        return copy(
            skill = skill,
            continueMaxTurn = skill.buffContinue,
            continueCurrentTurn = 0,
            buffMaxLayer = skill.buffLayer,
            buffCurrentLayer = 1,
            buffValue = BuffFactory.genBuffValue(
                field,
                roleIdentifier,
                skill.effectReference[index],
                skill.effectNum[index],
                skillTrigger
            ).let {
                // 圣盾不要小数
                if (this.id == HOLY_SHIELD) {
                    it.roundToInt().toDouble()
                } else {
                    it
                }
            },
            disposable = skill.isDispel
        )
    }

    fun isStamp(): Boolean {
        return id in 1007..1012 || id in 7001..7004
    }

    /**
     *  0=非buff效果
    1=最多一层
     */
    fun getLayerString(): String {
        return if (buffMaxLayer > 1) buffCurrentLayer.toString() + AppWrapper.getString(R.string.layer) else AppWrapper.getString(
                    R.string.unstack)
    }

    fun reachedMaxTurn(result: BattleField, increase: Int): Boolean {
        return if (result.isOnesTurn(addOnWhosTurn)) {
            continueCurrentTurn + 1 >= increase + continueMaxTurn
        } else {
            continueCurrentTurn >= increase + continueMaxTurn
        }
    }

    fun isThisControlImmune(buffCarrier: BuffCarrier): Boolean {
        return when {
            isFrenzy() -> buffCarrier.isFrenzyImmune()
            isDisarm() -> buffCarrier.isDisarmImmune()
            isHealForbidden() -> buffCarrier.isHealForbiddenImmune()
            isPalsy() -> buffCarrier.isPalsyImmune()
            isSilent() -> buffCarrier.isFrozenImmune()
            else -> false
        }
    }

    fun disposableBadBuff(): Boolean {
        return buffType == 2 && isDisposable()
    }

    fun disposableGoodBuff(): Boolean {
        return buffType == 1 && isDisposable()
    }

    private fun getRealMaxTurn(role: Role): Int {
        return role.getBuffContinueIncrease(this) + continueMaxTurn
    }

    fun leftTurnString(role: Role): String {
        return if (isEternal()) {
            AppWrapper.getString(R.string.infinite)
        } else {
            "${(getRealMaxTurn(role) - continueCurrentTurn)}"
        }
    }

    fun isDisposable(): Boolean {
        // 这个强化如何整合到RoleComposer去
        val skillDisposable =
            (skill?.getAllEnhancements()?.none { it.enhancementId == enhancement7DispelImmune })
                ?: true
        return disposable && skillDisposable
    }

    fun isCombined(): Boolean {
        return combinedId != 0
    }

    fun isGood(): Boolean {
        return buffType == 1
    }
}