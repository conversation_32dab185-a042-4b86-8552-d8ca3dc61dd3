package com.moyu.core.model.tcg

import com.moyu.core.GameCore
import com.moyu.core.model.ConfigData
import kotlinx.serialization.Serializable

fun getTypeName(cardType: Int): String {
    return GameCore.instance.getTcgCardTypePool().first { it.type == cardType }.name
}

@Serializable
data class TcgCardType(
    override val id: Int,
    val type: Int,
    val name: String,
    val total: Int,
): ConfigData