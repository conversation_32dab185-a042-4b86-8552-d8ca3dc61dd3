package com.moyu.core.model.action

import com.moyu.core.model.buff.Buff
import com.moyu.core.model.damage.DamageResult
import com.moyu.core.model.heal.HealResult
import com.moyu.core.model.skill.Skill

data class ActionState(
    val type: ActionStateType = ActionStateType.Normal,
    val skill: Skill? = null,
    val damage: DamageResult? = null,
    val value: Int = 0,
    val healResult: HealResult? = null,
    val buff: Buff? = null,
    val targets: List<String> = emptyList(),
) {
    fun isState(stateType: ActionStateType): Bo<PERSON>an {
        return stateType == type
    }
}