package com.moyu.core.model.encounter

import kotlinx.serialization.Serializable
import com.moyu.core.model.ConfigData

@Serializable
data class Encounter(
    override val id: Int = 0,
    val name: String,
    val desc1: String,
    val answer1: String,
    val answer2: String,
    val result1: String,
    val result2: String,
    val rate1: List<Int>,
    val rate2: List<Int>,
    val reward1: List<Int>,
    val reward2: List<Int>,
    val rewardSubType1: List<Int>,
    val rewardSubType2: List<Int>,
    val rewardNum1: List<Int>,
    val rewardNum2: List<Int>,
    val npcPic: String,
    val npcName: String,
    val fail1: Int,
    val fail2: Int,
): ConfigData