package com.moyu.core.model.enemy

import com.moyu.core.AppWrapper
import com.moyu.core.R


fun Int.toElite(): EliteType {
    return when(this) {
        1 -> EliteType.Normal
        2 -> EliteType.Elite
        else -> EliteType.Boss
    }
}
/**
 * 这里都index，普通/精英/boss在表里的index是固定的
 */
open class EliteType(val name: String, val value: Int) {
    object Normal: EliteType("", 1)
    object Elite: EliteType(AppWrapper.getString(R.string.elite), 2)
    object Boss: EliteType("Boss", 3)
}