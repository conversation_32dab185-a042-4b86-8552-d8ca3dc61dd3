package com.moyu.core.model.sell

import com.moyu.core.GameCore
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient

@Serializable
data class Gift(
    val id: Int = 0,
    @Transient
    val name: String = "",
    @Transient
    val icon: String = "",
    @Transient
    val content: String = "",
    @Transient
    val pic: String = "",
    @Transient
    val label_pic: String = "",
    @Transient
    val poolId: Int = 0,
    @Transient
    val price: Int = 0,
    @Transient
    val priceDollar: Double = 0.0,
    @Transient
    val unlockId: Int = 0,
    @Transient
    val limitTime: Int = 0,
    @Transient
    val triggerType: Int = 0,
    @Transient
    val limitBuy: Int = 0,
    @Transient
    val priority: Int = 0,
    val displayTime: Long = 0,
    val dialogShowed: Boolean = false,
) {
    fun isFirstCharge(): Boolean {
        return id == GameCore.instance.getGiftPool().first().id
    }

    fun isStory(): Boolean {
        return id == 40004 || id == 40005
    }

    fun isTriggerEveryDay(): Boolean {
        return triggerType == 2
    }
}