package com.moyu.core.model.damage

import com.moyu.core.AppWrapper
import com.moyu.core.R

enum class DamageType(val display: String, val defenseDisplay: String, val value: Int) {
    DamageType1(AppWrapper.getString(R.string.damage1), AppWrapper.getString(R.string.defense1), 1),
    DamageType2(AppWrapper.getString(R.string.damage2), AppWrapper.getString(R.string.defense2), 2),
    DamageType3(AppWrapper.getString(R.string.damage3), AppWrapper.getString(R.string.defense2), 3),
    DamageType4(AppWrapper.getString(R.string.damage4), AppWrapper.getString(R.string.defense2), 4),
    DamageType5(AppWrapper.getString(R.string.damage5), AppWrapper.getString(R.string.defense2), 5),;

    companion object {
        fun fromTypeValue(type: Int): DamageType? {
            return DamageType.values().firstOrNull { it.value == type }
        }

        fun fromDisplayValue(display: String): DamageType? {
            return DamageType.values().firstOrNull { it.display == display }
        }
    }
}