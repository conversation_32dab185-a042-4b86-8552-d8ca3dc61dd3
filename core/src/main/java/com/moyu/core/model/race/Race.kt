package com.moyu.core.model.race

import com.moyu.core.AppWrapper
import com.moyu.core.GameCore
import com.moyu.core.R
import com.moyu.core.model.ConfigData
import com.moyu.core.model.property.Property
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient

fun Int.getRaceTypeName(): String {
    return when (this) {
        1 -> AppWrapper.getString(R.string.race1)
        2 -> AppWrapper.getString(R.string.race2)
        3 -> AppWrapper.getString(R.string.race3)
        4 -> AppWrapper.getString(R.string.race4)
        else -> AppWrapper.getString(R.string.race5)
    }
}

@Serializable
data class Race(
    override val id: Int = 1,
    val roleId: Int = 0,
    val name: String = "",
    val raceType: Int = 0,
    val level: Int = 0,
    val star: Int = 0,
    val quality: Int = 0,
    @Transient val attribute1: Int = 0,
    @Transient val attribute2: Int = 0,
    @Transient val attribute3: Int = 0,
    @Transient val attribute4: Double = 0.0,
    @Transient val attribute5: Double = 0.0,
    @Transient val attribute6: Double = 0.0,
    @Transient val attribute7: Double = 0.0,
    @Transient val attribute8: Int = 0,

    val skillId: List<Int> = emptyList(),
    val randomSkillId: List<Int> = emptyList(),
    val randomSkillNum: List<Int> = emptyList(),
    val pic: String = "",
    val story: String = "",
): ConfigData {
    fun getProperty(): Property {
        val defense = listOf(attribute2)
        return Property(
            attack = attribute1,
            defenses = defense,
            hp = attribute3,
            fatalRate = attribute4,
            fatalDamage = attribute5,
            dodgeRate = attribute6,
            suckBloodRate = attribute7,
            speed = attribute8
        )
    }

    fun getAlly() = GameCore.instance.getAllyById(id)
}