package com.moyu.core.model.talent

import kotlinx.serialization.Serializable
import com.moyu.core.model.ConfigData

@Serializable
data class Talent(
  override val id: Int = 0,
  val mainId: Int = 0,
  val type: Int = 0,
  val name: String = "",
  val level: Int = 0,
  val levelLimit: Int = 0,
  val position: List<Int> = emptyList(),
  val talentSkill: Int = 0,
  val cost: Int = 0,
  val conditionType: Int =0,
  val conditionNum: Int = 0,
  val icon: String = "",
): ConfigData