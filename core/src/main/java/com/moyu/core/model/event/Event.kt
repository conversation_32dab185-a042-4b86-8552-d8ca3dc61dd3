package com.moyu.core.model.event

import com.moyu.core.GameCore
import com.moyu.core.model.property.Property
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient

@Serializable
data class Event(
    @SerialName("z")
    val id: Int,
    @Transient
    val name: String = "",
    @Transient
    val age: List<Int> = emptyList(),
    @Transient
    val appear: Int = 0,
    @Transient
    val same: Int = 0,
    @Transient
    val front: List<Int> = emptyList(),
    @Transient
    val disappear: List<Int> = emptyList(),
    @Transient
    val condition: Int = 0,
    @Transient
    val play: Int = 0,
    @Transient
    val playPara1: List<Int> = emptyList(),
    @Transient
    val playPara2: List<Double> = emptyList(),
    @Transient
    val winReward: List<Int> = emptyList(),
    @Transient
    val loseReward: List<Int> = emptyList(),
    @Transient
    val dialogId: Int = 0,
    @Transient
    val dialogType: Int = 0,
    @Transient
    val showText: String = "",
    @Transient
    val startText: String = "",
    @Transient
    val winText: String = "",
    @Transient
    val loseText: String = "",
    @Transient
    val isEnd: Boolean = true,
    @Transient
    val endType: Int = 0,
    @Transient
    val storyDesc1: String = "",
    @Transient
    val storyDesc2: String = "",
    @Transient
    val storyBag: Int = 0,
    @Transient val eventAttribute1: Int = 0,
    @Transient val eventAttribute2: Int = 0,
    @Transient val eventAttribute3: Int = 0,
    @Transient val eventAttribute4: Double = 0.0,
    @Transient val eventAttribute5: Double = 0.0,
    @Transient val eventAttribute6: Double = 0.0,
    @Transient val eventAttribute7: Double = 0.0,
    @Transient val eventAttribute8: Int = 0,
    @Transient val pic: String = "",
    @Transient val regularDialog: String = "",
    @Transient val regularNPCName: String = "",
    @Transient val regularNPCPic: String = "",
    @Transient val endTitle: String = "",
    @Transient val bgPic: String = "",
    @Transient val tag: String = "",
    @Transient val storyBagLimit: Int = 0,
    @Transient val isMainLine: Int = 0,
    @Transient val isHard: Int = 0,
    @Transient val isRepeat: Int = 0,
    val uuid: String = ""
) {
    fun getDiffProperty(): Property {
        return Property(
            attack = eventAttribute1,
            defenses = listOf(eventAttribute2),
            hp = eventAttribute3,
            fatalRate = eventAttribute4,
            fatalDamage = eventAttribute5,
            dodgeRate = eventAttribute6,
            suckBloodRate = eventAttribute7,
            speed = eventAttribute8,
        )
    }

    fun create(): Event {
        return GameCore.instance.getEventPool().first { it.id == this.id }
    }
    fun createOrNull(): Event? {
        return GameCore.instance.getEventPool().firstOrNull { it.id == this.id }
    }

    fun isBad(): Boolean {
        return play == 9 || play == 10 || play == 11 || play == 12
    }

    fun createUUID(): Event {
        return copy(uuid = java.util.UUID.randomUUID().toString())
    }

    override fun equals(other: Any?): Boolean {
        return if (isRepeat != 1) {
            id == other?.let { (it as Event).id }
        } else {
            super.equals(other)
        }
    }
}