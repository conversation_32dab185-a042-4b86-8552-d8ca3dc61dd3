package com.moyu.core.model.skill

import com.moyu.core.AppWrapper
import com.moyu.core.R
import com.moyu.core.logic.damage.processor.subTypeToDamageType
import com.moyu.core.model.damage.DamageType
import com.moyu.core.model.race.getRaceTypeName
import com.moyu.core.util.realValueToDotWithOneDigits


@JvmInline
value class SkillEnhancementType(val type: Int)

val enhancementTypeNull = SkillEnhancementType(0)
val enhancement1Fatal = SkillEnhancementType(1)
val enhancement2DoubleSkill = SkillEnhancementType(2)
val enhancement3ImmuneDodge = SkillEnhancementType(3)
val enhancement4IgnoreShield = SkillEnhancementType(4)
val enhancement5IgnoreImmune = SkillEnhancementType(5)
val enhancement6IgnoreTaunt = SkillEnhancementType(6)
val enhancement7DispelImmune = SkillEnhancementType(7)
val enhancement8SkillBoost = SkillEnhancementType(8)
val enhancement9SkillWeaken = SkillEnhancementType(9)
val enhancement10CDInc = SkillEnhancementType(10)
val enhancement11CDDec = SkillEnhancementType(11)
val enhancement12SkillBuffInc = SkillEnhancementType(12)
val enhancement13SkillBuffDec = SkillEnhancementType(13)
val enhancement14SkillRateInc = SkillEnhancementType(14)
val enhancement15SkillRateDec = SkillEnhancementType(15)
val enhancement16SkillTurnLimitInc = SkillEnhancementType(16)
val enhancement17SkillTurnLimitDec = SkillEnhancementType(17)
val enhancement18SkillAllLimitInc = SkillEnhancementType(18)
val enhancement19SkillAllLimitDec = SkillEnhancementType(19)
val enhancement20SuckBlood = SkillEnhancementType(20)
val enhancement61Divide = SkillEnhancementType(61)
fun Int.isRaceDamageIncEnhancement(raceType: Int): Boolean {
    return this in 41..50 && raceType == this - 40
}
fun Int.isRaceDamageDecEnhancement(raceType: Int): Boolean {
    return this in 51..60 && raceType == this - 50
}

fun SkillEnhancementType.matchDamage(damageType: DamageType): Boolean {
    return this.type - 20 == damageType.value
}

/**
 * 技能强化属性
 */
data class SkillEnhancement(
    val enhancementId: SkillEnhancementType = enhancementTypeNull,
    val enhancementValue: Double = 0.0,
) {
    fun desc(): String {
        val enhanceInfo = when (enhancementId.type) {
            enhancement1Fatal.type -> {
                AppWrapper.getString(R.string.designated_skill_strike_rate_increase) + (enhancementValue).realValueToDotWithOneDigits()
            }
            enhancement2DoubleSkill.type -> {
                AppWrapper.getString(R.string.increase_in_the_number_of_times_a_given_skill_can_be_cast) + (enhancementValue).realValueToDotWithOneDigits()
            }
            enhancement3ImmuneDodge.type -> {
                AppWrapper.getString(R.string.immunity_to_blocking_for_specified_skills)
            }
            enhancement4IgnoreShield.type -> {
                AppWrapper.getString(R.string.designated_skills_ignore_stance)
            }
            enhancement5IgnoreImmune.type ->{
                AppWrapper.getString(R.string.designated_skills_ignore_invincibility)
            }
            enhancement6IgnoreTaunt.type->{
                AppWrapper.getString(R.string.designated_skills_ignore_taunts)
            }
            enhancement7DispelImmune.type -> {
                AppWrapper.getString(R.string.cannot_be_dispersed)
            }
            enhancement8SkillBoost.type -> {
                AppWrapper.getString(R.string.increased_damage_dealt_by_designated_skills) + (enhancementValue).realValueToDotWithOneDigits() + "%"
            }
            enhancement9SkillWeaken.type -> {
                AppWrapper.getString(R.string.designated_skills_deal_less_damage) + (enhancementValue).realValueToDotWithOneDigits() + "%"
            }
            enhancement10CDInc.type -> {
                AppWrapper.getString(R.string.designated_skill_cd_increase) + (enhancementValue).realValueToDotWithOneDigits()
            }
            enhancement11CDDec.type -> {
                AppWrapper.getString(R.string.designated_skill_cd_decrease) + (enhancementValue).realValueToDotWithOneDigits()
            }
            enhancement12SkillBuffInc.type -> {
                AppWrapper.getString(R.string.increased_number_of_rounds) + (enhancementValue).realValueToDotWithOneDigits()
            }
            enhancement13SkillBuffDec.type -> {
                AppWrapper.getString(R.string.decreased_number_of_rounds) + (enhancementValue).realValueToDotWithOneDigits()
            }
            enhancement14SkillRateInc.type -> {
                AppWrapper.getString(R.string.increased_probability_of_releasing_designated_skills) + (enhancementValue).realValueToDotWithOneDigits() + "%"
            }
            enhancement15SkillRateDec.type -> {
                AppWrapper.getString(R.string.reduced_probability_of_releasing_designated_skills) + (enhancementValue).realValueToDotWithOneDigits() + "%"
            }
            enhancement16SkillTurnLimitInc.type-> {
                AppWrapper.getString(R.string.increase_in_the_number_of_times_a_given_skill_can_be_released_per_round) + (enhancementValue).realValueToDotWithOneDigits()
            }
            enhancement17SkillTurnLimitDec.type-> {
                AppWrapper.getString(R.string.reduced_limit_on_the_number_of_times_a_given_skill_can_be_released_per_round) + (enhancementValue).realValueToDotWithOneDigits()
            }
            enhancement18SkillAllLimitInc.type-> {
                AppWrapper.getString(R.string.increase_in_the_number_of_restricted_releases) + (enhancementValue).realValueToDotWithOneDigits()
            }
            enhancement19SkillAllLimitDec.type-> {
                AppWrapper.getString(R.string.reduced_limit_on_number_of_releases) + (enhancementValue).realValueToDotWithOneDigits()
            }
            enhancement20SuckBlood.type -> {
                AppWrapper.getString(R.string.increased_blood_absorption_rate_of_skills) + (enhancementValue).realValueToDotWithOneDigits() + "%"
            }
            in 21..30 -> {
                val damageType = enhancementId.type - 20
                AppWrapper.getString(R.string.designated_skills_ignore_enemies) + damageType.subTypeToDamageType().display + damageType.subTypeToDamageType().defenseDisplay + (enhancementValue).realValueToDotWithOneDigits() + "%"
            }
            in 31..40 -> {
                val damageType = enhancementId.type - 30
                AppWrapper.getString(R.string.skill_damage_type_changes_to) + damageType.subTypeToDamageType().display
            }
            in 41..50 -> {
                val raceType = enhancementId.type - 40
                AppWrapper.getString(R.string.designated_skills_vs_race) + raceType.getRaceTypeName() + AppWrapper.getString(
                                    R.string.damage_increase) + (enhancementValue).realValueToDotWithOneDigits() + "%"
            }
            in 51..60 -> {
                val raceType = enhancementId.type - 50
                AppWrapper.getString(R.string.designated_skills_vs_race) + raceType.getRaceTypeName() + AppWrapper.getString(
                                    R.string.damage_decrease) + (enhancementValue).realValueToDotWithOneDigits() + "%"
            }
            enhancement61Divide.type->{
                AppWrapper.getString(R.string.gain_split_effect)
            }
            else -> {
                ""
            }
        }
        return enhanceInfo
    }


    fun isDamageTypeChange(): Boolean {
        return enhancementId.type in 31..40
    }

    fun damageType(): DamageType {
        return DamageType.fromTypeValue(this.enhancementId.type - 30)!!

    }
}