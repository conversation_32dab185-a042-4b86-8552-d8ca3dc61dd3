package com.moyu.core.model.sell

import com.moyu.core.AppWrapper
import com.moyu.core.GameCore
import com.moyu.core.R
import com.moyu.core.model.ally.Ally
import com.moyu.core.model.property.AdventureProps
import com.moyu.core.model.property.EMPTY_ADV_PROPS
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skin.Skin
import com.moyu.core.model.tcg.TcgCard
import com.moyu.core.util.perMultiI
import com.moyu.core.util.perPlusI
import kotlinx.serialization.Serializable

val EMPTY_ELEMENTS = listOf(
    0, 0, 0, 0, 0
)

// todo 新增国家，要改下面两个定义
val EMPTY_REPUTATION = listOf(
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0
)

val EMPTY_BADGE = listOf(
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0
)

const val IMPOSSIBLE_BADGE = 10

@Serializable
data class Award(
    val code: String = "",
    val message: String = "",
    val heroes: List<Skill> = emptyList(),
    val loseHeroes: List<Skill> = emptyList(),
    val outHeroes: List<Skill> = emptyList(),
    val allies: List<Ally> = emptyList(),
    val loseAllies: List<Ally> = emptyList(),
    val outAllies: List<Ally> = emptyList(),
    val skills: List<Skill> = emptyList(),
    val loseSkills: List<Skill> = emptyList(),
    val outSkills: List<Skill> = emptyList(),
    val tcgs: List<TcgCard> = emptyList(),
    val skins: List<Skin> = emptyList(),
    val property: AdventureProps = EMPTY_ADV_PROPS,
    val badges: List<Int> = EMPTY_BADGE,
    val reputations: List<Int> = EMPTY_REPUTATION,
    val elements: List<Int> = EMPTY_ELEMENTS,
    val extraElements: List<Int> = EMPTY_ELEMENTS,
    val diamond: Int = 0,
    val key: Int = 0,
    val reputationMoney: Int = 0,
    val couponAlly: Int = 0,
    val couponSkill: Int = 0,
    val couponHero: Int = 0,
    val warPass: Int = 0,
    val warPass2: Int = 0,
    val pvpDiamond: Int = 0,
    val pvpScore: Int = 0,
    val electric: Int = 0,
    val lotteryMoney: Int = 0,
    val adMoney: Int = 0,
    val realMoney: Int = 0,
    val heal: Int = 0,
    val exp: Int = 0,
    val extraDiamond: Int = 0,// 仅vip显示，实际已经加入money
    val extraKey: Int = 0, // 仅vip显示，实际已经加入exp
    val unlockList: List<Int> = emptyList(),
    val showQuestion: Boolean = false,
    val valid: Boolean = false,
    val dueTime: Long = 0L,
) {
    operator fun plus(singleAward: Award): Award {
        return copy(
            heroes = mutableListOf<Skill>().apply {
                addAll(heroes)
                addAll(singleAward.heroes)
            },
            loseHeroes = mutableListOf<Skill>().apply {
                addAll(loseHeroes)
                addAll(singleAward.loseHeroes)
            },
            outHeroes = mutableListOf<Skill>().apply {
                addAll(outHeroes)
                addAll(singleAward.outHeroes)
            },
            skills = mutableListOf<Skill>().apply {
                addAll(skills)
                addAll(singleAward.skills)
            },
            loseSkills = mutableListOf<Skill>().apply {
                addAll(loseSkills)
                addAll(singleAward.loseSkills)
            },
            outSkills = mutableListOf<Skill>().apply {
                addAll(outSkills)
                addAll(singleAward.outSkills)
            },
            allies = mutableListOf<Ally>().apply {
                addAll(allies)
                addAll(singleAward.allies)
            },
            loseAllies = mutableListOf<Ally>().apply {
                addAll(loseAllies)
                addAll(singleAward.loseAllies)
            },
            outAllies = mutableListOf<Ally>().apply {
                addAll(outAllies)
                addAll(singleAward.outAllies)
            },
            tcgs = mutableListOf<TcgCard>().apply {
                addAll(tcgs)
                addAll(singleAward.tcgs)
            },
            skins = mutableListOf<Skin>().apply {
                addAll(skins)
                addAll(singleAward.skins)
            },
            unlockList = mutableListOf<Int>().apply {
                addAll(unlockList)
                addAll(singleAward.unlockList)
            },
            elements = elements.perPlusI(singleAward.elements),
            extraElements = extraElements.perPlusI(singleAward.extraElements),
            reputations = reputations.perPlusI(singleAward.reputations),
            badges = badges.perPlusI(singleAward.badges),
            key = key + singleAward.key,
            heal = heal + singleAward.heal,
            diamond = diamond + singleAward.diamond,
            couponAlly = couponAlly + singleAward.couponAlly,
            couponSkill = couponSkill + singleAward.couponSkill,
            couponHero = couponHero + singleAward.couponHero,
            reputationMoney = reputationMoney + singleAward.reputationMoney,
            lotteryMoney = lotteryMoney + singleAward.lotteryMoney,
            pvpDiamond = pvpDiamond + singleAward.pvpDiamond,
            pvpScore = pvpScore + singleAward.pvpScore,
            electric = electric + singleAward.electric,
            realMoney = realMoney + singleAward.realMoney,
            warPass = warPass + singleAward.warPass,
            warPass2 = warPass2 + singleAward.warPass2,
            exp = exp + singleAward.exp,
            adMoney = adMoney + singleAward.adMoney,
            extraDiamond = extraDiamond + singleAward.extraDiamond,
            extraKey = extraKey + singleAward.extraKey,
            property = property + singleAward.property,
            showQuestion = showQuestion || singleAward.showQuestion
        )
    }

    fun isEmpty(): Boolean {
        return this == Award()
    }

    operator fun unaryMinus(): Award {
        return this.copy(
            loseHeroes = heroes,
            heroes = emptyList(),
            loseAllies = allies,
            allies = emptyList(),
            loseSkills = skills,
            skills = emptyList(),
            key = -key,
            realMoney = -realMoney,
            diamond = -diamond,
            couponAlly = -couponAlly,
            couponSkill = -couponSkill,
            couponHero = -couponHero,
            warPass = -warPass,
            warPass2 = -warPass2,
            electric = -electric,
            pvpDiamond = -pvpDiamond,
            lotteryMoney = -lotteryMoney,
            reputationMoney = -reputationMoney,
            elements = elements.map { -it },
            extraElements = extraElements.map { -it },
            reputations = reputations.map { -it },
            badges = badges.map { -it },
            property = EMPTY_ADV_PROPS - property,
        )
    }

    operator fun times(value: Int): Award {
        return copy(
            elements = elements.perMultiI(value),
            extraElements = extraElements.perMultiI(value),
            reputations = reputations.perMultiI(value),
            badges = badges.perMultiI(value),
            key = key * value,
            diamond = diamond * value,
            pvpDiamond = pvpDiamond * value,
            electric = electric * value,
            warPass = warPass * value,
            warPass2 = warPass2 * value,
            exp = exp * value,
            extraDiamond = extraDiamond * value,
            extraKey = extraKey * value,
            property = property * value,
        )
    }

    fun consumablePart(): Award {
        return Award(
            elements = this.elements,
            badges = this.badges
        )
    }

    fun noneConsumablePart(): Award {
        return copy(elements = EMPTY_ELEMENTS, badges = EMPTY_BADGE)
    }

    fun recreate(): Award {
        return copy(outAllies = this.outAllies.map {
            GameCore.instance.getAllyById(it.id).copy(num = it.num)
        }, outSkills = this.outSkills.map {
            GameCore.instance.getSkillById(it.id).copy(num = it.num)
        }, outHeroes = this.outHeroes.map {
            GameCore.instance.getSkillById(it.id).copy(num = it.num)
        })
    }

    fun getResourceString(): String {
        val stringBuilder = StringBuilder()
        elements.forEachIndexed { index, i ->
            if (i > 0) {
                stringBuilder.append(GameCore.instance.getResourceNameByIndex(index) + "+" + i + ",")
            }
        }
        extraElements.forEachIndexed { index, i ->
            if (i > 0) {
                stringBuilder.append(GameCore.instance.getResourceNameByIndex(index) + AppWrapper.getString(
                    R.string.production_capacity) + "+" + i + ",")
            }
        }
        return stringBuilder.toString()
    }
}
