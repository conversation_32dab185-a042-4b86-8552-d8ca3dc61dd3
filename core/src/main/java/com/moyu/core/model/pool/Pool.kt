package com.moyu.core.model.pool

import com.moyu.core.model.ConfigData

data class Pool(
    override val id: Int = 0,
    val type: List<Int>,
    val pool: List<Int>,
    val num: List<Int>,
    val rate: List<Int>,
    val quality: List<Int>,
): ConfigData {
    fun split(): List<Pool> {
        return List(pool.size) { index ->
            Pool(
                id = id,
                type = listOf(type[index]),
                pool = listOf(pool[index]),
                num = listOf(num[index]),
                rate = listOf(rate.getOrNull(index)?: 0),
                quality = listOf(quality.getOrNull(index)?: 0),
            )
        }
    }
}