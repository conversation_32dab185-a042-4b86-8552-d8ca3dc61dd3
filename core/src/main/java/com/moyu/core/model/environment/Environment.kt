package com.moyu.core.model.environment

val Normal = Environment("普通", 0)
val BattleZone = Environment("战场", 1)
val HolyPalace = Environment("神殿", 2)
val Volcano = Environment("火山", 3)
val Ocean = Environment("海洋", 4)
val Cemetery = Environment("墓园", 5)
val Abyss = Environment("深渊", 6)

class Environment(val name: String, val id: Int) {
    companion object {
        fun getEnvironmentById(id: Int): Environment {
            return listOf(BattleZone, Ocean, HolyPalace, Volcano, Cemetery, Abyss).find { it.id == id } ?: Normal
        }
    }

    fun getNameInfo(): String {
        return "【$name】"
    }

    fun isNormal(): Boolean {
        return id == 0
    }
}