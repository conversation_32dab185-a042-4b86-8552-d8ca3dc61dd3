package com.moyu.core.model.ally

import com.moyu.core.GameCore
import com.moyu.core.logic.identifier.Identifier
import com.moyu.core.logic.identifier.NoneRoleIdentifier
import com.moyu.core.model.GameItem
import com.moyu.core.model.property.Property
import com.moyu.core.model.race.Race
import com.moyu.core.model.skill.Skill
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient
import kotlinx.uuid.UUID
import kotlinx.uuid.generateUUID
import java.lang.Integer.max


@Serializable
data class Ally(
    @SerialName("x")
    val id: Int,
    @Transient
    val mainId: Int = 0,
    @Transient
    val name: String = "",
    @Transient
    val star: Int = 0,
    @Transient
    val starLimit: Int = 0,
    @Transient
    val quality: Int = 0,
    @Transient
    val type: Int = 0,
    @Transient
    val skillNum: Int = 0,
    @Transient
    val starUpNum: Int = 0,
    @Transient
    val starUpRes: Int = 0,
    @Transient
    val dropLimit: Int = 0,
    @Transient
    val story: String = "",
    @Transient
    val empire: String = "",
    @SerialName("z")
    val num: Int = 1,
    override val new: Boolean = true,
    @SerialName("u")
    val selected: Boolean = false,
    // 商店信息
    @Transient
    val peek: Boolean = false,
    // 局内信息
    @SerialName("o")
    val equipSkills: List<Skill> = emptyList(),
    @SerialName("r")
    val gameHp: Int = 100, // 0是死亡，改为百分比，100%是满血
    @SerialName("p")
    val exerciseProperty: Property? = null,
    @SerialName("c")
    val extraInfo: String = "", // 用来标记特殊文案，比如协助战斗，保护对象
    @SerialName("a")
    val selectedToBattle: Boolean = false, // 是否参战
    val selectedToBattleTime: Long = 0L, // 选择时间
    @SerialName("b")
    val inGame: Boolean = false, // 仅局内
    @SerialName("l")
    val roleIdentifier: Identifier = NoneRoleIdentifier,
    @SerialName("w")
    override val uuid: String = ""
) : GameItem {
    /**
     * 选中去游戏中，是一个新的实例，带uuid，和局外军团卡无关
     */
    fun copyToGame(): Ally {
        return copy(
            uuid = UUID.generateUUID().toString(),
            roleIdentifier = Identifier.player(name),
            inGame = true
        )
    }

    fun switchSelect(): Ally {
        return copy(selected = !selected, uuid = uuid.ifEmpty { UUID.generateUUID().toString() })
    }

    fun relive(): Ally {
        return copy(gameHp = 100)
    }

    fun hurt(percent: Int): Ally {
        return copy(gameHp = max(gameHp - percent, 1))
    }

    fun isDead(): Boolean {
        return gameHp <= 0
    }

    fun isHurt(): Boolean {
        return gameHp > 0 && gameHp != 100
    }

    fun getRaceType(): Int {
        return GameCore.instance.getRaceById(id).raceType
    }

    fun starUp(): Ally {
        val starUp =
            GameCore.instance.getAllyPool()
                .firstOrNull { it.mainId == mainId && it.star == star + 1 }
        return starUp?.copy(
            num = num,
            selected = selected,
            equipSkills = equipSkills,
            gameHp = gameHp,
            exerciseProperty = exerciseProperty,
            roleIdentifier = roleIdentifier,
            selectedToBattle = selectedToBattle,
            uuid = uuid,
            selectedToBattleTime = selectedToBattleTime
        ) ?: this
    }

    fun switchSelectToBattle(): Ally {
        return copy(
            selectedToBattle = !selectedToBattle,
            selectedToBattleTime = System.currentTimeMillis()
        )
    }

    override fun create(): Ally {
        return GameCore.instance.getAllyPool().first { it.id == id }.copy(
            num = this.num,
            selected = this.selected,
            uuid = this.uuid,
            roleIdentifier = Identifier.player(name),
            new = false,
            equipSkills = equipSkills.mapNotNull {
                it.create()
            },
            gameHp = gameHp,
            exerciseProperty = exerciseProperty,
            extraInfo = extraInfo,
            selectedToBattle = selectedToBattle,
            inGame = inGame,
        )
    }

    override fun setUnNew(): GameItem {
        return copy(new = false)
    }

    fun isAiFaDianAlly(): Boolean {
        return mainId in listOf(1022, 1095, 1108, 1120)
    }

    fun isAiFaDianAlly1(): Boolean {
        return mainId == 1022
    }

    fun isAiFaDianAlly2(): Boolean {
        return mainId == 1095
    }

    fun isAiFaDianAlly3(): Boolean {
        return mainId == 1108
    }

    fun isAiFaDianAlly4(): Boolean {
        return mainId == 1120
    }

    fun isOrange(): Boolean {
        return quality == 3
    }

    fun isXuanJiaJun(): Boolean {
        return mainId == 1101
    }

    fun isDaTianShi(): Boolean {
        return mainId == 1102
    }

    fun isTalentGift(): Boolean {
        return mainId in listOf(1015, 1004, 1027, 1041, 1054)
    }

    fun getRace(): Race {
        return GameCore.instance.getRaceById(id)
    }
}