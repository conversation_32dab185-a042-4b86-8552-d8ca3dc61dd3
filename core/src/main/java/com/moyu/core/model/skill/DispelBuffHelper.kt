package com.moyu.core.model.skill

import com.moyu.core.AppWrapper
import com.moyu.core.GameCore
import com.moyu.core.R
import com.moyu.core.logic.battle.BattleField
import com.moyu.core.logic.battle.skillChainHandler
import com.moyu.core.logic.info.addSkillCastInfo
import com.moyu.core.logic.skill.DispelEnemy
import com.moyu.core.logic.skill.DispelMyself
import com.moyu.core.model.buff.Buff
import com.moyu.core.model.role.Role

suspend fun clearRoleDebuff(
    role: Role,
    skillOwner: Role,
    result: BattleField,
    skill: Skill,
    clearBuffList: List<Buff>
) {
    role.setDispelState()
    if (role.cantBeDispelDebuff(skill)) {
        GameCore.instance.addSkillCastInfo(
            skillOwner, skillOwner.getSideName() + AppWrapper.getString(R.string.cant_dispel), skill
        )
    } else {
        if (clearBuffList.isNotEmpty()) {
            val clearedSize = clearBuffList.map { role.clearAnyBuff(result, role, it) }.size
            GameCore.instance.addSkillCastInfo(
                skillOwner,
                skillOwner.getSideName() + AppWrapper.getString(R.string.dispelled) + role.getSideName() + clearedSize + "debuff", skill
            )
            if (role.isPlayer() == skillOwner.isPlayer()) {
                //驱散自己效果
                skillChainHandler.invoke(
                    DispelMyself.copy(ownerIdentifier = skillOwner), result
                )
            } else {
                //驱散敌人效果
                skillChainHandler.invoke(
                    DispelEnemy.copy(ownerIdentifier = skillOwner), result
                )
            }
        }
    }
}

suspend fun clearRoleBuff(
    role: Role,
    skillOwner: Role,
    result: BattleField,
    skill: Skill,
    clearBuffList: List<Buff>
) {
    role.setDispelState()
    if (role.cantBeDispelBuff(skill)) {
        GameCore.instance.addSkillCastInfo(
            skillOwner, skillOwner.getSideName() + AppWrapper.getString(R.string.cant_dispel2), skill
        )
    } else {
        if (clearBuffList.isNotEmpty()) {
            val clearedSize = clearBuffList.map { role.clearAnyBuff(result, role, it) }.size
            GameCore.instance.addSkillCastInfo(
                skillOwner,
                skillOwner.getSideName() + AppWrapper.getString(R.string.dispelled) + role.getSideName() + clearedSize + "buff", skill
            )
            if (role.isPlayer() == skillOwner.isPlayer()) {
                //驱散自己效果
                skillChainHandler.invoke(
                    DispelMyself.copy(ownerIdentifier = skillOwner), result
                )
            } else {
                //驱散敌人效果
                skillChainHandler.invoke(
                    DispelEnemy.copy(ownerIdentifier = skillOwner), result
                )
            }
        } else {
            GameCore.instance.addSkillCastInfo(
                skillOwner, skillOwner.getSideName() + AppWrapper.getString(R.string.no_dispelling_of_target_buffs), skill
            )
        }
    }
}