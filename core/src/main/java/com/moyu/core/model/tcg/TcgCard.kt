package com.moyu.core.model.tcg

import com.moyu.core.GameCore
import com.moyu.core.model.ConfigData
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient

@Serializable
data class TcgCard(
    override val id: Int,
    @Transient
    val type: Int = 0, // 卡牌大类，1怪物卡，2地图卡，3元素卡
    @Transient
    val name: String= "",
    @Transient
    val pic: String= "",
    @Transient
    val quality: Int = 0,
    val count: Int = 1,
) : ConfigData {
    fun increaseNum(i: Int): TcgCard {
        return copy(count = count + i)
    }

    fun create(): TcgCard {
        return GameCore.instance.getTcgCardById(id).copy(count = this.count)
    }
}