package com.moyu.core.model.info

import com.moyu.core.AppWrapper
import com.moyu.core.R

enum class BattleInfoType(val value: String) {
    Damage(AppWrapper.getString(R.string.damage)),
    Heal(AppWrapper.getString(R.string.Heal)),
    TriggerSkill(AppWrapper.getString(R.string.TriggerSkill)),
    ExtraSkill(AppWrapper.getString(R.string.ExtraSkill)),
    Dungeon(AppWrapper.getString(R.string.Dungeon)),
    <PERSON>(AppWrapper.getString(R.string.Battle)),
    <PERSON>uff("【Buff】"),
    Property(AppWrapper.getString(R.string.Property)),
    BattleData(AppWrapper.getString(R.string.BattleData)),
    LevelUp(AppWrapper.getString(R.string.LevelUp)),
    SkillComposed(AppWrapper.getString(R.string.SkillComposed)),
}