package com.moyu.core.model.sell

import com.moyu.core.GameCore
import com.moyu.core.model.BattlePass
import com.moyu.core.model.Sign
import com.moyu.core.model.Vip
import com.moyu.core.model.ally.Ally
import com.moyu.core.model.level.ReputationLevel
import com.moyu.core.model.pool.Pool
import com.moyu.core.model.property.AdventureProps
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.isAdventure
import com.moyu.core.model.skill.isBattle
import com.moyu.core.model.skill.isHalo
import com.moyu.core.model.skill.isHeroSkill
import com.moyu.core.model.skill.quality
import com.moyu.core.model.task.GameTask
import com.moyu.core.model.tcg.TcgAward
import com.moyu.core.util.RANDOM
import com.moyu.core.util.nextIntClosure
import java.util.Random

fun ReputationLevel.toAward(typeIndex: Int): Award {
    return GameCore.instance.getPoolById(this.awardPoolId)
        .toAward()
}

fun Sign.toAward(): Award {
    return GameCore.instance.getPoolById(this.reward).toAward()
}

fun TcgAward.toAward(): Award {
    return GameCore.instance.getPoolById(this.reward).toAward()
}

fun Sell.toAward(): Award {
    val pool = GameCore.instance.getPoolById(itemId)
    return pool.toAward() + if (isAifadian() && GameCore.instance.hasBilling()) {
        // 爱发电商品，需要默认加入一个电量 todo 地心开始，爱发电买的是独立货币，已经获得了电力，任何地方不会再有电力产出。
        Award(electric = price)
    } else Award()
}

fun List<Award>.toAward(): Award {
    return this.reduceOrNull { acc, award -> acc + award } ?: Award()
}

fun Pool.toAward(forceNum: Int? = null, random: Random = RANDOM): Award {
    return toAwards(forceNum, random).toAward()
}

fun Vip.toAward(): Award {
    return if (effectType == 1) {
        GameCore.instance.getPoolById(effectId).toAward(effectNum)
    } else {
        Award()
    }
}

fun BattlePass.toAward(): Award {
    return GameCore.instance.getPoolById(reward).toAward()
}

fun Pool.toAwards(
    forceNum: Int? = null,
    random: Random = RANDOM,
    onLoseAlly: (() -> List<Ally>)? = null,
    onLoseSkill: (() -> List<Skill>)? = null
): List<Award> {
    val result = List(type.size) { index ->
        val realNum = forceNum ?: num[index]
        when (type[index]) {
            1 -> {
                if (pool[index].isEnum()) {
                    val isDrop = onLoseAlly?.invoke() != null
                    Award(
                        allies = (onLoseAlly?.invoke() ?: GameCore.instance.getAllyPool())
                            .filter { if (isDrop) true else it.star == 0 }
                            .filter { it.getRaceType() == pool[index] || pool[index] == 0 }
                            .filter { quality.first() == 0 || it.quality == quality.first() }
                            .shuffled(random).take(realNum)
                    )
                } else {
                    Award(
                        allies = listOf(
                            GameCore.instance.getAllyPool().first { it.id == pool[index] }
                                .copy(num = if (realNum == 0) 1 else realNum)
                        )
                    )
                }
            }

            2 -> {
                if (pool[index].isEnum()) {
                    Award(
                        skills = (onLoseSkill?.invoke() ?: GameCore.instance.getSkillPool())
                            .filter { it.isAdventure() }
                            .filter { quality.first() == 0 || it.quality() == quality.first() }
                            .filter { it.elementType == pool[index] || pool[index] == 0 }
                            .shuffled(random).take(realNum)
                    )
                } else {
                    Award(
                        skills = listOf(
                            GameCore.instance.getSkillById(pool[index])
                                .copy(num = if (realNum == 0) 1 else realNum)
                        )
                    )
                }
            }

            3 -> {
                if (pool[index].isEnum()) {
                    val isDrop = onLoseSkill?.invoke() != null
                    Award(
                        skills = (onLoseSkill?.invoke() ?: GameCore.instance.getSkillPool())
                            .filter { it.isBattle() }
                            .filter { if (isDrop) true else it.level == 0 }
                            .filter { quality.first() == 0 || it.quality() == quality.first() }
                            .filter { it.elementType == pool[index] || pool[index] == 0 }
                            .shuffled(random).take(realNum)
                    )
                } else {
                    Award(
                        skills = listOf(
                            GameCore.instance.getSkillById(pool[index])
                                .copy(num = if (realNum == 0) 1 else realNum)
                        )
                    )
                }
            }

            4 -> {
                if (pool[index].isEnum()) {
                    val isDrop = onLoseSkill?.invoke() != null
                    Award(
                        heroes = (onLoseSkill?.invoke() ?: GameCore.instance.getSkillPool())
                            .filter { it.isHeroSkill() }
                            .filter { if (isDrop) true else it.level == 0 }
                            .filter { quality.first() == 0 || it.quality() == quality.first() }
                            .filter { it.elementType == pool[index] || pool[index] == 0 }
                            .shuffled(random).take(realNum)
                    )
                } else {
                    Award(
                        heroes = listOf(
                            GameCore.instance.getSkillById(pool[index])
                                .copy(num = if (realNum == 0) 1 else realNum)
                        )
                    )
                }
            }

            5 -> {
                if (pool[index] == 0) {
                    Award(elements = EMPTY_ELEMENTS.toMutableList().apply {
                        set(random.nextIntClosure(0, EMPTY_ELEMENTS.size - 1), realNum)
                    })
                } else {
                    Award(elements = EMPTY_ELEMENTS.toMutableList().apply {
                        set(pool[index] - 1, realNum)
                    })
                }
            }

            6 -> {
                if (pool[index] == 0) {
                    Award(reputations = EMPTY_REPUTATION.toMutableList().apply {
                        set(random.nextIntClosure(0, EMPTY_REPUTATION.size - 1), realNum)
                    })
                } else {
                    Award(reputations = EMPTY_REPUTATION.toMutableList().apply {
                        set(pool[index] - 1, realNum)
                    })
                }
            }

            7 -> {
                if (pool[index] == 0) {
                    Award(reputations = EMPTY_REPUTATION.toMutableList().apply {
                        set(
                            random.nextIntClosure(0, EMPTY_REPUTATION.size - 1),
                            realNum.reputationLevelToValue()
                        )
                    })
                } else {
                    Award(reputations = EMPTY_REPUTATION.toMutableList().apply {
                        set(pool[index] - 1, realNum.reputationLevelToValue())
                    })
                }
            }

            8 -> {
                if (pool[index] == 0) {
                    Award(badges = EMPTY_BADGE.toMutableList().apply {
                        set(random.nextIntClosure(0, EMPTY_BADGE.size - 1).let {
                           if (it == IMPOSSIBLE_BADGE) it - 1 else it
                        }, realNum)
                    })
                } else {
                    Award(badges = EMPTY_BADGE.toMutableList().apply {
                        set(pool[index] - 1, realNum)
                    })
                }
            }

            9 -> {
                Award(property = AdventureProps.getRoleProperty(pool[index], realNum))
            }

            10 -> {
                if (pool[index] == 0) {
                    Award(extraElements = EMPTY_ELEMENTS.toMutableList().apply {
                        set(random.nextIntClosure(0, EMPTY_ELEMENTS.size - 1), realNum)
                    })
                } else {
                    Award(extraElements = EMPTY_ELEMENTS.toMutableList().apply {
                        set(pool[index] - 1, realNum)
                    })
                }
            }

            11 -> {
                if (pool[index].isEnum()) {
                    Award(
                        skills = GameCore.instance.getSkillPool()
                            .filter { it.isHalo() }
                            .filter { quality.first() == 0 || it.quality() == quality.first() }
                            .filter { it.elementType == pool[index] || pool[index] == 0 }
                            .shuffled(random).take(realNum)
                    )
                } else {
                    Award(
                        skills = listOf(
                            GameCore.instance.getSkillById(pool[index])
                                .copy(num = if (realNum == 0) 1 else realNum)
                        )
                    )
                }
            }

            12 -> {
                Award(heal = realNum)
            }

            21 -> {
                if (pool[index].isEnum()) {
                    Award(
                        outAllies = GameCore.instance.getAllyPool()
                            .filter { it.star == 0 }
                            .filter { it.getRaceType() == pool[index] || pool[index] == 0 }
                            .filter { it.quality == quality.first() || quality.first() == 0 }
                            .filter { it.dropLimit != 1 }
                            .shuffled(random).take(enumNum.first()).map {
                                it.copy(num = if (realNum == 0) 1 else realNum)
                            }
                    )
                } else {
                    Award(
                        outAllies = listOf(
                            GameCore.instance.getAllyPool().first { it.id == pool[index] }
                                .copy(num = if (realNum == 0) 1 else realNum)
                        )
                    )
                }
            }

            22 -> {
                if (pool[index].isEnum()) {
                    Award(
                        outSkills = GameCore.instance.getSkillPool()
                            .filter { it.isBattle() }
                            .filter { it.level == 0 }
                            .filter { quality.first() == 0 || it.quality() == quality.first() }
                            .filter { it.elementType == pool[index] || pool[index] == 0 }
                            .shuffled(random).take(enumNum.first()).map {
                                it.copy(num = if (realNum == 0) 1 else realNum)
                            }
                    )
                } else {
                    Award(
                        outSkills = listOf(
                            GameCore.instance.getSkillById(pool[index])
                                .copy(num = if (realNum == 0) 1 else realNum)
                        )
                    )
                }
            }

            23 -> {
                if (pool[index].isEnum()) {
                    Award(
                        outHeroes = GameCore.instance.getSkillPool()
                            .filter { it.isHeroSkill() }
                            .filter { it.level == 0 }
                            .filter { quality.first() == 0 || it.quality() == quality.first() }
                            .filter { it.elementType == pool[index] || pool[index] == 0 }
                            .shuffled(random).take(enumNum.first()).map {
                                it.copy(num = if (realNum == 0) 1 else realNum)
                            }
                    )
                } else {
                    Award(
                        outHeroes = listOf(
                            GameCore.instance.getSkillById(pool[index])
                                .copy(num = if (realNum == 0) 1 else realNum)
                        )
                    )
                }
            }

            24 -> {
                Award(diamond = realNum)
            }

            25 -> {
                Award(key = realNum)
            }

            26 -> {
                if (pool[index].isEnum()) {
                    Award(
                        tcgs = GameCore.instance.getTcgCardPool().filter { it.type == pool[index] || pool[index] == 0 }
                            .filter { quality.first() == 0 || it.quality == quality.first() }
                            .shuffled(random).take(realNum), showQuestion = true
                    )
                } else {
                    Award(tcgs = listOf(GameCore.instance.getTcgCardById(pool[index])))
                }
            }

            27 -> {
                Award(warPass = realNum)
            }

            28 -> {
                Award(electric = realNum)
            }

            29 -> {
                Award(unlockList = listOf(pool[index]))
            }

            30 -> {
                Award(warPass2 = realNum)
            }

            31 -> {
                Award(pvpDiamond = realNum)
            }

            32 -> {
                Award(lotteryMoney = realNum)
            }

            33 -> {
                Award(reputationMoney = realNum)
            }

            34 -> {
                if (pool[index] == 1) {
                    Award(couponAlly = realNum)
                } else if (pool[index] == 2) {
                    Award(couponSkill = realNum)
                } else {
                    Award(couponHero = realNum)
                }
            }
            else -> {
                error("不存在的枚举${type[index]}")
            }
        }
    }
    return result.filter { !it.isEmpty() }.let {
        // 如果总数太多，要随机，否则固定
        if (it.size <= totalNum) it else it.shuffled(random).take(totalNum)
    }
}

// todo pool表的第二个字段有双重含义，如果是100以内，是枚举，否则是固定id
private fun Int.isEnum(): Boolean {
    return this < 100
}

private fun Int.reputationLevelToValue(): Int {
    return GameCore.instance.getReputationLevelPool().first {
        it.level >= this
    }.expTotal
}

fun GameTask.toAward(): Award {
    return GameCore.instance.getPoolById(reward).toAward()
}