package com.moyu.core.model.sell

import com.moyu.core.GameCore
import com.moyu.core.model.ConfigData
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient

@Serializable
data class Sell(
    @SerialName("i")
  override val id: Int,
    @Transient
    val name: String = "",
    @Transient
    val type: Int = 0,
    @Transient
    val regularType: Int = 0,
    @Transient
    val desc2: String = "",
    @Transient
    val priceType: Int = 0,
    @Transient
    val price: Int = 0,
    @Transient
    val priceDollar: Double = 0.0,
    @SerialName("z")
    val storage: Int = 0,
    @Transient
    val storageType: Int = 0,
    @Transient
    val itemId: Int = 0,
    @SerialName("p")
    val num: Int = 0,
    @Transient
    val pic: String = "",
    @Transient
    val condition: List<Int> = emptyList(),
    @Transient
    val order: Int = 0,
    @Transient
    val desc: String = "",
    @Transient
    val unlock: Int = 0,
    @Transient
    val showCondition: Int = 0,
    @Transient
    val googleItemId: String = "0",
    @Transient
    val title: String = "",
    @Transient
    val itemId2: Int = 0,
    @SerialName("k")
    val opened: Boolean = false
): ConfigData {
    fun isAifadian(): Boolean {
        return priceType == 5
    }

    fun isFreeGift(): Boolean {
        return type == 1
    }

    fun isAd(): Boolean {
        return priceType == 3
    }

    fun isTriggerGift(): Boolean {
        return GameCore.instance.getGiftPool().any { it.id == this.id }
    }


    fun isSevenDay(): Boolean {
        return type == 21
    }

    fun isHoliday(): Boolean {
        return type == 22
    }

    fun isLotteryGift(): Boolean {
        return type == 30
    }

    fun isPvpMoney(): Boolean {
        return priceType == 4
    }

    fun isKeyMoney(): Boolean {
        return priceType == 2
    }

    fun isDiamondMoney(): Boolean {
        return priceType == 1
    }

    fun isGoogle(): Boolean {
        return type == 101
    }

    fun isTaptap(): Boolean {
        return type == 102
    }

    fun isExpGift(): Boolean {
        return type == 23
    }

    fun isMonthCard(): Boolean {
        return type == 31
    }

    fun isTower(): Boolean {
        return type == 24
    }
}