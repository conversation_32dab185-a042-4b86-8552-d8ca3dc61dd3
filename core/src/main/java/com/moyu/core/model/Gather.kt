package com.moyu.core.model

import com.moyu.core.util.RANDOM
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient

@Serializable
data class Gather(
    @SerialName("i")
    override val id: Int = 0,
    @Transient
    val heroLevel: List<Int> = emptyList(),
    @Transient
    val level: Int = 0,
    @Transient
    val conditionNum: Int = 0,
    @Transient
    val heroRate: Int = 0,
    @Transient
    val conditionRate: Int = 0,
    @Transient
    val attribute1: Int = 0,
    @Transient
    val attribute2: Int = 0,
    @Transient
    val attribute3: Int = 0,
    @Transient
    val attribute4: Double = 0.0,
    @Transient
    val attribute5: Double = 0.0,
    @Transient
    val attribute6: Double = 0.0,
    @Transient
    val attribute7: Double = 0.0,
    @Transient
    val attribute8: Int = 0,
    @Transient
    val reward: Int = 0,
    @Transient
    val time: Int = 0,
    @SerialName("n")
    val randomAttrs: List<Int> = emptyList(),
) : ConfigData {
    fun create(): Gather {
        return copy(
            randomAttrs = (1..17).toList().shuffled(
                RANDOM
            ).take(conditionNum)
        )
    }

    fun getAttrValue(attrType: Int): Double {
        return listOf(attribute1.toDouble(), attribute2.toDouble(), attribute3.toDouble(), attribute4, attribute5, attribute6.toDouble(), attribute7.toDouble(), attribute8.toDouble(), )[attrType - 1]
    }
}