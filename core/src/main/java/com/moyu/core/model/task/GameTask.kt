package com.moyu.core.model.task

import com.moyu.core.model.ConfigData
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient

@Serializable
data class GameTask(
    @SerialName("a")
    override val id: Int = 0,
    @Transient
    val taskType: Int = 1,
    @Transient
    val name: String = "",
    @Transient
    val desc: String = "",
    @Transient
    val type: Int = 0,
    @Transient
    val subType: List<Int> = emptyList(),
    @Transient
    val num: Int = 0,
    @Transient
    val reward: Int = 0,
    @Transient
    val order: Int = 0,
    @Transient
    val talent: List<Int> = emptyList(),
    @SerialName("i")
    val done: Boolean = false,
    @SerialName("z")
    val opened: Boolean = false,
    @SerialName("xx")
    val needRemoveCount: Int = 0,
) : ConfigData {
    fun isNewTask(): Boolean {
        return taskType == 4
    }

    fun isDailyTask(): Boolean {
        return taskType == 1
    }

    fun isOneTimeTask(): Boolean {
        return taskType == 2
    }

    fun isHolidayTask(): Boolean {
        return taskType == 12
    }

    fun isWarPassTask(): Boolean {
        return taskType == 3
    }

    fun isPvpTask(): Boolean {
        return taskType == 6
    }

    fun isWarPass2Task(): Boolean {
        return taskType == 5
    }

    fun isCollectTask(): Boolean {
        return taskType == 8
    }

    fun isCostTask(): Boolean {
        return taskType == 9
    }

    fun isChargeTask(): Boolean {
        return taskType == 10
    }

    fun isDrawTask(): Boolean {
        return taskType == 11
    }

    /**
     * 是否是单次计数
     * 比如持续年龄就是单次计数，但是超过100岁次数就不是，而是累计次数
     */
    fun isInstant(): Boolean {
        return (type == 2 && subType.first() == 1) // 持续xx年
                || type == 6 // 国家属性
                || type == 13 // 声望等级
                || type == 17 // 天赋等级
    }
}