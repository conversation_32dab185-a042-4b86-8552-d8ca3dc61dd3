package com.moyu.core.model.environment

import com.moyu.core.GameCore
import com.roguelike.gamecore.model.environment.EnvironmentHolder

class DefaultEnvironment : EnvironmentHolder {
    private var environment = Normal
    override fun clearEnvironment() {
        changeEnvironment(Normal)
    }

    override fun changeEnvironment(environment: Environment) {
//        GameApplication.applicationScope.launch {
//            if (environment == Normal) {
//                MusicManager.playDungeon()
//            } else {
//                MusicManager.playBattle(environment)
//            }
//        }
        GameCore.instance.onEnvironmentUpdate(environment)
        this.environment = environment
    }

    override fun getEnvironment(): Environment {
        return environment
    }
}