package com.moyu.core.model.property

import com.moyu.core.AppWrapper
import com.moyu.core.GameCore
import com.moyu.core.R
import com.moyu.core.model.level.ReputationLevel
import kotlinx.serialization.Serializable

val EMPTY_ADV_PROPS = AdventureProps()

@Serializable
data class AdventureProps(
    val age: Int = 0,
    // 冒险属性
    val science: Int = 0,
    val politics: Int = 0,
    val military: Int = 0,
    val population: Int = 0,

    val gainExp: Int = 0,
    val gainMoney: Int = 0,
    val gainReputation: Int = 0,
    val shopPriceMercenary: Int = 0,
    val winEventPlayIds: List<Int> = emptyList(),
    val failEventPlayIds: List<Int> = emptyList()
) {

    companion object {

        fun getRoleProperty(type: Int, diff: Int): AdventureProps {
            return when (type) {
                1 -> AdventureProps(science = diff)
                2 -> AdventureProps(politics = diff)
                3 -> AdventureProps(military = diff)
                4 -> AdventureProps(population = diff)
                else -> AdventureProps()
            }
        }

        fun rolePropertyName(i: Int): String {
            return when (i) {
                1 -> AppWrapper.getString(R.string.prop1)
                2 -> AppWrapper.getString(R.string.prop2)
                3 -> AppWrapper.getString(R.string.prop3)
                4 -> AppWrapper.getString(R.string.prop4)
                5 -> AppWrapper.getString(R.string.prop5)
                6 -> AppWrapper.getString(R.string.prop6)
                7 -> AppWrapper.getString(R.string.populations)
                else -> error("无效属性")
            }
        }

        fun getReputationLevel(targetReputation: Int): Int {
            return GameCore.instance.getReputationLevelPool().reversed()
                .firstOrNull { it.expTotal <= targetReputation }?.level
                ?: GameCore.instance.getReputationLevelPool().last().level
        }

        fun getReputationLevelData(targetReputation: Int): ReputationLevel {
            return GameCore.instance.getReputationLevelPool().reversed()
                .firstOrNull { it.expTotal <= targetReputation }
                ?: GameCore.instance.getReputationLevelPool().last()
        }

        fun createNew(): AdventureProps {
            val initProps = GameCore.instance.getInitAdvProps()
            return AdventureProps(
                age = 0,
                science = initProps[0],
                politics = initProps[1],
                military = initProps[2],
                population = initProps[3],
            )
        }
    }

    operator fun plus(diffProperty: AdventureProps): AdventureProps {
        return copy(
            science = science + diffProperty.science,
            politics = politics + diffProperty.politics,
            military = military + diffProperty.military,
            age = age + diffProperty.age,
            population = population + diffProperty.population,


            gainExp = gainExp + diffProperty.gainExp,
            gainMoney = gainMoney + diffProperty.gainMoney,
            gainReputation = gainReputation + diffProperty.gainReputation,
            shopPriceMercenary = shopPriceMercenary + diffProperty.shopPriceMercenary,
            winEventPlayIds = winEventPlayIds + diffProperty.winEventPlayIds,
            failEventPlayIds = failEventPlayIds + diffProperty.failEventPlayIds,
        )
    }

    operator fun minus(diffProperty: AdventureProps): AdventureProps {
        return copy(
            science = science - diffProperty.science,
            politics = politics - diffProperty.politics,
            military = military - diffProperty.military,
            age = age - diffProperty.age,
            population = population - diffProperty.population,

            gainExp = gainExp - diffProperty.gainExp,
            gainMoney = gainMoney - diffProperty.gainMoney,
            gainReputation = gainReputation - diffProperty.gainReputation,
            shopPriceMercenary = shopPriceMercenary - diffProperty.shopPriceMercenary,
            winEventPlayIds = winEventPlayIds - diffProperty.winEventPlayIds.toSet(),
            failEventPlayIds = failEventPlayIds - diffProperty.failEventPlayIds.toSet(),
        )
    }


    operator fun times(times: Int): AdventureProps {
        return this.copy(
            science = science * times,
            politics = politics * times,
            military = military * times,
            population = population * times,
        )
    }

    fun isPunish(): Boolean {
        return science < 0 || politics < 0 || military < 0 || age < 0 || population < 0
    }

    fun isNotEmpty(): Boolean {
        return this != EMPTY_ADV_PROPS
    }

    fun getPropertyByTarget(propertyEnum: Int): Int {
        return when (propertyEnum) {
            1 -> {
                science
            }

            2 -> {
                politics
            }

            3 -> {
                military
            }

            4 -> {
                population
            }

            else -> {
                error("无效effectReference")
            }
        }
    }

    fun perBiggerI(property: AdventureProps): Boolean {
        return this.science >= property.science &&
                this.politics >= property.politics &&
                this.military >= property.military &&
                (if (property.population > 0) this.population <= property.population else true) // todo 气温是反向要求
    }

    fun ensureNonNegative(): AdventureProps {
        return copy(
            science = science.coerceAtLeast(0),
            politics = politics.coerceAtLeast(0),
            military = military.coerceAtLeast(0),
            population = population,
        )
    }

    fun ensureStartPopulation(): AdventureProps {
        return copy(
            population = population.coerceAtLeast(0),
        )
    }
}
