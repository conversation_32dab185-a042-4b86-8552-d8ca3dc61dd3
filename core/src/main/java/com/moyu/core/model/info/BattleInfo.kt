package com.moyu.core.model.info

import com.moyu.core.model.buff.Buff
import com.moyu.core.model.damage.DamageResult
import com.moyu.core.model.property.Property
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.Skill

data class BattleInfo(
    val content: String,
    val skill: Skill? = null,
    val buff: Buff? = null,
    val doer: Role? = null,
    val target: Role? = null,
    val property: Property? = null,
    val turn: Int = 0,
    val type: BattleInfoType = BattleInfoType.Dungeon,
    val battleInfoLevel: BattleInfoLevel = BattleInfoLevel.NORMAL,
    val damageData: DamageResult? = null,
)