package com.moyu.core.util

import java.util.Random
import kotlin.math.roundToInt

val RANDOM = Random(System.currentTimeMillis())
fun Int.chance(): <PERSON><PERSON>an {
    return RANDOM.nextIntClosure(0, 99) < this
}

/**
 *  数值范围在 0-100 之间 概率判断
 */
fun Double.chance(): <PERSON><PERSON><PERSON> {
    return RANDOM.nextInt(10000) < this * 100
}

/**
 *  数值范围在 0-1.0 之间 进行概率判断
 */
fun Double.chance100(): Boolean {
    return RANDOM.nextInt(10000) < this * 10000
}

fun <T> Iterable<T>.gameShuffled(): List<T> = toMutableList().apply { shuffle(RANDOM) }

/**
 * 把double转成小数点后一位的字符串，比如 0.51转0.5
 * 如果数值小于1，保留两位小数点，否则，1位
 */
fun Double.realValueToDotWithOneDigits(): String {
    return if (this >=1) ((this * 10).roundToInt().toFloat() / 10).toString() else ((this * 100).roundToInt().toFloat() / 100).toString()
}

fun Double.realValueToDotWithNoDigits(): String {
    return (this.roundToInt()).toString()
}

/**
 * 把double转成小数点后一位的字符串，比如 0.51转50
 * 如果数值小于1，保留两位小数点，否则，1位
 */
fun Double.percentValueToDot(): String {
    return ((this * 10000).roundToInt() / 100f).toString()
}

/**
 * 把double转成小数点后一位的字符串，比如0.5转50.0%
 */
fun Double.percentValueToDotWithOneDigits(): String {
    return ((this * 10000).roundToInt() / 100f).toString() + "%"
}

/**
 * 把double转成小数点后一位的字符串，比如0.5转50%
 */
fun Double.percentValueToDotWithNoDigits(): String {
    return (this * 100).roundToInt().toString() + "%"
}

fun Int.toThreeDigits(): String {
    return if (this >= 100) this.toString() else if (this>=10) "0${this}" else "00${this}"
}

/**
 * 包含end，闭区间[start, end]
 */
fun Random.nextIntClosure(start: Int, end: Int): Int {
    return this.nextInt(end - start + 1) + start
}

/**
 * 返回一个随机数，范围是闭区间 [start, end]
 */
fun Random.nextDoubleClosure(start: Double, end: Double): Double {
    return this.nextDouble() * (end - start) + start
}