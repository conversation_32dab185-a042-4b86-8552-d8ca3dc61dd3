<?xml version='1.0' encoding='utf-8'?>																			
<resources>																			
  <string name="ssss">Get Legion</string>																			
  <string name="cast_skill">cast tactics</string>																			
  <string name="gain_permanent_debuff">Gained a permanent debuff</string>																			
  <string name="gain_permanent_buff">Gained a permanent benefit</string>																			
  <string name="execute_failed">Decimate was cast, but the target is immune to death, so Decimate is ineffective</string>																			
  <string name="execute_not_work">Decimate was cast, but the target\'s remaining troops are greater than your troop maximum, so Decimate is ineffective</string>																			
  <string name="execute_done">You cast Decimate, and you killed your opponent.</string>																			
  <string name="gain_avoid_death">You gained immunity from death</string>																			
  <string name="avoid_death_failed">You gained immunity from death, but your opponent ignored immunity from death, so immunity from death is ineffective</string>																			
  <string name="direct_win">be cast. You win the battle.</string>																			
  <string name="gain_enhancement">Gained tactical enhancement</string>																			
  <string name="enhancement_skip">The target tactic was not learned.</string>																			
  <string name="damage_record">Damage Record:</string>																			
  <string name="attacker_with_comma">Attacker:</string>																			
  <string name="defender_with_comma">Defender:</string>																			
  <string name="initial_damage_with_comma">Initial damage:</string>																			
  <string name="fatal_with_comma">Critical Hit:</string>																			
  <string name="fatal_damage_with_comma">Critical damage:</string>																			
  <string name="race_damage_with_comma">Troop type damage increases:</string>																			
  <string name="normal_attack_with_comma">Normal attack damage increases:</string>																			
  <string name="skill_damage_with_comma">Other damage increases:</string>																			
  <string name="attacker_damage_inc">Damage increase/decrease (Attacker):</string>																			
  <string name="attacker_damage_inc_all">All damage increases and decreases (Attacker):</string>																			
  <string name="pierce_attacker">Penetration (Attacker):</string>																			
  <string name="damage_value">damage:</string>																			
  <string name="defender_init_defense">Initial defense of defender</string>																			
  <string name="defender_real_defense">Actual defense of the defender</string>																			
  <string name="defender_reduce_damage">Defensive Damage Reduction Effect (Defender):</string>																			
  <string name="defender_reduce_value">Damage reduction (Defender):</string>																			
  <string name="defender_race_reduce">Defense troops type damage reduction:</string>																			
  <string name="defender_all_reduce">All damage reduction for the defender:</string>																			
  <string name="defender_immune">Defender Immunity:</string>																			
  <string name="defender_holy_shield">Defense Mirror:</string>																			
  <string name="defender_dodge">Block:</string>																			
  <string name="single_damage">Single damage limit:</string>																			
  <string name="final_damage">Final Damage:</string>																			
  <string name="single_damage_protect">Single damage protection:</string>																			
  <string name="yes">Yes</string>																			
  <string name="no">No</string>																			
  <string name="event">event</string>																			
  <string name="ally">Legion</string>																			
  <string name="skill">Tactics</string>																			
  <string name="defeat_enemy">You have defeated the enemy</string>																			
  <string name="skill_typ1">Melee</string>																			
  <string name="skill_type2">Ranged</string>																			
  <string name="skill_type3">Siege</string>																			
  <string name="skill_type4">Sacred</string>																			
  <string name="skill_type5">Chaos</string>																			
  <string name="ones_turn">Round:</string>																			
  <string name="over_turn">You failed to defeat the opponent within the specified rounds, and the battle failed.</string>																			
  <string name="release">cast</string>																			
  <string name="gain">Obtained</string>																			
  <string name="you_release">You cast</string>																			
  <string name="let">make</string>																			
  <string name="lost">loss</string>																			
  <string name="hp">HP</string>																			
  <string name="control_immune">Immune to control and cannot be affected by</string>																			
  <string name="trigger_multi_cast">Triggered a morale boost</string>																			
  <string name="forbid_heal_tip">Affected by [Isolation], unable to replenish troops.</string>																			
  <string name="frenzy_tips">Affected by [Rampage], randomly select a target.</string>																			
  <string name="disarm_tips">Affected by [Disarm], unable to launch normal attacks.</string>																			
  <string name="silent_tips">Affected by [Chaos], unable to cast active tactics.</string>																			
  <string name="palsy_tips">Affected by [Shaken], unable to cast trigger tactics.</string>																			
  <string name="ban_skill_tip1">Affected by [Seal</string>																			
  <string name="ban_skill_tip2">], the tactic cannot be cast.</string>																			
  <string name="heal">Troop Replenishment</string>																			
  <string name="no_target">But there is no target</string>																			
  <string name="peer">Opponent</string>																			
  <string name="you">you</string>																			
  <string name="layer">layer</string>																			
  <string name="unstack">Unstackable</string>																			
  <string name="infinite">unlimited</string>																			
  <string name="rage">Rampage</string>																			
  <string name="unbreakable">Invincibility</string>																			
  <string name="holy_shield">mirror</string>																			
  <string name="dodge">BLO</string>																			
  <string name="fatal">Critical</string>																			
  <string name="physic">attack</string>																			
  <string name="defense">DEF</string>																			
  <string name="magic" />																			
  <string name="elite">Elite</string>																			
  <string name="part1">science</string>																			
  <string name="part2">politics</string>																			
  <string name="part3">military</string>																			
  <string name="part4">religion</string>																			
  <string name="part5">Business</string>																			
  <string name="part6">Art</string>																			
  <string name="prop1">science</string>																			
  <string name="prop2">politics</string>																			
  <string name="prop3">military</string>																			
  <string name="prop4">religion</string>																			
  <string name="prop5">Business</string>																			
  <string name="prop6">Art</string>																			
  <string name="race1">infantry</string>																			
  <string name="race2">cavalry</string>																			
  <string name="race3">Archers</string>																			
  <string name="race4">instrument</string>																			
  <string name="race5">Priesthood</string>																			
  <string name="level_up_tips">You\'ve leveled up!</string>																			
  <string name="enchant1">Increases critical hit rate for specified tactics</string>																			
  <string name="enchant2">Selected tactics have a chance to double cast:</string>																			
  <string name="enchant3">Specified tactical immunity to blocking</string>																			
  <string name="enchant4">Specify tactics to ignore shields</string>																			
  <string name="enchant5">Specified tactics ignore invincibility/immunity</string>																			
  <string name="enchant6">The generated buff/debuff cannot be destroyed</string>																			
  <string name="enchant7">Increases the damage caused by the specified tactics</string>																			
  <string name="enchant8">The damage caused by the specified tactics is reduced</string>																			
  <string name="enchant9">Specify tactics to ignore enemy physical defense</string>																			
  <string name="enchant10">Specify tactics to ignore enemy magic defense</string>																			
  <string name="enchant11">Specified tactic CD increased</string>																			
  <string name="enchant12">Designated tactic CD reduction</string>																			
  <string name="enchant13">Increased buff/debuff duration</string>																			
  <string name="enchant14">The number of rounds buff/debuff lasts is reduced</string>																			
  <string name="enchant15">Increases the chance of releasing a specific tactic</string>																			
  <string name="enchant16">The chance of releasing a specific tactic is reduced</string>																			
  <string name="enchant17">The number of times a specified tactic can be released per round is increased</string>																			
  <string name="enchant18">The limit on the number of times a specified tactic can be released per round is reduced</string>																			
  <string name="enchant19">The number of times a specific tactic can be released in a battle is increased</string>																			
  <string name="enchant20">The number of times a specific tactic can be released in a battle is reduced</string>																			
  <string name="enchant21">The number of multicasts of a specified tactic is increased:</string>																			
  <string name="dispelled">broken</string>																			
  <string name="cant_dispel">Immune to debuffs and cannot be destroyed</string>																			
  <string name="cant_dispel2">Immune to damage, cannot be damaged</string>																			
  <string name="game_over">game over</string>																			
  <string name="game_continues">Game on</string>																			
  <string name="game_round">Round</string>																			
  <string name="marked_for_death">【Marked for Death】</string>																			
  <string name="defeated_the_enemy">You defeated the enemy</string>																			
  <string name="remove">Remove</string>																			
  <string name="buff">Effect:</string>																			
  <string name="damage_record1">Injury Record:</string>																			
  <string name="attacker">\nAttacker "</string>																			
  <string name="defensive_side">\nDefender</string>																			
  <string name="initial_damage">\nInitial damage</string>																			
  <string name="critical_strike">\nCritical Hit</string>																			
  <string name="critical_strike_damage">\nCritical damage</string>																			
  <string name="racial_injury">\nArmy type injury</string>																			
  <string name="level">class</string>																			
  <string name="level1">class</string>																			
  <string name="death">die</string>																			
  <string name="continuous_damage">Continuous Damage</string>																			
  <string name="continued_treatment">Continuing rescue</string>																			
  <string name="drink">draw</string>																			
  <string name="i_achieved_positive_results">I gain</string>																			
  <string name="i_achieved_negative_effects">I get debuffs</string>																			
  <string name="dispel_the_opponent_buff">Destroy the opponent\'s gain</string>																			
  <string name="dispel_your_own_buff">Destroy your own gain</string>																			
  <string name="damage_self">hurt oneself</string>																			
  <string name="cure_yourself">Replenish your own troops</string>																			
  <string name="free_sb_from_death">Release from death</string>																			
  <string name="release_a_skill">Release tactics</string>																			
  <string name="got_damage">Hurt</string>																			
  <string name="got_hurt">cause some damages</string>																			
  <string name="successful_block">Successful Block</string>																			
  <string name="start_of_the_round">Round starts</string>																			
  <string name="end_of_the_round">End of Round</string>																			
  <string name="call_upon_a_servant">Calling in reinforcements</string>																			
  <string name="resisting" />																			
  <string name="defensive">defense</string>																			
  <string name="melee">Melee</string>																			
  <string name="remotely">remote</string>																			
  <string name="besiege_a_city">Siege</string>																			
  <string name="hallow">sacred</string>																			
  <string name="chao">confusion</string>																			
  <string name="release1">freed</string>																			
  <string name="no_target1">, but no goal</string>																			
  <string name="normal">usually</string>																			
  <string name="battle_zone">battlefield</string>																			
  <string name="HolyPalace">temple</string>																			
  <string name="Volcano">volcano</string>																			
  <string name="Ocean">ocean</string>																			
  <string name="Cemetery">Cemetery</string>																			
  <string name="Abyss">abyss</string>																			
  <string name="damage">【harm】</string>																			
  <string name="Heal">【Replenish】</string>																			
  <string name="TriggerSkill">【Tactical Release】</string>																			
  <string name="ExtraSkill">【Event Release】</string>																			
  <string name="Dungeon">[Dungeon]</string>																			
  <string name="Battle">【Battlefield Information】</string>																			
  <string name="Property">[Attribute changes]</string>																			
  <string name="BattleData">【Battle Information】</string>																			
  <string name="LevelUp">【upgrade】</string>																			
  <string name="SkillComposed">【Tactical Synthesis】</string>																			
  <string name="scientists">science</string>																			
  <string name="politically">politics</string>																			
  <string name="militarily">military</string>																			
  <string name="religions">religion</string>																			
  <string name="commercial">Business</string>																			
  <string name="artists">Art</string>																			
  <string name="populations">population</string>																			
  <string name="no_dispelling_of_target_buffs">No target damage effect</string>																			
  <string name="minion_already_exists">Reinforcements already exist and cannot be summoned anymore</string>																			
  <string name="velocity">speed</string>																			
  <string name="summoned">Summoned</string>																			
  <string name="remain">Left</string>																			
  <string name="year">Yr</string>																			
  <string name="designated_skill_strike_rate_increase">Increases critical hit rate for specified tactics</string>																			
  <string name="increase_in_the_number_of_times_a_given_skill_can_be_cast">Increase the number of times a specified tactic is released</string>																			
  <string name="immunity_to_blocking_for_specified_skills">Specified tactical immunity to blocking</string>																			
  <string name="designated_skills_ignore_stance">Specified tactics ignore shield</string>																			
  <string name="designated_skills_ignore_invincibility">Specified tactics ignore invincibility/immunity</string>																			
  <string name="designated_skills_ignore_taunts">Specify tactics to ignore taunts</string>																			
  <string name="cannot_be_dispersed">The buffs/debuffs generated by the tactic cannot be destroyed</string>																			
  <string name="increased_damage_dealt_by_designated_skills">Increased damage of designated tactics</string>																			
  <string name="designated_skills_deal_less_damage">Damage reduction for specified tactics</string>																			
  <string name="designated_skill_cd_increase">Specified tactics cooldown increased</string>																			
  <string name="designated_skill_cd_decrease">Specified tactical cooldown reduction</string>																			
  <string name="increased_number_of_rounds">Increased buff/debuff duration</string>																			
  <string name="decreased_number_of_rounds">The number of rounds that buffs/debuffs last is reduced</string>																			
  <string name="increased_probability_of_releasing_designated_skills">Increases the chance of releasing a specific tactic</string>																			
  <string name="reduced_probability_of_releasing_designated_skills">The chance of releasing a specific tactic is reduced</string>																			
  <string name="increase_in_the_number_of_times_a_given_skill_can_be_released_per_round">The number of times a specified tactic can be released per round is increased</string>																			
  <string name="reduced_limit_on_the_number_of_times_a_given_skill_can_be_released_per_round">The limit on the number of times a specified tactic can be released per round is reduced</string>																			
  <string name="increase_in_the_number_of_restricted_releases">The number of times a specific tactic can be released in a battle is increased</string>																			
  <string name="reduced_limit_on_number_of_releases">The number of times a specific tactic can be released in a battle is reduced</string>																			
  <string name="increased_blood_absorption_rate_of_skills">Improved tactical absorption</string>																			
  <string name="designated_skills_ignore_enemies">Specify tactics to ignore enemies</string>																			
  <string name="skill_damage_type_changes_to">The specified tactical damage type becomes</string>																			
  <string name="designated_skills_vs_race">Specify tactics against arms</string>																			
  <string name="damage_increase">Damage increased</string>																			
  <string name="damage_decrease">Damage reduction</string>																			
  <string name="gain_split_effect">The specified tactics gain splash effect</string>																			
  <string name="damage1">Melee</string>																			
  <string name="damage2">remote</string>																			
  <string name="damage3">Siege</string>																			
  <string name="damage4">sacred</string>																			
  <string name="damage5">confusion</string>																			
  <string name="defense1">defense</string>																			
  <string name="defense2">Resistance</string>																			
  <string name="not_effect">Not active</string>																			
</resources>																			