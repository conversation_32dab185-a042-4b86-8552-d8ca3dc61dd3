plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'kotlinx-serialization'
    id 'kotlin-kapt'
}

android {
    compileSdk 35

    defaultConfig {
        minSdk 23
        targetSdk 35
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
    }
    namespace 'com.moyu.core'
}

dependencies {
    // coroutines
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3")

    implementation("org.jetbrains.kotlinx:kotlinx-serialization-json:1.6.2")
    implementation("app.softwork:kotlinx-uuid-core:0.0.12")
}