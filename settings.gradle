dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        jcenter() // Warning: this repository is going to shut down soon
        flatDir {
            dirs 'src/main/libs'
        }
        mavenCentral()
        maven { url 'https://repo1.maven.org/maven2/' }
        maven {
            url = uri("https://dl-maven-android.mintegral.com/repository/mbridge_android_sdk_oversea")
        }
        maven {
            url = uri("https://artifact.bytedance.com/repository/pangle/")
        }
        maven {
            url 'https://artifact.bytedance.com/repository/pangle'
        }
    }
}
rootProject.name = "Composed"
include ':job'
include ':core'
include ':game'
include ':config'
