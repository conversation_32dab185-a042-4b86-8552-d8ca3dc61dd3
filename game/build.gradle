import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

plugins {
    id 'com.android.application'
    id 'kotlin-android'
    id 'kotlinx-serialization'
    id 'kotlin-kapt'
}

if (getGradle().getStartParameter().getTaskRequests()
        .toString().contains("Oversea")) {
    apply plugin: 'com.google.gms.google-services'
    apply plugin: 'com.google.firebase.crashlytics'
}
//apply from: '../gradle/tools/mcimage.gradle'

android {
    compileSdk 35

    defaultConfig {
        applicationId "com.moyu.chuanqirensheng"
        minSdk 23
        targetSdk 35
        versionCode 20902
        versionName "2.9.2"

        resValue("string", "qq", "924500484")
        resValue("string", "aifadian", "https://afdian.com/a/EmpireLife")

        vectorDrawables {
            useSupportLibrary true
        }

        // Similar to other properties in the defaultConfig block,
        // you can configure the ndk block for each product flavor
        // in your build configuration.
        ndk {
            // Specifies the ABI configurations of your native
            // libraries <PERSON>rad<PERSON> should build and package with your app.
            abiFilters 'x86', 'x86_64', 'armeabi-v7a', 'arm64-v8a'
        }
    }

    signingConfigs {
        release {
            storeFile file('../wujindeyuansushi.keystore')
            storePassword 'tujiulong123'
            keyAlias 'key0'
            keyPassword 'tujiulong123'
        }
    }
    productFlavors {
        hykb {
            dimension "platform"
            manifestPlaceholders = [PACKAGE_NAME: "com.moyu.diguoshengyuan_hykb", "APPLOG_SCHEME": "rangersapplog.5bb4eb6a025284ab".toLowerCase()]
            applicationId "com.moyu.diguoshengyuan_hykb"
            resValue("string", "main_page_url", "https://www.3839.com/")
            resValue("string", "platform_channel", "hykb")
            resValue("string", "luntan_link", "https://bbs.3839.com/thread-6149905.htm")

            resValue("bool", "has_billing", "false")
            resValue("string", "csjCodeId", "953443412")
            resValue("string", "csjAppId", "5428167")
            resValue("bool", "luntan_activity", "false")
        }
        taptap {
            dimension "platform"
            manifestPlaceholders = [PACKAGE_NAME: "com.moyu.diguoshengyuan", "APPLOG_SCHEME": "rangersapplog.5bb4eb6a025284ab".toLowerCase()]
            applicationId "com.moyu.diguoshengyuan"
            resValue("string", "main_page_url", "https://www.taptap.cn/app/384236")
            resValue("string", "platform_channel", "taptap")
            resValue("string", "luntan_link", "https://www.taptap.cn/group/645919/group-label/937376")
            resValue("bool", "has_billing", "true")

            resValue("string", "csjCodeId", "963165518")
            resValue("string", "csjAppId", "5650101")
            resValue("bool", "luntan_activity", "true")
        }
        taiwan {
            dimension "platform"
            manifestPlaceholders = [PACKAGE_NAME: "com.moyu.diguorensheng_taiwan", "APPLOG_SCHEME": "rangersapplog.5bb4eb6a025284ab".toLowerCase()]
            applicationId "com.moyu.diguorensheng_taiwan"
            resValue("string", "main_page_url", "https://www.taptap.cn/app/384236")
            resValue("string", "platform_channel", "googleplay")
            resValue("string", "luntan_link", "https://www.taptap.cn/group/645919/group-label/937376")

            resValue("bool", "has_billing", "true")
            resValue("bool", "luntan_activity", "false")
            resValue("string", "csjCodeId", "0")
            resValue("string", "csjAppId", "0")
        }

        china {
            dimension "region"

            resValue("bool", "has_google_service", "false")
            resValue("bool", "need_privacy_check", "true")
            resValue("bool", "need_permission_check", "true")
            resValue("bool", "need_anti_addict_check", "true")
            resValue("bool", "can_share", "true")
            resValue("string", "serverUrl", "http://101.43.24.173:9795/yuansuqiu/api/v2/")
//            resValue("string", "serverUrl", "http://192.168.31.30:9795/yuansuqiu/api/v2/")
        }

        oversea {
            dimension "region"

            resValue("bool", "has_billing", "true")
            resValue("bool", "has_google_service", "true")
            resValue("bool", "need_privacy_check", "false")
            resValue("bool", "need_permission_check", "false")
            resValue("bool", "need_anti_addict_check", "false")
            resValue("bool", "can_share", "false")
            resValue("string", "serverUrl", "http://43.153.185.194:9795/yuansuqiu/api/v2/")
            resValue("string", "luntan_link", "https://bbs.3839.com/thread-6149905.htm")
            resValue("bool", "luntan_activity", "false")
        }

        lite {
            dimension "debugable"
        }
        product {
            dimension "debugable"
        }
    }
    flavorDimensions "platform", "debugable", "region"
    buildTypes {
        release {
            minifyEnabled true
            shrinkResources false
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        debug {
            minifyEnabled false
            signingConfig signingConfigs.release
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
    }
    buildFeatures {
        compose true
        buildConfig = true
    }
    kotlin {
        jvmToolchain(17)
    }
    composeOptions {
        kotlinCompilerExtensionVersion "1.5.15"
    }
    packagingOptions {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
        }
    }

    bundle {
        language {
            enableSplit = false
        }
    }

    namespace 'com.moyu.chuanqirensheng'
}

dependencies {
    // projects
    implementation project(path: ':job')
    implementation project(path: ':core')
    // androidx
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.10.0'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.6.2'
    implementation 'androidx.activity:activity-compose:1.8.2'

    // compose
    implementation "androidx.compose.ui:ui:1.7.8"
    implementation "androidx.compose.material:material:1.7.8"
    // navigation
    implementation("androidx.navigation:navigation-compose:2.7.7")

    // drag to reorder
    implementation("org.burnoutcrew.composereorderable:reorderable:0.9.6")
    // data store
    implementation 'androidx.datastore:datastore-preferences:1.0.0'
    // timber
    implementation 'com.jakewharton.timber:timber:5.0.1'
    // 穿山甲SDK
    chinaImplementation 'com.pangle.cn:ads-sdk-pro:5.6.0.7'
    // bugly
    chinaImplementation 'com.tencent.bugly:crashreport:4.1.9.3'

    //wx 支付依赖(包含统计功能)
    chinaApi 'com.tencent.mm.opensdk:wechat-sdk-android:+'

    // coil with compose 仅taptap版本需要网络图片
    implementation 'io.coil-kt:coil-compose:2.5.0'
    implementation 'io.coil-kt:coil:2.5.0'
    // taptap升级sdk
    taptapImplementation 'com.squareup.okhttp3:okhttp:3.12.1'
    taptapImplementation 'com.google.android.flexbox:flexbox:3.0.0'
    // taptap
    taptapImplementation 'com.taptap:lc-storage-android:8.2.24'
    taptapImplementation 'com.taptap:lc-realtime-android:8.2.24'
    chinaImplementation 'io.reactivex.rxjava2:rxandroid:2.1.1'
    taptapImplementation fileTree(include: ['*.jar', '*.aar'], dir: 'src/taptap/libs')
    hykbImplementation fileTree(include: ['*.jar', '*.aar'], dir: 'src/hykb/libs')
    // charts
    implementation('io.github.bytebeats:compose-charts:0.1.2')
    // zip4j
    implementation("net.lingala.zip4j:zip4j:2.11.5")
    // ktor
    implementation("io.ktor:ktor-client-core:$ktor_version")
    implementation("io.ktor:ktor-client-content-negotiation:$ktor_version")
    implementation("io.ktor:ktor-serialization-kotlinx-json:$ktor_version")
    implementation "io.ktor:ktor-client-okhttp:$ktor_version"
    // json
    implementation("org.jetbrains.kotlinx:kotlinx-serialization-json:1.6.3")
    // root
    chinaImplementation 'com.scottyab:rootbeer-lib:0.1.0'
    // uuid
    implementation("app.softwork:kotlinx-uuid-core:0.0.12")
    // flip
    implementation "com.wajahatkarim:flippable:1.5.4"
    //exoplayer播放器
    implementation 'androidx.media3:media3-exoplayer:1.4.1'
    implementation 'androidx.media3:media3-ui:1.4.1'

    //wx 支付依赖(包含统计功能)
//    chinaApi 'com.tencent.mm.opensdk:wechat-sdk-android:+'

    // This dependency is downloaded from the Google’s Maven repository.
    // Make sure you also include that repository in your project's build.gradle file.
    overseaImplementation 'com.google.android.play:review:2.0.1'
    // For Kotlin users, also add the Kotlin extensions library for Play In-App Review:
    overseaImplementation 'com.google.android.play:review-ktx:2.0.1'

    overseaImplementation platform('com.google.firebase:firebase-bom:32.8.0')
    overseaImplementation('com.google.firebase:firebase-crashlytics-ktx')
    overseaImplementation('com.google.firebase:firebase-analytics-ktx')
    overseaImplementation 'com.android.billingclient:billing-ktx:7.0.0'
    overseaImplementation 'com.google.android.gms:play-services-auth:21.0.0'
    overseaImplementation 'com.google.android.gms:play-services-ads:23.2.0'

    overseaImplementation("com.google.ads.mediation:mintegral:16.7.81.0")
    overseaImplementation("com.google.ads.mediation:pangle:6.0.0.5.0")
    overseaImplementation("com.google.ads.mediation:applovin:12.5.0.1")

    overseaImplementation 'com.appsflyer:af-android-sdk:6.12.1'
    overseaImplementation "com.android.installreferrer:installreferrer:2.2"

    overseaImplementation 'com.google.firebase:firebase-config'
    overseaImplementation 'com.facebook.android:facebook-android-sdk:[8,9)'

    chinaImplementation fileTree(dir: 'libs', include: ['*.jar', '*.aar'])
}

allprojects {
    tasks.withType(KotlinCompile).configureEach {
        kotlinOptions {
            allWarningsAsErrors = false
            freeCompilerArgs += [
                    "-opt-in=androidx.compose.animation.ExperimentalAnimationApi",
                    "-opt-in=androidx.compose.ui.ExperimentalComposeUiApi",
                    "-opt-in=androidx.compose.foundation.ExperimentalFoundationApi",
                    "-opt-in=androidx.media3.common.util.UnstableApi",
                    "-opt-in=androidx.compose.foundation.layout.ExperimentalLayoutApi",
            ]
        }
    }
}