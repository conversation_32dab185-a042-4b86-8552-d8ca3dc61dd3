package com.moyu.chuanqirensheng.sub.remoteab

import android.annotation.SuppressLint
import com.google.firebase.Firebase
import com.google.firebase.remoteconfig.ktx.remoteConfigSettings
import com.google.firebase.remoteconfig.remoteConfig
import com.moyu.chuanqirensheng.debug.DebugManager

const val MUTE_DIALOG = "mute_dialog"

val remote_keys = mapOf(
    MUTE_DIALOG to true,
)

object RemoteAbConfigManager {
    // 单activity应用，直接使用Firebase.remoteConfig即可
    @SuppressLint("StaticFieldLeak")
    private val remoteConfig = Firebase.remoteConfig
    fun init() {
        val configSettings = remoteConfigSettings {
            minimumFetchIntervalInSeconds = 0 // Fetches at least once per session
        }

        remoteConfig.setConfigSettingsAsync(configSettings)

        remoteConfig.setDefaultsAsync(remote_keys)

        // Fetch and activate once at app launch
        remoteConfig.fetchAndActivate()
    }

    fun muteDialog(): Boolean {
        if (DebugManager.useClientDefault) {
            return remote_keys[MUTE_DIALOG] ?: true
        }
        return remoteConfig.getBoolean(MUTE_DIALOG)
    }
}