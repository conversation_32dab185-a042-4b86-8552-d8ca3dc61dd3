package com.moyu.chuanqirensheng.sub.report

import android.os.Bundle
import androidx.compose.runtime.mutableStateOf
import com.appsflyer.AFInAppEventParameterName
import com.appsflyer.AFInAppEventType
import com.appsflyer.AppsFlyerLib
import com.facebook.appevents.AppEventsConstants.EVENT_NAME_COMPLETED_REGISTRATION
import com.facebook.appevents.AppEventsLogger
import com.google.firebase.analytics.FirebaseAnalytics
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.datastore.KEY_REPORT_20_YEAR
import com.moyu.chuanqirensheng.datastore.KEY_REPORT_25_YEAR
import com.moyu.chuanqirensheng.datastore.KEY_REPORT_30_YEAR
import com.moyu.chuanqirensheng.datastore.KEY_REPORT_AF_AD
import com.moyu.chuanqirensheng.datastore.KEY_REPORT_AF_PURCHASE1
import com.moyu.chuanqirensheng.datastore.KEY_REPORT_AF_PURCHASE2
import com.moyu.chuanqirensheng.datastore.KEY_REPORT_ALL_DIE
import com.moyu.chuanqirensheng.datastore.KEY_REPORT_FIRST_AD
import com.moyu.chuanqirensheng.datastore.KEY_REPORT_FIRST_GAME
import com.moyu.chuanqirensheng.datastore.KEY_REPORT_FIRST_LOGIN
import com.moyu.chuanqirensheng.datastore.KEY_REPORT_SECOND_DAY_LOGIN
import com.moyu.chuanqirensheng.datastore.KEY_REPORT_SECOND_GAME
import com.moyu.chuanqirensheng.datastore.KEY_REPORT_SECOND_LOGIN
import com.moyu.chuanqirensheng.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.talent.TalentManager
import com.moyu.chuanqirensheng.logic.task.TaskManager
import com.moyu.chuanqirensheng.logic.task.getLoginDays
import com.moyu.chuanqirensheng.util.getVersionCode
import com.moyu.core.model.skill.isBattleSkill
import java.math.BigDecimal
import java.util.Currency


const val AF_AD_ID = "af_ad_id"
const val AF_NEW_USER = "af_new_user"
const val AF_SECOND_PURCHASE = "af_second_purchase"

const val KEY_FIRST_ALLY_DIE = "all_ally_die"
const val KEY_FIRST_AD = "first_ad"
const val KEY_FIRST_PAY = "first_pay"
const val KEY_FIRST_OPEN = "my_first_open"
const val KEY_SECOND_PAY = "second_pay"
const val KEY_FIRST_LOGIN = "first_login"
const val KEY_SECOND_LOGIN = "second_login"
const val KEY_SECOND_DAY_LOGIN = "second_day_login"
const val KEY_GAME = "play_game"
const val KEY_FIRST_GAME = "first_game"
const val KEY_SECOND_GAME = "second_game"
const val KEY_GUIDE = "my_guide_"
const val KEY_PURCHASE = "purchase_in_shop"
const val KEY_REVIEW_GAME = "review_game"
const val KEY_PRELOAD_AD = "preload_ad"
const val KEY_LOAD_AD = "load_ad"
const val KEY_PLAY_AD = "play_ad"
const val KEY_PLAY_AD_DONE = "play_ad_done"
const val KEY_PAGE_ROUTE = "page_route"
const val KEY_ALLY_UPGRADE_OUT_GAME = "star_up_out"
const val KEY_ALLY_UPGRADE_IN_GAME = "star_up_in"
const val KEY_TALENT_UPGRADE = "talent_up"
const val KEY_STORY_PACK = "story_select"
const val KEY_BATTLE_END = "battle_end"

const val PARAM_GAME_MODE = "param_game_mode"
const val PARAM_PURCHASE_TYPE = "param_purchase_type"
const val PARAM_STAR = "param_star"
const val PARAM_KEY = "param_key"
const val PARAM_DIAMOND = "param_diamond"
const val PARAM_ELECTRIC = "param_electric"
const val PARAM_TALENT = "param_talent_sum"
const val PARAM_NEW_QUEST = "param_new_quest"
const val PARAM_DAILY_QUEST = "param_daily_quest"
const val PARAM_ACHIEVE_QUEST = "param_achieve_quest"
const val PARAM_PASS1_QUEST = "param_pass1_quest"
const val PARAM_PASS2_QUEST = "param_pass2_quest"
const val PARAM_AD_TYPE = "ad_type"
const val PARAM_PAGE = "page"
const val PARAM_ID = "param_id"
const val PARAM_LEVEL = "param_level"
const val PARAM_COST = "param_cost"
const val PARAM_SELECT = "param_select"
const val PARAM_WIN = "param_win"
const val PARAM_ALLY_NUM = "param_ally_num"
const val PARAM_SKILL_NUM = "param_skill_num"

const val KEY_TRY_PLAY = "try_play"
const val KEY_DOUBLE_SIGN = "sign"

object ReportManager {
    // 初始化 Firebase Analytics
    lateinit var firebaseAnalytics: FirebaseAnalytics
    val firstAFAd = mutableStateOf(false)
    val firstAFPurchase = mutableStateOf(false)
    val secondAFPurchase = mutableStateOf(false)
    val year20 = mutableStateOf(false)
    val year25 = mutableStateOf(false)
    val year30 = mutableStateOf(false)
    val firstGame = mutableStateOf(false)
    val secondGame = mutableStateOf(false)
    val firstLogin = mutableStateOf(false)
    val secondLogin = mutableStateOf(false)
    val allAllyDie = mutableStateOf(false)
    val firstAd = mutableStateOf(false)
    val secondDayLogin = mutableStateOf(false)
    val guideReportMap = mutableMapOf<Int, Boolean>()

    fun init() {
        firstAFAd.value = getBooleanFlowByKey(KEY_REPORT_AF_AD)
        firstAFPurchase.value = getBooleanFlowByKey(KEY_REPORT_AF_PURCHASE1)
        secondAFPurchase.value = getBooleanFlowByKey(KEY_REPORT_AF_PURCHASE2)
        year20.value = getBooleanFlowByKey(KEY_REPORT_20_YEAR)
        year25.value = getBooleanFlowByKey(KEY_REPORT_25_YEAR)
        year30.value = getBooleanFlowByKey(KEY_REPORT_30_YEAR)
        firstGame.value = getBooleanFlowByKey(KEY_REPORT_FIRST_GAME)
        secondGame.value = getBooleanFlowByKey(KEY_REPORT_SECOND_GAME)
        firstLogin.value = getBooleanFlowByKey(KEY_REPORT_FIRST_LOGIN)
        secondLogin.value = getBooleanFlowByKey(KEY_REPORT_SECOND_LOGIN)
        allAllyDie.value = getBooleanFlowByKey(KEY_REPORT_ALL_DIE)
        firstAd.value = getBooleanFlowByKey(KEY_REPORT_FIRST_AD)
        secondDayLogin.value = getBooleanFlowByKey(KEY_REPORT_SECOND_DAY_LOGIN)
    }

    fun open() {
        val bundle = bundleWithCommonParam()
        if (GameApp.newUser) {
            firebaseAnalytics.logEvent(KEY_FIRST_OPEN, bundle)
        }
        firebaseAnalytics.logEvent(FirebaseAnalytics.Event.APP_OPEN, bundle)

        val logger = AppEventsLogger.newLogger(GameApp.instance)
        logger.logEvent(EVENT_NAME_COMPLETED_REGISTRATION)
    }

    fun allAllyDie() {
        if (!allAllyDie.value) {
            val bundle = bundleWithCommonParam()
            firebaseAnalytics.logEvent(KEY_FIRST_ALLY_DIE, bundle)
            allAllyDie.value = true
            setBooleanValueByKey(KEY_REPORT_ALL_DIE, true)
        }
    }

    fun guide(index: Int) {
        if (guideReportMap[index] != true) {
            val bundle = bundleWithCommonParam()
            firebaseAnalytics.logEvent(KEY_GUIDE + index, bundle)
            guideReportMap[index] = true
        }
    }

    fun onLogin() {
        val eventValues: MutableMap<String, Any> = HashMap()
        eventValues[AF_NEW_USER] = if (GameApp.newUser) 1 else 0
        eventValues[AFInAppEventParameterName.NEW_VERSION] = getVersionCode()
        eventValues[AFInAppEventParameterName.CUSTOMER_USER_ID] = GameApp.instance.getObjectId()?:"none"
        if (!firstLogin.value) {
            firstLogin.value = true
            setBooleanValueByKey(KEY_REPORT_FIRST_LOGIN, true)
            AppsFlyerLib.getInstance()
                .logEvent(GameApp.instance, AFInAppEventType.LOGIN, eventValues)

            val bundle = bundleWithCommonParam()
            firebaseAnalytics.logEvent(KEY_FIRST_LOGIN, bundle)
        } else if (!secondLogin.value) {
            secondLogin.value = true
            setBooleanValueByKey(KEY_REPORT_SECOND_LOGIN, true)
            AppsFlyerLib.getInstance().logEvent(GameApp.instance, AFInAppEventType.LOGIN, eventValues)

            val bundle = bundleWithCommonParam()
            firebaseAnalytics.logEvent(KEY_SECOND_LOGIN, bundle)
        }

        if (getLoginDays() >= 2) {
            secondDayLogin()
        }

        val bundle = bundleWithCommonParam()
        bundle.putInt(PARAM_NEW_QUEST, TaskManager.newTasks.filter { it.done }.size)
        bundle.putInt(PARAM_ACHIEVE_QUEST, TaskManager.oneTimeTasks.filter { it.done }.size)
        bundle.putInt(PARAM_DAILY_QUEST, TaskManager.dailyTasks.filter { it.done }.size)
        bundle.putInt(PARAM_PASS1_QUEST, TaskManager.warPassTasks.filter { it.done }.size)
        bundle.putInt(PARAM_PASS2_QUEST, TaskManager.warPass2Tasks.filter { it.done }.size)
        firebaseAnalytics.logEvent(FirebaseAnalytics.Event.LOGIN, bundle)
    }

    fun secondDayLogin() {
        if (!secondDayLogin.value) {
            secondDayLogin.value = true
            setBooleanValueByKey(KEY_REPORT_SECOND_DAY_LOGIN, true)
            val bundle = bundleWithCommonParam()
            firebaseAnalytics.logEvent(KEY_SECOND_DAY_LOGIN, bundle)
        }
    }

    fun onAdCompletedAF(adId: String) {
        if (!firstAFAd.value) {
            firstAFAd.value = true
            setBooleanValueByKey(KEY_REPORT_AF_AD, true)
            val eventValues: MutableMap<String, Any> = HashMap()
            eventValues[AF_AD_ID] = adId
            eventValues[AF_NEW_USER] = if (GameApp.newUser) 1 else 0
            eventValues[AFInAppEventParameterName.NEW_VERSION] = getVersionCode()
            eventValues[AFInAppEventParameterName.CUSTOMER_USER_ID] = GameApp.instance.getObjectId()?:"none"
            AppsFlyerLib.getInstance().logEvent(GameApp.instance, AFInAppEventType.AD_VIEW, eventValues)
        }

        // 定义一个购买事件
        val bundle = bundleWithCommonParam()
        bundle.putString(AF_AD_ID, adId)
        firebaseAnalytics.logEvent(FirebaseAnalytics.Event.AD_IMPRESSION, bundle)
    }

    fun onPurchaseCompletedAF(purchaseId: String, amount: Double, number: Int) {
        val eventValues: MutableMap<String, Any> = HashMap()
        eventValues[AFInAppEventParameterName.REVENUE] = amount
        eventValues[AFInAppEventParameterName.CONTENT_ID] = purchaseId
        eventValues[AF_NEW_USER] = if (GameApp.newUser) 1 else 0
        eventValues[AFInAppEventParameterName.NEW_VERSION] = getVersionCode()
        eventValues[AFInAppEventParameterName.QUANTITY] = number
        eventValues[AFInAppEventParameterName.CUSTOMER_USER_ID] = GameApp.instance.getObjectId()?:"none"
        if (!firstAFPurchase.value) {
            firstAFPurchase.value = true
            setBooleanValueByKey(KEY_REPORT_AF_PURCHASE1, true)
            eventValues[AF_SECOND_PURCHASE] = 0
            AppsFlyerLib.getInstance().logEvent(GameApp.instance, AFInAppEventType.PURCHASE, eventValues)

            val bundle = bundleWithCommonParam()
            bundle.putInt(AFInAppEventParameterName.REVENUE, amount.toInt())
            bundle.putString(AFInAppEventParameterName.CONTENT_ID, purchaseId)
            bundle.putInt(AFInAppEventParameterName.QUANTITY, number)
            firebaseAnalytics.logEvent(KEY_FIRST_PAY, bundle)
        } else if (!secondAFPurchase.value) {
            secondAFPurchase.value = true
            setBooleanValueByKey(KEY_REPORT_AF_PURCHASE2, true)
            eventValues[AF_SECOND_PURCHASE] = 1
            AppsFlyerLib.getInstance().logEvent(GameApp.instance, AFInAppEventType.PURCHASE, eventValues)

            val bundle = bundleWithCommonParam()
            bundle.putInt(AFInAppEventParameterName.REVENUE, amount.toInt())
            bundle.putString(AFInAppEventParameterName.CONTENT_ID, purchaseId)
            bundle.putInt(AFInAppEventParameterName.QUANTITY, number)
            firebaseAnalytics.logEvent(KEY_SECOND_PAY, bundle)
        }

        val bundle = bundleWithCommonParam()
        bundle.putInt(AFInAppEventParameterName.REVENUE, amount.toInt())
        bundle.putString(AFInAppEventParameterName.CONTENT_ID, purchaseId)
        bundle.putInt(AFInAppEventParameterName.QUANTITY, number)
        firebaseAnalytics.logEvent(FirebaseAnalytics.Event.REFUND, bundle)

        val logger = AppEventsLogger.newLogger(GameApp.instance)
        logger.logPurchase(
            BigDecimal.valueOf(amount / 6), // 取近似
            Currency.getInstance("USD")
        )
    }

    fun onShopPurchase(sellId: Int, price: Int, priceType: Int) {
        val bundle = Bundle()
        bundle.putInt(AFInAppEventParameterName.REVENUE, price)
        bundle.putInt(PARAM_PURCHASE_TYPE, priceType)
        bundle.putString(AFInAppEventParameterName.CONTENT_ID, sellId.toString())
        firebaseAnalytics.logEvent(KEY_PURCHASE, bundle)
    }

    fun onNewGame(mode: Int) {
        val bundle = bundleWithCommonParam()
        bundle.putInt(PARAM_GAME_MODE, mode)
        if (!firstGame.value) {
            firstGame.value = true
            setBooleanValueByKey(KEY_REPORT_FIRST_GAME, true)
            firebaseAnalytics.logEvent(KEY_FIRST_GAME, bundle)
        } else if (!secondGame.value) {
            secondGame.value = true
            setBooleanValueByKey(KEY_REPORT_SECOND_GAME, true)
            firebaseAnalytics.logEvent(KEY_SECOND_GAME, bundle)
        }
        firebaseAnalytics.logEvent(KEY_GAME, bundle)
    }

    fun onReview(star: Int) {
        val bundle = bundleWithCommonParam()
        bundle.putInt(PARAM_STAR, star)
        firebaseAnalytics.logEvent(KEY_REVIEW_GAME, bundle)
    }

    fun onPreLoadAd(adId: String) {
        val bundle = bundleWithCommonParam()
        bundle.putString(PARAM_AD_TYPE, adId)
        firebaseAnalytics.logEvent(KEY_PRELOAD_AD, bundle)
    }

    fun onLoadAd(adId: String) {
        val bundle = bundleWithCommonParam()
        bundle.putString(PARAM_AD_TYPE, adId)
        firebaseAnalytics.logEvent(KEY_LOAD_AD, bundle)
    }

    fun onPlayAd(adId: String) {
        val bundle = bundleWithCommonParam()
        if (!firstAd.value) {
            firstAd.value = true
            setBooleanValueByKey(KEY_REPORT_FIRST_AD, true)
            firebaseAnalytics.logEvent(KEY_FIRST_AD, bundle)
        }
        bundle.putString(PARAM_AD_TYPE, adId)
        firebaseAnalytics.logEvent(KEY_PLAY_AD, bundle)
    }

    fun onPlayAdDone(adId: String) {
        val bundle = bundleWithCommonParam()
        bundle.putString(PARAM_AD_TYPE, adId)
        firebaseAnalytics.logEvent(KEY_PLAY_AD_DONE, bundle)
    }

    fun onPage(page: String) {
//        val bundle = bundleWithCommonParam()
//        bundle.putString(PARAM_PAGE, page)
//        firebaseAnalytics.logEvent(KEY_PAGE_ROUTE, bundle)
    }

    fun onUpgradeAllyOutGame(allyId: Int, allyStar: Int) {
//        val bundle = bundleWithCommonParam()
//        bundle.putInt(PARAM_ID, allyId)
//        bundle.putInt(PARAM_LEVEL, allyStar)
//        firebaseAnalytics.logEvent(KEY_ALLY_UPGRADE_OUT_GAME, bundle)
    }

    fun onUpgradeAllyInGame(allyId: Int, allyStar: Int) {
//        val bundle = bundleWithCommonParam()
//        bundle.putInt(PARAM_ID, allyId)
//        bundle.putInt(PARAM_LEVEL, allyStar)
//        firebaseAnalytics.logEvent(KEY_ALLY_UPGRADE_IN_GAME, bundle)
    }

    fun onTalentUp(talentId: Int, talentLevel: Int, diamond: Int) {
//        val bundle = bundleWithCommonParam()
//        bundle.putInt(PARAM_ID, talentId)
//        bundle.putInt(PARAM_LEVEL, talentLevel)
//        bundle.putInt(PARAM_COST, diamond)
//        firebaseAnalytics.logEvent(KEY_TALENT_UPGRADE, bundle)
    }

    fun onStoryPack(storyId: Int, select: Int) {
//        val bundle = bundleWithCommonParam()
//        bundle.putInt(PARAM_ID, storyId)
//        bundle.putInt(PARAM_SELECT, select)
//        firebaseAnalytics.logEvent(KEY_STORY_PACK, bundle)
    }


    private fun bundleWithCommonParam():Bundle {
        val bundle = Bundle()
        bundle.putInt(PARAM_KEY, AwardManager.key.value)
        bundle.putInt(PARAM_DIAMOND, AwardManager.diamond.value)
        bundle.putInt(PARAM_ELECTRIC, AwardManager.electric.value)
        bundle.putInt(PARAM_TALENT, TalentManager.talents.values.sum())
        bundle.putInt(AF_NEW_USER, if (GameApp.newUser) 1 else 0)
        bundle.putInt(AFInAppEventParameterName.NEW_VERSION, getVersionCode())
        bundle.putString(AFInAppEventParameterName.CUSTOMER_USER_ID, GameApp.instance.getObjectId()?:"none")
        return bundle
    }

    fun onGameEnd(win: Int) {
        val bundle = bundleWithCommonParam()
        bundle.putInt(PARAM_WIN, win)
        bundle.putInt(PARAM_ALLY_NUM, BattleManager.allyGameData.filter { !it.isDead() }.size)
        bundle.putInt(PARAM_SKILL_NUM, BattleManager.skillGameData.filter { it.isBattleSkill() }.size)
        bundle.putInt(PARAM_LEVEL, BattleManager.adventureProps.value.age)

        firebaseAnalytics.logEvent(KEY_BATTLE_END, bundle)
    }
}