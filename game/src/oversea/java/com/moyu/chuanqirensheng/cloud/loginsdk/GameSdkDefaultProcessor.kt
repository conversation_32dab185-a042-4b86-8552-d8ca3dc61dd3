package com.moyu.chuanqirensheng.cloud.loginsdk

import android.provider.Settings
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.fragment.app.FragmentActivity
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.auth.api.signin.GoogleSignInAccount
import com.google.android.gms.auth.api.signin.GoogleSignInClient
import com.google.android.gms.auth.api.signin.GoogleSignInOptions
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.cloud.bill.RC_SIGN_IN
import com.moyu.chuanqirensheng.datastore.KEY_LOGIN_STYLE
import com.moyu.chuanqirensheng.datastore.KEY_OBJECT_ID
import com.moyu.chuanqirensheng.datastore.getNoEncryptStringValueByKey
import com.moyu.chuanqirensheng.datastore.getStringFlowByKey
import com.moyu.chuanqirensheng.datastore.setNoEncryptStringValueByKey
import com.moyu.chuanqirensheng.datastore.setStringValueByKey
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.ui.theme.padding300
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding69
import com.moyu.chuanqirensheng.util.killSelf
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

const val GOOGLE_LOGIN = "google"
const val TOURIST_LOGIN = "tourist"

class GameSdkDefaultProcessor : GameSdkProcessor {
    private val antiAddictionContent = mutableStateOf("")
    private val loginStatus = mutableStateOf(false)
    private val avatar = mutableStateOf<String?>(null)
    private val userName = mutableStateOf<String?>(null)
    private val antiAddictionStatus = mutableStateOf(false)
    private val objectId = mutableStateOf<String?>(null)

    private var init = false
    private var mGoogleSignInClient: GoogleSignInClient? = null
    var mGoogleSignInAccount: GoogleSignInAccount? = null
    private var isGoogleLogin = false

    override fun initGameSdk() {
        antiAddictionStatus.value = false
    }

    // 初始化SDK，判断是否有登录态，没有则调用好游快爆登录，
    // 登录后校验防沉迷，若返回登录成功，则正常显示菜单，否则弹窗提示错误原因，并退出
    override fun initSDK(activity: FragmentActivity) {
        if (init) return
        init = true

        // 仅对非中文地区开放这个功能，降低对当前数据的冲击
        if (false) {
//        if (getStringFlowByKey(KEY_OBJECT_ID).isEmpty() && !LanguageManager.isSelectedChinese()) {
            Dialogs.alertDialog.value =
                CommonAlert(confirmText = GameApp.instance.getWrapString(R.string.google_login),
                    cancelText = GameApp.instance.getWrapString(R.string.tourist_login),
                    heightInDp = padding300,
                    frame = R.drawable.common_window_small,
                    onConfirm = {
                        isGoogleLogin = true
                        setNoEncryptStringValueByKey(KEY_LOGIN_STYLE, GOOGLE_LOGIN)
                        val gso = GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
                            .requestEmail()
                            .build()
                        mGoogleSignInClient = GoogleSignIn.getClient(activity, gso)
                        login(activity)
                    },
                    onCancel = {
                        isGoogleLogin = false
                        setNoEncryptStringValueByKey(KEY_LOGIN_STYLE, TOURIST_LOGIN)
                        login(activity)
                    },
                    onDismiss = {
                        isGoogleLogin = false
                        setNoEncryptStringValueByKey(KEY_LOGIN_STYLE, TOURIST_LOGIN)
                        login(activity)
                    },
                    title = GameApp.instance.getWrapString(R.string.login_mode),
                    extraContent = {
                        Column {
                            Spacer(modifier = Modifier.weight(1f))
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceEvenly,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Image(
                                    painter = painterResource(id = R.drawable.play5_icon),
                                    contentDescription = null,
                                    modifier = Modifier
                                        .size(padding69)
                                        .graphicsLayer {
                                            translationX = -padding4.toPx()
                                        }
                                )
                                Image(
                                    painter = painterResource(id = R.drawable.play10_icon),
                                    contentDescription = null,
                                    modifier = Modifier
                                        .size(padding69)
                                        .graphicsLayer {
                                            translationX = padding4.toPx()
                                        }
                                )
                            }
                        }
                    })
        } else {
            isGoogleLogin = getNoEncryptStringValueByKey(KEY_LOGIN_STYLE, GOOGLE_LOGIN) == GOOGLE_LOGIN
            if (isGoogleLogin) {
                val gso = GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
                    .requestEmail()
                    .build()
                mGoogleSignInClient = GoogleSignIn.getClient(activity, gso)
            }
            login(activity)
        }
    }

    override fun login(activity: FragmentActivity) {
        if (isGoogleLogin) {
            GoogleSignIn.getLastSignedInAccount(activity)?.let { account ->
                mGoogleSignInAccount = account
                dealAfterLogin(
                    account.displayName ?: "No Name",
                    account.id!!,
                    account.photoUrl.toString(),
                    activity
                )
            } ?: kotlin.run {
                val signInIntent = mGoogleSignInClient!!.signInIntent
                activity.startActivityForResult(signInIntent, RC_SIGN_IN)
            }
        } else {
            val androidId =
                Settings.Secure.getString(activity.contentResolver, Settings.Secure.ANDROID_ID)
            dealAfterLogin("Tourist_${androidId.take(6)}", androidId, "", activity)
        }
    }

    override fun antiAddictPassed(): MutableState<Boolean> {
        return antiAddictionStatus
    }

    override fun hasLogin(): Boolean {
        return loginStatus.value
    }

    override fun getAvatarUrl(): String {
        return "npc_17"
    }

    override fun getUserName(): String? {
        return userName.value
    }

    override fun getObjectId(): String? {
        return objectId.value
    }

    override fun getAntiAddictionContent(): String {
        return antiAddictionContent.value
    }

    override fun dealAfterLogin(
        name: String,
        id: String,
        avatarUrl: String,
        activity: FragmentActivity
    ) {
        loginStatus.value = true
        userName.value = name
        objectId.value = id
        avatar.value = avatarUrl
        checkAntiAddiction(activity)
        GameApp.globalScope.launch {
            val oldAccount = getStringFlowByKey(KEY_OBJECT_ID)
            if (oldAccount.isEmpty()) {
                setStringValueByKey(KEY_OBJECT_ID, objectId.value ?: "")
            } else if (oldAccount != objectId.value) {
                GameApp.instance.getWrapString(R.string.account_switch_tips).toast()
                delay(2000)
                killSelf()
            }
        }
        GameApp.instance.tryLogin()
    }

    override fun isAgeUnder8(): Boolean {
        return false
    }

    override fun isAgeIn8To16(): Boolean {
        return true
    }

    override fun checkAntiAddiction(activity: FragmentActivity) {
    }
}
