package com.moyu.chuanqirensheng.cloud.bill

import com.moyu.chuanqirensheng.model.award.AwardConvert


val shopList = mutableListOf(
    AwardConvert(
        code = KEY_FIRST_CHARGE,
        key = 500,
        diamond = 300,
        allies = listOf(10670),
        heroes = listOf(40450),
        electric = 6
    ),
    AwardConvert(
        code = KEY_150,
        key = 150,
        electric = 6
    ),
    AwardConvert(
        code = KEY_750,
        key = 750+85,
        electric = 30
    ),
    AwardConvert(
        code = KEY_2450,
        key = 2450+650,
        electric = 98
    ),
    AwardConvert(
        code = KEY_4950,
        key = 4950+1850,
        electric = 198
    ),
    AwardConvert(
        code = KEY_8200,
        key = 8200+8200,
        electric = 328
    ),
    AwardConvert(
        code = ALLY_1,
        allies = listOf(10220),
        electric = 28,
    ),
    AwardConvert(
        code = ALLY_2,
        allies = listOf(10950),
        electric = 28,
    ),
    AwardConvert(
        code = ALLY_3,
        allies = listOf(11080),
        electric = 28,
    ),
    AwardConvert(
        code = ALLY_4,
        allies = listOf(11200),
        electric = 28,
    ),
    AwardConvert(
        code = ALLY_5,
        allies = listOf(11380),
        electric = 28,
    ),
    AwardConvert(
        code = SKILL_1,
        skills = listOf(20010),
        electric = 28,
    ),
    AwardConvert(
        code = SKILL_2,
        skills = listOf(20280),
        electric = 28,
    ),
    AwardConvert(
        code = SKILL_3,
        skills = listOf(20700),
        electric = 28,
    ),
    AwardConvert(
        code = SKILL_4,
        skills = listOf(21720),
        electric = 28,
    ),
    AwardConvert(
        code = SKILL_5,
        skills = listOf(21820),
        electric = 28,
    ),
    AwardConvert(
        code = HERO_1,
        heroes = listOf(40130),
        electric = 38,
    ),
    AwardConvert(
        code = HERO_2,
        heroes = listOf(40890),
        electric = 38,
    ),
    AwardConvert(
        code = HERO_3,
        heroes = listOf(40900),
        electric = 38,
    ),
    AwardConvert(
        code = HERO_4,
        heroes = listOf(41000),
        electric = 38,
    ),
    AwardConvert(
        code = HERO_5,
        heroes = listOf(41250),
        electric = 38,
    ),
    AwardConvert(
        code = BATTLE_PASS_1,
        electric = 28,
        unlockList = listOf(10001)
    ),
    AwardConvert(
        code = BATTLE_PASS_2,
        electric = 28,
        unlockList = listOf(10002)
    ),
    AwardConvert(
        code = BATTLE_PASS2_1,
        electric = 30,
        unlockList = listOf(10011)
    ),
    AwardConvert(
        code = STORY_1,
        electric = 30,
        unlockList = listOf(1007)
    ),
    AwardConvert(
        code = STORY_2,
        electric = 30,
        heroes = listOf(41100),
        unlockList = listOf(1009)
    ),
    AwardConvert(
        code = STORY_3,
        electric = 30,
        allies = listOf(11270),
        unlockList = listOf(1010)
    ),
    AwardConvert(
        code = STORY_4,
        electric = 30,
        unlockList = listOf(1011)
    ),
)
