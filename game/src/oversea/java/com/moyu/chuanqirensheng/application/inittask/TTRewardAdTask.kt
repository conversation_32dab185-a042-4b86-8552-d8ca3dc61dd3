package com.moyu.chuanqirensheng.application.inittask

//import com.appsflyer.AppsFlyerLib
import com.facebook.FacebookSdk
import com.google.android.gms.ads.MobileAds
import com.google.firebase.analytics.FirebaseAnalytics
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.sub.remoteab.RemoteAbConfigManager
import com.moyu.chuanqirensheng.sub.report.ReportManager.firebaseAnalytics
import com.moyu.job.JobContent


/**
 * 穿山甲广告sdk初始化
 */
class TTRewardAdTask : JobContent<GameApp> {
    override suspend fun execute(context: GameApp) {
        MobileAds.initialize(context) {
            GameApp.adInit = true
        }

//        AppsFlyerLib.getInstance().init(appFlyerDevKey, null, context)
//        AppsFlyerLib.getInstance().start(context)

        // 初始化 Firebase Analytics
        firebaseAnalytics = FirebaseAnalytics.getInstance(context)
        RemoteAbConfigManager.init()

        FacebookSdk.sdkInitialize(context)
        FacebookSdk.setAutoInitEnabled(true)
    }
}