package com.moyu.chuanqirensheng.ad

import android.app.Activity
import com.google.android.gms.ads.AdError
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.FullScreenContentCallback
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.rewarded.RewardedAd
import com.google.android.gms.ads.rewarded.RewardedAdLoadCallback
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.lucky.AdManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.music.MusicManager
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.sub.language.LanguageManager
import com.moyu.chuanqirensheng.sub.report.ReportManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber

var playCount = 0

object TTAdPlayer : AdInterface {
    var isPlaying = false
    var lastPlayFailed = false

    var rewardedAd: RewardedAd? = null

    override fun playAd(adId: String, callback: suspend () -> Unit) {
        if (!GameApp.adInit) {
            GameApp.instance.getWrapString(R.string.ad_sdk_init_failed).toast()
        } else {
            if (isPlaying) {
                GameApp.instance.getWrapString(R.string.ad_still_playing).toast()
                return
            }
            isPlaying = true
            loadAdInternal(adId, callback)
        }
    }

    override fun preload(adId: String) {
        ReportManager.onPreLoadAd(adId)
        rewardedAd = null
        val adRequest = AdRequest.Builder().build()

        RewardedAd.load(
            GameApp.instance.activity,
            LanguageManager.getCountryAdId(),
            adRequest,
            object : RewardedAdLoadCallback() {
                override fun onAdFailedToLoad(loadAdError: LoadAdError) {
                    // Handle the error
                    rewardedAd = null
                    Timber.e(loadAdError.toString())
                }

                override fun onAdLoaded(rewardedAd: RewardedAd) {
                    <EMAIL> = rewardedAd
                    Timber.e("Ad was preloaded.")
                }
            })
    }

    private fun loadAdInternal(adId: String, callback: suspend () -> Unit) {
        playCount += 1
        val adRequest = AdRequest.Builder().build()
        rewardedAd?.fullScreenContentCallback = object : FullScreenContentCallback() {
            override fun onAdClicked() {
                // Called when a click is recorded for an ad.
                Timber.e("Ad was clicked.")
            }

            override fun onAdDismissedFullScreenContent() {
                // Called when ad is dismissed.
                // Set the ad reference to null so you don't show the ad a second time.
                Timber.e("Ad dismissed fullscreen content.")
                rewardedAd = null
            }

            override fun onAdFailedToShowFullScreenContent(adError: AdError) {
                // Called when ad fails to show.
                Timber.e("Ad failed to show fullscreen content.")
                rewardedAd = null
            }

            override fun onAdImpression() {
                // Called when an impression is recorded for an ad.
                Timber.e("Ad recorded an impression.")
            }

            override fun onAdShowedFullScreenContent() {
                // Called when ad is shown.
                Timber.e("Ad showed fullscreen content.")
            }
        }
        GameApp.globalScope.launch(Dispatchers.Main) {
            if (rewardedAd != null) {
                Timber.e("play preload ad.")
                playAdInternal(GameApp.instance.activity, rewardedAd, adId, callback)
            } else {
                ReportManager.onLoadAd(adId)
                RewardedAd.load(
                    GameApp.instance.activity,
                    LanguageManager.getCountryAdId(),
                    adRequest,
                    object : RewardedAdLoadCallback() {
                        override fun onAdFailedToLoad(adError: LoadAdError) {
                            adError.toString().toast()
                            lastPlayFailed = true
                            Timber.e(adError.toString())
                            rewardedAd = null
                            isPlaying = false
                        }

                        override fun onAdLoaded(ad: RewardedAd) {
                            Timber.e("Ad was loaded.")
                            lastPlayFailed = false
                            playAdInternal(GameApp.instance.activity, ad, adId, callback)
                        }
                    })
            }
        }
    }
}


private fun playAdInternal(
    activity: Activity,
    rewardedAd: RewardedAd?,
    adId: String,
    callback: suspend () -> Unit
) {
    TTAdPlayer.preload(adId)
    ReportManager.onPlayAd(adId)
    rewardedAd?.let { ad ->
        MusicManager.muteByAd(true)
        ad.show(activity) {
            GameApp.globalScope.launch(Dispatchers.Main) {
                ReportManager.onPlayAdDone(adId)
                AwardManager.adNum.value += 1
                AwardManager.adMoney.value += AdManager.getRandomAdMoney()
                callback.invoke()
                ReportManager.onAdCompletedAF(adId)
            }
            TTAdPlayer.isPlaying = false
            MusicManager.muteByAd(false)
            Timber.e("User earned the reward.")
        }
    } ?: run {
        Timber.e("The rewarded ad wasn't ready yet.")
    }
}