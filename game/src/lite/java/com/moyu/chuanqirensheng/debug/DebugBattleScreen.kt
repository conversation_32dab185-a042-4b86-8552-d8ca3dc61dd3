package com.moyu.chuanqirensheng.debug

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.arena.BattleFieldLayout
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.common.SettingColumn
import com.moyu.chuanqirensheng.screen.common.settingBattleItems
import com.moyu.chuanqirensheng.ui.theme.paddingSmallPlus
import com.moyu.chuanqirensheng.util.DEBUG_SCREEN
import com.moyu.chuanqirensheng.util.goto

@Composable
fun DebugBattleScreen() {
    BackPressHandler {
        repo.battle.value.terminate()
        repo.onGameOver()
        goto(DEBUG_SCREEN)
    }
    GameBackground(
        showCloseIcon = false,
        showPreviewIcon = false,
        gapStatusBar = false,
    ) {
        Column(
            Modifier.fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.SpaceEvenly
        ) {
            BattleFieldLayout(
                roleMinions = { repo.allies },
                enemyMinions = { repo.enemies },
            )
        }
        SettingColumn(
            Modifier
                .align(Alignment.CenterStart)
                .padding(start = paddingSmallPlus), settingBattleItems
        )
    }
}