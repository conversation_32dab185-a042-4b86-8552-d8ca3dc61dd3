package com.moyu.chuanqirensheng.debug

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.util.DEBUG_SCREEN
import com.moyu.chuanqirensheng.util.goto

@Composable
fun DebugButton(modifier: Modifier) {
    GameButton(
        modifier = modifier,
        text = "调试",
        onClick = {
            goto(DEBUG_SCREEN)
        }, buttonSize = ButtonSize.Small
    )
}