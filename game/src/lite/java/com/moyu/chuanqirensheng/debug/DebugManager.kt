package com.moyu.chuanqirensheng.debug

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue

/**
 * 测试能力开关
 */
object DebugManager : GameDebugConfig {
    var dryTest = false
    var debugBattle = false
    var debug = true

    override var unlockAll: Boolean
            by mutableStateOf(false)
    override var achieveAll: Boolean
            by mutableStateOf(false)
    override var allEvent: Boolean
            by mutableStateOf(false)
    override var easyEvent: Boolean
            by mutableStateOf(false)
    override var eventWin: Boolean
            by mutableStateOf(false)
    override var eventLose: Boolean
            by mutableStateOf(false)
    override var reputation10: Boolean
            by mutableStateOf(false)
    override var game10: Boolean
            by mutableStateOf(false)
    override var hp100: Boolean
            by mutableStateOf(false)
    override var easySkill: Boolean
            by mutableStateOf(false)
    override var unbreakable: Boolean
            by mutableStateOf(false)
    override var attack100: Boolean
            by mutableStateOf(false)
    override var defense100: Boolean
            by mutableStateOf(false)
    override var dodge100: Boolean
            by mutableStateOf(false)
    override var fatal100: Boolean
            by mutableStateOf(false)
    override var singleStep: Boolean
            by mutableStateOf(false)
    override var worldEvent: Boolean
            by mutableStateOf(false)
    override var oneCentShop: Boolean
            by mutableStateOf(false)
    override var useClientDefault: Boolean
            by mutableStateOf(false)
}