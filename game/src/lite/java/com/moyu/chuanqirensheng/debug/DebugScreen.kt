package com.moyu.chuanqirensheng.debug

import androidx.compose.foundation.background
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Checkbox
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Slider
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.datastore.KEY_GAME_LOGIN_DAY
import com.moyu.chuanqirensheng.datastore.KEY_TALENT_LEVEL
import com.moyu.chuanqirensheng.datastore.KEY_TCG_CARDS
import com.moyu.chuanqirensheng.datastore.increaseIntValueByKey
import com.moyu.chuanqirensheng.datastore.setMapObject
import com.moyu.chuanqirensheng.datastore.setObject
import com.moyu.chuanqirensheng.feature.battlepass.KEY_WAR_PASS2_UNLOCK_EVIDENCE
import com.moyu.chuanqirensheng.feature.battlepass.KEY_WAR_PASS_UNLOCK_EVIDENCE
import com.moyu.chuanqirensheng.feature.battlepass.KEY_WAR_PASS_UNLOCK_EVIDENCE2
import com.moyu.chuanqirensheng.feature.holiday.HolidayLotteryManager
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.talent.TalentManager
import com.moyu.chuanqirensheng.logic.talent.TalentManager.talents
import com.moyu.chuanqirensheng.logic.tcg.TcgManager
import com.moyu.chuanqirensheng.logic.tcg.toTcgCardsObject
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.Dialogs.debugSkillDialog
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.dialog.CommonDialog
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.bigButtonHeight
import com.moyu.chuanqirensheng.ui.theme.buttonHeight
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.ui.theme.paddingLarge
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.ui.theme.slideHeight
import com.moyu.chuanqirensheng.util.DEBUG_BATTLE
import com.moyu.chuanqirensheng.util.LOGIN_SCREEN
import com.moyu.chuanqirensheng.util.goto
import com.moyu.core.logic.enemy.DefaultAllyCreator
import com.moyu.core.logic.enemy.DefaultEnemyCreator
import com.moyu.core.model.property.Property
import com.moyu.core.model.race.getRaceTypeName
import com.moyu.core.model.role.Role
import com.moyu.core.model.sell.Award
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.isBattleSkill
import com.moyu.core.model.skill.isHeroSkill
import kotlinx.coroutines.launch
import kotlinx.serialization.builtins.serializer

val allRaceIds = (1..5).toList()

@Composable
fun DebugScreen() {
    BackPressHandler {
        DebugManager.dryTest = false
        DebugManager.debugBattle = false
        goto(LOGIN_SCREEN)
    }
    val enemySpeciesDialog = remember {
        mutableStateOf(false)
    }
    val enemySpecies = remember {
        mutableStateOf(1)
    }
    val enemySkills = remember {
        mutableListOf<Skill>()
    }
    val enemyNum = remember {
        mutableStateOf(1)
    }
    val enemyNumDialog = remember {
        mutableStateOf(false)
    }
    val playerSkills = remember {
        mutableListOf<Skill>()
    }
    GameBackground(title = "调试页") {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
            modifier = Modifier
                .paint(
                    painterResource(id = R.drawable.common_page_frame),
                    contentScale = ContentScale.FillBounds
                )
                .padding(top = paddingLarge)
                .verticalScroll(rememberScrollState())
        ) {
            Row {
                Column(
                    horizontalAlignment = Alignment.Start,
                    modifier = Modifier
                        .weight(1f)
                        .padding(paddingLarge)
                ) {
                    DebugItem(
                        DebugManager.easySkill, "技能概率100%"
                    ) {
                        DebugManager.easySkill = it
                    }
                    DebugItem(
                        DebugManager.dodge100, "格挡率100%"
                    ) {
                        DebugManager.dodge100 = it
                    }
                    DebugItem(
                        DebugManager.fatal100, "暴击率100%"
                    ) {
                        DebugManager.fatal100 = it
                    }
                    DebugItem(
                        DebugManager.attack100, "攻击100倍"
                    ) {
                        DebugManager.attack100 = it
                    }
                    DebugItem(
                        DebugManager.defense100, "防御100倍"
                    ) {
                        DebugManager.defense100 = it
                    }
                    DebugItem(
                        DebugManager.hp100, "100倍血量"
                    ) {
                        DebugManager.hp100 = it
                    }
                    DebugItem(
                        DebugManager.singleStep, "单步调试"
                    ) {
                        DebugManager.singleStep = it
                    }
                    DebugItem(
                        DebugManager.unbreakable, "无敌秒杀"
                    ) {
                        DebugManager.unbreakable = it
                    }
                    DebugItem(
                        DebugManager.useClientDefault, "默认AB配置"
                    ) {
                        DebugManager.useClientDefault = it
                    }
                }
                Column(
                    horizontalAlignment = Alignment.Start,
                    modifier = Modifier
                        .weight(1f)
                        .padding(paddingLarge)
                ) {
                    DebugItem(
                        DebugManager.unlockAll, "解锁全部(排行榜上传)"
                    ) {
                        DebugManager.unlockAll = it
                    }
                    DebugItem(
                        DebugManager.oneCentShop, "商品1分钱"
                    ) {
                        DebugManager.oneCentShop = it
                    }
                    DebugItem(
                        DebugManager.easyEvent, "事件条件满足"
                    ) {
                        DebugManager.easyEvent = it
                    }
                    DebugItem(
                        DebugManager.eventWin, "事件必定胜利"
                    ) {
                        DebugManager.eventWin = it
                    }
                    DebugItem(
                        DebugManager.eventLose, "事件必定失败"
                    ) {
                        DebugManager.eventLose = it
                    }
                    DebugItem(
                        DebugManager.allEvent, "显示所有事件"
                    ) {
                        DebugManager.allEvent = it
                    }
                    DebugItem(
                        DebugManager.worldEvent, "世界事件固定在第20年"
                    ) {
                        DebugManager.worldEvent = it
                    }
                    DebugItem(
                        DebugManager.reputation10, "10x声望特产国家属性资源"
                    ) {
                        DebugManager.reputation10 = it
                    }
                    DebugItem(
                        DebugManager.game10, "突破每天50局游戏限制"
                    ) {
                        DebugManager.game10 = it
                    }
                }
            }
            Row {
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            val allies = repo.gameCore.getAllyPool().filter {
                                it.star == 5
                            }
                            repo.allyManager.gain(allies)
                        }
                        "已获得".toast()
                    }) {
                    Text(
                        text = "获得所有5星军团卡", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(paddingLarge))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            val skills = repo.gameCore.getSkillPool().filter {
                                it.isBattleSkill() && it.level == 5
                            }
                            repo.skillManager.gain(skills)
                        }
                        "已获得".toast()
                    }) {
                    Text(
                        text = "获得所有5星战术卡", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(paddingLarge))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            val skills = repo.gameCore.getSkillPool().filter {
                                it.isHeroSkill() && it.level == 5
                            }.map { it.initYear() }
                            repo.heroManager.gain(skills)
                        }
                        "已获得".toast()
                    }) {
                    Text(
                        text = "获得所有5星英雄卡", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(paddingLarge))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            repo.skillManager.data.clear()
                            repo.skillManager.save()
                            repo.heroManager.data.clear()
                            repo.heroManager.save()
                            repo.allyManager.data.clear()
                            repo.allyManager.save()
                        }
                        "已删除".toast()
                    }) {
                    Text(
                        text = "删除所有技能史诗人物军团卡", style = MaterialTheme.typography.h4
                    )
                }
            }
            Spacer(modifier = Modifier.size(paddingLarge))
            Row {
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            val allies = repo.gameCore.getAllyPool().filter {
                                it.star == 0
                            }
                            repo.allyManager.gain(allies)
                        }
                        "已获得".toast()
                    }) {
                    Text(
                        text = "获得所有0星军团卡", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(paddingLarge))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            val skills = repo.gameCore.getSkillPool().filter {
                                it.isBattleSkill() && it.level == 0
                            }
                            repo.skillManager.gain(skills)
                        }
                        "已获得".toast()
                    }) {
                    Text(
                        text = "获得所有0星战术卡", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(paddingLarge))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            GameApp.globalScope.launch {
                                val skills = repo.gameCore.getSkillPool().filter {
                                    it.isHeroSkill() && it.level == 0
                                }.map { it.initYear() }
                                repo.heroManager.gain(skills)
                            }
                            "已获得".toast()
                        }
                        "已获得".toast()
                    }) {
                    Text(
                        text = "获得所有0星史诗人物卡", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(paddingLarge))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            repo.skillManager.data.filter { it.level == 0 }.forEach {
                                repo.skillManager.data.remove(it)
                            }
                            repo.skillManager.save()
                            repo.heroManager.data.filter { it.level == 0 }.forEach {
                                repo.heroManager.data.remove(it)
                            }
                            repo.heroManager.save()
                            repo.allyManager.data.filter { it.star == 0 }.forEach {
                                repo.allyManager.data.remove(it)
                            }
                            repo.allyManager.save()
                        }
                        "已删除".toast()
                    }) {
                    Text(
                        text = "删除0星技能史诗人物军团卡", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(paddingLarge))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            TalentManager.talents.clear()
                            repo.gameCore.getTalentPool().distinctBy { it.mainId }.forEach {
                                TalentManager.talents[it.mainId] = it.levelLimit
                            }
                            setMapObject(KEY_TALENT_LEVEL, talents, Int.serializer(), Int.serializer())
                        }
                        "已满级".toast()
                    }) {
                    Text(
                        text = "满级天赋", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(paddingLarge))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            TalentManager.talents.clear()
                            setMapObject(KEY_TALENT_LEVEL, talents, Int.serializer(), Int.serializer())
                        }
                        "已清除".toast()
                    }) {
                    Text(
                        text = "清除天赋", style = MaterialTheme.typography.h4
                    )
                }
            }
            Spacer(modifier = Modifier.size(paddingLarge))
            Row(modifier = Modifier.horizontalScroll(rememberScrollState())) {
                repeat(20) { star ->
                    EffectButton(modifier = Modifier
                        .height(bigButtonHeight)
                        .background(B50),
                        onClick = {
                            GameApp.globalScope.launch {
                                increaseIntValueByKey(
                                    KEY_GAME_LOGIN_DAY,
                                    star + 1
                                )
                            }
                            "已获得".toast()
                        }) {
                        Text(
                            text = "登录${star + 1}天", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(paddingSmall))
                }
            }
            Spacer(modifier = Modifier.size(padding8))
            val listState = rememberLazyListState()
            val targetItem = TowerManager.maxLevel.value
            // Scroll to the target item
            LaunchedEffect(targetItem) {
                listState.animateScrollToItem(targetItem)
            }
            LazyRow(state = listState) {
                items(2000) { star ->
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .background(B50)
                            .padding(end = padding4), // 用右边距替代 Spacer
                        onClick = { TowerManager.maxLevel.value = star }
                    ) {
                        Text(
                            text = "爬塔${star}层",
                            style = MaterialTheme.typography.h4
                        )
                    }
                }
            }
            Spacer(modifier = Modifier.size(paddingLarge))
            Row {
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            TcgManager.tcgCards.clear()
                            TcgManager.tcgCards.addAll(repo.gameCore.getTcgCardPool().map {
                                it.copy(count = 1)
                            })
                            setObject(
                                KEY_TCG_CARDS,
                                TcgManager.tcgCards.toTcgCardsObject(),
                            )
                        }
                        "已获得".toast()
                    }) {
                    Text(
                        text = "获得全部tcg卡牌", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(paddingLarge))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            TcgManager.tcgCards.clear()
                            setObject(
                                KEY_TCG_CARDS,
                                TcgManager.tcgCards.toTcgCardsObject(),
                            )
                        }
                        "已清除".toast()
                    }) {
                    Text(
                        text = "删除所有tcg卡牌", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(paddingLarge))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            Dialogs.awardDialog.value = Award(holidayMoney = 500).apply {
                                AwardManager.gainAward(this)
                            }
                        }
                    }) {
                    Text(
                        text = "圣诞币500", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(paddingLarge))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            HolidayLotteryManager.holidaySpinTotal.value += 10
                            HolidayLotteryManager.uploadHolidayRank()
                        }
                    }) {
                    Text(
                        text = "转盘数+10", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(paddingLarge))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            HolidayLotteryManager.holidaySpinTotal.value -= 10
                            HolidayLotteryManager.uploadHolidayRank()
                        }
                    }) {
                    Text(
                        text = "转盘数-10", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(paddingLarge))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            HolidayLotteryManager.holidaySpinTotal.value += 3
                            HolidayLotteryManager.uploadHolidayRank()
                        }
                    }) {
                    Text(
                        text = "转盘数+3", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(paddingLarge))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            HolidayLotteryManager.holidaySpinTotal.value -= 3
                            HolidayLotteryManager.uploadHolidayRank()
                        }
                    }) {
                    Text(
                        text = "转盘数-3", style = MaterialTheme.typography.h4
                    )
                }
            }
            Spacer(modifier = Modifier.size(paddingLarge))
            Row {
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            Dialogs.awardDialog.value = Award(diamond = 10000).apply {
                                AwardManager.gainAward(this)
                            }
                        }
                    }) {
                    Text(
                        text = "荣誉点10000", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(paddingLarge))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            Dialogs.awardDialog.value = Award(diamond = 500).apply {
                                AwardManager.gainAward(this)
                            }
                        }
                    }) {
                    Text(
                        text = "荣誉点500", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(paddingLarge))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            Dialogs.awardDialog.value = Award(key = 3000).apply {
                                AwardManager.gainAward(this)
                            }
                        }
                    }) {
                    Text(
                        text = "钻石3000", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(paddingLarge))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            AwardManager.lotteryMoney.value += 20000
                        }
                    }) {
                    Text(
                        text = "2w转盘币",
                        style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(paddingLarge))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            AwardManager.pvpDiamond.value += 9999
                        }
                    }) {
                    Text(
                        text = "9999竞技币",
                        style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(paddingLarge))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            Dialogs.awardDialog.value = Award(unlockList = listOf(
                                KEY_WAR_PASS_UNLOCK_EVIDENCE
                            )).apply {
                                AwardManager.gainAward(this)
                            }
                        }
                    }) {
                    Text(
                        text = "解锁战令第一季", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(paddingLarge))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            Dialogs.awardDialog.value = Award(unlockList = listOf(
                                KEY_WAR_PASS_UNLOCK_EVIDENCE2
                            )).apply {
                                AwardManager.gainAward(this)
                            }
                        }
                    }) {
                    Text(
                        text = "解锁战令第2季", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding16))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            Dialogs.awardDialog.value =
                                Award(unlockList = listOf(KEY_WAR_PASS2_UNLOCK_EVIDENCE)).apply {
                                    AwardManager.gainAward(this)
                                }
                        }
                    }) {
                    Text(
                        text = "解锁帝国崛起", style = MaterialTheme.typography.h4
                    )
                }
            }
            Spacer(modifier = Modifier.size(paddingLarge))
            Row {
                Spacer(modifier = Modifier.size(padding16))
                val electric = remember {
                    mutableStateOf(0f)
                }
                Column(
                    modifier = Modifier
                        .height(bigButtonHeight)
                        .weight(2f),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        GameButton(
                            text = if (electric.value == 0f) "特权值" else "${electric.value}",
                            buttonSize = ButtonSize.MediumMinus
                        ) {
                            GameApp.globalScope.launch {
                                Dialogs.awardDialog.value =
                                    Award(electric = electric.value.toInt()).apply {
                                        AwardManager.gainAward(this)
                                    }
                            }
                        }
                    }

                    Slider(
                        modifier = Modifier
                            .height(slideHeight)
                            .fillMaxWidth(),
                        steps = 10,
                        value = electric.value,
                        onValueChange = { electric.value = it.toInt().toFloat() },
                        valueRange = 0f..2000f,
                    )
                }
                Spacer(modifier = Modifier.size(padding16))
                val warpass = remember {
                    mutableStateOf(0f)
                }
                Column(
                    modifier = Modifier
                        .height(bigButtonHeight)
                        .weight(2f),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        GameButton(
                            text = if (warpass.value == 0f) "通行证" else "${warpass.value}",
                            buttonSize = ButtonSize.MediumMinus
                        ) {
                            GameApp.globalScope.launch {
                                Dialogs.awardDialog.value =
                                    Award(warPass = warpass.value.toInt()).apply {
                                        AwardManager.gainAward(this)
                                    }
                            }
                        }
                    }
                    Slider(
                        modifier = Modifier
                            .height(slideHeight)
                            .fillMaxWidth(),
                        steps = 10,
                        value = warpass.value,
                        onValueChange = { warpass.value = it.toInt().toFloat() },
                        valueRange = 0f..100f,
                    )
                }
                val warpass2 = remember {
                    mutableStateOf(0f)
                }
                Column(
                    modifier = Modifier
                        .height(bigButtonHeight)
                        .weight(2f),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        GameButton(
                            text = if (warpass2.value == 0f) "通行证2" else "${warpass2.value}",
                            buttonSize = ButtonSize.MediumMinus
                        ) {
                            GameApp.globalScope.launch {
                                Dialogs.awardDialog.value =
                                    Award(warPass2 = warpass2.value.toInt()).apply {
                                        AwardManager.gainAward(this)
                                    }
                            }
                        }
                    }
                    Slider(
                        modifier = Modifier
                            .height(slideHeight)
                            .fillMaxWidth(),
                        steps = 10,
                        value = warpass2.value,
                        onValueChange = { warpass2.value = it.toInt().toFloat() },
                        valueRange = 0f..100f,
                    )
                }
            }
            Spacer(modifier = Modifier.size(paddingLarge))
            Row {
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        enemySkills.clear()
                        debugSkillDialog.value = {
                            enemySkills.add(it)
                        }
                    }) {
                    Text(
                        text = "敌人技能", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(paddingLarge))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        enemySpeciesDialog.value = true
                    }) {
                    Text(
                        text = "敌人种族", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(paddingLarge))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        enemyNumDialog.value = true
                    }) {
                    Text(
                        text = "双方数量", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(paddingLarge))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        playerSkills.clear()
                        debugSkillDialog.value = {
                            playerSkills.add(it)
                        }
                    }) {
                    Text(
                        text = "玩家技能", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(paddingLarge))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        DebugManager.debugBattle = true
                        val enemies = mutableListOf<Role>()
                        repeat(enemyNum.value) {
                            DefaultEnemyCreator.create(
                                repo.gameCore.getRacePool().filter {
                                    it.raceType == enemySpecies.value
                                }.first(), Property()
                            ).apply {
                                    setSkills(emptyList())
                                    if (enemies.isEmpty()) {
                                        // 设置的技能只有第一个敌人学习，不然有点乱
                                        setSkills(emptyList())
                                        enemySkills.forEach {
                                            learnSkill(it, this.roleIdentifier)
                                        }
                                        learnSkill(
                                            repo.gameCore.getSkillById(89001), this.roleIdentifier
                                        )
                                    } else {
                                        // 其他只有普攻
                                        if (enemies.size == 1) {
                                            learnSkill(
                                                repo.gameCore.getSkillById(89002), this.roleIdentifier
                                            )
                                        } else {
                                            learnSkill(
                                                repo.gameCore.getSkillById(89003), this.roleIdentifier
                                            )
                                        }
                                    }
                                    enemies.add(this)
                                }
                        }
                        val allies = mutableListOf<Role>()
                        repeat(enemyNum.value) {
                            DefaultAllyCreator.create(
                                repo.gameCore.getRacePool().first(), Property()
                            ).apply {
                                    setSkills(emptyList())
                                    if (allies.isEmpty()) {
                                        // 设置的技能只有第一个敌人学习，不然有点乱
                                        playerSkills.forEach {
                                            learnSkill(it, this.roleIdentifier)
                                        }
                                        learnSkill(
                                            repo.gameCore.getSkillById(89001), this.roleIdentifier
                                        )
                                    } else {
                                        // 其他只有普攻
                                        if (allies.size == 1) {
                                            learnSkill(
                                                repo.gameCore.getSkillById(89004), this.roleIdentifier
                                            )
                                        } else {
                                            learnSkill(
                                                repo.gameCore.getSkillById(89005), this.roleIdentifier
                                            )
                                        }
                                    }
                                    allies.add(this)
                                }
                        }
                        repo.enemies.clear()
                        repo.enemies.addAll(enemies)
                        repo.allies.clear()
                        repo.allies.addAll(allies)
                        repo.startBattle()
                        goto(DEBUG_BATTLE)
                    }) {
                    Text(
                        text = "开始战斗", style = MaterialTheme.typography.h4
                    )
                }
            }
        }
    }
    if (enemySpeciesDialog.value) {
        CommonDialog("敌人种族", onDismissRequest = {
            enemySpeciesDialog.value = false
        }) {
            LazyVerticalGrid(
                horizontalArrangement = Arrangement.SpaceEvenly,
                modifier = Modifier.fillMaxWidth(),
                columns = GridCells.Fixed(5)
            ) {
                items(allRaceIds.size) { index ->
                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                        Text(
                            text = (index + 1).getRaceTypeName(),
                            style = MaterialTheme.typography.h4
                        )
                        GameButton(text = "选择") {
                            enemySpecies.value = allRaceIds[index]
                            enemySpeciesDialog.value = false
                        }
                    }
                }
            }
        }
    }
    if (enemyNumDialog.value) {
        CommonDialog("敌人数量", onDismissRequest = {
            enemyNumDialog.value = false
        }) {
            Row(
                horizontalArrangement = Arrangement.SpaceEvenly,
                modifier = Modifier.fillMaxWidth(),
            ) {
                repeat(3) { index ->
                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                        Text(
                            text = (index + 1).toString(), style = MaterialTheme.typography.h4
                        )
                        GameButton(text = "选择") {
                            enemyNum.value = index + 1
                            enemyNumDialog.value = false
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun DebugItem(checked: Boolean, content: String, onChecked: suspend (Boolean) -> Unit) {
    Row(verticalAlignment = Alignment.CenterVertically) {
        Checkbox(
            checked = checked, onCheckedChange = {
                GameApp.globalScope.launch {
                    onChecked(it)
                }
            }, modifier = Modifier.size(
                buttonHeight
            )
        )
        Spacer(modifier = Modifier.size(paddingSmall))
        Text(
            text = content.trimIndent(), style = MaterialTheme.typography.h4
        )
    }
}
