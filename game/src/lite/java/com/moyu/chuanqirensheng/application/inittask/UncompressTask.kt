package com.moyu.chuanqirensheng.application.inittask

import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.config.compressConfigs
import com.moyu.chuanqirensheng.util.getVersions
import com.moyu.core.configPath
import com.moyu.job.JobContent
import java.io.File

/**
 * 在lite版本其实是compress和加密
 * 目标路径是filesDir+impossible.mp3
 */
class UncompressTask : JobContent<GameApp> {
    override suspend fun execute(context: GameApp) {
        val path = GameApp.instance.filesDir
        val compressDir = File(path.absolutePath + File.separator + configPath)
        if (compressDir.exists()) {
            compressDir.delete()
        }
        compressConfigs(compressDir.absolutePath, getVersions())
    }
}