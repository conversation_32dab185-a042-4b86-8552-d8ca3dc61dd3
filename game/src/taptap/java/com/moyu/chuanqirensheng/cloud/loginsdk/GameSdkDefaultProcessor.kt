package com.moyu.chuanqirensheng.cloud.loginsdk

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.fragment.app.FragmentActivity
import com.moyu.chuanqirensheng.BuildConfig
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.cloud.privacy.PrivacyManager
import com.moyu.chuanqirensheng.datastore.KEY_OBJECT_ID
import com.moyu.chuanqirensheng.datastore.KEY_VERIFIED
import com.moyu.chuanqirensheng.datastore.getStringFlowByKey
import com.moyu.chuanqirensheng.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.datastore.setStringValueByKey
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.util.getVersion
import com.moyu.chuanqirensheng.util.killSelf
import com.moyu.core.tapClientId
import com.moyu.core.tapClientToken
import com.moyu.core.tapServerUrl
import com.tapsdk.antiaddiction.Config
import com.tapsdk.antiaddiction.constants.Constants
import com.tapsdk.antiaddictionui.AntiAddictionUIKit
import com.tapsdk.bootstrap.Callback
import com.tapsdk.bootstrap.TapBootstrap
import com.tapsdk.bootstrap.account.TDSUser
import com.tapsdk.bootstrap.exceptions.TapError
import com.tds.common.entities.TapConfig
import com.tds.common.entities.TapDBConfig
import com.tds.common.models.TapRegionType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber

class GameSdkDefaultProcessor : GameSdkProcessor {
    private val antiAddictionContent = mutableStateOf("")
    private val loginStatus = mutableStateOf(false)
    private val avatar = mutableStateOf<String?>(null)
    private val userName = mutableStateOf<String?>(null)
    private val antiAddictionStatus = mutableStateOf(false)
    private val objectId = mutableStateOf<String?>(null)

    private var isAntiAddict = false
    private var init = false

    override fun initGameSdk() {
        antiAddictionStatus.value = false
    }
    // 初始化SDK，判断是否有登录态，没有则调用taptap登录，
    // 登录后校验防沉迷，若返回登录成功，则正常显示菜单，否则弹窗提示错误原因，并退出
    override fun initSDK(activity: FragmentActivity) {
        if (init) return
        init = true

        val tapDBConfig = TapDBConfig()
        tapDBConfig.isEnable = true //是否开启 TapDB
        tapDBConfig.channel = BuildConfig.FLAVOR //分包渠道，长度不大于 256
        tapDBConfig.gameVersion = getVersion() //游戏版本，为空时，自动获取游戏安装包的版本，长度不大于 256

        // 防止重复初始化
        val tdsConfig = TapConfig.Builder()
            .withAppContext(activity) // Context 上下文
            .withClientId(tapClientId) // 开发者中心对应 Client ID
            .withClientToken(tapClientToken) // 开发者中心对应 Client Token
            .withServerUrl(tapServerUrl) // 开发者中心 > 你的游戏 > 游戏服务 > 云服务 > 数据存储 > 服务设置 > 自定义域名 绑定域名
            .withRegionType(TapRegionType.CN) // TapRegionType.CN: 中国大陆  TapRegionType.IO: 其他国家或地区
            .withTapDBConfig(tapDBConfig)
            .build()
        TapBootstrap.init(activity, tdsConfig)
        try {
            TDSUser.currentUser()?.let {
                dealAfterLogin(it["nickname"] as String, it.objectId, it["avatar"] as String, activity)
            } ?: run {
                // force login
                if (!antiAddictionStatus.value) {
                    login(activity)
                }
            }
        } catch (e: Exception) {
            ("taptap登录异常：" + e.message).toast()
        }
    }

    override fun login(activity: FragmentActivity) {
        try {
            TDSUser.loginWithTapTap(activity, object : Callback<TDSUser> {
                override fun onSuccess(resultUser: TDSUser) {
                    "登录成功".toast()
                    dealAfterLogin(resultUser["nickname"] as String, resultUser.objectId, resultUser["avatar"] as String, activity)
                }

                override fun onFail(error: TapError) {
                    "登录失败，原因：${error.message}".toast()
                }
            }, "public_profile")
        } catch (e: Exception) {
            ("taptap登录异常：" + e.message).toast()
        }
    }

    override fun antiAddictPassed(): MutableState<Boolean> {
        return antiAddictionStatus
    }

    override fun hasLogin(): Boolean {
        return loginStatus.value
    }

    override fun getAvatarUrl(): String? {
        return avatar.value
    }

    override fun getUserName(): String? {
        return userName.value
    }

    override fun getObjectId(): String? {
        return objectId.value
    }

    override fun getAntiAddictionContent(): String {
        return antiAddictionContent.value
    }

    override fun dealAfterLogin(name: String, id: String, avatarUrl: String, activity: FragmentActivity) {
        loginStatus.value = true
        userName.value =  name
        avatar.value = avatarUrl
        objectId.value = id
        checkAntiAddiction(activity)
        GameApp.globalScope.launch {
            val oldAccount = getStringFlowByKey(KEY_OBJECT_ID)
            if (oldAccount.isEmpty()) {
                setStringValueByKey(KEY_OBJECT_ID, objectId.value?:"")
            } else if (oldAccount!= objectId.value) {
                "不支持账号切换，请卸载重装".toast()
                delay(2000)
                killSelf()
            }
        }
        GameApp.instance.tryLogin()
    }

    override fun checkAntiAddiction(activity: FragmentActivity) {
        if (antiAddictionStatus.value || PrivacyManager.antiAddictVerified) {
            return
        }
        if (isAntiAddict) return
        isAntiAddict = true
        val config = Config.Builder().withClientId(tapClientId).enableTapLogin(true).build()
        try {
            AntiAddictionUIKit.init(activity, config) { code, e ->
                Timber.d("code = [${code}], _ = [${e}]")
                // 根据 code 不同提示玩家不同信息，详见下面的说明
                if (code == Constants.ANTI_ADDICTION_CALLBACK_CODE.LOGIN_SUCCESS) {
                    antiAddictionContent.value = "已通过防沉迷认证"
                    antiAddictionStatus.value = true
                    setBooleanValueByKey(KEY_VERIFIED, true)
                    // 开始计时
                    AntiAddictionUIKit.enterGame()
                } else {
                    "实名认证失败，错误代码：${code}".toast()
                    when (code) {
                        Constants.ANTI_ADDICTION_CALLBACK_CODE.PERIOD_RESTRICT -> {
                            antiAddictionContent.value = "未成年玩家当前无法进行游戏"
                            antiAddictionStatus.value = false
                        }
                        Constants.ANTI_ADDICTION_CALLBACK_CODE.DURATION_LIMIT -> {
                            antiAddictionContent.value = "当前时间段无法进行游戏"
                            antiAddictionStatus.value = false
                        }
                        Constants.ANTI_ADDICTION_CALLBACK_CODE.REAL_NAME_STOP -> {
                            antiAddictionContent.value = "实名认证过程被中断"
                            antiAddictionStatus.value = false
                        }
                        Constants.ANTI_ADDICTION_CALLBACK_CODE.EXITED -> {
                            antiAddictionContent.value = "退出账号，验证失败"
                            antiAddictionStatus.value = false
                        }
                    }
                }
                var remainingTimeInSeconds = AntiAddictionUIKit.getRemainingTime() // 单位：秒
                if (remainingTimeInSeconds > 0 && !isAdult()) {
                    "剩余游戏时间：${remainingTimeInSeconds / 60}分钟".toast()
                    GameApp.globalScope.launch {
                        while (true) {
                            if (remainingTimeInSeconds <= 300) {
                                "游戏剩余时间即将结束，请尽快保存游戏".toast()
                                delay(remainingTimeInSeconds * 1000L)
                                killSelf()
                                break
                            } else {
                                delay(60*5000L)
                                remainingTimeInSeconds -= 300
                                "剩余游戏时间：${remainingTimeInSeconds / 60}分钟".toast()
                            }
                        }
                    }
                }
            }
            GameApp.globalScope.launch(Dispatchers.Main) {
                while (TDSUser.getCurrentUser() == null) {
                    delay(1000)
                }
                AntiAddictionUIKit.startup(activity, TDSUser.currentUser().objectId, true)
            }
            isAntiAddict = false
        } catch (e: Exception) {
            isAntiAddict = false
            ("防沉迷验证异常：" + e.message).toast()
            GameApp.globalScope.launch {
                setBooleanValueByKey(KEY_VERIFIED, true)
            }
            PrivacyManager.antiAddictVerified = true
        }
    }

    override fun isAgeIn8To16(): Boolean {
        return try {
            AntiAddictionUIKit.getAgeRange() in 8..16
        } catch (e: Exception) {
            false
        }
    }

    override fun isAgeUnder8(): Boolean {
        return try {
            AntiAddictionUIKit.getAgeRange() in 1..8
        } catch (e: Exception) {
            false
        }
    }

    fun isAdult(): Boolean {
        return try {
            AntiAddictionUIKit.getAgeRange() >= 16
        } catch (e: Exception) {
            true
        }
    }
}