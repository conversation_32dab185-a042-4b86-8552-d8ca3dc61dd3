package com.moyu.chuanqirensheng.ad

import android.app.Activity
import android.os.Bundle
import android.widget.Toast
import com.bytedance.sdk.openadsdk.AdSlot
import com.bytedance.sdk.openadsdk.TTAdConstant
import com.bytedance.sdk.openadsdk.TTAdLoadType
import com.bytedance.sdk.openadsdk.TTAdNative
import com.bytedance.sdk.openadsdk.TTAdSdk
import com.bytedance.sdk.openadsdk.TTAppDownloadListener
import com.bytedance.sdk.openadsdk.TTRewardVideoAd
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.lucky.AdManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.music.MusicManager.muteByAd
import com.moyu.chuanqirensheng.repository.toast
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber

object TTAdPlayer: AdInterface {
    var isPlaying = false

    override fun playAd(reportKey: String, callback: suspend () -> Unit) {
        if (!GameApp.adInit) {
            "穿山甲广告SDK初始化失败，无法播放广告，请反馈".toast()
        } else {
            if (isPlaying) {
                "检测到广告正在播放中，如果是异常情况，尝试杀进程重进。".toast()
                return
            }
            isPlaying = true
            loadAdInternal(callback)
        }
    }

    override fun preload(adId: String) {

    }

    fun release() {
        isPlaying = false
    }
}

private fun loadAdInternal(callback: suspend () -> Unit) {
    val ttAdNative = TTAdSdk.getAdManager().createAdNative(GameApp.instance.activity)
    val adSlot = AdSlot.Builder()
        .setCodeId(GameApp.instance.resources.getString(R.string.csjCodeId))
        .setSupportDeepLink(true)
        //模板广告需要设置期望个性化模板广告的大小,单位dp,激励视频场景，只要设置的值大于0即可
        //且仅是模板渲染的代码位ID使用，非模板渲染代码位切勿使用
        .setExpressViewAcceptedSize(500f, 500f)
        .setOrientation(TTAdConstant.VERTICAL) //必填参数，期望视频的播放方向：TTAdConstant.HORIZONTAL 或 TTAdConstant.VERTICAL
        .setAdLoadType(TTAdLoadType.LOAD)//推荐使用，用于标注此次的广告请求用途为预加载（当做缓存）还是实时加载，方便后续为开发者优化相关策略
        .build()
    ttAdNative.loadRewardVideoAd(adSlot, object : TTAdNative.RewardVideoAdListener {
        override fun onError(p0: Int, p1: String?) {
            Timber.e("TTAdSdk#onError($p0,$p1)")
            "很抱歉，广告内部错误($p0,$p1)，请及时反馈给游戏开发。".toast()
            TTAdPlayer.isPlaying = false
        }

        override fun onRewardVideoAdLoad(p0: TTRewardVideoAd?) {
            Timber.e("TTAdSdk#onRewardVideoAdLoad(${p0?.rewardVideoAdType})")
        }

        @Deprecated("Deprecated in Java")
        override fun onRewardVideoCached() {
            Timber.e("TTAdSdk#onRewardVideoCached")
        }

        override fun onRewardVideoCached(ad: TTRewardVideoAd?) {
            Timber.e("TTAdSdk#onRewardVideoCached($ad)")
            GameApp.globalScope.launch {
                playAdInternal(GameApp.instance.activity, ad, callback)
            }
        }
    })
}

private suspend fun playAdInternal(
    activity: Activity,
    ad: TTRewardVideoAd?,
    callback: suspend () -> Unit
) {
    withContext(Dispatchers.Main) {
        var verified = false
        ad?.setDownloadListener(object : TTAppDownloadListener {
            override fun onIdle() {
            }

            override fun onDownloadActive(p0: Long, p1: Long, p2: String?, p3: String?) {
                Toast.makeText(GameApp.instance, "开始下载广告应用，请关注网络流量", Toast.LENGTH_LONG).show()
            }

            override fun onDownloadPaused(p0: Long, p1: Long, p2: String?, p3: String?) {
            }

            override fun onDownloadFailed(p0: Long, p1: Long, p2: String?, p3: String?) {
            }

            override fun onDownloadFinished(p0: Long, p1: String?, p2: String?) {
                Toast.makeText(GameApp.instance, "广告应用下载完成，请关注网络流量", Toast.LENGTH_LONG).show()
            }

            override fun onInstalled(p0: String?, p1: String?) {
                Toast.makeText(GameApp.instance, "广告应用安装完成，请关注新增应用", Toast.LENGTH_LONG).show()
            }
        })
        ad?.setRewardAdInteractionListener(object :
            TTRewardVideoAd.RewardAdInteractionListener {
            //广告的下载bar点击回调
            override fun onAdVideoBarClick() {
                Timber.e("TTAdSdk#onAdVideoBarClick")
                Toast.makeText(GameApp.instance, "您点击了下载视频按钮", Toast.LENGTH_LONG).show()
            }

            //视频广告关闭回调
            override fun onAdClose() {
                Timber.e("TTAdSdk#onAdClose")
                if (verified) {
                    GameApp.globalScope.launch(Dispatchers.Main) {
                        AwardManager.adNum.value += 1
                        AwardManager.adMoney.value += AdManager.getRandomAdMoney()
                        callback.invoke()
                    }
                } else {
                    "很抱歉，广告验证失败，请及时反馈给游戏开发。".toast()
                }
                muteByAd(false)
                TTAdPlayer.release()
            }

            //视频播放完成回调
            override fun onVideoComplete() {
                Timber.e("TTAdSdk#onVideoComplete")
                TTAdPlayer.release()
            }

            //视频广告播放错误回调
            override fun onVideoError() {
                Timber.e("TTAdSdk#onVideoError")
                "很抱歉，广告内部错误，请及时反馈给游戏开发。".toast()
            }

            //视频播放完成后，奖励验证回调，rewardVerify：是否有效，rewardAmount：奖励梳理，rewardName：奖励名称，code：错误码，msg：错误信息
            @Deprecated("Deprecated in Java")
            override fun onRewardVerify(
                rewardVerify: Boolean,
                rewardAmount: Int,
                rewardName: String,
                code: Int,
                msg: String
            ) {
                Timber.e("TTAdSdk#onRewardVerify($rewardVerify,$rewardAmount,$rewardName,$code,$msg)")
            }

            override fun onRewardArrived(p0: Boolean, p1: Int, p2: Bundle?) {
                Timber.e("TTAdSdk#onRewardArrived($p0,$p1)")
                verified = p0
            }

            //视频广告跳过回调
            override fun onSkippedVideo() {
                Timber.e("TTAdSdk#onSkippedVideo")
            }

            //视频广告展示回调
            override fun onAdShow() {
                Timber.e("TTAdSdk#onAdShow")
                Toast.makeText(GameApp.instance, "穿山甲广告加载成功", Toast.LENGTH_LONG).show()
                muteByAd(true)
            }
        })
        ad?.showRewardVideoAd(activity, TTAdConstant.RitScenes.CUSTOMIZE_SCENES, "get_more")
    }
}