package com.moyu.chuanqirensheng.cloud.bill

import RetrofitBillModel.checkPayOk
import RetrofitBillModel.getPrepay
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.api.CommonResult
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.datastore.KEY_PAY_DATA
import com.moyu.chuanqirensheng.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.datastore.getListObject
import com.moyu.chuanqirensheng.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.datastore.setListObject
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.util.AESUtil.getPrivateKeyFromAssets
import com.moyu.chuanqirensheng.util.AESUtil.sha256AndBase64
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.core.APPID
import com.moyu.core.merchantId
import com.moyu.core.model.sell.Sell
import com.moyu.core.wcMusic
import com.tencent.mm.opensdk.modelpay.PayReq
import com.tencent.mm.opensdk.openapi.IWXAPI
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.uuid.UUID
import kotlinx.uuid.generateUUID


object BillingManager {
    var api: IWXAPI? = null
    val payClientDataList = mutableStateListOf<PayClientData>()

    val isPaying = mutableStateOf(false)

    init {
        getListObject<PayClientData>(KEY_PAY_DATA).takeIf { it.isNotEmpty() }?.let {
            payClientDataList.addAll(it)
        }
    }

    //initiate purchase on consume button click
    fun consume(productId: String) {

    }

    fun queryAsync() {

    }

    suspend fun prepay(sell: Sell, award: () -> Unit) {
        if (isPaying.value) {
            GameApp.instance.getWrapString(R.string.is_paying).toast()
            return
        }
        if (GameApp.instance.isAgeUnder8()) {
            GameApp.instance.getWrapString(R.string.child_pay_tips).toast()
            return
        }

        isPaying.value = true
        GameApp.globalScope.launch(Dispatchers.IO) {
            async {
                // 防止玩家快速点击，每两秒最多下单1次
                delay(2000)
                isPaying.value = false
            }
        }
        val realMoney = if (DebugManager.oneCentShop) 1 else sell.price * 100
        val userId = GameApp.instance.getObjectId()!!
        getPrepay(userId, sell.name, realMoney)?.copy(award = award)
            ?.let { payClientData ->
                payClientDataList.add(payClientData)
                setListObject(KEY_PAY_DATA, payClientDataList)
                pay(payClientData)
            } ?: GameApp.instance.getWrapString(R.string.order_error).toast()
    }

    // todo 等有版号后需要删除测试代码
    suspend fun checkIfOk(payClientData: PayClientData): CommonResult {
        var repeat = 20
        while (repeat-- > 0) {
            delay(1000)
            checkPayOk(payClientData).takeIf { it.succeeded }?.let { return it }
            delay(2000)
        }
        return checkPayOk(payClientData)
    }

    fun pay(payClientData: PayClientData) {
        val request = PayReq().let { request ->
            request.appId = APPID
            request.prepayId = payClientData.prepayId
            request.partnerId = merchantId
            request.packageValue = "Sign=WXPay"
            request.timeStamp = getCurrentTime().toString()
            request.nonceStr = UUID.generateUUID().toString().take(32)
            val sdkSign = sha256AndBase64(
                request.appId + "\n" + request.timeStamp + "\n" + request.nonceStr + "\n" + request.prepayId + "\n",
                getPrivateKeyFromAssets(GameApp.instance, wcMusic)
            )
            request.sign = sdkSign
            request
        }
        api?.sendReq(request)
    }

    fun checkIfPayed(payClientData: PayClientData) {
        Dialogs.payBlockingDialog.value = GameApp.instance.getWrapString(R.string.pay_blocking_tips)
        GameApp.globalScope.launch(Dispatchers.Main) {
            checkIfOk(payClientData).let {
                if (it.succeeded) {
                    removePayClientData(payClientData)
                    Dialogs.payBlockingDialog.value = null
                    checkIfCanAward(payClientData)
                } else {
                    if (Dialogs.payBlockingDialog.value != null) {
                        it.message.toast()
                        Dialogs.alertDialog.value = CommonAlert(
                            title = GameApp.instance.getWrapString(R.string.error_order), content =
                                GameApp.instance.getWrapString(
                                    R.string.error_order_info,
                                    payClientData.orderName,
                                    payClientData.prepayId + payClientData.userId,
                                    GameApp.instance.getUserName()?: "",
                                    it.message
                                )
                        )
                    }
                    Dialogs.payBlockingDialog.value = null
                }
            }
        }
    }

    private fun checkIfCanAward(payClientData: PayClientData) {
        if (getBooleanFlowByKey(payClientData.tradeNo.reversed().replace("-", ""))) {
            return
        } else {
            setBooleanValueByKey(payClientData.tradeNo.reversed().replace("-", ""), true)
            payClientData.award()
        }
    }

    fun removePayClientData(payData: PayClientData) {
        payClientDataList.remove(payData)
        setListObject(KEY_PAY_DATA, payClientDataList)
    }
}