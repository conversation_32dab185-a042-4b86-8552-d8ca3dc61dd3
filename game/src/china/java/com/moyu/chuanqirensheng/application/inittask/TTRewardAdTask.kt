package com.moyu.chuanqirensheng.application.inittask

import com.bytedance.sdk.openadsdk.TTAdConfig
import com.bytedance.sdk.openadsdk.TTAdConstant
import com.bytedance.sdk.openadsdk.TTAdSdk
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.job.JobContent
import timber.log.Timber

/**
 * 穿山甲广告sdk初始化
 */
class TTRewardAdTask : JobContent<GameApp> {
    override suspend fun execute(context: GameApp) {
        if (!Dialogs.showPrivacyDialog.value && !Dialogs.showPermissionDialog.value) {
            // 穿山甲SDK初始化
            TTAdSdk.init(context,
                TTAdConfig.Builder()
                    .appId(GameApp.instance.resources.getString(R.string.csjAppId))
                    .useTextureView(true) //默认使用SurfaceView播放视频广告,当有SurfaceView冲突的场景，可以使用TextureView
                    .appName(context.getString(R.string.app_name))
                    .titleBarTheme(TTAdConstant.TITLE_BAR_THEME_DARK)//落地页主题
                    .allowShowNotify(true) //是否允许sdk展示通知栏提示,若设置为false则会导致通知栏不显示下载进度
                    .debug(false) //测试阶段打开，可以通过日志排查问题，上线时去除该调用
                    .directDownloadNetworkType() //允许直接下载的网络状态集合,没有设置的网络下点击下载apk会有二次确认弹窗，弹窗中会披露应用信息
                    .supportMultiProcess(false) //是否支持多进程，true支持
                    //.httpStack(new MyOkStack3())//自定义网络库，demo中给出了okhttp3版本的样例，其余请自行开发或者咨询工作人员。
                    .build(), object : TTAdSdk.InitCallback {
                    override fun success() {
                        Timber.e("TTAdSdk Init success")
                        GameApp.adInit = true
                    }

                    override fun fail(p0: Int, p1: String?) {
                        "广告SDK初始化失败，请及时反馈 [$p0]: $p1".toast()
                        GameApp.adInit = false
                    }
                })
        }
    }
}