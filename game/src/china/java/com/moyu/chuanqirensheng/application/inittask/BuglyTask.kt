package com.moyu.chuanqirensheng.application.inittask

import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.core.buglyId
import com.moyu.job.JobContent
import com.tencent.bugly.crashreport.CrashReport

/**
 * Bugly初始化，用来上报crash
 */
class BuglyTask : JobContent<GameApp> {
    override suspend fun execute(context: GameApp) {
        if (!Dialogs.showPrivacyDialog.value && !Dialogs.showPermissionDialog.value) {
            CrashReport.initCrashReport(context.applicationContext, buglyId, false)
        }
    }
}