package com.moyu.chuanqirensheng.sub.review

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.core.content.ContextCompat
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp

fun requestInAppReview() {
    val uri: Uri =
        Uri.parse(GameApp.instance.resources.getString(R.string.main_page_url))
    val intent = Intent(Intent.ACTION_VIEW, uri)
    ContextCompat.startActivity(
        GameApp.instance.activity,
        intent,
        Bundle()
    )
}