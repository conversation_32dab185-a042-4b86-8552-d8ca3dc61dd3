package com.moyu.chuanqirensheng.sub.report


const val KEY_TRY_PLAY = "try_play"
const val KEY_BUY_AD_ITEM = "buy_ad_item"

object ReportManager {

    fun init() {
    }

    fun onLogin() {
    }

    fun onAdCompletedAF(adId: String) {
    }

    fun onPurchaseCompletedAF(purchaseId: String, amount: Double, number: Int) {

    }

    fun pk(i: Int, value: Int) {

    }

    fun battle(i: Int, i1: Int, age: Int) {

    }

    fun onShopPurchase(sellId: Int, price: Int, priceType: Int) {

    }

    fun onNewGame(i: Int) {

    }

    fun onContinueGame(i: Int) {

    }

    fun onReview(star: Int) {

    }

    fun guide(value: Int) {

    }

    fun open() {

    }

    fun onUpgradeAllyOutGame(id: Int, star: Int) {

    }

    fun onUpgradeAllyInGame(id: Int, star: Int) {

    }

    fun allAllyDie() {

    }

    fun year20() {

    }

    fun year25() {

    }

    fun year30() {

    }

    fun onGameEnd(i: Int) {

    }

    fun onStoryPack(id: Int, i: Int) {

    }

    fun onTalentUp(id: Int, level: Int, diamond: Int) {

    }

    fun onPage(route: String) {

    }
}