<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 配置APP KEY -->
    <meta-data
        android:name="4f04fb96bc"
        android:value="8c32302b-2c88-43da-8bf0-86c57716426b" />
    <!-- 配置APP版本号 -->
    <meta-data
        android:name="BUGLY_APP_VERSION"
        android:value="4.0.4" />
    <!-- 配置APP渠道号 -->
    <meta-data
        android:name="BUGLY_APP_CHANNEL"
        android:value="公测" />
    <meta-data
        android:name="BUGLY_ENABLE_DEBUG"
        android:value="false" />
    <meta-data
        android:name="PACKAGE_NAME"
        android:value="${PACKAGE_NAME}" >
    </meta-data>
    <queries>
        <package android:name="com.taptap" />
        <package android:name="com.taptap.global" />
    </queries>

    <queries>
        <package android:name="com.tencent.mm" />
    </queries>
    <application>
        <provider
            android:name="com.bytedance.sdk.openadsdk.TTFileProvider"
            android:authorities="${PACKAGE_NAME}.TTFileProvider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>
        <activity
            android:name="com.moyu.diguoshengyuan.wxapi.WXPayEntryActivity"
            android:label="@string/app_name"
            android:theme="@android:style/Theme.Translucent.NoTitleBar"
            android:exported="true"
            android:taskAffinity="${applicationId}"
            android:launchMode="singleTask">
        </activity>
        <activity
            android:name="com.moyu.diguoshengyuan.wxapi.WXEntryActivity"
            android:label="@string/app_name"
            android:theme="@android:style/Theme.Translucent.NoTitleBar"
            android:exported="true"
            android:taskAffinity="${applicationId}"
            android:launchMode="singleTask">
        </activity>
        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />
    </application>
</manifest>