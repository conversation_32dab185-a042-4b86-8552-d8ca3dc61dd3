package com.moyu.chuanqirensheng.model.record

import kotlinx.serialization.Serializable

@Serializable
data class AllRankData(
    val time: Long,
    val versionCode: Int = 0,
    val userName: String = "",
    val userId: String = "",
    val userPic: String = "",
    val tcgValue: Int = 0,
    val loseAlly: Int = 0,
    val killEnemy: Int = 0,
    val talentNum: Int = 0,
    val endingNum: Int = 0,
    val age: Int = 0,
    val rankType: Int = 0, // 1-5
    val keyNum: Int = 0,
    val diamondNum: Int = 0,
    val electric: Int = 0,
    val platformChannel: String = "taptap",
    val pvpScore: Int = 0,
    val pvpData: PvpData = PvpData(),
    val holidayNum: Int = 0,
    val towerLevel: Int = 0
)