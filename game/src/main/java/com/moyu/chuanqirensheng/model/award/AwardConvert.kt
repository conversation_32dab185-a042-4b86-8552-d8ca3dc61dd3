package com.moyu.chuanqirensheng.model.award

import com.moyu.chuanqirensheng.repository.repo
import com.moyu.core.model.sell.Award
import com.moyu.core.model.sell.toAward
import kotlinx.serialization.Serializable

@Serializable
data class AwardConvert(
    val code: String = "",
    val message: String = "",
    val heroes: List<Int> = emptyList(),
    val allies: List<Int> = emptyList(),
    val skills: List<Int> = emptyList(),
    val tcgs: List<Int> = emptyList(),
    val diamond: Int = 0,
    val pvpDiamond: Int = 0,
    val key: Int = 0,
    val warPass: Int = 0,
    val electric: Int = 0,
    val unlockList: List<Int> = emptyList(),
    val poolIds: List<Int> = emptyList(),
    val sellId: Int = 0,
    val valid: Boolean = true,
) {
    fun toAward(): Award {
        val realSkills = skills.map { skillId ->
            repo.gameCore.getSkillById(skillId)
        }
        val realHeroes = heroes.map {
            repo.gameCore.getSkillById(it)
        }
        val realAllies = allies.map {
            repo.gameCore.getAllyPool().first { ally -> ally.id == it }
        }
        val realTcgs = tcgs.map {
            repo.gameCore.getTcgCardById(it)
        }
        val poolAwards = poolIds.map {
            repo.gameCore.getPoolById(it).toAward()
        }.reduceOrNull { acc, award -> acc + award }?: Award()
        val sellAward = if (sellId != 0) {
            repo.gameCore.getSellPool().firstOrNull { it.id == sellId }?.let {
                it.toAward()
            }?: Award()
        } else Award()
        return Award(
            message = message, valid = valid, outAllies = realAllies,
            electric = electric,
            outHeroes = realHeroes, outSkills = realSkills, tcgs = realTcgs,
            diamond = diamond, pvpDiamond = pvpDiamond, key = key, unlockList = unlockList,
            sellId = sellId
        ) + poolAwards + sellAward
    }
}
