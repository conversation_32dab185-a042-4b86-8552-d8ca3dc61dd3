package com.moyu.chuanqirensheng

import android.annotation.TargetApi
import android.app.LocaleManager
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import androidx.activity.OnBackPressedDispatcher
import androidx.activity.addCallback
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.fragment.app.FragmentActivity
import androidx.navigation.NavController
import androidx.navigation.NavDestination
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.HeathTipsLayout
import com.moyu.chuanqirensheng.application.inittask.BillingTask
import com.moyu.chuanqirensheng.application.inittask.BuglyTask
import com.moyu.chuanqirensheng.application.inittask.RootCheckerTask
import com.moyu.chuanqirensheng.application.inittask.TTRewardAdTask
import com.moyu.chuanqirensheng.cloud.bill.BillingManager
import com.moyu.chuanqirensheng.cloud.bill.GoogleActivityResultHandler
import com.moyu.chuanqirensheng.cloud.bill.pay.ErrorOrderDialog
import com.moyu.chuanqirensheng.cloud.bill.pay.PayBlockDialog
import com.moyu.chuanqirensheng.datastore.KEY_NEED_SHOW_PERMISSION
import com.moyu.chuanqirensheng.datastore.KEY_NEED_SHOW_PRIVACY
import com.moyu.chuanqirensheng.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.debug.DebugAdvSkillDialog
import com.moyu.chuanqirensheng.debug.DebugBattleScreen
import com.moyu.chuanqirensheng.debug.DebugScreen
import com.moyu.chuanqirensheng.debug.DebugSkillDialog
import com.moyu.chuanqirensheng.feature.battlepass.ui.BattlePass2Screen
import com.moyu.chuanqirensheng.feature.battlepass.ui.BattlePassScreen
import com.moyu.chuanqirensheng.feature.battlepass.ui.WarPass2UnlockDialog
import com.moyu.chuanqirensheng.feature.battlepass.ui.WarPassUnlockDialog
import com.moyu.chuanqirensheng.feature.draw.ui.DrawAllScreen
import com.moyu.chuanqirensheng.feature.draw.ui.DrawResultDialog
import com.moyu.chuanqirensheng.feature.gift.ui.GiftDetailDialog
import com.moyu.chuanqirensheng.feature.holiday.ui.HolidayAllScreen
import com.moyu.chuanqirensheng.feature.lottery.ui.CheapLotteryScreen
import com.moyu.chuanqirensheng.feature.lottery.ui.ExpensiveLotteryScreen
import com.moyu.chuanqirensheng.feature.mode.isTower
import com.moyu.chuanqirensheng.feature.newTask.ui.SevenDayAllScreen
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.feature.pvp.ui.PvpBattleScreen
import com.moyu.chuanqirensheng.feature.pvp.ui.PvpChooseEnemyScreen
import com.moyu.chuanqirensheng.feature.pvp.ui.PvpEntryScreen
import com.moyu.chuanqirensheng.feature.pvp.ui.PvpQuestScreen
import com.moyu.chuanqirensheng.feature.pvp.ui.PvpRankScreen
import com.moyu.chuanqirensheng.feature.pvp.ui.PvpSellScreen
import com.moyu.chuanqirensheng.feature.sign.ui.SignScreen
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.feature.tower.ui.TowerAllScreen
import com.moyu.chuanqirensheng.feature.tower.ui.TowerBattleScreen
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.guide.GuideManager
import com.moyu.chuanqirensheng.logic.story.StoryManager
import com.moyu.chuanqirensheng.music.playerMusicByScreen
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.Dialogs.adPoolDialog
import com.moyu.chuanqirensheng.repository.Dialogs.adventureSkillDialog
import com.moyu.chuanqirensheng.repository.Dialogs.aiFaDianDialog
import com.moyu.chuanqirensheng.repository.Dialogs.alertDialog
import com.moyu.chuanqirensheng.repository.Dialogs.allyDetailDialog
import com.moyu.chuanqirensheng.repository.Dialogs.allyStarUpDialog
import com.moyu.chuanqirensheng.repository.Dialogs.battleAwardDialog
import com.moyu.chuanqirensheng.repository.Dialogs.battleSkillDialog
import com.moyu.chuanqirensheng.repository.Dialogs.buffDetailDialog
import com.moyu.chuanqirensheng.repository.Dialogs.challengeGameDialog
import com.moyu.chuanqirensheng.repository.Dialogs.debugAdvSkillDialog
import com.moyu.chuanqirensheng.repository.Dialogs.debugSkillDialog
import com.moyu.chuanqirensheng.repository.Dialogs.detailTalentDialog
import com.moyu.chuanqirensheng.repository.Dialogs.drawPoolDialog
import com.moyu.chuanqirensheng.repository.Dialogs.drawResultDialog
import com.moyu.chuanqirensheng.repository.Dialogs.endingDetailDialog
import com.moyu.chuanqirensheng.repository.Dialogs.endingDialog
import com.moyu.chuanqirensheng.repository.Dialogs.equipDialog
import com.moyu.chuanqirensheng.repository.Dialogs.errorOrderDialog
import com.moyu.chuanqirensheng.repository.Dialogs.eventDetailDialog
import com.moyu.chuanqirensheng.repository.Dialogs.eventFailDialog
import com.moyu.chuanqirensheng.repository.Dialogs.eventPassDialog
import com.moyu.chuanqirensheng.repository.Dialogs.extraInfoDialog
import com.moyu.chuanqirensheng.repository.Dialogs.fatalEnemyDialog
import com.moyu.chuanqirensheng.repository.Dialogs.gameAllyDialog
import com.moyu.chuanqirensheng.repository.Dialogs.gameLoseDialog
import com.moyu.chuanqirensheng.repository.Dialogs.gameReviewDialog
import com.moyu.chuanqirensheng.repository.Dialogs.gameWinDialog
import com.moyu.chuanqirensheng.repository.Dialogs.getKeyDialog
import com.moyu.chuanqirensheng.repository.Dialogs.giftDetailDialog
import com.moyu.chuanqirensheng.repository.Dialogs.guideDialog
import com.moyu.chuanqirensheng.repository.Dialogs.heroDetailDialog
import com.moyu.chuanqirensheng.repository.Dialogs.infoDialog
import com.moyu.chuanqirensheng.repository.Dialogs.itemsDialog
import com.moyu.chuanqirensheng.repository.Dialogs.moneyTransferDialog
import com.moyu.chuanqirensheng.repository.Dialogs.reputationDialog
import com.moyu.chuanqirensheng.repository.Dialogs.roleDetailDialog
import com.moyu.chuanqirensheng.repository.Dialogs.selectAllyToBattleDialog
import com.moyu.chuanqirensheng.repository.Dialogs.selectAllyToGameDialog
import com.moyu.chuanqirensheng.repository.Dialogs.selectHeroToGameDialog
import com.moyu.chuanqirensheng.repository.Dialogs.selectOneAllyDialog
import com.moyu.chuanqirensheng.repository.Dialogs.selectOneHeroDialog
import com.moyu.chuanqirensheng.repository.Dialogs.selectOneSkillDialog
import com.moyu.chuanqirensheng.repository.Dialogs.selectSkillToGameDialog
import com.moyu.chuanqirensheng.repository.Dialogs.shareImageDialog
import com.moyu.chuanqirensheng.repository.Dialogs.showPermissionDialog
import com.moyu.chuanqirensheng.repository.Dialogs.showPrivacyDialog
import com.moyu.chuanqirensheng.repository.Dialogs.skillDetailDialog
import com.moyu.chuanqirensheng.repository.Dialogs.statisticView
import com.moyu.chuanqirensheng.repository.Dialogs.storyDetailDialog
import com.moyu.chuanqirensheng.repository.Dialogs.storyDialog
import com.moyu.chuanqirensheng.repository.Dialogs.tcgAwardDetail
import com.moyu.chuanqirensheng.repository.Dialogs.tutorDialog
import com.moyu.chuanqirensheng.repository.Dialogs.useSaveDialog
import com.moyu.chuanqirensheng.repository.Dialogs.warPass2UnlockDialog
import com.moyu.chuanqirensheng.repository.Dialogs.warPassUnlockDialog
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.about.AboutScreen
import com.moyu.chuanqirensheng.screen.about.AboutShareRewardsScreen
import com.moyu.chuanqirensheng.screen.about.UserShareGameScreen
import com.moyu.chuanqirensheng.screen.ally.AllyDetailDialog
import com.moyu.chuanqirensheng.screen.buff.BuffDetailDialog
import com.moyu.chuanqirensheng.screen.common.GameBackColor
import com.moyu.chuanqirensheng.screen.common.GameSnackBar
import com.moyu.chuanqirensheng.screen.common.StatusBarMask
import com.moyu.chuanqirensheng.screen.dialog.AdPoolDialog
import com.moyu.chuanqirensheng.screen.dialog.AdventureSkillDialog
import com.moyu.chuanqirensheng.screen.dialog.AiFaDianDialog
import com.moyu.chuanqirensheng.screen.dialog.AllyStarUpDialog
import com.moyu.chuanqirensheng.screen.dialog.AwardDialog
import com.moyu.chuanqirensheng.screen.dialog.BattleAwardDialog
import com.moyu.chuanqirensheng.screen.dialog.BattleSkillDialog
import com.moyu.chuanqirensheng.screen.dialog.ChallengeGameDialog
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.screen.dialog.CommonAlertDialog
import com.moyu.chuanqirensheng.screen.dialog.DrawPoolDialog
import com.moyu.chuanqirensheng.screen.dialog.EndingDetailDialog
import com.moyu.chuanqirensheng.screen.dialog.EndingDialog
import com.moyu.chuanqirensheng.screen.dialog.EventDetailDialog
import com.moyu.chuanqirensheng.screen.dialog.EventFailDialog
import com.moyu.chuanqirensheng.screen.dialog.EventPassDialog
import com.moyu.chuanqirensheng.screen.dialog.ExtraInfoDialog
import com.moyu.chuanqirensheng.screen.dialog.FatalEnemyDialog
import com.moyu.chuanqirensheng.screen.dialog.GameAllyDialog
import com.moyu.chuanqirensheng.screen.dialog.GameLoseDialog
import com.moyu.chuanqirensheng.screen.dialog.GameReviewDialog
import com.moyu.chuanqirensheng.screen.dialog.GameWinDialog
import com.moyu.chuanqirensheng.screen.dialog.GetKeyDialog
import com.moyu.chuanqirensheng.screen.dialog.GuideDialog
import com.moyu.chuanqirensheng.screen.dialog.HeroDialog
import com.moyu.chuanqirensheng.screen.dialog.InfoDialog
import com.moyu.chuanqirensheng.screen.dialog.ItemsDialog
import com.moyu.chuanqirensheng.screen.dialog.LoginFailedGameDialog
import com.moyu.chuanqirensheng.screen.dialog.MoneyTransferDialog
import com.moyu.chuanqirensheng.screen.dialog.PermissionAlertDialog
import com.moyu.chuanqirensheng.screen.dialog.PrivacyAlertDialog
import com.moyu.chuanqirensheng.screen.dialog.ReputationDialog
import com.moyu.chuanqirensheng.screen.dialog.RoleDetailDialog
import com.moyu.chuanqirensheng.screen.dialog.SelectAllyToBattleDialog
import com.moyu.chuanqirensheng.screen.dialog.SelectAllyToGameDialog
import com.moyu.chuanqirensheng.screen.dialog.SelectHeroToGameDialog
import com.moyu.chuanqirensheng.screen.dialog.SelectOneAllyDialog
import com.moyu.chuanqirensheng.screen.dialog.SelectOneHeroDialog
import com.moyu.chuanqirensheng.screen.dialog.SelectOneSkillDialog
import com.moyu.chuanqirensheng.screen.dialog.SelectSkillToGameDialog
import com.moyu.chuanqirensheng.screen.dialog.ShareImageDialog
import com.moyu.chuanqirensheng.screen.dialog.SkillLevelInfoDialog
import com.moyu.chuanqirensheng.screen.dialog.StatisticDialog
import com.moyu.chuanqirensheng.screen.dialog.StoryDetailDialog
import com.moyu.chuanqirensheng.screen.dialog.StoryDialog
import com.moyu.chuanqirensheng.screen.dialog.TcgAwardDetailDialog
import com.moyu.chuanqirensheng.screen.dialog.UseCloudSaverDialog
import com.moyu.chuanqirensheng.screen.effect.FullScreenDrawCardEffect
import com.moyu.chuanqirensheng.screen.equip.HeroDetailDialog
import com.moyu.chuanqirensheng.screen.event.EventDetailScreen
import com.moyu.chuanqirensheng.screen.event.EventSelectScreen
import com.moyu.chuanqirensheng.screen.fortune.FortuneScreen
import com.moyu.chuanqirensheng.screen.guide.GuideMask
import com.moyu.chuanqirensheng.screen.life.LifeScreen
import com.moyu.chuanqirensheng.screen.life.SellAllScreen
import com.moyu.chuanqirensheng.screen.life.StoryScreen
import com.moyu.chuanqirensheng.screen.login.CreateCountryScreen
import com.moyu.chuanqirensheng.screen.login.LoginScreen
import com.moyu.chuanqirensheng.screen.luntan.LuntanScreen
import com.moyu.chuanqirensheng.screen.more.MoreScreen
import com.moyu.chuanqirensheng.screen.more.VipScreen
import com.moyu.chuanqirensheng.screen.quest.QuestAllScreen
import com.moyu.chuanqirensheng.screen.record.FamousScreen
import com.moyu.chuanqirensheng.screen.record.RankScreen
import com.moyu.chuanqirensheng.screen.setting.SettingScreen
import com.moyu.chuanqirensheng.screen.skill.SkillDetailDialog
import com.moyu.chuanqirensheng.screen.talent.TalentDetailDialog
import com.moyu.chuanqirensheng.screen.tutor.TutorDialog
import com.moyu.chuanqirensheng.sub.language.LanguageManager
import com.moyu.chuanqirensheng.ui.theme.ComposedTheme
import com.moyu.chuanqirensheng.util.ABOUT_SCREEN
import com.moyu.chuanqirensheng.util.ABOUT_SHARE_REWARDS_SCREEN
import com.moyu.chuanqirensheng.util.CREATE_COUNTRY_SCREEN
import com.moyu.chuanqirensheng.util.DEBUG_BATTLE
import com.moyu.chuanqirensheng.util.DEBUG_SCREEN
import com.moyu.chuanqirensheng.util.DRAW_SCREEN
import com.moyu.chuanqirensheng.util.EVENT_DETAIL_SCREEN
import com.moyu.chuanqirensheng.util.EVENT_SELECT_SCREEN
import com.moyu.chuanqirensheng.util.FAMOUS_SCREEN
import com.moyu.chuanqirensheng.util.FORTUNE_SCREEN
import com.moyu.chuanqirensheng.util.HOLIDAY_SCREEN
import com.moyu.chuanqirensheng.util.LIFE_SCREEN
import com.moyu.chuanqirensheng.util.LOGIN_SCREEN
import com.moyu.chuanqirensheng.util.LOTTERY_SCREEN1
import com.moyu.chuanqirensheng.util.LOTTERY_SCREEN2
import com.moyu.chuanqirensheng.util.LUNTAN_SCREEN
import com.moyu.chuanqirensheng.util.MORE_SCREEN
import com.moyu.chuanqirensheng.util.PAGE_PARAM_TAB_INDEX
import com.moyu.chuanqirensheng.util.PVP_BATTLE_SCREEN
import com.moyu.chuanqirensheng.util.PVP_CHOOSE_ENEMY_SCREEN
import com.moyu.chuanqirensheng.util.PVP_QUEST_SCREEN
import com.moyu.chuanqirensheng.util.PVP_RANK_SCREEN
import com.moyu.chuanqirensheng.util.PVP_SCREEN
import com.moyu.chuanqirensheng.util.PVP_SELL_SCREEN
import com.moyu.chuanqirensheng.util.QUEST_SCREEN
import com.moyu.chuanqirensheng.util.RANK_SCREEN
import com.moyu.chuanqirensheng.util.SELL_SCREEN_PREFIX
import com.moyu.chuanqirensheng.util.SETTING_SCREEN
import com.moyu.chuanqirensheng.util.SEVEN_DAY_SCREEN
import com.moyu.chuanqirensheng.util.SIGN_SCREEN
import com.moyu.chuanqirensheng.util.STORY_SCREEN
import com.moyu.chuanqirensheng.util.TOWER_BATTLER_SCREEN
import com.moyu.chuanqirensheng.util.TOWER_SCREEN
import com.moyu.chuanqirensheng.util.USER_SHARE_GAME_SCREEN
import com.moyu.chuanqirensheng.util.VIP_SCREEN
import com.moyu.chuanqirensheng.util.WARPASS2_SCREEN
import com.moyu.chuanqirensheng.util.WARPASS_SCREEN
import com.moyu.chuanqirensheng.util.goto
import com.moyu.chuanqirensheng.util.isCurrentRoute
import com.moyu.chuanqirensheng.util.killSelf
import com.moyu.chuanqirensheng.util.popTop
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.Locale

class GameActivity : FragmentActivity() {
    override fun attachBaseContext(newBase: Context?) {
        // 对于API 33+，新的LocaleManager会自动处理语言配置
        // 不需要在attachBaseContext中手动设置
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            super.attachBaseContext(newBase)
        } else {
            super.attachBaseContext(updateBaseContext(newBase))
        }
    }

    fun updateBaseContext(context: Context?): Context {
        if (context == null) return context!!

        // 对于 Android 13+ (API 33+)，优先使用系统的 per-app language 设置
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            try {
                val localeManager = context.getSystemService(LocaleManager::class.java)
                val appLocales = localeManager?.applicationLocales
                if (appLocales != null && !appLocales.isEmpty) {
                    val locale = appLocales[0]
                    Locale.setDefault(locale)
                    return updateResourcesLocale(context, locale)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        // 回退到传统方法
        val lang = LanguageManager.selectedLanguage.value
        if (lang.isNotEmpty()) {
            val locale = Locale.forLanguageTag(lang)
            Locale.setDefault(locale)
            return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                updateResourcesLocale(context, locale)
            } else {
                updateResourcesLocaleLegacy(context, locale)
            }
        }
        return context
    }

    @TargetApi(Build.VERSION_CODES.N)
    private fun updateResourcesLocale(context: Context?, locale: Locale): Context {
        val configuration = context?.resources?.configuration
        configuration?.setLocale(locale)
        return context!!.createConfigurationContext(configuration!!)
    }

    private fun updateResourcesLocaleLegacy(context: Context?, locale: Locale): Context {
        val resources = context!!.resources
        val configuration = resources.configuration
        configuration.locale = locale
        resources.updateConfiguration(configuration, resources.displayMetrics)
        return context
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        GameApp.instance.activity = this
        setContent {
            val navController = rememberNavController()
            ComposedTheme {
                Scaffold { padding ->
                    val heathTipsVisible = remember {
                        mutableStateOf(true)
                    }
                    GameBackColor()
                    if (!heathTipsVisible.value || GameApp.instance.resources.getBoolean(R.bool.has_google_service)) {
                        RegisterScreens(navController, padding)
                        RegisterDialogs()
                        RegisterFullScreenEffect()
                        RegisterRouter(navController)
                        RegisterSnackBar()
                        RegisterBackCallback(onBackPressedDispatcher)
                    } else {
                        HeathTipsLayout(heathTipsVisible)
                    }
                    StatusBarMask()
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        BillingManager.queryAsync()
    }

    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        GoogleActivityResultHandler.onActivityResult(requestCode, resultCode, data)
    }
}

@Composable
fun RegisterFullScreenEffect() {
    GuideMask()
    FullScreenDrawCardEffect()
}

@Composable
fun RegisterScreens(
    navController: NavHostController,
    padding: PaddingValues,
) {
    NavHost(
        navController = navController,
        startDestination = LOGIN_SCREEN,
        modifier = Modifier.padding(padding),
    ) {
        composable(route = LOGIN_SCREEN) { LoginScreen() }
        composable(route = CREATE_COUNTRY_SCREEN) { CreateCountryScreen() }
        composable(route = FORTUNE_SCREEN) { FortuneScreen() }
        composable(route = STORY_SCREEN) { StoryScreen() }
        composable(route = LIFE_SCREEN) { LifeScreen() }
        composable(route = SIGN_SCREEN) { SignScreen() }
        composable(route = SELL_SCREEN_PREFIX + "{${PAGE_PARAM_TAB_INDEX}}") {
            SellAllScreen(
                it.arguments?.getString(
                    PAGE_PARAM_TAB_INDEX
                )?.toInt() ?: 0
            )
        }
        composable(route = QUEST_SCREEN) { QuestAllScreen() }
        composable(route = RANK_SCREEN) { RankScreen() }
        composable(route = FAMOUS_SCREEN) { FamousScreen() }
        composable(route = ABOUT_SCREEN) { AboutScreen() }
        composable(route = ABOUT_SHARE_REWARDS_SCREEN) { AboutShareRewardsScreen() }
        composable(route = USER_SHARE_GAME_SCREEN) { UserShareGameScreen() }
        composable(route = SETTING_SCREEN) { SettingScreen() }
        composable(route = MORE_SCREEN) { MoreScreen() }
        composable(route = EVENT_SELECT_SCREEN) { EventSelectScreen() }
        composable(route = EVENT_DETAIL_SCREEN) { EventDetailScreen() }
        composable(route = VIP_SCREEN) { VipScreen() }
        composable(route = WARPASS_SCREEN) { BattlePassScreen() }
        composable(route = WARPASS2_SCREEN) { BattlePass2Screen() }
        composable(route = LUNTAN_SCREEN) { LuntanScreen() }
        composable(route = SEVEN_DAY_SCREEN) { SevenDayAllScreen() }
        composable(route = LOTTERY_SCREEN1) { CheapLotteryScreen() }
        composable(route = LOTTERY_SCREEN2) { ExpensiveLotteryScreen() }
        composable(route = PVP_SCREEN) { PvpEntryScreen() }
        composable(route = PVP_SELL_SCREEN) { PvpSellScreen() }
        composable(route = PVP_CHOOSE_ENEMY_SCREEN) { PvpChooseEnemyScreen() }
        composable(route = PVP_RANK_SCREEN) { PvpRankScreen() }
        composable(route = PVP_QUEST_SCREEN) { PvpQuestScreen() }
        composable(route = PVP_BATTLE_SCREEN) { PvpBattleScreen() }
        composable(route = DRAW_SCREEN) { DrawAllScreen() }
        composable(route = HOLIDAY_SCREEN) { HolidayAllScreen() }
        composable(route = TOWER_BATTLER_SCREEN) { TowerBattleScreen() }
        composable(route = TOWER_SCREEN) { TowerAllScreen() }
        composable(route = DEBUG_SCREEN) { DebugScreen() }
        composable(route = DEBUG_BATTLE) { DebugBattleScreen() }
    }
}

@Composable
fun RegisterDialogs() {
    ErrorOrderDialog(errorOrderDialog)
    RoleDetailDialog(roleDetailDialog)
    SkillDetailDialog(skillDetailDialog)
    AllyDetailDialog(allyDetailDialog)
    AllyStarUpDialog(allyStarUpDialog)
    WarPassUnlockDialog(warPassUnlockDialog)
    WarPass2UnlockDialog(warPass2UnlockDialog)
    TalentDetailDialog(detailTalentDialog)
    GameWinDialog(gameWinDialog)
    GameLoseDialog(gameLoseDialog)
    AdPoolDialog(adPoolDialog)
    DrawPoolDialog(drawPoolDialog)
    StatisticDialog(statisticView)
    TutorDialog(tutorDialog)
    BattleAwardDialog(battleAwardDialog)
    BuffDetailDialog(buffDetailDialog)
    InfoDialog(infoDialog)
    ExtraInfoDialog(extraInfoDialog)
    GuideDialog(guideDialog)
    HeroDetailDialog(heroDetailDialog)
    GameReviewDialog(gameReviewDialog)
    GiftDetailDialog(giftDetailDialog)

    SelectSkillToGameDialog(selectSkillToGameDialog)
    SelectAllyToGameDialog(selectAllyToGameDialog)
    SelectAllyToBattleDialog(selectAllyToBattleDialog)
    SelectOneAllyDialog(selectOneAllyDialog)
    SelectOneSkillDialog(selectOneSkillDialog)
    SelectOneHeroDialog(selectOneHeroDialog)
    GameAllyDialog(gameAllyDialog)
    BattleSkillDialog(battleSkillDialog)
    AdventureSkillDialog(adventureSkillDialog)
    HeroDialog(equipDialog)
    SelectHeroToGameDialog(selectHeroToGameDialog)
    ReputationDialog(reputationDialog)
    EventPassDialog(eventPassDialog)
    EventFailDialog(eventFailDialog)
    EventDetailDialog(eventDetailDialog)
    FatalEnemyDialog(fatalEnemyDialog)
    EndingDetailDialog(endingDetailDialog)
    EndingDialog(endingDialog)
    StoryDialog(storyDialog)
    StoryDetailDialog(storyDetailDialog)
    ItemsDialog(itemsDialog)
    TcgAwardDetailDialog(tcgAwardDetail)
    AiFaDianDialog(aiFaDianDialog)
    GetKeyDialog(getKeyDialog)
    ChallengeGameDialog(challengeGameDialog)
    SkillLevelInfoDialog(Dialogs.skillLevelInfoDialog)
    PayBlockDialog(Dialogs.payBlockingDialog)
    ErrorOrderDialog(Dialogs.errorOrderDialog)
    DrawResultDialog(drawResultDialog)

    DebugSkillDialog(debugSkillDialog)
    DebugAdvSkillDialog(debugAdvSkillDialog)
    CommonAlertDialog(alertDialog)
    LoginFailedGameDialog()
    MoneyTransferDialog(moneyTransferDialog)
    AwardDialog(Dialogs.awardDialog)
    UseCloudSaverDialog(useSaveDialog)
    ShareImageDialog(shareImageDialog)

    PrivacyAlertDialog(switch = showPrivacyDialog, quit = { killSelf() }, confirm = {
        setBooleanValueByKey(KEY_NEED_SHOW_PRIVACY, false)
        showPermissionDialog.value = true
    })
    PermissionAlertDialog(switch = showPermissionDialog, confirm = {
        setBooleanValueByKey(KEY_NEED_SHOW_PERMISSION, false)
        GameApp.instance.initSDK(GameApp.instance.activity)
        GameApp.globalScope.launch {
            delay(1500) //有时候会拉不起来登录，delay下其他操作
            BuglyTask().execute(GameApp.instance)
            BillingTask().execute(GameApp.instance)
            TTRewardAdTask().execute(GameApp.instance)
            RootCheckerTask().execute(GameApp.instance)
        }
    })
}

@Composable
fun RegisterRouter(navController: NavHostController) {
    LaunchedEffect(Unit) {
        GameApp.instance.navController = navController
        navController.addOnDestinationChangedListener { _: NavController, _: NavDestination, _: Bundle? ->
            GameApp.globalScope.launch {
                // todo 不delay的话，这里判定时候，页面还没有完成退出
                delay(100)
                playerMusicByScreen()
            }
        }
    }
}

@Composable
fun RegisterSnackBar() {
    GameSnackBar()
}

@Composable
fun RegisterBackCallback(onBackPressedDispatcher: OnBackPressedDispatcher) {
    LaunchedEffect(Unit) {
        onBackPressedDispatcher.addCallback(
            GameApp.instance.activity,
        ) {
            if (!GuideManager.canBack()) {
                GameApp.instance.getWrapString(R.string.guide_block_quit).toast() // 引导时候全局block返回按钮
            } else if (isCurrentRoute(PVP_BATTLE_SCREEN)) {
                alertDialog.value = CommonAlert(
                    title = GameApp.instance.getWrapString(R.string.quit_pvp_title),
                    content = GameApp.instance.getWrapString(R.string.quit_pvp_content),
                    onConfirm = {
                        repo.battle.value.terminate()
                        repo.inBattle.value = false
                        PvpManager.pkFailed(emptyList(), repo.enemies)
                    }
                )
            } else if (repo.gameMode.value.getGameMode().isTower() && isCurrentRoute(TOWER_BATTLER_SCREEN)) {
                alertDialog.value = CommonAlert(
                    title = GameApp.instance.getWrapString(R.string.quit_battle_title),
                    content = GameApp.instance.getWrapString(R.string.quit_battle_content),
                    onConfirm = {
                        repo.battle.value.terminate()
                        repo.inBattle.value = false
                        TowerManager.failed(
                            emptyList(),
                            emptyList())
                        goto(TOWER_SCREEN)
                    }
                )
            } else if (repo.inBattle.value) {
                alertDialog.value = CommonAlert(
                    title = GameApp.instance.getWrapString(R.string.quit_battle_title),
                    content = GameApp.instance.getWrapString(R.string.quit_battle_content),
                    onConfirm = {

                        repo.battle.value.terminate()
                        repo.inBattle.value = false
                        EventManager.doEventBattleResult(EventManager.selectedEvent.value, false)
                    }
                )
            } else if (isCurrentRoute(EVENT_DETAIL_SCREEN)) {
                alertDialog.value = CommonAlert(
                    title = GameApp.instance.getWrapString(R.string.quit_event_title),
                    content = GameApp.instance.getWrapString(R.string.quit_event_content),
                    onConfirm = {
                        EventManager.selectedEvent.value?.let {
                            EventManager.doEventResult(
                                it,
                                EventManager.getOrCreateHandler(it).skipWin
                            )
                        }
                    }
                )
            } else if (isCurrentRoute(EVENT_SELECT_SCREEN)) {
                alertDialog.value = CommonAlert(
                    title = GameApp.instance.getWrapString(R.string.quit_life_title),
                    content = GameApp.instance.getWrapString(R.string.quit_life_content),
                    confirmText = GameApp.instance.getWrapString(R.string.quit_life_title),
                    cancelText = GameApp.instance.getWrapString(R.string.temp_save),
                    onCancel = {
                        repo.inGame.value = false
                        goto(LOGIN_SCREEN)
                    },
                    onConfirm = {
                        val story = StoryManager.saveStory(
                            repo.you.value,
                            EventManager.eventRecorder.usedEvents,
                            EventManager.eventRecorder.succeededEvents
                        )
                        EventManager.ending(story)
                        if (story == null) {
                            // story为空，需要手动跳转
                            repo.inGame.value = false
                            goto(LOGIN_SCREEN)
                        }
                    }
                )
            } else {
                popTop()
            }
        }
    }
}