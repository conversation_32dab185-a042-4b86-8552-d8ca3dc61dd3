package com.moyu.chuanqirensheng.feature.gift.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.cloud.bill.BillingManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.ui.theme.paddingMediumPlus
import com.moyu.chuanqirensheng.ui.theme.paddingSmall

@Composable
fun ErrorOrder(modifier: Modifier = Modifier) {
    if (BillingManager.payClientDataList.isNotEmpty()) {
        Column(horizontalAlignment = Alignment.CenterHorizontally, modifier = modifier) {
            EffectButton(modifier = Modifier.graphicsLayer {
                translationY = paddingMediumPlus.toPx()
            }, onClick = {
                Dialogs.errorOrderDialog.value = true
            }) {
                Image(
                    modifier = Modifier
                        .size(ItemSize.LargePlus.itemSize),
                    painter = painterResource(id = R.drawable.error_bill),
                    contentDescription = null
                )
            }
            Spacer(modifier = Modifier.size(paddingSmall))
            Text(
                text = stringResource(R.string.error_order),
                maxLines = 2,
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.h5
            )
        }
    }
}
