package com.moyu.chuanqirensheng.feature.monthcard

import androidx.compose.runtime.mutableStateListOf
import com.moyu.chuanqirensheng.datastore.KEY_MONTH_CARD_INFO
import com.moyu.chuanqirensheng.datastore.getListObject
import com.moyu.chuanqirensheng.datastore.setListObject
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.isSameDay
import com.moyu.chuanqirensheng.util.millis2Days
import com.moyu.core.model.sell.Sell
import com.moyu.core.model.sell.toAward
import kotlinx.serialization.Serializable

@Serializable
data class MonthCardBoughtInfo(
    val sellId: Int,
    val boughtTime: Long,
    val lastGainTime: Long,
    val storage: Int,          // Number of days for which rewards can be claimed
) {
    fun expired(): Boolean {
        val currentTime = getCurrentTime()
        val currentDay = millis2Days(currentTime)
        val boughtDay = millis2Days(boughtTime)
        // 到达第 (boughtDay + storage) 日的 0 点即过期，所以用 >= 判断
        return currentDay > (boughtDay + storage)
    }

    fun isLastDay(): Boolean {
        val currentTime = getCurrentTime()
        val currentDay = millis2Days(currentTime)
        val boughtDay = millis2Days(boughtTime)
        // 到达第 (boughtDay + storage) 日的 0 点即过期，所以用 >= 判断
        return currentDay == (boughtDay + storage)
    }
}

object MonthCardManager {
    val cardBoughtIds = mutableStateListOf<MonthCardBoughtInfo>()

    fun init() {
        cardBoughtIds.clear()
        val list = getListObject<MonthCardBoughtInfo>(KEY_MONTH_CARD_INFO)
        cardBoughtIds.addAll(list.filter { !it.expired() })
    }

    fun canCardGainDailyAward(sellId: Int): Boolean {
        val now = getCurrentTime()
        return cardBoughtIds.any { card ->
            card.sellId == sellId &&
                    !card.expired() &&
                    !isSameDay(card.lastGainTime, now)
        }
    }

    fun buyCard(sellId: Int, storage: Int, time: Long) {
        val existItem = cardBoughtIds.firstOrNull { it.sellId == sellId }
        cardBoughtIds.removeAll { it.sellId == sellId }

        val newCard = MonthCardBoughtInfo(
            sellId = sellId,
            boughtTime =  (existItem?.boughtTime?: time),
            lastGainTime = (existItem?.lastGainTime?: 0L),
            storage = storage + (existItem?.storage?: -1) // todo -1表示当天也算一天，毕竟第一天可以领取
        )
        cardBoughtIds.add(newCard)

        setListObject(KEY_MONTH_CARD_INFO, cardBoughtIds)
    }

    fun markGotDailyAward(sellId: Int, time: Long): Boolean {
        val index = cardBoughtIds.indexOfFirst { it.sellId == sellId && !it.expired() }
        if (index != -1) {
            val currentCard = cardBoughtIds[index]
            val lastGainTime = currentCard.lastGainTime
            if (isSameDay(lastGainTime, time)) {
                return false
            } else {
                if (currentCard.isLastDay()) {
                    cardBoughtIds.removeAll { it.sellId == sellId }
                } else {
                    val updatedCard = currentCard.copy(lastGainTime = time)
                    cardBoughtIds[index] = updatedCard
                }

                // Save changes
                setListObject(KEY_MONTH_CARD_INFO, cardBoughtIds)
                return true
            }
        } else {
            return false
        }
    }

    suspend fun openPackage(sell: Sell) {
        val oneTimeAward = sell.toAward()
        buyCard(sell.id, sell.storage, getCurrentTime())
        AwardManager.gainAward(oneTimeAward)
        Dialogs.awardDialog.value = oneTimeAward
    }

    suspend fun gainDailyAward(sell: Sell) {
        if (markGotDailyAward(sell.id, getCurrentTime())) {
            val award = repo.gameCore.getPoolById(sell.itemId2).toAward()
            AwardManager.gainAward(award)
            Dialogs.awardDialog.value = award
        }
    }

    fun getItemById(id: Int): MonthCardBoughtInfo? {
        return cardBoughtIds.firstOrNull { it.sellId == id }
    }

    fun getLeftDay(id: Int): Int? {
        return cardBoughtIds.firstOrNull { it.sellId == id }?.let { card ->
            val currentDay = millis2Days(getCurrentTime())
            val boughtDay = millis2Days(card.boughtTime)
            // 到第 (boughtDay + storage) 天凌晨过期，还剩多少天？
            // 假如还剩 10 天，就代表在接下来的这 10 个自然日里还有效
            val leftDay = (boughtDay + card.storage) - currentDay

            if (leftDay > 0) leftDay.toInt() else null
        }
    }
}
