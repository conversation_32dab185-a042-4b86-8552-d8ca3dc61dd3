package com.moyu.chuanqirensheng.feature.lottery

import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.datastore.KEY_FREE_CHEAP_LOTTERY_DONE
import com.moyu.chuanqirensheng.datastore.KEY_FREE_EXPENSIVE_LOTTERY_DONE
import com.moyu.chuanqirensheng.datastore.KEY_LOTTERY_CHEAP_BOUGHT
import com.moyu.chuanqirensheng.datastore.KEY_LOTTERY_CHEAP_NUM
import com.moyu.chuanqirensheng.datastore.KEY_LOTTERY_EXPENSIVE_BOUGHT
import com.moyu.chuanqirensheng.datastore.KEY_LOTTERY_EXPENSIVE_NUM
import com.moyu.chuanqirensheng.datastore.KEY_LOTTERY_INIT_RAW_TIME
import com.moyu.chuanqirensheng.datastore.KEY_LOTTERY_INIT_TIME_IN_A_WEEK
import com.moyu.chuanqirensheng.datastore.KEY_LOTTERY_SELLS
import com.moyu.chuanqirensheng.datastore.KEY_LOTTERY_WEEK_NUM
import com.moyu.chuanqirensheng.datastore.KEY_RESET_CHEAP_LOTTERY_NUM
import com.moyu.chuanqirensheng.datastore.KEY_RESET_EXPENSIVE_LOTTERY_NUM
import com.moyu.chuanqirensheng.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.datastore.getListObject
import com.moyu.chuanqirensheng.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.datastore.increaseIntValueByKey
import com.moyu.chuanqirensheng.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.datastore.setListObject
import com.moyu.chuanqirensheng.datastore.setLongValueByKey
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.util.gapWeek
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.core.model.TurnTable
import com.moyu.core.model.sell.Award
import com.moyu.core.model.sell.Sell
import com.moyu.core.model.sell.toAward
import kotlin.random.Random

const val MAX_RESET_LOTTERY_NUM = 50

object LotteryManager {
    val spinning = mutableStateOf(false)
    val cheapBoughtIndex = mutableStateListOf<Int>()
    val expensiveBoughtIndex = mutableStateListOf<Int>()

    val packages = mutableStateListOf<Sell>()

    val savedWeekNum = mutableStateOf(0)

    // 返回最终随机到的奖励，以及在全部8个奖品里，随机到的奖品的index[0, 7]
    fun lotteryCheatRandomAward(): Pair<Award, Int> {
        // 确保无法通过回滚存档修改随机结果
        val random = Random(
            (GameApp.instance.getObjectId()?:"no user").toCharArray().sumOf { it.code } + getIntFlowByKey(
                KEY_LOTTERY_CHEAP_NUM
            ) + 1000 * getIntFlowByKey(KEY_RESET_CHEAP_LOTTERY_NUM))
        val leftAwards = getCheapLotteryAwards().filterIndexed { index, _ -> !cheapBoughtIndex.contains(index) }
        val leftTurnTables = repo.gameCore.getTurnTablePool().filter { it.isCheap() }.filterIndexed { index, _ -> !cheapBoughtIndex.contains(index) }
        val totalWeight = leftTurnTables.sumOf { it.price }
        val randomAwardWeight = random.nextInt(totalWeight)
        var cumulativeWeight = 0
        val randomAwardIndex = leftTurnTables.indexOfFirst {
            cumulativeWeight += it.price
            randomAwardWeight < cumulativeWeight
        }
        val realRandomAwardIndex = if (cheapBoughtIndex.size < 4) {
            // 前4次不能随机到重要奖励，也就是不能获得最后一个
            if (randomAwardIndex == leftTurnTables.size - 1) {
                0
            } else {
                randomAwardIndex
            }
        } else {
            randomAwardIndex
        }
        val allIndex = repo.gameCore.getTurnTablePool().filter { it.isCheap() }.indexOfFirst { it.id == leftTurnTables[realRandomAwardIndex].id }
        return leftAwards[realRandomAwardIndex] to allIndex
    }

    fun lotteryExpensiveRandomAward(): Pair<Award, Int> {
        // 确保无法通过回滚存档修改随机结果
        val random = Random(
            (GameApp.instance.getObjectId()?:"no user").toCharArray().sumOf { it.code } + getIntFlowByKey(
                KEY_LOTTERY_EXPENSIVE_NUM
            ) + 1000 * getIntFlowByKey(KEY_RESET_EXPENSIVE_LOTTERY_NUM))
        val leftAwards = getExpensiveAwards().filterIndexed { index, _ -> !expensiveBoughtIndex.contains(index) }
        val leftTurnTables = repo.gameCore.getTurnTablePool().filter { it.isExpensive() }.filterIndexed { index, _ -> !expensiveBoughtIndex.contains(index) }
        val totalWeight = leftTurnTables.sumOf { it.price }
        val randomAwardWeight = random.nextInt(totalWeight)
        var cumulativeWeight = 0
        val randomAwardIndex = leftTurnTables.indexOfFirst {
            cumulativeWeight += it.price
            randomAwardWeight < cumulativeWeight
        }
        val realRandomAwardIndex = if (expensiveBoughtIndex.size < 4) {
            // 前4次不能随机到重要奖励，也就是不能获得最后一个
            if (randomAwardIndex == leftTurnTables.size - 1) {
                0
            } else {
                randomAwardIndex
            }
        } else {
            randomAwardIndex
        }
        val allIndex = repo.gameCore.getTurnTablePool().filter { it.isExpensive() }.indexOfFirst { it.id == leftTurnTables[realRandomAwardIndex].id }
        return leftAwards[realRandomAwardIndex] to allIndex
    }

    fun isCheapFreeLottery(): Boolean {
        return if (getBooleanFlowByKey(KEY_FREE_CHEAP_LOTTERY_DONE)) {
            false
        } else {
            true
        }
    }

    fun isExpensiveFreeLottery(): Boolean {
        return if (getBooleanFlowByKey(KEY_FREE_EXPENSIVE_LOTTERY_DONE)) {
            false
        } else {
            true
        }
    }

    fun canResetCheap(): Boolean {
        return getIntFlowByKey(KEY_RESET_CHEAP_LOTTERY_NUM) < MAX_RESET_LOTTERY_NUM
    }

    fun canResetExpensive(): Boolean {
        return getIntFlowByKey(KEY_RESET_EXPENSIVE_LOTTERY_NUM) < MAX_RESET_LOTTERY_NUM
    }

    fun resetCheapLottery() {
        GameApp.instance.getWrapString(R.string.lottery_reset).toast()
        increaseIntValueByKey(KEY_RESET_CHEAP_LOTTERY_NUM, 1)
        setIntValueByKey(KEY_LOTTERY_CHEAP_NUM, 0)
        cheapBoughtIndex.clear()
        setListObject(KEY_LOTTERY_CHEAP_BOUGHT, cheapBoughtIndex)
    }

    fun resetExpensiveLottery() {
        GameApp.instance.getWrapString(R.string.lottery_reset).toast()
        increaseIntValueByKey(KEY_RESET_EXPENSIVE_LOTTERY_NUM, 1)
        setIntValueByKey(KEY_LOTTERY_EXPENSIVE_NUM, 0)
        expensiveBoughtIndex.clear()
        setListObject(KEY_LOTTERY_EXPENSIVE_BOUGHT, expensiveBoughtIndex)
    }

    fun getCheapLotteryAwards(): List<Award> {
        val weekIndex = getWeekIndexLimit4()
        val turnTables = repo.gameCore.getTurnTablePool().filter { it.isCheap() }
        return turnTables.map {
            when (weekIndex) {
                0 -> it.reward1
                1 -> it.reward2
                2 -> it.reward3
                3 -> it.reward4
                else -> 0
            }
        }.map { if (it == 0) Award() else repo.gameCore.getPoolById(it).toAward() }
    }

    fun getExpensiveAwards(): List<Award> {
        val weekIndex = getWeekIndexLimit4()
        val turnTables = repo.gameCore.getTurnTablePool().filter { it.isExpensive() }
        return turnTables.map {
            when (weekIndex) {
                0 -> it.reward1
                1 -> it.reward2
                2 -> it.reward3
                3 -> it.reward4
                else -> 0
            }
        }.map { if (it == 0) Award() else repo.gameCore.getPoolById(it).toAward() }
    }

    fun gainCheapLotteryAward(index: Int) {
        setBooleanValueByKey(KEY_FREE_CHEAP_LOTTERY_DONE, true)
        increaseIntValueByKey(KEY_LOTTERY_CHEAP_NUM, 1)
        cheapBoughtIndex.add(index)
        setListObject(KEY_LOTTERY_CHEAP_BOUGHT, cheapBoughtIndex)
        if (cheapBoughtIndex.size >= 8 && canResetCheap()) {
            resetCheapLottery()
        }
    }

    fun gainExpensiveLotteryAward(index: Int) {
        setBooleanValueByKey(KEY_FREE_EXPENSIVE_LOTTERY_DONE, true)
        increaseIntValueByKey(KEY_LOTTERY_EXPENSIVE_NUM, 1)
        expensiveBoughtIndex.add(index)
        setListObject(KEY_LOTTERY_EXPENSIVE_BOUGHT, expensiveBoughtIndex)
        if (expensiveBoughtIndex.size >= 8 && canResetExpensive()) {
            resetExpensiveLottery()
        }
    }

    fun refresh() {
        if (getLongFlowByKey(KEY_LOTTERY_INIT_TIME_IN_A_WEEK) == 0L || getLongFlowByKey(KEY_LOTTERY_INIT_RAW_TIME) == 0L) {
            setLongValueByKey(KEY_LOTTERY_INIT_TIME_IN_A_WEEK, getCurrentTime())
            setLongValueByKey(KEY_LOTTERY_INIT_RAW_TIME, getCurrentTime())
        }
        savedWeekNum.value = getIntFlowByKey(KEY_LOTTERY_WEEK_NUM)
        cheapBoughtIndex.clear()
        cheapBoughtIndex.addAll(getListObject(KEY_LOTTERY_CHEAP_BOUGHT))
        expensiveBoughtIndex.clear()
        expensiveBoughtIndex.addAll(getListObject(KEY_LOTTERY_EXPENSIVE_BOUGHT))

        val currentWeekIndex = gapWeek(getCurrentTime(), getLongFlowByKey(KEY_LOTTERY_INIT_TIME_IN_A_WEEK))
        if (currentWeekIndex != savedWeekNum.value) {
            // 更新周数
            savedWeekNum.value = currentWeekIndex
            setIntValueByKey(KEY_LOTTERY_WEEK_NUM, currentWeekIndex)
            // 清除已领取记录
            cheapBoughtIndex.clear()
            setListObject(KEY_LOTTERY_CHEAP_BOUGHT, cheapBoughtIndex)
            expensiveBoughtIndex.clear()
            setListObject(KEY_LOTTERY_EXPENSIVE_BOUGHT, expensiveBoughtIndex)
            // 清除重置次数
            setIntValueByKey(KEY_RESET_CHEAP_LOTTERY_NUM, 0)
            setIntValueByKey(KEY_RESET_EXPENSIVE_LOTTERY_NUM, 0)
            // 清除免费抽奖是否已完成
            setBooleanValueByKey(KEY_FREE_CHEAP_LOTTERY_DONE, false)
            setBooleanValueByKey(KEY_FREE_EXPENSIVE_LOTTERY_DONE, false)
            // 更新刷新时间，时间还是保持和KEY_LOTTERY_INIT_TIME初始一样，只是如果超过了一周，往前推进一周
            val rawGapTimeFromAWeek = getLongFlowByKey(KEY_LOTTERY_INIT_RAW_TIME) % (1000 * 60 * 60 * 24 * 7)
            val currentGapTimeFromAWeek = getCurrentTime() % (1000 * 60 * 60 * 24 * 7)
            setLongValueByKey(KEY_LOTTERY_INIT_TIME_IN_A_WEEK, getCurrentTime() - currentGapTimeFromAWeek + rawGapTimeFromAWeek)
            // 刷新商品
            setListObject(KEY_LOTTERY_SELLS, emptyList<Sell>())
            packages.clear()
        }
    }

    // 返回0-3
    fun getWeekIndexLimit4(): Int {
        return gapWeek(getCurrentTime(), getLongFlowByKey(KEY_LOTTERY_INIT_RAW_TIME)) % 4
    }

    fun getCheapTurnTables(): List<TurnTable> {
        return repo.gameCore.getTurnTablePool().filter { it.isCheap() }
    }

    fun getExpensiveTurnTables(): List<TurnTable> {
        return repo.gameCore.getTurnTablePool().filter { it.isExpensive() }
    }

    fun getCheapCost(): Int {
        return repo.gameCore.getCheapLotteryCosts().getOrNull(cheapBoughtIndex.size) ?: repo.gameCore.getCheapLotteryCosts().last()
    }

    fun getExpensiveCost(): Int {
        return repo.gameCore.getExpensiveLotteryCosts().getOrNull(expensiveBoughtIndex.size) ?:  repo.gameCore.getExpensiveLotteryCosts().last()
    }

    fun showCheap(): Boolean {
        if (getLongFlowByKey(KEY_LOTTERY_INIT_TIME_IN_A_WEEK) == 0L) {
            return true
        }
        return getCurrentTime() - getLongFlowByKey(KEY_LOTTERY_INIT_TIME_IN_A_WEEK) < 5 * (1000 * 60 * 60 * 24)
    }

    fun createGifts() {
        if (packages.isEmpty()) {
            getListObject<Sell>(KEY_LOTTERY_SELLS).let {
                packages.addAll(it.mapNotNull { sell ->
                    repo.gameCore.getSellPool().firstOrNull { it.id == sell.id }
                        ?.copy(opened = sell.opened, num = sell.num, storage = sell.storage)
                })
            }
        }
        val shouldShowPackages = repo.gameCore.getSellPool().filter {
            it.isLotteryGift()
        }

        val currentPackageIds = packages.map { it.id }
        val tobeAdded = shouldShowPackages.filter {
            !currentPackageIds.contains(it.id)
        }.map { sell ->
            sell
        }.sortedBy { it.order }

        packages.addAll(tobeAdded)
        setListObject(KEY_LOTTERY_SELLS, packages)
    }

    fun canDoCheap(): Boolean {
        return cheapBoughtIndex.size < 8
    }

    fun canDoExpensive(): Boolean {
        return expensiveBoughtIndex.size < 8
    }
}