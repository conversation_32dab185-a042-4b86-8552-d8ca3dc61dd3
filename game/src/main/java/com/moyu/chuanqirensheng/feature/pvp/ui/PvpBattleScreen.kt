package com.moyu.chuanqirensheng.feature.pvp.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.datastore.KEY_PK_ALLY1_IDS
import com.moyu.chuanqirensheng.datastore.KEY_PK_ALLY2_IDS
import com.moyu.chuanqirensheng.datastore.KEY_PK_ALLY3_IDS
import com.moyu.chuanqirensheng.datastore.KEY_PK_ALLY_IDS
import com.moyu.chuanqirensheng.datastore.KEY_PK_SKILL1_IDS
import com.moyu.chuanqirensheng.datastore.KEY_PK_SKILL2_IDS
import com.moyu.chuanqirensheng.datastore.KEY_PK_SKILL3_IDS
import com.moyu.chuanqirensheng.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.datastore.setListObject
import com.moyu.chuanqirensheng.feature.mode.EndlessGameMode
import com.moyu.chuanqirensheng.feature.mode.PVPGameMode
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.arena.BattleFieldLayout
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.common.SettingColumn
import com.moyu.chuanqirensheng.screen.common.settingBattleItems
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.paddingLargePlus
import com.moyu.core.model.skill.isBattle

@Composable
fun PvpBattleScreen() {
    LaunchedEffect(Unit) {
        repo.inGame.value = true
        repo.gameMode.value = PVPGameMode()
        // 将所有角色加入到局内
        BattleManager.init()
        BattleManager.selectAllToPvpOrTowerGame()
        PvpManager.lastPvpAllyIds.forEachIndexed { index, savedId ->
            BattleManager.allyGameData.firstOrNull { it.id == savedId }?.let {
                BattleManager.selectAllyToBattle(it)
                BattleManager.getBattleAllies().getOrNull(index)?.let { ally ->
                    PvpManager.lastPvpSkillAllyIds.forEachIndexed { index, mutableState ->
                        // 加一层校验，防止位置变化后，技能错位
                        if (mutableState.value == ally.id) {
                            PvpManager.lastPvpSkillLists[index].forEach { skillId ->
                                BattleManager.skillGameData.firstOrNull { it.id == skillId }?.let {
                                    BattleManager.equipSkillToAlly(it, ally)
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    DisposableEffect(Unit) {
        onDispose {
            repo.inGame.value = false
            repo.gameMode.value = EndlessGameMode()
        }
    }
    GameBackground(
        title = stringResource(R.string.pvp),
        bgMask = B50,
        background = R.drawable.bg_pvp_battle
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(start = paddingLargePlus),
            verticalArrangement = Arrangement.SpaceEvenly,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            if (repo.inBattle.value) {
                BattleFieldLayout({ repo.allies }, { repo.enemies })
            } else {
                PvpPrepareBattleLayout {
                    if (BattleManager.getBattleAllies().isEmpty()) {
                        Dialogs.alertDialog.value =
                            CommonAlert(content = GameApp.instance.getWrapString(R.string.no_role_to_battle_tips),
                                onConfirm = {
                                    repo.battle.value.terminate()
                                    repo.inBattle.value = false
                                    PvpManager.pkFailed(emptyList(), repo.enemies)
                                })
                    } else {
                        val battleAllies = BattleManager.getPvpBattleRoles()
                        PvpManager.lastPvpAllyIds.clear()
                        PvpManager.lastPvpAllyIds.addAll(battleAllies.map { it.getAlly().id })
                        setListObject(
                            KEY_PK_ALLY_IDS,
                            PvpManager.lastPvpAllyIds
                        )

                        PvpManager.lastPvpSkill1Ids.clear()
                        PvpManager.lastPvpAlly1Id.value = 0
                        battleAllies.getOrNull(0)?.let {
                            PvpManager.lastPvpSkill1Ids.addAll(
                                it.getSkills().filter { it.isBattle() }.map { it.id })
                            PvpManager.lastPvpAlly1Id.value = it.getAlly().id
                        }
                        setIntValueByKey(KEY_PK_ALLY1_IDS, PvpManager.lastPvpAlly1Id.value)
                        setListObject(
                            KEY_PK_SKILL1_IDS,
                            PvpManager.lastPvpSkill1Ids
                        )

                        PvpManager.lastPvpSkill2Ids.clear()
                        PvpManager.lastPvpAlly2Id.value = 0
                        battleAllies.getOrNull(1)?.let {
                            PvpManager.lastPvpSkill2Ids.addAll(
                                it.getSkills().filter { it.isBattle() }.map { it.id })
                            PvpManager.lastPvpAlly2Id.value = it.getAlly().id
                        }
                        setIntValueByKey(KEY_PK_ALLY2_IDS, PvpManager.lastPvpAlly2Id.value)
                        setListObject(
                            KEY_PK_SKILL2_IDS,
                            PvpManager.lastPvpSkill2Ids
                        )


                        PvpManager.lastPvpSkill3Ids.clear()
                        PvpManager.lastPvpAlly3Id.value = 0
                        battleAllies.getOrNull(2)?.let {
                            setListObject(
                                KEY_PK_SKILL3_IDS,
                                it.getSkills().filter { it.isBattle() }.map { it.id }
                            )
                            PvpManager.lastPvpSkill3Ids.addAll(
                                it.getSkills().filter { it.isBattle() }.map { it.id })
                            PvpManager.lastPvpAlly3Id.value = it.getAlly().id
                        }
                        setIntValueByKey(KEY_PK_ALLY3_IDS, PvpManager.lastPvpAlly3Id.value)
                        setListObject(
                            KEY_PK_SKILL3_IDS,
                            PvpManager.lastPvpSkill3Ids
                        )

                        repo.setCurrentAllies(battleAllies)
                        if (repo.enemies.isEmpty() || repo.enemies.all { it.isDeath() }) {
                            GameApp.instance.getWrapString(R.string.pvp_error_ally).toast()
                        } else {
                            repo.startBattle()
                        }
                    }
                }
            }
        }
        SettingColumn(
            Modifier
                .align(Alignment.CenterStart)
                .padding(start = padding6), settingBattleItems
        )
    }
}