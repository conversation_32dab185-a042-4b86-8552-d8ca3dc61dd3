package com.moyu.chuanqirensheng.feature.battlepass.ui

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.battlepass.BattlePassManager
import com.moyu.chuanqirensheng.feature.battlepass.BattlePassManager.season
import com.moyu.chuanqirensheng.feature.battlepass.KEY_WAR_PASS_UNLOCK_EVIDENCE
import com.moyu.chuanqirensheng.feature.battlepass.KEY_WAR_PASS_UNLOCK_EVIDENCE2
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.ally.SingleDetailAllyCard
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.CurrentPassPoint
import com.moyu.chuanqirensheng.screen.common.VipLevel
import com.moyu.chuanqirensheng.screen.dialog.AwardList
import com.moyu.chuanqirensheng.screen.dialog.defaultParam
import com.moyu.chuanqirensheng.ui.theme.Yellow60
import com.moyu.chuanqirensheng.ui.theme.cheatDecHeight
import com.moyu.chuanqirensheng.ui.theme.cheatFrameHeight
import com.moyu.chuanqirensheng.ui.theme.gapMedium
import com.moyu.chuanqirensheng.ui.theme.imageHugeFrame
import com.moyu.chuanqirensheng.ui.theme.imageLargePlus
import com.moyu.chuanqirensheng.ui.theme.imageMediumMinus
import com.moyu.chuanqirensheng.ui.theme.paddingHugeLite
import com.moyu.chuanqirensheng.ui.theme.paddingLarge
import com.moyu.chuanqirensheng.ui.theme.paddingLargePlus
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingMediumMinis
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.ui.theme.paddingSmallPlus
import com.moyu.chuanqirensheng.ui.theme.paddingTiny
import com.moyu.chuanqirensheng.util.gotoQuest
import com.moyu.core.model.BattlePass
import com.moyu.core.model.sell.toAward
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun WarPassPage() {
    val currentSeason = BattlePassManager.getPassSeason()
    // for animate
    val infiniteTransition = rememberInfiniteTransition(label = "")
    val angle by infiniteTransition.animateFloat(
        initialValue = 0f, targetValue = 360f, animationSpec = infiniteRepeatable(
            animation = tween(10000, easing = LinearEasing)
        ), label = ""
    )
    Column(
        modifier = Modifier
            .fillMaxSize()
            .paint(
                painterResource(id = R.drawable.common_page_frame),
                contentScale = ContentScale.FillBounds
            )
    ) {
        Spacer(modifier = Modifier.size(paddingMedium))
        CurrentPassPoint(Modifier.align(Alignment.CenterHorizontally))
        if (season.value > 1) {
            Text(text = stringResource(R.string.switch_war_pass_negative_tips), style = MaterialTheme.typography.h6, color = Color.Yellow)
        }
        Spacer(modifier = Modifier.size(paddingSmallPlus))
        Box(modifier = Modifier.fillMaxSize()) {
            Image(modifier = Modifier
                .align(Alignment.CenterEnd)
                .height(cheatDecHeight)
                .graphicsLayer {
                    translationX = gapMedium.toPx()
                    translationY = -gapMedium.toPx()
                    this.rotationZ = angle
                }
                .scale(1.6f),
                contentScale = ContentScale.FillHeight,
                painter = painterResource(id = R.drawable.effect_shine),
                contentDescription = null)
            CurrentPassItem(modifier = Modifier
                .align(Alignment.CenterEnd)
                .graphicsLayer {
                    translationY = -gapMedium.toPx()
                })

            val pool = repo.gameCore.getBattlePassPool().filter { it.season == currentSeason }
            LazyColumn(
                modifier = Modifier.align(Alignment.CenterStart),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                items(pool.size) { index ->
                    if (index == 0) {
                        Spacer(modifier = Modifier.size(paddingMedium))
                    }
                    Column {
                        Spacer(modifier = Modifier.size(paddingMedium))
                        OnePassItem(pool[index]) {
                            GameApp.globalScope.launch(Dispatchers.Main) {
                                val unlocked = (BattlePassManager.getCurrentWarPass()?.id
                                    ?: 0) >= pool[index].id
                                if (unlocked) {
                                    if (pool[index].unlockType == 2 && !AwardManager.battlePassBought[BattlePassManager.getPassSeason()]!!.value) {
                                        if (BattlePassManager.getPassSeason() == 1) {
                                            repo.gameCore.getUnlockById(KEY_WAR_PASS_UNLOCK_EVIDENCE).desc.toast()
                                        } else {
                                            repo.gameCore.getUnlockById(KEY_WAR_PASS_UNLOCK_EVIDENCE2).desc.toast()
                                        }
                                    } else {
                                        BattlePassManager.gain(pool[index])
                                    }
                                } else {
                                    GameApp.instance.getWrapString(R.string.battlepass_not_enough)
                                        .toast()
                                }
                            }
                        }
                    }

                }
            }
        }
        Spacer(modifier = Modifier.size(paddingMedium))
    }
}


@Composable
fun OnePassItem(cheat: BattlePass, callback: () -> Unit) {
    EffectButton(
        modifier = Modifier.size(cheatFrameHeight), onClick = callback
    ) {
        PassAwards(cheat, callback = callback)
        VipLevel(
            modifier = Modifier
                .align(Alignment.TopStart)
                .offset(y = -paddingLarge),
            cheatLevel = cheat.level,
            frame = painterResource(id = R.drawable.battlepass_level_frame)
        )
    }
}

@Composable
fun PassAwards(cheat: BattlePass, callback: () -> Unit) {
    val unlocked = (BattlePassManager.getCurrentWarPass()?.id ?: 0) >= cheat.id
    Box(contentAlignment = Alignment.Center, modifier = Modifier.fillMaxSize()) {
        if (unlocked) {
            Image(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingTiny),
                contentScale = ContentScale.FillBounds,
                painter = painterResource(id = R.drawable.battlepass_get_reward),
                contentDescription = null
            )
        }
        val colorFilter = if (unlocked) {
            ColorFilter.tint(
                Yellow60, BlendMode.SrcAtop
            )
        } else {
            null
        }
        Image(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingMediumMinis),
            contentScale = ContentScale.FillBounds,
            colorFilter = colorFilter,
            painter = painterResource(id = R.drawable.battlepass_frame),
            contentDescription = null
        )
        val award = cheat.toAward()
        if (unlocked) {
            AwardList(
                Modifier,
                param = defaultParam.copy(
                    peek = true,
                    textColor = Color.White,
                    callback = callback
                ),
                award = award,
            )
        } else {
            AwardList(
                Modifier,
                param = defaultParam.copy(peek = true, textColor = Color.White),
                award = award
            )
        }
        if (cheat.unlockType == 1) {
            Box(
                Modifier
                    .align(Alignment.TopEnd)
                    .padding(top = paddingMediumMinis),
                contentAlignment = Alignment.Center
            ) {
                Image(
                    modifier = Modifier
                        .width(imageLargePlus)
                        .graphicsLayer {
                            rotationY = 180f
                        },
                    painter = painterResource(R.drawable.common_label),
                    contentDescription = null
                )
                Text(
                    modifier = Modifier.padding(bottom = paddingTiny),
                    text = stringResource(id = R.string.free),
                    style = MaterialTheme.typography.h5
                )
            }
        } else {
            if (!AwardManager.battlePassBought[BattlePassManager.getPassSeason()]!!.value) {
                Image(
                    modifier = Modifier
                        .align(Alignment.BottomStart)
                        .size(imageMediumMinus)
                        .offset(y = -paddingMedium, x = paddingTiny),
                    painter = painterResource(R.drawable.common_lock),
                    contentDescription = null
                )
            }
        }
        if (unlocked) {
            Box(
                modifier = Modifier
                    .align(Alignment.TopCenter)
                    .padding(top = paddingSmallPlus),
                contentAlignment = Alignment.Center
            ) {
                if (BattlePassManager.isThisLevelGained(cheat)) {
                    Image(
                        modifier = Modifier.width(imageHugeFrame + paddingSmallPlus),
                        contentScale = ContentScale.FillWidth,
                        painter = painterResource(id = R.drawable.battlepass_get),
                        contentDescription = null
                    )
                    Text(
                        text = stringResource(id = R.string.already_got),
                        style = MaterialTheme.typography.h3
                    )
                }
            }
        } else {
            // null
        }
    }
}

@Composable
fun CurrentPassItem(modifier: Modifier) {
    Column(
        modifier = modifier
            .padding(end = paddingLargePlus)
            .scale(0.9f)
            .graphicsLayer {
                translationX = paddingHugeLite.toPx()
            },
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Spacer(modifier = Modifier.size(paddingMedium))
        val item = repo.gameCore.getBattlePassPool()
            .first { it.season == BattlePassManager.getPassSeason() }
        val poolId = item.themeReward
        val award = repo.gameCore.getPoolById(poolId).toAward()
        val hero = award.outAllies.first().copy(peek = true)
        EffectButton(onClick = {
            Dialogs.allyDetailDialog.value = hero
        }) {
            SingleDetailAllyCard(ally = hero, BattleManager.getRoleByAlly(ally = hero))
        }
        Spacer(modifier = Modifier.size(paddingMedium))
        if (item.isFree == 1) {
            GameButton(
                text = stringResource(R.string.gain_warpass_exp),
                buttonSize = ButtonSize.Big,
                buttonStyle = ButtonStyle.Orange
            ) {
                GameApp.globalScope.launch {
                    gotoQuest(3)
                }
            }
        } else {
            if (AwardManager.battlePassBought[BattlePassManager.getPassSeason()]!!.value) {
                Text(
                    text = stringResource(R.string.warpass_unlocked),
                    style = MaterialTheme.typography.h3
                )
            } else {
                GameButton(
                    text = stringResource(R.string.unlock_warpass),
                    buttonSize = ButtonSize.Big,
                    buttonStyle = ButtonStyle.Red,
                    textColor = Color.White
                ) {
                    Dialogs.warPassUnlockDialog.value = true
                }
            }
        }
        Spacer(modifier = Modifier.size(paddingSmall))
    }
}