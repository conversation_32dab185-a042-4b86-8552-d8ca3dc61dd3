package com.moyu.chuanqirensheng.feature.pvp.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.BuildConfig
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.logic.task.TaskManager
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.equip.SearchView
import com.moyu.chuanqirensheng.screen.quest.SingleQuest
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.millisToHoursMinutesSeconds
import com.moyu.chuanqirensheng.util.millisToMidnight
import com.moyu.chuanqirensheng.util.refreshNetTime
import com.moyu.core.model.task.GameTask
import kotlinx.coroutines.delay

@Composable
fun PvpQuestScreen() {
    val leftUpdateTime = remember {
        mutableStateOf(0L)
    }
    GameBackground(
        title = stringResource(R.string.pvp_quest),
        bgMask = B50,
        background = R.drawable.bg_pvp_quest
    ) {
        val tasks = remember {
            mutableStateListOf<GameTask>()
        }
        val refresh = remember {
            mutableIntStateOf(0)
        }
        val search = remember {
            mutableStateOf("")
        }
        LaunchedEffect(refresh.intValue.toString() + search.value) {
            if (!isNetTimeValid()) {
                refreshNetTime()
            }
            PvpManager.init()
            TaskManager.init()
            // 完成的任务排前面，已领取的排最后
            TaskManager.pvpTasks.map {
                it.copy(done = TaskManager.getTaskDoneFlow(it))
            }.sortedBy { it.order }.sortedByDescending { (if (it.done) 1000 else 0) + (if (it.opened) -5000 else 0) }
                .apply {
                    tasks.clear()
                    if (search.value.isNotEmpty()) {
                        tasks.addAll(this.filter { it.name.contains(search.value) })
                    } else {
                        tasks.addAll(this)
                    }
                }
        }
        LaunchedEffect(refresh) {
            refreshNetTime()
            if (isNetTimeValid()) {
                while (true) {
                    leftUpdateTime.value = millisToMidnight(getCurrentTime())
                    if (leftUpdateTime.value <= 1000) {
                        delay(1000)
                        // 修改这个，上面的LauncherEffect会刷新任务
                        refresh.intValue += 1
                    }
                    delay(500)
                }
            }
        }
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Spacer(modifier = Modifier.size(padding4))
            if (BuildConfig.FLAVOR.contains("Lite")) {
                SearchView(search)
            }
            Row(Modifier.fillMaxWidth()) {
                Text(
                    modifier = Modifier
                        .padding(start = padding12),
                    text = stringResource(R.string.last_pvp_rank_tips),
                    style = MaterialTheme.typography.h3
                )
                Spacer(modifier = Modifier.weight(1f))
                Text(
                    modifier = Modifier
                        .padding(end = padding12),
                    text = stringResource(R.string.task_refresh_left_time) + leftUpdateTime.value.millisToHoursMinutesSeconds(),
                    style = MaterialTheme.typography.h3
                )
            }

            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .paint(
                        painterResource(id = R.drawable.common_big_frame),
                        contentScale = ContentScale.FillBounds
                    )
                    .verticalScroll(rememberScrollState())
            ) {
                tasks.forEach {
                    SingleQuest(it) {
                        refresh.intValue += 1
                    }
                    Spacer(modifier = Modifier.size(padding10))
                }
            }
        }
    }
}