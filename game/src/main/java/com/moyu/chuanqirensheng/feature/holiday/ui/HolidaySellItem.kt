package com.moyu.chuanqirensheng.feature.holiday.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.dialog.AwardList
import com.moyu.chuanqirensheng.screen.dialog.defaultParam
import com.moyu.chuanqirensheng.screen.sell.SellButton
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding100
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding120
import com.moyu.chuanqirensheng.ui.theme.padding165
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding42
import com.moyu.chuanqirensheng.ui.theme.padding7
import com.moyu.chuanqirensheng.ui.theme.padding80
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.chuanqirensheng.util.toDayHourMinuteSecond
import com.moyu.core.model.sell.Sell
import com.moyu.core.model.sell.toAward


@Composable
fun HolidaySellItem(sell: Sell, leftUpdateTime: Long) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(padding165)
    ) {
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(R.drawable.common_frame_focus_big),
            contentDescription = null
        )
        Row(
            Modifier
                .fillMaxSize()
                .padding(padding19),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            Box(Modifier.width(padding80)) {
                Image(
                    modifier = Modifier.size(padding120, padding100),
                    contentScale = ContentScale.FillWidth,
                    painter = painterResource(id = getImageResourceDrawable(sell.pic)),
                    contentDescription = null
                )
                Text(
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .graphicsLayer {
                            translationY = padding10.toPx()
                        },
                    text = sell.name,
                    style = MaterialTheme.typography.h4,
                    color = Color.White,
                    softWrap = false,
                    overflow = TextOverflow.Visible
                )
                if (sell.desc2 != "0") {
                    Box(
                        contentAlignment = Alignment.Center,
                        modifier = Modifier
                            .padding(top = padding12)
                            .align(Alignment.TopStart)
                    ) {
                        Image(
                            painter = painterResource(id = R.drawable.shop_discount),
                            contentDescription = null,
                            modifier = Modifier.size(padding42)
                        )
                        Text(
                            text = sell.desc2,
                            style = MaterialTheme.typography.h5,
                            modifier = Modifier.offset(y = -padding7)
                        )
                    }
                }
            }
            Spacer(modifier = Modifier.weight(1f))
            Column(Modifier.graphicsLayer {
                translationY = padding4.toPx()
            }, horizontalAlignment = Alignment.CenterHorizontally) {
                Text(
                    text = stringResource(
                        R.string.time_left
                    ) + leftUpdateTime.coerceAtLeast(0).toDayHourMinuteSecond(),
                    style = MaterialTheme.typography.h3,
                    color = Color.White
                )
                AwardList(
                    // todo 商店里不要显示电力，获得时候有就行
                    award = sell.toAward().copy(electric = 0), param = defaultParam.copy(itemSize = ItemSize.Large, showName = false)
                )
            }
            Spacer(modifier = Modifier.weight(1f))
            if (leftUpdateTime > 0) {
                SellButton(sell = sell)
            }
        }
    }
}