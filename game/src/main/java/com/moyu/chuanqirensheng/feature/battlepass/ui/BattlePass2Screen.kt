package com.moyu.chuanqirensheng.feature.battlepass.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.battlepass.BattlePass2Manager
import com.moyu.chuanqirensheng.logic.task.TaskManager
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.common.NavigationTab

@Composable
fun BattlePass2Screen() {
    val listTabItems = remember {
        mutableStateListOf(
            GameApp.instance.getWrapString(R.string.battle_pass_award),
            GameApp.instance.getWrapString(R.string.battle_pass_quest),
        )
    }
    val pagerState = rememberPagerState(initialPage = 0) {
        listTabItems.size
    }
    GameBackground(title = stringResource(R.string.war_pass2)) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            NavigationTab(
                pageState = pagerState,
                titles = listTabItems,
                redIcons = List(listTabItems.size) { index ->
                    if (index == 0) {
                        BattlePass2Manager.hasRed()
                    } else {
                        TaskManager.warPass2Tasks.any {
                            TaskManager.getTaskDoneFlow(it) && !it.opened
                        }
                    }
                }
            )
            HorizontalPager(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
                state = pagerState,
            ) { page ->
                when (page) {
                    0 -> WarPass2Page()
                    else -> WarPass2QuestScreen()
                }
            }
        }
    }
}