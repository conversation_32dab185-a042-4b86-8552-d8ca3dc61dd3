package com.moyu.chuanqirensheng.feature.battlepass.ui

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.battlepass.BattlePass2Manager
import com.moyu.chuanqirensheng.feature.battlepass.KEY_WAR_PASS2_UNLOCK_EVIDENCE
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.CurrentPass2Point
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.common.VipLevel
import com.moyu.chuanqirensheng.screen.dialog.AwardList
import com.moyu.chuanqirensheng.screen.dialog.defaultParam
import com.moyu.chuanqirensheng.ui.theme.Yellow60
import com.moyu.chuanqirensheng.ui.theme.cheatDecHeight
import com.moyu.chuanqirensheng.ui.theme.cheatFrameHeight
import com.moyu.chuanqirensheng.ui.theme.gapMedium
import com.moyu.chuanqirensheng.ui.theme.imageHugeFrame
import com.moyu.chuanqirensheng.ui.theme.imageLargePlus
import com.moyu.chuanqirensheng.ui.theme.imageMedium
import com.moyu.chuanqirensheng.ui.theme.imageMediumMinus
import com.moyu.chuanqirensheng.ui.theme.paddingHugeLite
import com.moyu.chuanqirensheng.ui.theme.paddingLarge
import com.moyu.chuanqirensheng.ui.theme.paddingLargePlus
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingMediumMinis
import com.moyu.chuanqirensheng.ui.theme.paddingMediumPlus
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.ui.theme.paddingSmallPlus
import com.moyu.chuanqirensheng.ui.theme.paddingTiny
import com.moyu.chuanqirensheng.util.gotoQuest
import com.moyu.core.model.BattlePass
import com.moyu.core.model.sell.toAward
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun WarPass2Page() {
    val currentSeason = BattlePass2Manager.getPassSeason()
    // for animate
    val infiniteTransition = rememberInfiniteTransition(label = "")
    val angle by infiniteTransition.animateFloat(
        initialValue = 0f, targetValue = 360f, animationSpec = infiniteRepeatable(
            animation = tween(10000, easing = LinearEasing)
        ), label = ""
    )
    Column(
        modifier = Modifier
            .fillMaxSize()
            .paint(
                painterResource(id = R.drawable.common_page_frame),
                contentScale = ContentScale.FillBounds
            )
    ) {
        Spacer(modifier = Modifier.size(paddingMedium))
        CurrentPass2Point(Modifier.align(Alignment.CenterHorizontally))
        Spacer(modifier = Modifier.size(paddingSmallPlus))
        Box(modifier = Modifier.fillMaxSize()) {
            Image(modifier = Modifier
                .align(Alignment.CenterEnd)
                .height(cheatDecHeight)
                .graphicsLayer {
                    translationX = gapMedium.toPx()
                    translationY = -gapMedium.toPx()
                    this.rotationZ = angle
                }
                .scale(1.6f),
                contentScale = ContentScale.FillHeight,
                painter = painterResource(id = R.drawable.effect_shine),
                contentDescription = null)
            CurrentPass2Item(modifier = Modifier
                .align(Alignment.CenterEnd)
                .graphicsLayer {
                    translationY = -gapMedium.toPx()
                })

            val pool = repo.gameCore.getBattlePass2Pool().filter { it.season == currentSeason }
            LazyColumn(
                modifier = Modifier.align(Alignment.CenterStart),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                items(pool.size) { index ->
                    if (index == 0) {
                        Spacer(modifier = Modifier.size(paddingMedium))
                    }
                    Column {
                        Spacer(modifier = Modifier.size(paddingMedium))
                        OnePass2Item(pool[index]) {
                            GameApp.globalScope.launch(Dispatchers.Main) {
                                val unlocked = (BattlePass2Manager.getCurrentWarPass()?.id
                                    ?: 0) >= pool[index].id
                                if (unlocked) {
                                    if (pool[index].unlockType == 2 && !AwardManager.battlePass2Bought[BattlePass2Manager.getPassSeason()]!!.value) {
                                        repo.gameCore.getUnlockById(KEY_WAR_PASS2_UNLOCK_EVIDENCE).desc.toast()
                                    } else {
                                        BattlePass2Manager.gain(pool[index])
                                    }
                                } else {
                                    GameApp.instance.getWrapString(R.string.battlepass2_not_enough)
                                        .toast()
                                }
                            }
                        }
                    }

                }
            }
        }
        Spacer(modifier = Modifier.size(paddingMedium))
    }
}


@Composable
fun OnePass2Item(cheat: BattlePass, callback: () -> Unit) {
    EffectButton(
        modifier = Modifier.size(cheatFrameHeight), onClick = callback
    ) {
        Pass2Awards(cheat, callback = callback)
        VipLevel(
            modifier = Modifier
                .align(Alignment.TopStart)
                .offset(y = -paddingLarge),
            cheatLevel = cheat.level,
            frame = painterResource(id = R.drawable.battlepass_level_frame)
        )
    }
}

@Composable
fun Pass2Awards(cheat: BattlePass, callback: () -> Unit) {
    val unlocked = (BattlePass2Manager.getCurrentWarPass()?.id ?: 0) >= cheat.id
    Box(contentAlignment = Alignment.Center, modifier = Modifier.fillMaxSize()) {
        if (unlocked) {
            Image(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingTiny),
                contentScale = ContentScale.FillBounds,
                painter = painterResource(id = R.drawable.battlepass_get_reward),
                contentDescription = null
            )
        }
        val colorFilter = if (unlocked) {
            ColorFilter.tint(
                Yellow60, BlendMode.SrcAtop
            )
        } else {
            null
        }
        Image(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingMediumMinis),
            contentScale = ContentScale.FillBounds,
            colorFilter = colorFilter,
            painter = painterResource(id = R.drawable.battlepass_frame),
            contentDescription = null
        )
        val award = cheat.toAward()
        if (unlocked) {
            AwardList(
                Modifier,
                param = defaultParam.copy(
                    peek = true,
                    textColor = Color.White,
                    callback = callback
                ),
                award = award,
            )
        } else {
            AwardList(
                Modifier,
                param = defaultParam.copy(peek = true, textColor = Color.White),
                award = award
            )
        }
        if (cheat.unlockType == 1) {
            Box(
                Modifier
                    .align(Alignment.TopEnd)
                    .padding(top = paddingMediumMinis),
                contentAlignment = Alignment.Center
            ) {
                Image(
                    modifier = Modifier
                        .width(imageLargePlus)
                        .graphicsLayer {
                            rotationY = 180f
                        },
                    painter = painterResource(R.drawable.common_label),
                    contentDescription = null
                )
                Text(
                    modifier = Modifier.padding(bottom = paddingTiny),
                    text = stringResource(id = R.string.free),
                    style = MaterialTheme.typography.h5
                )
            }
        } else {
            if (!AwardManager.battlePass2Bought[BattlePass2Manager.getPassSeason()]!!.value) {
                Image(
                    modifier = Modifier
                        .align(Alignment.BottomStart)
                        .size(imageMediumMinus)
                        .offset(y = -paddingMedium, x = paddingTiny),
                    painter = painterResource(R.drawable.common_lock),
                    contentDescription = null
                )
            }
        }
        if (unlocked) {
            Box(
                modifier = Modifier
                    .align(Alignment.TopCenter)
                    .padding(top = paddingSmallPlus),
                contentAlignment = Alignment.Center
            ) {
                if (BattlePass2Manager.isThisLevelGained(cheat)) {
                    Image(
                        modifier = Modifier.width(imageHugeFrame + paddingSmallPlus),
                        contentScale = ContentScale.FillWidth,
                        painter = painterResource(id = R.drawable.battlepass_get),
                        contentDescription = null
                    )
                    Text(
                        text = stringResource(id = R.string.already_got),
                        style = MaterialTheme.typography.h3
                    )
                }
            }
        } else {
            // null
        }
    }
}

@Composable
fun CurrentPass2Item(modifier: Modifier) {
    Column(
        modifier = modifier
            .padding(end = paddingLargePlus)
            .scale(0.9f)
            .graphicsLayer {
                translationX = paddingHugeLite.toPx()
            },
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Spacer(modifier = Modifier.size(paddingMedium))
        val item = repo.gameCore.getBattlePass2Pool()
            .first { it.season == BattlePass2Manager.getPassSeason() }
        val poolId = item.themeReward
        val award = repo.gameCore.getPoolById(poolId).toAward()
        Box {
            AwardList(award = award, param = defaultParam.copy(itemSize = ItemSize.HugeTalent, peek = true, textColor = Color.White))
            // todo 这里itemId改成了id，兼容下配置修改
            val sell = repo.gameCore.getSellPool().first { it.id == KEY_WAR_PASS2_UNLOCK_EVIDENCE }
            if (sell.desc2 != "0") {
                Box(
                    contentAlignment = Alignment.Center,
                    modifier = Modifier
                        .align(Alignment.TopCenter)
                        .offset(x = -paddingLargePlus, y = paddingMediumPlus)
                )
                {
                    Image(
                        painter = painterResource(id = R.drawable.shop_discount),
                        contentDescription = null,
                        modifier = Modifier.size(imageMedium)
                    )
                    Text(
                        text = sell.desc2,
                        style = MaterialTheme.typography.h6,
                        modifier = Modifier.offset(x = paddingTiny, y = -paddingSmall)
                    )
                }
            }
        }
        Spacer(modifier = Modifier.size(paddingMedium))
        if (item.isFree == 1) {
            GameButton(
                text = stringResource(R.string.gain_warpass2_exp),
                buttonSize = ButtonSize.Big,
                buttonStyle = ButtonStyle.Orange
            ) {
                GameApp.globalScope.launch {
                    gotoQuest(3)
                }
            }
        } else {
            if (AwardManager.battlePass2Bought[BattlePass2Manager.getPassSeason()]!!.value) {
                Text(
                    text = stringResource(R.string.warpass2_unlocked),
                    style = MaterialTheme.typography.h3
                )
            } else {
                GameButton(
                    text = stringResource(R.string.unlock_warpass2),
                    buttonSize = ButtonSize.Big,
                    buttonStyle = ButtonStyle.Red,
                    textColor = Color.White
                ) {
                    Dialogs.warPass2UnlockDialog.value = true
                }
            }
        }
        Spacer(modifier = Modifier.size(paddingSmall))
    }
}