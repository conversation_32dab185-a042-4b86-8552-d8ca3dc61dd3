package com.moyu.chuanqirensheng.feature.tower.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import com.moyu.chuanqirensheng.logic.ally.getFrameDrawable
import com.moyu.chuanqirensheng.logic.ally.getTouchInfo
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.screen.arena.RoleHpWithAnim
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.common.getTextStyle
import com.moyu.chuanqirensheng.screen.more.Stars
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.hpHeight
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.model.role.Role


@Composable
fun SingleRoleView(
    role: Role,
    showName: Boolean = true,
    showHp: Boolean = true,
    extraInfo: String = "",
    textColor: Color = Color.White,
    itemSize: ItemSize = ItemSize.LargePlus,
    selectCallBack: (Role) -> Unit = { Dialogs.roleDetailDialog.value = it }
) {
    val race = role.getRace()
    val ally = role.getAlly()
    EffectButton(modifier = Modifier.width(itemSize.frameSize), onClick = {
        selectCallBack(role)
    }) {
        Column(modifier = Modifier, horizontalAlignment = Alignment.CenterHorizontally) {
            Box(contentAlignment = Alignment.Center) {
                Image(
                    modifier = Modifier.size(itemSize.frameSize),
                    painter = painterResource(ally.getFrameDrawable()),
                    contentDescription = null,
                )
                Image(
                    modifier = Modifier
                        .size(itemSize.itemSize)
                        .clip(RoundedCornerShape(itemSize.itemSize / 12)),
                    alignment = Alignment.TopCenter,
                    contentScale = ContentScale.Crop,
                    painter = painterResource(id = getImageResourceDrawable(race.pic)),
                    contentDescription = ally.getTouchInfo()
                )
                if (extraInfo.isNotEmpty()) {
                    Box(
                        modifier = Modifier
                            .background(B50)
                            .padding(padding4)
                    ) {
                        Text(text = extraInfo, style = itemSize.getTextStyle())
                    }
                }
                Stars(
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .padding(bottom = itemSize.itemSize / 12),
                    ally.star,
                    starHeight = itemSize.itemSize / 5
                )
            }
            if (showHp) {
                Box(
                    modifier = Modifier.size(itemSize.frameSize * 0.88f, hpHeight)
                ) {
                    RoleHpWithAnim { role }
                }
                Spacer(modifier = Modifier.size(padding2))
            }
            if (showName) {
                Text(
                    text = race.name,
                    style = MaterialTheme.typography.h3,
                    maxLines = 1,
                    overflow = TextOverflow.Visible,
                    softWrap = false,
                    textAlign = TextAlign.Center,
                    color = textColor
                )
            }
        }
    }
}