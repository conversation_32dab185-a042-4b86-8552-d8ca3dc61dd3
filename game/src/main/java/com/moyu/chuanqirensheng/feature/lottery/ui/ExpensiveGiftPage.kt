package com.moyu.chuanqirensheng.feature.lottery.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.feature.lottery.LotteryManager
import com.moyu.chuanqirensheng.screen.common.CurrentKeyPoint
import com.moyu.chuanqirensheng.screen.common.CurrentLotteryMoney
import com.moyu.chuanqirensheng.screen.sell.OneSellItem
import com.moyu.chuanqirensheng.ui.theme.gapLarge
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.paddingLarge
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.ui.theme.paddingSmallPlus


@Composable
fun ExpensiveGiftPage() {
    val shopChests = LotteryManager.packages.sortedBy { it.order }
    LaunchedEffect(Unit) {
        LotteryManager.createGifts()
    }
    Column(
        modifier = Modifier
            .fillMaxSize()
            .paint(
                painterResource(id = R.drawable.common_page_frame),
                contentScale = ContentScale.FillBounds
            ),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Spacer(modifier = Modifier.size(paddingMedium))
        Row(
            Modifier
                .align(Alignment.End)
                .padding(end = paddingSmallPlus)
        ) {
            CurrentLotteryMoney(showFrame = true)
            CurrentKeyPoint(showPlus = true, showFrame = true)
        }
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState()),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.size(padding30))
            FlowRow(
                modifier = Modifier,
                horizontalArrangement = Arrangement.spacedBy(paddingLarge),
                verticalArrangement = Arrangement.spacedBy(paddingSmall),
            ) {
                shopChests.forEach {
                    OneSellItem(it, scale = 1.3f)
                }
            }
            Spacer(modifier = Modifier.size(gapLarge))
        }
    }
}