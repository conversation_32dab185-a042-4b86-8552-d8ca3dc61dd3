package com.moyu.chuanqirensheng.feature.pvp.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.ally.AllyCardsRow
import com.moyu.chuanqirensheng.screen.arena.EnemiesRow
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.dialog.SelectAllyData
import com.moyu.chuanqirensheng.ui.theme.gapHugeMinus
import com.moyu.chuanqirensheng.ui.theme.hugeButtonHeight
import com.moyu.chuanqirensheng.ui.theme.singleRoleHeight
import com.moyu.core.model.ally.Ally


@Composable
fun PvpPrepareBattleLayout(
    extraTitle: String = "", allyFilter: (Ally) -> Boolean = { true }, start: () -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally, verticalArrangement = Arrangement.Center
    ) {
        EnemiesRow { repo.enemies }
        Spacer(modifier = Modifier.size(gapHugeMinus))
        EffectButton(onClick = {
            start()
        }) {
            Image(
                modifier = Modifier.height(hugeButtonHeight),
                contentScale = ContentScale.FillHeight,
                painter = painterResource(id = R.drawable.common_large_button),
                contentDescription = null
            )
            Text(text = stringResource(R.string.start_battle), style = MaterialTheme.typography.h1, color = Color.Black,  textAlign = TextAlign.Center)
        }
        Spacer(modifier = Modifier.size(gapHugeMinus))
        AllyCardsRow(modifier = Modifier
            .height(singleRoleHeight)
            .fillMaxWidth(),
            allies = BattleManager.getBattleAllies().filter { !it.isDead() }.filter(allyFilter),
            capacity = 3,
            showName = true,
            canDrag = true,
            showHp = true,
            allyClick = {
                BattleManager.selectAllyToBattle(it)
            }) {
                Dialogs.selectAllyToBattleDialog.value = SelectAllyData(extraString = extraTitle, filter = allyFilter, select = {
                    BattleManager.selectAllyToBattle(it)
                }) {
                    start()
                    true
                }
        }
    }
}