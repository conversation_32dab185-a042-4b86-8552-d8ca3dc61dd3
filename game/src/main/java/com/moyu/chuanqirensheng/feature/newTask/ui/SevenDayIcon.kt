package com.moyu.chuanqirensheng.feature.newTask.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.datastore.KEY_INIT_GAME_TIME
import com.moyu.chuanqirensheng.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.feature.newTask.SevenDayManager
import com.moyu.chuanqirensheng.logic.unlock.UNLOCK_SEVEN_DAY
import com.moyu.chuanqirensheng.logic.unlock.UnlockManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.ui.theme.imageTinyPlus
import com.moyu.chuanqirensheng.ui.theme.paddingMediumPlus
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.util.SEVEN_DAY_SCREEN
import com.moyu.chuanqirensheng.util.getCurrentDay
import com.moyu.chuanqirensheng.util.goto
import com.moyu.chuanqirensheng.util.isNetTimeValid

@Composable
fun SevenDayIcon(modifier: Modifier = Modifier) {
    val unlock = repo.gameCore.getUnlockById(UNLOCK_SEVEN_DAY)
    if (isNetTimeValid() && UnlockManager.getUnlockedFlow(unlock)) {
        val day = getCurrentDay(getLongFlowByKey(KEY_INIT_GAME_TIME))
        // 有可能KEY_INIT_GAME_TIME==0，也表明是新用户，可以显示
        if (day <= 7 || getLongFlowByKey(KEY_INIT_GAME_TIME) == 0L) {
            LaunchedEffect(Unit) {
                SevenDayManager.init()
            }
            Column(horizontalAlignment = Alignment.CenterHorizontally, modifier = modifier) {
                EffectButton(modifier = Modifier.graphicsLayer {
                    translationY = paddingMediumPlus.toPx()
                }, onClick = {
                    goto(SEVEN_DAY_SCREEN)
                }) {
                    Image(
                        modifier = Modifier
                            .size(ItemSize.LargePlus.itemSize),
                        painter = painterResource(id = R.drawable.seven_day_icon),
                        contentDescription = null
                    )
                    if (SevenDayManager.hasRed()) {
                        Image(
                            modifier = Modifier
                                .align(Alignment.TopEnd)
                                .padding(ItemSize.LargePlus.itemSize / 20)
                                .size(imageTinyPlus),
                            painter = painterResource(R.drawable.red_icon),
                            contentDescription = null
                        )
                    }
                }
                Spacer(modifier = Modifier.size(paddingSmall))
                Text(
                    text = stringResource(R.string.seven_day_title),
                    maxLines = 2,
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.h5
                )
            }
        }
    }
}
