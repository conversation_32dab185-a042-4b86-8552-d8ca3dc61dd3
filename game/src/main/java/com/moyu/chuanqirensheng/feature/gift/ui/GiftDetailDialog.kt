package com.moyu.chuanqirensheng.feature.gift.ui

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.zIndex
import androidx.core.content.ContextCompat
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.GameApp.Companion.instance
import com.moyu.chuanqirensheng.cloud.bill.BillingManager
import com.moyu.chuanqirensheng.datastore.KEY_GIFT_AWARDED
import com.moyu.chuanqirensheng.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.logic.sell.SellManager
import com.moyu.chuanqirensheng.logic.sell.getPriceTextWithUnit
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.dialog.AwardList
import com.moyu.chuanqirensheng.screen.dialog.CommonDialog
import com.moyu.chuanqirensheng.screen.dialog.defaultParam
import com.moyu.chuanqirensheng.screen.effect.StrokedText
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.padding45
import com.moyu.chuanqirensheng.ui.theme.padding620
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.model.sell.Gift
import com.moyu.core.model.sell.toAward
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun GiftDetailDialog(show: MutableState<Gift?>) {
    show.value?.takeIf { !getBooleanFlowByKey(KEY_GIFT_AWARDED + it.id) || it.limitBuy == 0 }?.let { gift ->
        CommonDialog(title = gift.name, onDismissRequest = {
            GiftManager.setShowed(gift)
            show.value = null
        }) {
            Image(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(bottom = padding22)
                    .padding(horizontal = padding45),
                painter = painterResource(id = getImageResourceDrawable(gift.pic)),
                contentDescription = null
            )
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(padding620)
                    .padding(horizontal = padding22),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                StrokedText(
                    modifier = Modifier
                        .align(Alignment.Start)
                        .padding(horizontal = padding30, vertical = padding10)
                        .zIndex(999f),
                    text = gift.content.replace("\\n", "\n"),
                    style = MaterialTheme.typography.h1,
                    textColor = Color.Red,
                    strokeColor = Color.White
                )
                Spacer(modifier = Modifier.weight(1f))
                gift.label_pic.takeIf { it != "0" }?.let {
                    Image(
                        modifier = Modifier
                            .align(Alignment.Start)
                            .height(imageLarge)
                            .scale(1.5f)
                            .graphicsLayer {
                                translationY = -padding19.toPx()
                                translationX = padding22.toPx()
                            },
                        contentScale = ContentScale.FillHeight,
                        painter = painterResource(id = getImageResourceDrawable(gift.label_pic)),
                        contentDescription = null
                    )
                }
                Row(
                    Modifier
                        .fillMaxWidth()
                        .background(B50)
                        .clip(RoundedCornerShape(padding10)),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    val sell = repo.gameCore.getSellPool().first { it.id == gift.id }
                    AwardList(
                        award = sell.toAward(),
                        param = defaultParam.copy(
                            itemSize = ItemSize.Large,
                            textColor = Color.White
                        )
                    )
                }
                Spacer(modifier = Modifier.height(padding12))
                val sell = repo.gameCore.getSellPool().first { it.id == gift.id }
                GameButton(text = sell.getPriceTextWithUnit()) {
                    GiftManager.setShowed(gift)
                    show.value = null
                    if (getBooleanFlowByKey(KEY_GIFT_AWARDED + gift.id) && gift.limitBuy != 0) {
                        // 已经买了，并且是只能买一次的东西，就不给再买
                        instance.getWrapString(R.string.sold_out).toast()
                    } else {
                        GameApp.globalScope.launch(Dispatchers.Main) {
                            if (instance.resources.getBoolean(R.bool.has_billing)) {
                                BillingManager.prepay(sell) {
                                    GameApp.globalScope.launch(Dispatchers.Main) {
                                        SellManager.dealAfterPay(sell, sell.toAward())
                                    }
                                }
                            } else {
                                if (instance.canShowAifadian()) {
                                    // todo
                                    val sell = repo.gameCore.getSellPool().first { it.id == gift.id }
                                    val uri: Uri = Uri.parse(sell.desc)
                                    val intent = Intent(Intent.ACTION_VIEW, uri)
                                    ContextCompat.startActivity(
                                        instance.activity,
                                        intent,
                                        Bundle()
                                    )
                                } else {
                                    instance.getWrapString(R.string.cant_get_yet).toast()
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}