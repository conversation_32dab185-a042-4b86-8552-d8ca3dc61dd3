package com.moyu.chuanqirensheng.feature.holiday

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateMapOf
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.datastore.KEY_HOLIDAY_MISSION_GAINED
import com.moyu.chuanqirensheng.datastore.KEY_HOLIDAY_SIGN_GAINED
import com.moyu.chuanqirensheng.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.model.award.GuardedB
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.util.getDaySinceDec24
import com.moyu.core.model.Sign
import com.moyu.core.model.sell.toAward


object HolidaySignManager {
    private val gainedMap = mutableStateMapOf<Int, MutableState<Boolean>>()

    fun init() {
        repo.gameCore.getSignPool().filter { it.type == 2 }.forEach {
            gainedMap[it.id] = GuardedB(KEY_HOLIDAY_SIGN_GAINED + it.id)
        }
    }

    suspend fun gain(mission: Sign, double: Boolean = false) {
        val gained = gainedMap.get(mission.id) ?: return
        if (gained.value) {
            GameApp.instance.getWrapString(R.string.already_got).toast()
            return
        }
        if (getDaySinceDec24() < mission.day) {
            GameApp.instance.getWrapString(R.string.not_enough_day).toast()
            return
        }
        gained.value = true
        setBooleanValueByKey(KEY_HOLIDAY_MISSION_GAINED + mission.id, true)
        val award = mission.toAward().let {
            if (double) {
                it + it
            } else it
        }
        Dialogs.awardDialog.value = award
        AwardManager.gainAward(award)
    }

    fun isSignGained(mission: Sign): Boolean {
        return gainedMap[mission.id]?.value ?: false
    }

    fun getShowSigns(): List<Sign> {
        val objects = repo.gameCore.getSignPool().filter { it.type == 2 }
        return objects
    }

    fun showRed(): Boolean {
        return getShowSigns().any {
            getDaySinceDec24() >= it.day && !isSignGained(it)
        }
    }
}