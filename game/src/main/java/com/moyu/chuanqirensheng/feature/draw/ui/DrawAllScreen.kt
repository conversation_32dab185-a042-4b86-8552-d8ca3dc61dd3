package com.moyu.chuanqirensheng.feature.draw.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.draw.DrawManager
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.common.NavigationTab

@Composable
fun DrawAllScreen() {
    val listTabItems = remember {
        mutableStateListOf(
            GameApp.instance.getWrapString(R.string.draw_title),
            GameApp.instance.getWrapString(R.string.draw_task),
        )
    }
    val pagerState = rememberPagerState {
        listTabItems.size
    }
    LaunchedEffect(Unit) {
        DrawManager.refresh()
    }
    GameBackground(title = stringResource(R.string.draw_icon)) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            NavigationTab(pagerState, listTabItems, DrawManager.getReds())
            HorizontalPager(
                state = pagerState,
            ) { page ->
                when (page) {
                    0 -> DrawPage()
                    else -> DrawTaskPage()
                }
            }
        }
    }
}