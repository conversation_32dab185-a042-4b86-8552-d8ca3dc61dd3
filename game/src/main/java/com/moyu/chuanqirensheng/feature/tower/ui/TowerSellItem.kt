package com.moyu.chuanqirensheng.feature.tower.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.dialog.AwardList
import com.moyu.chuanqirensheng.screen.sell.SellButton
import com.moyu.chuanqirensheng.ui.theme.padding100
import com.moyu.chuanqirensheng.ui.theme.padding150
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding28
import com.moyu.chuanqirensheng.ui.theme.padding40
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding7
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.ui.theme.padding80
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.model.Tower
import com.moyu.core.model.sell.toAward


@Composable
fun TowerSellItem(tower: Tower) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(padding150)
    ) {
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(R.drawable.common_frame_focus2),
            contentDescription = null
        )
        Row(
            Modifier
                .fillMaxSize()
                .padding(horizontal = padding19),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            val sell = repo.gameCore.getSellPool().first { it.id == tower.reward }
            Box(Modifier.width(padding100)) {
                Image(
                    modifier = Modifier.size(padding100, padding80).graphicsLayer {
                        translationY = -padding8.toPx()
                    },
                    contentScale = ContentScale.FillHeight,
                    painter = painterResource(id = getImageResourceDrawable(sell.pic)),
                    contentDescription = null
                )
                Text(
                    modifier = Modifier.width(padding100)
                        .align(Alignment.BottomCenter)
                        .graphicsLayer {
                            translationY = padding28.toPx()
                        },
                    text = stringResource(R.string.tower_level, tower.id),
                    style = MaterialTheme.typography.h3,
                    color = Color.White,
                    minLines = 2,
                    textAlign = TextAlign.Center
                )
                if (sell.desc2 != "0") {
                    Box(
                        contentAlignment = Alignment.Center,
                        modifier = Modifier
                            .align(Alignment.TopStart).graphicsLayer {
                                translationY = -padding8.toPx()
                            }
                    ) {
                        Image(
                            painter = painterResource(id = R.drawable.shop_discount),
                            contentDescription = null,
                            modifier = Modifier.size(padding40)
                        )
                        Text(
                            text = sell.desc2,
                            style = MaterialTheme.typography.h6,
                            modifier = Modifier.width(padding26).offset(y = -padding7),
                            maxLines = 2,
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }
            Column(Modifier
                .weight(1f)
                .graphicsLayer {
                    translationY = padding6.toPx()
                }, horizontalAlignment = Alignment.CenterHorizontally) {
                AwardList(
                    // todo 商店里不要显示电力，获得时候有就行
                    award = sell.toAward().copy(electric = 0)
                )
            }
            if (tower.id > TowerManager.maxLevel.value) {
                GameButton(
                    buttonSize = ButtonSize.MediumMinus,
                    text = stringResource(R.string.tower_not_done),
                    enabled = false
                ) {
                    GameApp.instance.getWrapString(R.string.tower_not_done_tips).toast()
                }
            } else if (tower.id in TowerManager.gainedLevels) {
                GameButton(
                    buttonSize = ButtonSize.MediumMinus,
                    text = stringResource(R.string.sold_out),
                    enabled = false
                ) {

                }
            } else {
                SellButton(sell)
            }
        }
    }
}