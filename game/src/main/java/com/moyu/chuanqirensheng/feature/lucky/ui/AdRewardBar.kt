package com.moyu.chuanqirensheng.feature.lucky.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.GenericShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.LayoutDirection
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.feature.lucky.AdManager
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.dialog.ForeverGif
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.imageMedium
import com.moyu.chuanqirensheng.ui.theme.imageMediumMinus
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.imageSmallPlus
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding8

@Composable
fun AdRewardBar(
    modifier: Modifier = Modifier,
    currentValue: Int,
    maxValue: Int,
    emptyRes: Int = R.drawable.ad_frame,
    fullRes: Int = R.drawable.ad_line,
    showNum: Boolean = true,
    textColor: Color = Color.White,
    style: TextStyle = MaterialTheme.typography.h6,
) {
    Row(modifier = modifier, verticalAlignment = Alignment.CenterVertically) {
        Image(
            modifier = Modifier.size(imageMedium),
            painter = painterResource(id = R.drawable.ad_value_icon),
            contentDescription = null
        )
        Column(Modifier.fillMaxSize()) {
            Row(
                Modifier
                    .height(padding36)
                    .padding(start = padding6, end = padding12)) {
                AdManager.luckyList.forEach {
                    Spacer(modifier = Modifier.weight(it.weight()))
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(text = it.price.toString(), style = style, color = textColor)
                        Image(
                            modifier = Modifier.size(imageSmallPlus),
                            painter = painterResource(id = R.drawable.ad_index),
                            contentDescription = null
                        )
                    }
                }
            }
            Box(Modifier
                .fillMaxWidth()
                .weight(1f), contentAlignment = Alignment.Center) {
                AdBar(
                    modifier = Modifier
                        .fillMaxWidth(),
                    currentValue = currentValue,
                    maxValue = maxValue,
                    emptyRes = emptyRes,
                    fullRes = fullRes,
                    showNum = showNum,
                    textColor = textColor,
                    style = style
                )
                Text(modifier = Modifier.align(Alignment.CenterEnd).padding(end = padding8),
                    text = stringResource(R.string.ad_money_desc), style = MaterialTheme.typography.h6
                )
            }

            Row(
                Modifier
                    .height(padding36)
                    .padding(start = padding6, end = padding12)) {
                AdManager.luckyList.forEach {
                    Spacer(modifier = Modifier.weight(it.weight()))
                    EffectButton(onClick = {
                        if (AdManager.canGetAdMoney(it) && AdManager.alreadyGot(lucky = it).not()) {
                            AdManager.getAdMoney(it)
                        }
                    }) {
                        val available = AdManager.canGetAdMoney(it)
                        val gained = AdManager.alreadyGot(it)
                        if (available && !gained) {
                            ForeverGif(
                                modifier = Modifier
                                    .size(imageMediumMinus)
                                    .scale(1.2f).graphicsLayer { translationY = padding8.toPx() },
                                "pay_",
                                7,
                            )
                        }
                        Image(
                            modifier = Modifier.size(imageMediumMinus),
                            painter = painterResource(id = R.drawable.ad_chest),
                            colorFilter = if (available) null else ColorFilter.tint(
                                B50, BlendMode.SrcAtop
                            ),
                            contentDescription = null
                        )
                        if (gained) {
                            Image(
                                modifier = Modifier.align(Alignment.BottomEnd).size(imageSmall),
                                painter = painterResource(id = R.drawable.talent_learned),
                                contentDescription = null
                            )
                        }
                        if (available && !gained) {
                            Image(
                                modifier = Modifier
                                    .align(Alignment.TopEnd)
                                    .padding(imageMediumMinus / 20)
                                    .size(imageSmall),
                                painter = painterResource(R.drawable.red_icon),
                                contentDescription = null
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun AdBar(
    modifier: Modifier = Modifier,
    currentValue: Int,
    maxValue: Int,
    emptyRes: Int = R.drawable.ad_frame,
    fullRes: Int = R.drawable.ad_line,
    showNum: Boolean = true,
    textColor: Color = Color.White,
    style: TextStyle = MaterialTheme.typography.h6
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = modifier
    ) {
        val expBuilder: Path.(size: Size, layoutDirection: LayoutDirection) -> Unit =
            { size: Size, _: LayoutDirection ->
                this.addRect(
                    Rect(
                        0f,
                        0f,
                        size.width * currentValue.toFloat() / maxValue,
                        size.height
                    )
                )
            }
        Image(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = padding2, vertical = padding2)
                .clip(GenericShape(expBuilder))
                .clip(RoundedCornerShape(padding2))
                .align(Alignment.CenterStart),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(id = fullRes),
            contentDescription = null
        )
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(id = emptyRes),
            contentDescription = null
        )
        if (showNum) {
            Text(
                text = "$currentValue/$maxValue",
                style = style,
                color = textColor
            )
        }
    }
}
