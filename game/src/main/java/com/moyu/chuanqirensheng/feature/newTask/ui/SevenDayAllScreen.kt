package com.moyu.chuanqirensheng.feature.newTask.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.newTask.SevenDayManager
import com.moyu.chuanqirensheng.logic.sell.SellManager
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.common.NavigationTab

val sevenDayTabs = if (GameApp.instance.resources.getBoolean(R.bool.has_billing)) {
    mutableStateListOf(
        GameApp.instance.getWrapString(R.string.new_task_tab2),
        GameApp.instance.getWrapString(R.string.new_task_tab3),
        GameApp.instance.getWrapString(R.string.new_task_tab4),
        GameApp.instance.getWrapString(R.string.new_task_tab5),
    )
} else {
    mutableStateListOf(
        GameApp.instance.getWrapString(R.string.new_task_tab2),
        GameApp.instance.getWrapString(R.string.new_task_tab3),
        GameApp.instance.getWrapString(R.string.new_task_tab4),
        GameApp.instance.getWrapString(R.string.new_task_tab5_taptap),
    )
}

@Composable
fun SevenDayAllScreen() {
    val listTabItems = remember {
        sevenDayTabs
    }
    val pagerState = rememberPagerState(initialPage = 0) {
        listTabItems.size
    }
    LaunchedEffect(Unit) {
        SellManager.init()
    }
    GameBackground(title = stringResource(id = R.string.seven_day_title)) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            NavigationTab(
                pageState = pagerState,
                titles = listTabItems,
                redIcons = SevenDayManager.getRedIcons()
            )
            HorizontalPager(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
                state = pagerState,
            ) { page ->
                when (page) {
                    0 -> SevenDayCollectTaskScreen()
                    1 -> SevenDaySellScreen()
                    2 -> SevenDayCostTaskScreen()
                    else -> SevenDayChargeTaskScreen()
                }
            }
        }
    }
}