package com.moyu.chuanqirensheng.feature.pvp.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.model.record.AllRankData
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.common.NavigationTab
import com.moyu.chuanqirensheng.ui.theme.padding12

val pvpRanks = mutableStateOf(emptyList<AllRankData>())
val lastPvpRanks = mutableStateOf(emptyList<AllRankData>())

val pvpRankTabs = listOf(
    GameApp.instance.getWrapString(R.string.pvp_rank1),
    GameApp.instance.getWrapString(R.string.pvp_rank2)
)

@Composable
fun PvpRankScreen() {
    LaunchedEffect(Unit) {
        pvpRanks.value = emptyList()
        lastPvpRanks.value = emptyList()
    }
    val pagerState = rememberPagerState {
        pvpRankTabs.size
    }
    GameBackground(
        title = stringResource(R.string.pvp_rank), background = R.drawable.bg_pvp_quest
    ) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            NavigationTab(pageState = pagerState, titles = pvpRankTabs)
            Row(Modifier.fillMaxWidth()) {
                Text(
                    modifier = Modifier
                        .padding(start = padding12),
                    text = stringResource(R.string.last_pvp_rank_tips),
                    style = MaterialTheme.typography.h3
                )
            }
            HorizontalPager(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
                state = pagerState,
            ) { page ->
                when (page) {
                    0 -> PvpRanksToday()
                    else -> PvpRanksLastDay()
                }
            }
        }
    }
}