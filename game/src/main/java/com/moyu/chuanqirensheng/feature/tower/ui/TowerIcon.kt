package com.moyu.chuanqirensheng.feature.tower.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.logic.unlock.UNLOCK_TOWER
import com.moyu.chuanqirensheng.logic.unlock.UnlockManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.ui.theme.imageTinyPlus
import com.moyu.chuanqirensheng.ui.theme.paddingMediumPlus
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.util.TOWER_SCREEN
import com.moyu.chuanqirensheng.util.goto

@Composable
fun TowerIcon(modifier: Modifier = Modifier) {
    val unlock = repo.gameCore.getUnlockById(UNLOCK_TOWER)
    if (UnlockManager.getUnlockedFlow(unlock)) {
        Column(horizontalAlignment = Alignment.CenterHorizontally, modifier = modifier) {
            EffectButton(modifier = Modifier.graphicsLayer {
                translationY = paddingMediumPlus.toPx()
            }, onClick = {
                goto(TOWER_SCREEN)
            }) {
                Image(
                    modifier = Modifier
                        .size(ItemSize.LargePlus.itemSize),
                    painter = painterResource(id = R.drawable.tower_icon),
                    contentDescription = null
                )
                if (TowerManager.hasRed()) {
                    Image(
                        modifier = Modifier
                            .align(Alignment.TopEnd)
                            .padding(ItemSize.LargePlus.itemSize / 20)
                            .size(imageTinyPlus),
                        painter = painterResource(R.drawable.red_icon),
                        contentDescription = null
                    )
                }
            }
            Spacer(modifier = Modifier.size(paddingSmall))
            Text(
                text = stringResource(R.string.tower_mode),
                maxLines = 2,
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.h5
            )
        }
    }
}
