package com.moyu.chuanqirensheng.feature.pvp.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.api.getLastDayRanks
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.datastore.json
import com.moyu.chuanqirensheng.model.record.AllRankData
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.record.SingleRecord
import kotlinx.coroutines.delay
import kotlinx.serialization.builtins.ListSerializer
import timber.log.Timber

@Composable
fun PvpRanksLastDay() {
    LaunchedEffect(Unit) {
        try {
            delay(200)
            if (lastPvpRanks.value.isEmpty()) {
                getLastDayRanks(
                    GameApp.instance.resources.getString(
                        R.string.platform_channel)).let {
                    lastPvpRanks.value = json.decodeFromString(
                        ListSerializer(AllRankData.serializer()), it.message)
                }
            }
        } catch (e: Exception) {
            Timber.e(e)
            GameApp.instance.getWrapString(R.string.net_error_retry).toast()
        }
    }
    LazyColumn(
        horizontalAlignment = Alignment.CenterHorizontally, modifier = Modifier
            .fillMaxSize(),
        content = {
            items(lastPvpRanks.value.size) { index->
                SingleRecord(
                    lastPvpRanks.value[index], index + 1,
                    content = { rankData, index ->
                        Column {
                            Text(
                                text = stringResource(R.string.pvp_score) + "：" + (rankData.pvpScore),
                                style = MaterialTheme.typography.h3
                            )
                            Text(
                                text = stringResource(
                                    R.string.today_pk,
                                    rankData.pvpData.win,
                                    rankData.pvpData.lose
                                ),
                                style = MaterialTheme.typography.h5
                            )
                        }
                    })
            }
        }
    )
}