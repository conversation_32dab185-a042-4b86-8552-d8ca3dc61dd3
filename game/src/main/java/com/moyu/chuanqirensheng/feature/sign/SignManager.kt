package com.moyu.chuanqirensheng.feature.sign

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateMapOf
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.datastore.KEY_MISSION_GAINED
import com.moyu.chuanqirensheng.datastore.KEY_SIGN_GAINED
import com.moyu.chuanqirensheng.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.task.getLoginDays
import com.moyu.chuanqirensheng.model.award.GuardedB
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.core.model.Sign
import com.moyu.core.model.sell.toAward
import kotlin.math.min


object SignManager {
    private val gainedMap = mutableStateMapOf<Int, MutableState<Boolean>>()

    fun init() {
        repo.gameCore.getSignPool().filter { it.type == 1 }.forEach {
            gainedMap[it.id] = GuardedB(KEY_SIGN_GAINED + it.id)
        }
    }

    suspend fun gain(mission: Sign, double: Boolean = false) {
        val gained = gainedMap[mission.id]!!
        if (gained.value) {
            GameApp.instance.getWrapString(R.string.already_got).toast()
            return
        }
        if (getLoginDays() < mission.day) {
            GameApp.instance.getWrapString(R.string.not_enough_day).toast()
            return
        }
        gained.value = true
        setBooleanValueByKey(KEY_MISSION_GAINED + mission.id, true)
        val award = mission.toAward().let {
            if (double) {
                it + it
            } else it
        }
        Dialogs.awardDialog.value = award
        AwardManager.gainAward(award)
    }

    fun isSignGained(mission: Sign): Boolean {
        return gainedMap[mission.id]?.value ?: false
    }

    fun getShowSigns(): List<Sign> {
        val gainedSize = gainedMap.values.count { it.value }
        val objects = repo.gameCore.getSignPool().filter { it.type == 1 }

        val startIndex = if (gainedSize == objects.size) {
            objects.size - 30
        } else {
            (gainedSize / 30) * 30
        }
        val endIndex = min(startIndex + 30, objects.size)

        return objects.subList(startIndex, endIndex)
    }
}