package com.moyu.chuanqirensheng.feature.battlepass.ui

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.compose.foundation.layout.Arrangement.Absolute.spacedBy
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.core.content.ContextCompat
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.cloud.bill.BillingManager
import com.moyu.chuanqirensheng.feature.battlepass.BattlePass2Manager
import com.moyu.chuanqirensheng.feature.battlepass.KEY_WAR_PASS2_UNLOCK_EVIDENCE
import com.moyu.chuanqirensheng.logic.sell.SellManager
import com.moyu.chuanqirensheng.logic.sell.getPriceTextWithUnit
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.dialog.AwardList
import com.moyu.chuanqirensheng.screen.dialog.CommonDialog
import com.moyu.chuanqirensheng.screen.dialog.defaultParam
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.core.model.sell.toAward
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun WarPass2UnlockDialog(show: MutableState<Boolean>) {
    show.value.takeIf { it }?.let {
        val sell = repo.gameCore.getSellPool().first { it.id == KEY_WAR_PASS2_UNLOCK_EVIDENCE }
        CommonDialog(title = stringResource(R.string.unlock_warpass2), onDismissRequest = {
            show.value = false
        }) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState()),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Text(
                    text = stringResource(R.string.unlock_all_gain),
                    style = MaterialTheme.typography.h3,
                    color = Color.Black
                )
                val warPassLocked = repo.gameCore.getBattlePass2Pool().filter { it.unlockType == 2 }
                AwardList(
                    Modifier,
                    award = warPassLocked.map { it.toAward() }.toAward(),
                    mainAxisAlignment = spacedBy(padding10),
                    param = defaultParam.copy(
                        peek = true,
                        textColor = Color.Black,
                        itemSize = ItemSize.LargePlus,
                    ),
                )
                if (warPassLocked.filter {
                        it.level <= (BattlePass2Manager.getCurrentWarPass()?.level ?: 0)
                    }.isNotEmpty()) {
                    Text(
                        text = stringResource(R.string.unlock_gain_now),
                        style = MaterialTheme.typography.h3,
                        color = Color.Black
                    )
                    AwardList(
                        Modifier,
                        award = warPassLocked.filter {
                            it.level <= (BattlePass2Manager.getCurrentWarPass()?.level ?: 0)
                        }.map { it.toAward() }.toAward(),
                        mainAxisAlignment = spacedBy(padding10),
                        param = defaultParam.copy(
                            peek = true,
                            textColor = Color.Black,
                            itemSize = ItemSize.LargePlus,
                        ),
                    )
                }
                Spacer(modifier = Modifier.weight(1f))
                GameButton(
                    buttonSize = ButtonSize.Medium,
                    enabled = sell.price == 0 || sell.storage > 0,
                    buttonStyle = ButtonStyle.Orange,
                    text = sell.getPriceTextWithUnit()
                ) {
                    val item = repo.gameCore.getBattlePass2Pool()
                        .first { it.season == BattlePass2Manager.getPassSeason() }
                    if (GameApp.instance.canShowAifadian()) {
                        if (!GameApp.instance.resources.getBoolean(R.bool.has_billing)) {
                            val unlockUrl = repo.gameCore.getUnlockById(KEY_WAR_PASS2_UNLOCK_EVIDENCE).url
                            val uri: Uri = Uri.parse(unlockUrl)
                            val intent = Intent(Intent.ACTION_VIEW, uri)
                            ContextCompat.startActivity(GameApp.instance.activity, intent, Bundle())
                        } else {
                            GameApp.globalScope.launch(Dispatchers.Main) {
                                val sell = repo.gameCore.getSellPool()
                                    .first { it.id.toString() == BattlePass2Manager.getGoogleItemIdBySeason(item.season) }
                                BillingManager.prepay(sell = sell) {
                                    GameApp.globalScope.launch(Dispatchers.Main) {
                                        SellManager.dealAfterPay(sell, sell.toAward())
                                    }
                                }
                            }
                        }
                    } else {
                        GameApp.instance.doJumpQQ()
                    }
            }
                // 是付费商品
//                GameApp.globalScope.launch(Dispatchers.Main) {
//                    BillingManager.prepay(sell.name, sell.price * 100, sell.googleItemId) {
//                        GameApp.globalScope.launch(Dispatchers.Main) {
//                            sell.toAward().apply {
//                                AwardManager.gainAward(this)
//                                Dialogs.awardDialog.value = this
//                            }
//                            show.value = false
//                        }
//                    }
//                }
            }
        }
    }
}