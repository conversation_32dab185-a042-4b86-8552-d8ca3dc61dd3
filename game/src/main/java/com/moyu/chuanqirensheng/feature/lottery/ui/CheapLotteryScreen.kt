package com.moyu.chuanqirensheng.feature.lottery.ui

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.datastore.KEY_LOTTERY_INIT_TIME_IN_A_WEEK
import com.moyu.chuanqirensheng.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.feature.lottery.LotteryManager
import com.moyu.chuanqirensheng.screen.common.CurrentKeyPoint
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.ui.theme.SkillLevel6Color
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.paddingLargePlus
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.util.LOGIN_SCREEN
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.goto
import com.moyu.chuanqirensheng.util.timeLeft
import com.moyu.chuanqirensheng.util.toDayHourMinuteSecond
import kotlinx.coroutines.delay

@Composable
fun CheapLotteryScreen() {
    GameBackground(title = stringResource(id = R.string.lottery_title1)) {
        val leftUpdateTime = remember {
            mutableLongStateOf(0L)
        }
        LaunchedEffect(Unit) {
            LotteryManager.refresh()
        }
        LaunchedEffect(Unit) {
            while (true) {
                leftUpdateTime.longValue = timeLeft(
                    getCurrentTime(), getLongFlowByKey(
                        KEY_LOTTERY_INIT_TIME_IN_A_WEEK
                    ), 5
                )
                if (leftUpdateTime.longValue <= 1000) {
                    delay(1000)
                    goto(LOGIN_SCREEN)
                }
                delay(500)
            }
        }
        Column(
            modifier = Modifier
                .fillMaxSize()
                .paint(
                    painterResource(id = R.drawable.common_page_frame),
                    contentScale = ContentScale.FillBounds
                )
                .verticalScroll(rememberScrollState()),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Row(Modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically) {
                if (leftUpdateTime.longValue > 0 && LotteryManager.showCheap()) {
                    Text(
                        modifier = Modifier
                            .padding(start = padding12),
                        text = stringResource(R.string.time_left) + leftUpdateTime.longValue.toDayHourMinuteSecond(),
                        style = MaterialTheme.typography.h3
                    )
                }
                Spacer(modifier = Modifier.weight(1f))
                CurrentKeyPoint(showPlus = true, showFrame = true)
            }
            Spacer(modifier = Modifier.size(paddingSmall))
            Text(
                modifier = Modifier
                    .padding(horizontal = padding12),
                text = stringResource(R.string.do_lottery_tips),
                style = MaterialTheme.typography.h3,
                color = SkillLevel6Color
            )
            Spacer(modifier = Modifier.size(paddingLargePlus))
            Box(
                Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
                CheapLotteryLayout()
            }
        }
        // 最后3秒钟也不让点击了，准备要自动关闭
        if (LotteryManager.spinning.value || leftUpdateTime.longValue <= 3000) {
            Spacer(modifier = Modifier
                .fillMaxSize()
                .clickable { })
        }
    }
}