package com.moyu.chuanqirensheng.feature.tower

import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.BuildConfig
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.api.postTowerRankData
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.GameApp.Companion.instance
import com.moyu.chuanqirensheng.datastore.KEY_GAINED_TOWER_LEVELS
import com.moyu.chuanqirensheng.datastore.KEY_INVISIBLE_IN_RANK
import com.moyu.chuanqirensheng.datastore.KEY_MAX_TOWER_LEVEL
import com.moyu.chuanqirensheng.datastore.KEY_TOWER_ALLY1_IDS
import com.moyu.chuanqirensheng.datastore.KEY_TOWER_ALLY2_IDS
import com.moyu.chuanqirensheng.datastore.KEY_TOWER_ALLY3_IDS
import com.moyu.chuanqirensheng.datastore.KEY_TOWER_ALLY_IDS
import com.moyu.chuanqirensheng.datastore.KEY_TOWER_SKILL1_IDS
import com.moyu.chuanqirensheng.datastore.KEY_TOWER_SKILL2_IDS
import com.moyu.chuanqirensheng.datastore.KEY_TOWER_SKILL3_IDS
import com.moyu.chuanqirensheng.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.datastore.getListObject
import com.moyu.chuanqirensheng.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.datastore.setListObject
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.feature.newTask.SevenDayManager.markGoogleSellItem
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.sell.SELL_FOREVER
import com.moyu.chuanqirensheng.logic.unlock.UNLOCK_TOWER
import com.moyu.chuanqirensheng.logic.unlock.UnlockManager
import com.moyu.chuanqirensheng.model.award.Guarded
import com.moyu.chuanqirensheng.model.record.AllRankData
import com.moyu.chuanqirensheng.repository.Dialogs.awardDialog
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.sub.report.ReportManager
import com.moyu.chuanqirensheng.util.TOWER_BATTLER_SCREEN
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.getVersionCode
import com.moyu.chuanqirensheng.util.goto
import com.moyu.core.model.Tower
import com.moyu.core.model.ally.Ally
import com.moyu.core.model.role.Role
import com.moyu.core.model.sell.Award
import com.moyu.core.model.sell.Sell
import com.moyu.core.model.sell.toAward
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber

fun Tower.getTypeName(): String {
    return when (type.first()) {
        1 -> GameApp.instance.getWrapString(R.string.tower_type1)
        2 -> GameApp.instance.getWrapString(R.string.tower_type2)
        3 -> GameApp.instance.getWrapString(R.string.tower_type3)
        else -> GameApp.instance.getWrapString(R.string.tower_type4)
    }
}

fun Tower.getTypeIcon(): Int {
    return when (type.first()) {
        1 -> R.drawable.tower_battle1
        2 -> R.drawable.tower_battle2
        3 -> R.drawable.tower_battle3
        else -> R.drawable.tower_battle4
    }
}

object TowerManager {
    val targetLevel = mutableStateOf(repo.gameCore.getTowerPool().first())
    val maxLevel = Guarded(KEY_MAX_TOWER_LEVEL)
    val gainedLevels = mutableStateListOf<Int>()
    val lastTowerAllyIds = mutableListOf<Int>()

    val lastTowerSkill1Ids = mutableListOf<Int>()
    val lastTowerSkill2Ids = mutableListOf<Int>()
    val lastTowerSkill3Ids = mutableListOf<Int>()
    val lastTowerAlly1Id = mutableStateOf(0)
    val lastTowerAlly2Id = mutableStateOf(0)
    val lastTowerAlly3Id = mutableStateOf(0)
    val lastTowerSkillLists = listOf(lastTowerSkill1Ids, lastTowerSkill2Ids, lastTowerSkill3Ids)
    val lastTowerSkillAllyIds = listOf(lastTowerAlly1Id, lastTowerAlly2Id, lastTowerAlly3Id)


    fun init() {
        gainedLevels.clear()
        gainedLevels.addAll(getListObject(KEY_GAINED_TOWER_LEVELS))

        lastTowerAllyIds.clear()
        lastTowerAllyIds.addAll(getListObject(KEY_TOWER_ALLY_IDS))

        lastTowerSkill1Ids.clear()
        lastTowerSkill1Ids.addAll(getListObject(KEY_TOWER_SKILL1_IDS))
        lastTowerSkill2Ids.clear()
        lastTowerSkill2Ids.addAll(getListObject(KEY_TOWER_SKILL2_IDS))
        lastTowerSkill3Ids.clear()
        lastTowerSkill3Ids.addAll(getListObject(KEY_TOWER_SKILL3_IDS))
        lastTowerAlly1Id.value = getIntFlowByKey(KEY_TOWER_ALLY1_IDS)
        lastTowerAlly2Id.value = getIntFlowByKey(KEY_TOWER_ALLY2_IDS)
        lastTowerAlly3Id.value = getIntFlowByKey(KEY_TOWER_ALLY3_IDS)

    }

    fun hasRed(): Boolean {
        // 获取当前等级可以解锁的所有奖励关卡
        val rewardLevels = (5..maxLevel.value).filter { it % 5 == 0 }.filter { towerId ->
            val tower = repo.gameCore.getTowerPool().first { it.id == towerId }
            val sell = repo.gameCore.getSellPool().first { it.id == tower.reward }
            sell.isFreeGift()
        }

        // 检查这些奖励关卡中是否有未领取的奖励
        return rewardLevels.any { !gainedLevels.contains(it) }
    }


    fun failed(allies: List<Role>, enemies: List<Role>) {
        repo.inBattle.value = false
    }

    fun win(allies: List<Role>, enemies: List<Role>) {
        if (targetLevel.value.id > maxLevel.value) {
            maxLevel.value = targetLevel.value.id
            val award = Award(diamond = repo.gameCore.getTowerAwardKey(targetLevel.value.layer))
            awardDialog.value = award
            GameApp.globalScope.launch(Dispatchers.Main) {
                AwardManager.gainAward(award)
            }
            uploadTowerRank()
        }
        repo.inBattle.value = false
    }

    fun fight(tower: Tower) {
        if (tower.id > maxLevel.value + 1) {
            GameApp.instance.getWrapString(R.string.tower_fight_tips).toast()
            return
        } else if (tower.id <= maxLevel.value) {
            return
        }
        targetLevel.value = tower
        goto(TOWER_BATTLER_SCREEN)
    }

    /**
     * "战斗类型
     *  1=0
     *  2=诅咒ID
     *  3=限定种族，1~4，不限定英雄
     *  4=限时回合数"
     */
    fun getTowerFilter(tower: Tower): (Ally) -> Boolean {
        return if (tower.type.first() == 3) {
            {
                it.getRace().raceType == tower.playPara1.first()
            }
        } else {
            {
                true
            }
        }
    }

    fun uploadTowerRank() {
        // todo 放开lite包上传
        if ((DebugManager.unlockAll || !BuildConfig.FLAVOR.contains("Lite"))) {
            GameApp.globalScope.launch(Dispatchers.IO) {
                try {
                    postTowerRankData(
                        AllRankData(
                            time = getCurrentTime(),
                            versionCode = getVersionCode(),
                            userName = if (getBooleanFlowByKey(KEY_INVISIBLE_IN_RANK)) "????" else GameApp.instance.getUserName()
                                ?: GameApp.instance.getWrapString(R.string.not_login),
                            userId = GameApp.instance.getObjectId() ?: "0",
                            userPic = if (getBooleanFlowByKey(KEY_INVISIBLE_IN_RANK)) "skill_7019" else GameApp.instance.getAvatarUrl() ?: "0",
                            platformChannel = GameApp.instance.resources.getString(R.string.platform_channel),
                            towerLevel = maxLevel.value
                        )
                    )
                } catch (e: Exception) {
                    Timber.e(e)
                }
            }
        }
    }

    suspend fun openPackage(sell: Sell) {
        if (sell.isKeyMoney()) {
            AwardManager.gainKey(-sell.price)
        }

        val tower = repo.gameCore.getTowerPool().first { it.reward == sell.id }
        if (gainedLevels.contains(tower.id)) {
            return
        }
        gainedLevels.add(tower.id)
        setListObject(KEY_GAINED_TOWER_LEVELS, gainedLevels)

        ReportManager.onShopPurchase(
            sellId = sell.id,
            price = sell.price,
            priceType = sell.priceType
        )

        // 这些商品都是付费商品，但是在taptap，用了英雄币，已经获得了特权值奖励，这里要去掉特权
        val realAward = sell.toAward().let {
            if (instance.resources.getBoolean(R.bool.has_billing)) {
                it
            } else {
                it.copy(electric = 0)
            }
        }
        AwardManager.gainAward(realAward)
        awardDialog.value = realAward
        setBooleanValueByKey(SELL_FOREVER + sell.id, true)
        markGoogleSellItem(sell)
    }

    fun getRedIcons(): List<Boolean> {
        return listOf(false, hasRed(), false)
    }

    fun unlocked(): Boolean {
        val unlock = repo.gameCore.getUnlockById(UNLOCK_TOWER)
        return UnlockManager.getUnlockedFlow(unlock)
    }
}