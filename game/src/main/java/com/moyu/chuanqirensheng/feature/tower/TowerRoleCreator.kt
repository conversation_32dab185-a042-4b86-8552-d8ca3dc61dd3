package com.moyu.chuanqirensheng.feature.tower

import com.moyu.chuanqirensheng.feature.pvp.pvpTalentMainIds
import com.moyu.chuanqirensheng.logic.skill.ExtraSkillProcessor.doPvpSkillProperty
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.core.logic.enemy.DefaultAllyCreator
import com.moyu.core.logic.enemy.DefaultEnemyCreator
import com.moyu.core.model.Tower
import com.moyu.core.model.property.Property
import com.moyu.core.model.race.Race
import com.moyu.core.model.role.Role


fun createTowerRole(race: Race, tower: Tower): Role {
    val diffProperty = tower.getDiffProperty()
    return DefaultEnemyCreator.create(
        race,
        diffProperty,
        emptyList()
    )
}


fun createTowerRole(race: Race, talents: Map<Int, Int>): Role {
    var resultProperty = Property()
    talents.filterKeys { it in pvpTalentMainIds }.keys.forEach { mainId ->
        val talentSkill = repo.gameCore.getSkillPool().first { it.mainId == mainId && it.level == talents[mainId] }
        resultProperty += talentSkill.doPvpSkillProperty(race)
    }
    return DefaultAllyCreator.create(
        race,
        resultProperty,
        emptyList(),
    )
}
