package com.moyu.chuanqirensheng.feature.tower.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.logic.rank.TOWER_TYPE
import com.moyu.chuanqirensheng.model.record.AllRankData
import com.moyu.chuanqirensheng.screen.record.RankPage
import com.moyu.chuanqirensheng.ui.theme.padding12

val towerRanks = mutableStateOf(emptyList<AllRankData>())


@Composable
fun TowerRankPage() {
    LaunchedEffect(Unit) {
        towerRanks.value = emptyList()
    }
    RankPage(type = TOWER_TYPE, data = towerRanks) { rankData, rankIndex ->
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Text(
                text = stringResource(R.string.tower_score) + "：" + (rankData.towerLevel),
                style = MaterialTheme.typography.h3
            )
            Spacer(modifier = Modifier.size(padding12))
        }
    }
}