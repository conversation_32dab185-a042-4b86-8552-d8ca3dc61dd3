package com.moyu.chuanqirensheng.feature.pvp

import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.datastore.KEY_DIED_IN_PVP
import com.moyu.chuanqirensheng.datastore.KEY_GAME_TASK_PROGRESS
import com.moyu.chuanqirensheng.datastore.KEY_PK_ALLY1_IDS
import com.moyu.chuanqirensheng.datastore.KEY_PK_ALLY2_IDS
import com.moyu.chuanqirensheng.datastore.KEY_PK_ALLY3_IDS
import com.moyu.chuanqirensheng.datastore.KEY_PK_ALLY_IDS
import com.moyu.chuanqirensheng.datastore.KEY_PK_NUM
import com.moyu.chuanqirensheng.datastore.KEY_PK_SKILL1_IDS
import com.moyu.chuanqirensheng.datastore.KEY_PK_SKILL2_IDS
import com.moyu.chuanqirensheng.datastore.KEY_PK_SKILL3_IDS
import com.moyu.chuanqirensheng.datastore.KEY_PK_TARGET
import com.moyu.chuanqirensheng.datastore.KEY_PK_UPDATE_TIME_IN_MILLIS
import com.moyu.chuanqirensheng.datastore.KEY_PVP_LOSE
import com.moyu.chuanqirensheng.datastore.KEY_PVP_LOSE_TODAY
import com.moyu.chuanqirensheng.datastore.KEY_PVP_SCORE
import com.moyu.chuanqirensheng.datastore.KEY_PVP_WIN
import com.moyu.chuanqirensheng.datastore.KEY_PVP_WIN_TODAY
import com.moyu.chuanqirensheng.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.datastore.getListObject
import com.moyu.chuanqirensheng.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.datastore.increaseIntValueByKey
import com.moyu.chuanqirensheng.datastore.setListObject
import com.moyu.chuanqirensheng.datastore.setLongValueByKey
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.feature.mode.PVPGameMode
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.award.VipManager
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.event.createPvpEnemyRole
import com.moyu.chuanqirensheng.logic.sell.VIP_INDEX
import com.moyu.chuanqirensheng.logic.talent.TalentManager
import com.moyu.chuanqirensheng.logic.task.TaskEvent
import com.moyu.chuanqirensheng.model.award.Guarded
import com.moyu.chuanqirensheng.model.record.AllRankData
import com.moyu.chuanqirensheng.model.record.PvpData
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.screen.effect.dialogEffectState
import com.moyu.chuanqirensheng.screen.effect.loseBattleEffect
import com.moyu.chuanqirensheng.screen.effect.restartEffect
import com.moyu.chuanqirensheng.screen.effect.winBattleEffect
import com.moyu.chuanqirensheng.util.PVP_BATTLE_SCREEN
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.goto
import com.moyu.chuanqirensheng.util.gotoSellWithTabIndex
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.isSameDay
import com.moyu.core.GameCore
import com.moyu.core.model.role.Role
import com.moyu.core.model.sell.Award
import com.moyu.core.model.skill.isBattle
import com.moyu.core.music.SoundEffect
import com.moyu.core.util.RANDOM
import com.moyu.core.util.nextIntClosure
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.UUID

const val MAX_PVP_NUM = 10
const val MAX_ALL_PVP_NUM = 20
const val INIT_PVP_SCORE = 1000

// todo pvp战斗中能生效的天赋列举
val pvpTalentMainIds = listOf(
    6042,6097,6043,6098,6044,6099,6045,6100,6046,6101,6047,6102,6048,6103,6049,6104,6050,6105,6051,6106,6052,6107,6053,6108,6054,6109,6055,6110,6056,6111,6057,6058,6059,6060,6061,6062,6063,6064,6065,6066
)

val pvpLevelBlockMainIds = listOf(
    6097,6098,6099,6100,6101,6102,6103,6104,6105,6106,6107,6108,6109,6110,6111,6042,6043,6044,6045,6046,6047,6048,6049,6050,6051,6052,6053,6054,6055,6056,6057,6058,6059,6060,6061,6062,6063,6064,6065,6066
)

val pvpBlockMainIds = listOf(
    6097,6098,6099,6100,6101,6102,6103,6104,6105,6106,6107,6108,6109,6110,6111
)

object PvpManager {
    val pvpScore = Guarded(KEY_PVP_SCORE, mutableStateOf(INIT_PVP_SCORE))
    val lastPvpAllyIds = mutableListOf<Int>()
    val lastPvpSkill1Ids = mutableListOf<Int>()
    val lastPvpSkill2Ids = mutableListOf<Int>()
    val lastPvpSkill3Ids = mutableListOf<Int>()
    val lastPvpAlly1Id = mutableStateOf(0)
    val lastPvpAlly2Id = mutableStateOf(0)
    val lastPvpAlly3Id = mutableStateOf(0)
    val lastPvpSkillLists = listOf(lastPvpSkill1Ids, lastPvpSkill2Ids, lastPvpSkill3Ids)
    val lastPvpSkillAllyIds = listOf(lastPvpAlly1Id, lastPvpAlly2Id, lastPvpAlly3Id)
    val pkNumToday = Guarded(KEY_PK_NUM)
    val pkWin = Guarded(KEY_PVP_WIN)
    val pkLose = Guarded(KEY_PVP_LOSE)
    val pkWinToday = Guarded(KEY_PVP_WIN_TODAY)
    val pkLoseToday = Guarded(KEY_PVP_LOSE_TODAY)
    val pkTargetList = mutableStateListOf<String>()
    val currentTarget = mutableStateOf(AllRankData(System.currentTimeMillis()))
    val targetsFromServer = mutableStateOf(emptyList<AllRankData>())
    val currentTargets = mutableStateOf(emptyList<AllRankData>())

    suspend fun init() {
        initPkNum()
        lastPvpAllyIds.clear()
        lastPvpAllyIds.addAll(getListObject(KEY_PK_ALLY_IDS))
        lastPvpSkill1Ids.clear()
        lastPvpSkill1Ids.addAll(getListObject(KEY_PK_SKILL1_IDS))
        lastPvpSkill2Ids.clear()
        lastPvpSkill2Ids.addAll(getListObject(KEY_PK_SKILL2_IDS))
        lastPvpSkill3Ids.clear()
        lastPvpSkill3Ids.addAll(getListObject(KEY_PK_SKILL3_IDS))
        lastPvpAlly1Id.value = getIntFlowByKey(KEY_PK_ALLY1_IDS)
        lastPvpAlly2Id.value = getIntFlowByKey(KEY_PK_ALLY2_IDS)
        lastPvpAlly3Id.value = getIntFlowByKey(KEY_PK_ALLY3_IDS)
    }

    fun initPkNum() {
        if (!isNetTimeValid()) {
            return
        }
        if (isSameDay(
                getLongFlowByKey(KEY_PK_UPDATE_TIME_IN_MILLIS),
                getCurrentTime()
            )
        ) {
            if (pkTargetList.isEmpty()) {
                pkTargetList.addAll(getListObject(KEY_PK_TARGET))
            }
        } else {
            pvpScore.value = INIT_PVP_SCORE
            pkNumToday.value = 0
            pkWinToday.value = 0
            pkLoseToday.value = 0
            pkTargetList.clear()
            setLongValueByKey(KEY_PK_UPDATE_TIME_IN_MILLIS, getCurrentTime())
            setListObject(KEY_PK_TARGET, emptyList<String>())
        }
    }

    fun pk(rankData: AllRankData) {
        if (!DebugManager.unlockAll) {
            if (pkWinToday.value + pkLoseToday.value >= MAX_PVP_NUM + VipManager.getExtraPvpNum()
                || pkNumToday.value >= MAX_PVP_NUM + VipManager.getExtraPvpNum()
            ) {
                if (MAX_PVP_NUM + VipManager.getExtraPvpNum() < MAX_ALL_PVP_NUM) {
                    Dialogs.alertDialog.value = CommonAlert(
                        content = GameApp.instance.getWrapString(R.string.pvp_num_limit2),
                        onConfirm = {
                            gotoSellWithTabIndex(VIP_INDEX)
                        }
                    )
                } else {
                    GameApp.instance.getWrapString(R.string.pvp_num_limit).toast()
                }
                return
            }
        }
        if (rankData.pvpData.allyIds.isEmpty()) {
            GameApp.instance.getWrapString(R.string.pvp_error_ally).toast()
            return
        }
        if (currentTarget.value.userId.isNotEmpty()) {
            // 已经选择了对手，正在pk中
            return
        }
        val allies = rankData.pvpData.allyIds.take(3)
        allies.forEach { ally ->
            if (repo.gameCore.getAllyPool().firstOrNull { it.id == ally } == null) {
                GameApp.instance.getWrapString(R.string.pvp_upgrade).toast()
                return
            }
        }
        currentTarget.value = rankData
        repo.gameMode.value = PVPGameMode()
        increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + TaskEvent.PVP_BATTLE.id)
        pkNumToday.value += 1
        val enemies = mutableListOf<Role>()
        repeat(allies.size) {
            val skills =
                if (it == 0) rankData.pvpData.skill1Ids else if (it == 1) rankData.pvpData.skill2Ids else rankData.pvpData.skill3Ids
            enemies.add(
                createPvpEnemyRole(
                    repo.gameCore.getAllyById(allies[it]).getRace(),
                    skills,
                    rankData.pvpData.talentIds,
                )
            )
        }
        repo.enemies.clear()
        repo.enemies.addAll(enemies)

        goto(PVP_BATTLE_SCREEN)
    }

    fun pkFailed(allies: List<Role>, enemies: List<Role>) {
        // 需要根据时间确认是否刷新pk数据
        initPkNum()

        pkLose.value += 1
        pkLoseToday.value += 1
        val index = currentTargets.value.sortedByDescending { it.pvpScore }
            .indexOfFirst { it.userId == currentTarget.value.userId }
        val pvpData = if (index == -1 || index >= repo.gameCore.getPvpPool().size) {
            repo.gameCore.getPvpPool().last()
        } else {
            repo.gameCore.getPvpPool()[index]
        }
        if (lessThanMax()) {
            GameApp.globalScope.launch {
                Dialogs.awardDialog.value =
                    Award(pvpDiamond = pvpData.loseToken, pvpScore = pvpData.losePoint).apply {
                        AwardManager.gainAward(this)
                    }
                EventManager.uploadPvpRank(allies)
            }
        }
        repo.inBattle.value = false
        GameCore.instance.onBattleEffect(SoundEffect.BattleFailed)
        Dialogs.gameLoseDialog.value = allies + enemies
        restartEffect(dialogEffectState, loseBattleEffect)
        increaseIntValueByKey(KEY_DIED_IN_PVP)
        GiftManager.getDisplayGifts(isBattle = true).firstOrNull { !it.dialogShowed }?.let {
            Dialogs.giftDetailDialog.value = it
        }
    }

    private fun lessThanMax(): Boolean {
        return (pkLoseToday.value + pkWinToday.value <= MAX_PVP_NUM + VipManager.getExtraPvpNum()) &&
                pkNumToday.value <= MAX_PVP_NUM + VipManager.getExtraPvpNum()
    }

    fun pkWined(allies: List<Role>, enemies: List<Role>) {
        // 需要根据时间确认是否刷新pk数据
        initPkNum()

        pkWin.value += 1
        pkWinToday.value += 1
        if (pkTargetList.contains(currentTarget.value.userId)) {
            GameApp.instance.getWrapString(R.string.pvp_error_ally).toast()
            repo.inBattle.value = false
        } else {
            pkTargetList.add(currentTarget.value.userId)
            val index = currentTargets.value.sortedByDescending { it.pvpScore }
                .indexOfFirst { it.userId == currentTarget.value.userId }
            val pvpData = if (index == -1 || index >= repo.gameCore.getPvpPool().size) {
                repo.gameCore.getPvpPool().last()
            } else {
                repo.gameCore.getPvpPool()[index]
            }
            if (lessThanMax()) {
                GameApp.globalScope.launch(Dispatchers.Main) {
                    setListObject(KEY_PK_TARGET, pkTargetList)
                    Dialogs.awardDialog.value =
                        Award(pvpDiamond = pvpData.winToken, pvpScore = pvpData.winPoint).apply {
                            AwardManager.gainAward(this)
                        }
                    EventManager.uploadPvpRank(allies)
                }
            }
            repo.inBattle.value = false
            GameCore.instance.onBattleEffect(SoundEffect.BattleWin)
            Dialogs.gameWinDialog.value = allies + enemies
            restartEffect(dialogEffectState, winBattleEffect)
        }
    }

    fun getMockPvpData(): List<AllRankData> {
        val allyList =
            repo.gameCore.getAllyPool().filter { it.star == 0 && it.quality == 3 }
                .shuffled(RANDOM)
                .take(6)
        return List(20) {
            val id = UUID.randomUUID().toString().take(5)
            AllRankData(
                time = getCurrentTime(),
                userId = id,
                userPic = GameApp.instance.getAvatarUrl() ?: "",
                userName = GameApp.instance.getWrapString(R.string.pvp_mock) + id,
                pvpScore = RANDOM.nextIntClosure(
                    (pvpScore.value),
                    (pvpScore.value * 1.1f).toInt()
                ),
                pvpData = PvpData(
                    allyIds = allyList.map { it.id })
            )
        } + List(30) {
            val id = UUID.randomUUID().toString().take(5)
            AllRankData(
                time = getCurrentTime(),
                userId = id,
                userPic = GameApp.instance.getAvatarUrl() ?: "",
                userName = GameApp.instance.getWrapString(R.string.pvp_mock) + id,
                pvpScore = RANDOM.nextIntClosure(
                    (pvpScore.value * 0.9f).toInt(),
                    (pvpScore.value),
                ),
                pvpData = PvpData(
                    allyIds = allyList.map { it.id })
            )
        }
    }

    fun getPvpData(allies: List<Role>): PvpData {
        return PvpData(
            allyIds = allies.map { it.getAlly().id },
            talentIds = TalentManager.getPvpTalents(),
            skill1Ids = allies.getOrNull(0)?.getSkills()?.filter { it.isBattle() }?.map { it.id }?: emptyList(),
            skill2Ids = allies.getOrNull(1)?.getSkills()?.filter { it.isBattle() }?.map { it.id }?: emptyList(),
            skill3Ids = allies.getOrNull(2)?.getSkills()?.filter { it.isBattle() }?.map { it.id }?: emptyList(),
            win = pkWinToday.value,
            lose = pkLoseToday.value
        )
    }

    fun refreshTargets() {
        val listAbove = targetsFromServer.value.filter { it.pvpScore > pvpScore.value }
            .filter { it.userId !in pkTargetList }.shuffled()
        val listBelow = targetsFromServer.value.filter { it.pvpScore <= pvpScore.value }
            .filter { it.userId !in pkTargetList }.shuffled()
        if (listAbove.size < 2) {
            currentTargets.value =
                (listAbove + listBelow.take(5 - listAbove.size)).sortedByDescending { it.pvpScore }
        } else if (listBelow.size < 3) {
            currentTargets.value =
                (listAbove.take(5 - listBelow.size) + listBelow).sortedByDescending { it.pvpScore }
        } else {
            currentTargets.value =
                (listAbove.take(2) + listBelow.take(3)).sortedByDescending { it.pvpScore }
        }
    }
}