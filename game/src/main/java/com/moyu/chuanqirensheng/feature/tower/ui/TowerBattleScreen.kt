package com.moyu.chuanqirensheng.feature.tower.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.datastore.KEY_TOWER_ALLY1_IDS
import com.moyu.chuanqirensheng.datastore.KEY_TOWER_ALLY2_IDS
import com.moyu.chuanqirensheng.datastore.KEY_TOWER_ALLY3_IDS
import com.moyu.chuanqirensheng.datastore.KEY_TOWER_ALLY_IDS
import com.moyu.chuanqirensheng.datastore.KEY_TOWER_SKILL1_IDS
import com.moyu.chuanqirensheng.datastore.KEY_TOWER_SKILL2_IDS
import com.moyu.chuanqirensheng.datastore.KEY_TOWER_SKILL3_IDS
import com.moyu.chuanqirensheng.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.datastore.setListObject
import com.moyu.chuanqirensheng.feature.mode.TowerGameMode
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.feature.tower.createTowerRole
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.arena.BattleFieldLayout
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.common.SettingColumn
import com.moyu.chuanqirensheng.screen.common.settingBattleItems
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.ui.theme.B65
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.util.TOWER_SCREEN
import com.moyu.chuanqirensheng.util.goto
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.isBattle

@Composable
fun TowerBattleScreen() {
    val clickedStartBattle = remember { mutableStateOf(false) }
    val tower = TowerManager.targetLevel.value
    LaunchedEffect(Unit) {
        repo.inGame.value = true
        repo.gameMode.value = TowerGameMode()

        // 将所有角色加入到局内
        BattleManager.selectAllToPvpOrTowerGame()
        val allyList = mutableListOf<Role>()
        val pool = repo.gameCore.getPoolById(tower.playPara2)
        pool.pool.map {
            repo.gameCore.getRaceById(it)
        }.map {
            createTowerRole(it, tower)
        }.forEachIndexed { index, role ->
            allyList.add(role)
        }
        repo.setCurrentEnemies(allyList)
        if (tower.type.first() == 2) {
            // 天气
            repo.gameCore.getSkillById(tower.playPara1.first()).apply {
                allyList.firstOrNull()?.let {
                    it.learnSkill(this, it)
                }
            }
        }
        TowerManager.lastTowerAllyIds.forEachIndexed { index, savedId ->
            BattleManager.allyGameData.firstOrNull { it.id == savedId }?.let {
                BattleManager.selectAllyToBattle(it)
                BattleManager.getBattleAllies().getOrNull(index)?.let { ally ->
                    TowerManager.lastTowerSkillAllyIds.forEachIndexed { index, mutableState ->
                        // 加一层校验，防止位置变化后，技能错位
                        if (mutableState.value == ally.id) {
                            TowerManager.lastTowerSkillLists[index].forEach { skillId ->
                                BattleManager.skillGameData.firstOrNull { it.id == skillId }?.let {
                                    BattleManager.equipSkillToAlly(it, ally)
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    GameBackground(
        title = stringResource(R.string.tower_mode), bgMask = B65, background = R.drawable.bg_pvp
    ) {
        Column(
            modifier = Modifier.fillMaxSize(),
            verticalArrangement = Arrangement.SpaceEvenly,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            if (repo.inBattle.value) {
                Box(modifier = Modifier.graphicsLayer {
                    // todo 简单处理pvp战斗场景的位置
                    translationY = -padding22.toPx()
                }) {
                    BattleFieldLayout({repo.allies}, {repo.enemies})
                }
            } else {
                val filter = TowerManager.getTowerFilter(tower)
                TowerPrepareBattleLayout(allyFilter = filter) {
                    if (clickedStartBattle.value) {
                        // 防止快速点击导致的异常
                        false
                    } else if (BattleManager.getBattleAllies().isEmpty()) {
                        Dialogs.alertDialog.value =
                            CommonAlert(
                                content = GameApp.instance.getWrapString(R.string.no_role_to_battle_tips),
                                onConfirm = {
                                    repo.battle.value.terminate()
                                    repo.inBattle.value = false
                                    TowerManager.failed(
                                        emptyList(),
                                        emptyList())
                                    goto(TOWER_SCREEN)
                                })
                        true
                    } else if (BattleManager.getBattleAllies().any { !filter(it) }) {
                        GameApp.instance.getWrapString(R.string.tower_ally_not_match).toast()
                        false
                    } else if (repo.enemies.none { it.isOver() } == false) {
                        GameApp.instance.getWrapString(R.string.pvp_error_ally).toast()
                        false
                    } else {
                        val battleAllies = BattleManager.getTowerBattleRoles()
                        TowerManager.lastTowerAllyIds.clear()
                        TowerManager.lastTowerAllyIds.addAll(battleAllies.map { it.getAlly().id })
                        setListObject(KEY_TOWER_ALLY_IDS, TowerManager.lastTowerAllyIds)

                        TowerManager.lastTowerSkill1Ids.clear()
                        TowerManager.lastTowerAlly1Id.value = 0
                        battleAllies.getOrNull(0)?.let {
                            TowerManager.lastTowerSkill1Ids.addAll(
                                it.getSkills().filter { it.isBattle() }.map { it.id })
                            TowerManager.lastTowerAlly1Id.value = it.getAlly().id
                        }
                        setIntValueByKey(KEY_TOWER_ALLY1_IDS, TowerManager.lastTowerAlly1Id.value)
                        setListObject(KEY_TOWER_SKILL1_IDS, TowerManager.lastTowerSkill1Ids)

                        TowerManager.lastTowerSkill2Ids.clear()
                        TowerManager.lastTowerAlly2Id.value = 0
                        battleAllies.getOrNull(1)?.let {
                            TowerManager.lastTowerSkill2Ids.addAll(
                                it.getSkills().filter { it.isBattle() }.map { it.id })
                            TowerManager.lastTowerAlly2Id.value = it.getAlly().id
                        }
                        setIntValueByKey(KEY_TOWER_ALLY2_IDS, TowerManager.lastTowerAlly2Id.value)
                        setListObject(KEY_TOWER_SKILL2_IDS, TowerManager.lastTowerSkill2Ids)

                        TowerManager.lastTowerSkill3Ids.clear()
                        TowerManager.lastTowerAlly3Id.value = 0
                        battleAllies.getOrNull(2)?.let {
                            TowerManager.lastTowerSkill3Ids.addAll(
                                it.getSkills().filter { it.isBattle() }.map { it.id })
                            TowerManager.lastTowerAlly3Id.value = it.getAlly().id
                        }
                        setIntValueByKey(KEY_TOWER_ALLY3_IDS, TowerManager.lastTowerAlly3Id.value)
                        setListObject(KEY_TOWER_SKILL3_IDS, TowerManager.lastTowerSkill3Ids)


                        repo.setCurrentAllies(battleAllies)
                        if (repo.isCurrentEnemyEmptyOrDead()) {
                            GameApp.instance.getWrapString(R.string.pvp_error_ally).toast()
                        } else {
                            clickedStartBattle.value = true
                            repo.startBattle()
                        }
                        true
                    }
                }
            }
        }
        SettingColumn(
            Modifier
                .align(Alignment.CenterStart)
                .padding(start = padding6)
                .graphicsLayer {
                    translationY = padding16.toPx()
                }, settingBattleItems
        )
    }
}