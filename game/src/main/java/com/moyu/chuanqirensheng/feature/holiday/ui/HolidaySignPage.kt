package com.moyu.chuanqirensheng.feature.holiday.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.holiday.HolidaySignManager
import com.moyu.chuanqirensheng.logic.task.getLoginDays
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.dialog.AwardList
import com.moyu.chuanqirensheng.screen.dialog.ForeverGif
import com.moyu.chuanqirensheng.screen.dialog.defaultParam
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.imageTinyPlus
import com.moyu.chuanqirensheng.ui.theme.paddingHuge
import com.moyu.chuanqirensheng.ui.theme.paddingHugeLite
import com.moyu.chuanqirensheng.ui.theme.paddingLargePlus
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingMediumMinis
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.ui.theme.paddingSmallPlus
import com.moyu.chuanqirensheng.util.getDaySinceDec24
import com.moyu.core.model.sell.toAward
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun HolidaySignPage() {
    val signs = HolidaySignManager.getShowSigns()
    Column {
        Spacer(modifier = Modifier.size(paddingSmallPlus))
        LazyVerticalGrid(
            modifier = Modifier
                .padding(horizontal = paddingHuge)
                .padding(bottom = paddingMedium, top = paddingHugeLite),
            columns = GridCells.Fixed(4),
            content = {
                items(signs.size) { index ->
                    val sign = signs[index]
                    Box(contentAlignment = Alignment.Center) {
                        Text(
                            modifier = Modifier.align(Alignment.TopCenter),
                            text = stringResource(R.string.day_of, sign.day),
                            color = if (getLoginDays() >= sign.day) Color.White else Color.Gray,
                            style = MaterialTheme.typography.h3
                        )
                        val param = if (getDaySinceDec24() >= sign.day) {
                            defaultParam.copy(
                                itemSize = ItemSize.LargePlus,
                                showName = false,
                                showEffect = false
                            ) {
                                GameApp.globalScope.launch(Dispatchers.Main) {
                                    HolidaySignManager.gain(sign)
                                }
                            }
                        } else {
                            defaultParam.copy(
                                itemSize = ItemSize.LargePlus,
                                showName = false,
                                showEffect = false
                            )
                        }
                        AwardList(
                            award = sign.toAward(),
                            param = param
                        )
                        if (HolidaySignManager.isSignGained(sign)) {
                            Image(
                                painter = painterResource(id = R.drawable.common_choose),
                                contentDescription = null,
                                modifier = Modifier.size(imageLarge)
                            )
                        }
                        if (getDaySinceDec24() >= sign.day && !HolidaySignManager.isSignGained(sign)) {
                            Image(
                                modifier = Modifier
                                    .align(Alignment.TopEnd)
                                    .padding(top = paddingMediumMinis, end = paddingSmall)
                                    .size(imageTinyPlus),
                                painter = painterResource(R.drawable.red_icon),
                                contentDescription = null
                            )
                            ForeverGif(
                                modifier = Modifier
                                    .size(ItemSize.LargePlus.frameSize)
                                    .scale(1.25f)
                                    .graphicsLayer {
                                        translationY = paddingLargePlus.toPx()
                                    },
                                num = 30,
                                translateY = -paddingLargePlus,
                                resource = "guidesquare_",
                            )
                        }
                    }
                }
            })
    }
}