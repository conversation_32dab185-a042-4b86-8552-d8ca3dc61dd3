package com.moyu.chuanqirensheng.util

import android.content.Context
import android.os.SystemClock
import android.provider.Settings
import com.moyu.chuanqirensheng.BuildConfig
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.api.RetrofitModel
import com.moyu.chuanqirensheng.application.GameApp
import java.text.SimpleDateFormat
import java.util.*

/**
 * 日期格式字符串转换成时间戳
 * @param date 字符串日期
 * @param format 如：yyyy-MM-dd HH:mm:ss
 * @return
 */
// yyyy-MM-dd HH:mm:ss
fun date2TimeStamp(timestampMill: Long, format: String = "yyyy-MM-dd HH:mm:ss"): String {
    try {
        val sdf = SimpleDateFormat(format)
        return sdf.format(Date(timestampMill))
    } catch (e: Exception) {
        e.printStackTrace()
    }
    return ""
}

fun Long.millisToHoursMinutesSeconds(): String {
    val hours = this / 1000 / 60 / 60
    val minutes = this / 1000 / 60 % 60
    val seconds = this / 1000 % 60
    return "%02d:%02d:%02d".format(hours, minutes, seconds)
}

fun isSameDay(millis1: Long, millis2: Long): Boolean {
    val interval = millis1 - millis2
    return interval < 86400000 && interval > -86400000 && millis2Days(
        millis1
    ) == millis2Days(millis2)
}

fun inSomeHours(millis1: Long, millis2: Long, hours: Int): Boolean {
    val interval = millis1 - millis2
    return (interval < 3600 * 1000 * hours) && (interval > -3600 * 1000 * hours)
}

fun millis2Days(millis: Long): Long {
    return (28800000L + millis) / 86400000
}

suspend fun refreshNetTime() {
    if (GameApp.instance.lastNetWorkTime.value == 0L) {
        RetrofitModel.getLoginData()
    }
}

fun isNetTimeValid(): Boolean {
    return if (BuildConfig.FLAVOR.contains("Lite")) {
        true
    } else {
        GameApp.instance.lastNetWorkTime.value != 0L
    }
}

fun getCurrentTime(): Long {
    return if (BuildConfig.FLAVOR.contains("Lite")) {
        System.currentTimeMillis()
    } else {
        SystemClock.elapsedRealtime() - GameApp.instance.elapsedDiffTime
    }
}


fun getCurrentDay(initTime: Long): Int {
    return if (isNetTimeValid()) {
        ((getCurrentTime() - initTime) / (1000 * 60 * 60 * 24)).toInt() + 1
    } else 1
}


fun getCurrentDay(): Int {
    return millis2Days(getCurrentTime()).toInt()
}

fun timeLeft(now: Long, initTime: Long, keepDays: Int): Long {
    val targetTime = initTime + keepDays * 86400000L
    return targetTime - now
}

fun gapWeek(now: Long, initTime: Long): Int {
    val gap = now - initTime
    return (gap / 1000 / 60 / 60 / 24 / 7).toInt()
}

fun Long.toDayHourMinuteSecond(): String {
    val days = this / 1000 / 60 / 60 / 24
    return if (days > 0) {
        "$days" + GameApp.instance.getWrapString(R.string.time_left_day)
    } else {
        millisToHoursMinutesSeconds()
    }
}

fun Long.millisToDay(): Int {
    return (this / 1000 / 60 / 60 / 24).toInt()
}

fun isBetween23_45And00_15(millis1: Long): Boolean {
    return !isSameDay(millis1, millis1 - 60 * 1000 * 15) || !isSameDay(
        millis1,
        millis1 + 60 * 1000 * 15
    )
}

fun millisToMidnight(now: Long): Long {
    val todayStartMillis = (now + 28800000L) % 86400000L  // 从当天00:00:00算起已经过的毫秒数
    return 86400000L - todayStartMillis  // 今天剩下的毫秒数到午夜
}

fun getDaySinceDec24(): Int {
    val currentMillis = getCurrentTime()

    val calendar = Calendar.getInstance(TimeZone.getTimeZone("Asia/Shanghai"))
    calendar.set(Calendar.YEAR, 2025) // 设置为 2025 年
    calendar.set(Calendar.MONTH, Calendar.JANUARY) // 1 月（注意：Calendar.JANUARY = 0）
    calendar.set(Calendar.DAY_OF_MONTH, 28) // 2025 年除夕为1月28日
    calendar.set(Calendar.HOUR_OF_DAY, 0) // 0 时
    calendar.set(Calendar.MINUTE, 0)
    calendar.set(Calendar.SECOND, 0)
    calendar.set(Calendar.MILLISECOND, 0)
    val dec24StartMillis = calendar.timeInMillis

    // 计算当前时间距离 2024 年 12 月 24 日的天数
    val dayDifference = (currentMillis - dec24StartMillis) / (24 * 60 * 60 * 1000L)
    return dayDifference.toInt() + 1 // 返回第几天，需从 1 开始
}


// 获取纽约时间 2024 年 12 月 24 日 0 时的时间戳（毫秒）
//fun getNYStartTimeMillis(): Long {
//    val calendar = Calendar.getInstance(TimeZone.getTimeZone("America/New_York"))
//    calendar.set(Calendar.YEAR, 2024) // 设置为 2024 年
//    calendar.set(Calendar.MONTH, Calendar.DECEMBER) // 12 月
//    calendar.set(Calendar.DAY_OF_MONTH, 24) // 24 日
//    calendar.set(Calendar.HOUR_OF_DAY, 0) // 0 时
//    calendar.set(Calendar.MINUTE, 0)
//    calendar.set(Calendar.SECOND, 0)
//    calendar.set(Calendar.MILLISECOND, 0)
//
//    return calendar.timeInMillis
//}

// 获取北京时间 2015 年除夕当天早晨0点的时间戳（毫秒）
fun getNYStartTimeMillis(): Long {
    val calendar = Calendar.getInstance(TimeZone.getTimeZone("Asia/Shanghai"))
    calendar.set(Calendar.YEAR, 2025) // 设置为 2025 年
    calendar.set(Calendar.MONTH, Calendar.JANUARY) // 1 月（注意：Calendar.JANUARY = 0）
    calendar.set(Calendar.DAY_OF_MONTH, 28) // 2025 年除夕为1月28日
    calendar.set(Calendar.HOUR_OF_DAY, 0) // 0 时
    calendar.set(Calendar.MINUTE, 0)
    calendar.set(Calendar.SECOND, 0)
    calendar.set(Calendar.MILLISECOND, 0)

    return calendar.timeInMillis
}


fun isAutoTimeEnabled(context: Context): Boolean {
    return try {
        Settings.Global.getInt(context.contentResolver, Settings.Global.AUTO_TIME) == 1
    } catch (e: Settings.SettingNotFoundException) {
        false // 如果获取不到设置，则默认为 false
    }
}