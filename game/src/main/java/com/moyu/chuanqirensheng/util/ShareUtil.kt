package com.moyu.chuanqirensheng.util

import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.provider.MediaStore
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.repository.toast
import java.util.Calendar


fun shareImage(bitmap: Bitmap) {
    try {
        val intent = Intent()
        intent.action = Intent.ACTION_SEND
        val uri = Uri.parse(
            MediaStore.Images.Media.insertImage(
                GameApp.instance.activity.contentResolver,
                bitmap,
                "IMG" + Calendar.getInstance().getTime(),
                null
            )
        )
        intent.type = "image/*"
        intent.putExtra(Intent.EXTRA_STREAM, uri)
        GameApp.instance.activity.startActivity(Intent.createChooser(intent, "title"))
    } catch (e: Exception) {
        GameApp.instance.getWrapString(R.string.net_error_retry).toast()
    }
}