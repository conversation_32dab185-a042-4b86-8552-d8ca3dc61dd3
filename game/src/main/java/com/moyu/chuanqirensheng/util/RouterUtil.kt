package com.moyu.chuanqirensheng.util

import androidx.navigation.NavHostController
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.screen.quest.questListTabItems
import com.moyu.chuanqirensheng.sub.report.ReportManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

const val PAGE_PARAM_TAB_INDEX = "initTabIndex"


const val LOGIN_SCREEN = "login"
const val MORE_SCREEN = "more"
const val CREATE_COUNTRY_SCREEN = "create_country"
const val STORY_SCREEN = "story"

const val FORTUNE_SCREEN = "fortune"
const val LIFE_SCREEN = "life"
const val SELL_SCREEN = "sell"
const val QUEST_SCREEN = "quest"
const val SETTING_SCREEN = "setting"
const val SIGN_SCREEN = "sign"
const val SEVEN_DAY_SCREEN = "seven_day"


const val SELL_SCREEN_PREFIX = "sell/"
const val SELL_SCREEN_RAW = "sell/$PAGE_PARAM_TAB_INDEX"

const val VIP_SCREEN = "vip_screen"
const val LUNTAN_SCREEN = "luntan_screen"
const val RANK_SCREEN = "rank"
const val CHALLENGE_RANK_SCREEN = "challenge"
const val ABOUT_SCREEN = "about"
const val ABOUT_SHARE_REWARDS_SCREEN ="share_rewards"
const val USER_SHARE_GAME_SCREEN = "user_share_game"
const val FAMOUS_SCREEN = "famous_screen"
const val WARPASS_SCREEN = "warpass_screen"
const val WARPASS2_SCREEN = "warpass2_screen"


const val LOTTERY_SCREEN1 = "lottery_screen1"
const val LOTTERY_SCREEN2 = "lottery_screen2"

// 游戏调试
const val DEBUG_SCREEN = "debug_game"
const val DEBUG_BATTLE = "debug_battle"

// 事件
const val EVENT_SELECT_SCREEN = "event_select"
const val EVENT_DETAIL_SCREEN = "event_detail"


const val PVP_SCREEN = "pvp"
const val PVP_SELL_SCREEN = "pvp_sell"
const val PVP_RANK_SCREEN = "pvp_rank"
const val PVP_CHOOSE_ENEMY_SCREEN = "pvp_choose_enemy"
const val PVP_QUEST_SCREEN = "pvp_quest"
const val PVP_BATTLE_SCREEN = "pvp_battle"

const val DRAW_SCREEN = "draw"
const val HOLIDAY_SCREEN = "holiday"

const val TOWER_SCREEN = "tower_screen"
const val TOWER_BATTLER_SCREEN = "tower_battle_screen"

fun NavHostController.toSingleInstance(route: String) {
    GameApp.globalScope.launch(Dispatchers.Main) {
        if (!popBackStack(route, false)) {
            navigate(route)
            ReportManager.onPage(route)
        }
    }
}

fun isCurrentRoute(route: String): Boolean {
    return GameApp.instance.navController?.currentDestination?.route?.equals(
        route
    ) == true
}

fun popTop() {
    GameApp.instance.navController?.currentDestination?.route?.let {
        if (it == LOGIN_SCREEN) {
            Dialogs.alertDialog.value = CommonAlert(title = GameApp.instance.getWrapString(R.string.quit_game_toast), onConfirm = {
                killSelf()
            })
        } else {
            GameApp.globalScope.launch(Dispatchers.Main) {
                GameApp.instance.navController?.popBackStack(it, true)
            }
        }
    }
}

fun popScreen(route: String) {
    GameApp.globalScope.launch(Dispatchers.Main) {
        GameApp.instance.navController?.popBackStack(route, true)
    }
}

val needVerifyRoutes = listOf(
    SELL_SCREEN,
    ABOUT_SHARE_REWARDS_SCREEN,
    QUEST_SCREEN,
    SEVEN_DAY_SCREEN,
    SIGN_SCREEN,
    WARPASS_SCREEN,
    WARPASS2_SCREEN
)

fun goto(route: String) {
    if (route in needVerifyRoutes && !isNetTimeValid()) {
        (GameApp.instance.getWrapString(R.string.time_error_tips)).toast()
        goto(LOGIN_SCREEN)
    } else {
        GameApp.instance.navController?.toSingleInstance(route)
    }
}


suspend fun gotoQuest(index: Int) {
    withContext(Dispatchers.Main) {
        goto(QUEST_SCREEN)
//        delay(100)
//        questPagerState.scrollToPage(index)
    }
}

suspend fun gotoStory() {
    withContext(Dispatchers.Main) {
        goto(LIFE_SCREEN)
        delay(100)
//        lifePagerState.scrollToPage(1)
    }
}

fun gotoChallengeTask() {
    GameApp.globalScope.launch {
        withContext(Dispatchers.Main) {
            goto(QUEST_SCREEN)
            delay(250)
            if (questListTabItems.value.size == 3) {
//                questPagerState.scrollToPage(2)
            }
        }
    }
}

private fun String.getRawRoute(): String {
    return if (this.startsWith(SELL_SCREEN_PREFIX)) {
        SELL_SCREEN_RAW
    } else {
        this
    }
}

fun pop(route: String) {
    GameApp.instance.navController?.popBackStack(route.getRawRoute(), true)
}

fun gotoSell() {
    gotoSellWithTabIndex()
}

fun gotoSellWithTabIndex(tabIndex: Int = 0) {
    pop(SELL_SCREEN)
    goto("$SELL_SCREEN_PREFIX$tabIndex")

    // 这里是跳转商店，购买魔晶，可能打开着几个弹窗，需要关闭
    Dialogs.allyStarUpDialog.value = null
    Dialogs.allyDetailDialog.value = null
}