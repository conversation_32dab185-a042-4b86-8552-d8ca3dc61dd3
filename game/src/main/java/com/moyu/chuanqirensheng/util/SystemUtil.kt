package com.moyu.chuanqirensheng.util

import android.content.ComponentName
import android.content.Intent
import android.content.pm.PackageManager
import com.moyu.chuanqirensheng.application.GameApp


fun triggerRebirth() {
    val packageManager: PackageManager = GameApp.instance.packageManager
    val intent: Intent? = packageManager.getLaunchIntentForPackage(GameApp.instance.packageName)
    val componentName: ComponentName? = intent?.component
    val mainIntent: Intent = Intent.makeRestartActivityTask(componentName)
    GameApp.instance.startActivity(mainIntent)
    Runtime.getRuntime().exit(0)
}


fun killSelf() {
    Runtime.getRuntime().exit(0)
}