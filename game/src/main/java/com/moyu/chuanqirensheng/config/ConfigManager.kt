package com.moyu.chuanqirensheng.config

import com.moyu.core.config.ADVENTURE_SKILL_FILE_NAME
import com.moyu.core.config.ALLY_FILE_NAME
import com.moyu.core.config.AllyConfigParser
import com.moyu.core.config.BADGE_FILE_NAME
import com.moyu.core.config.BUFF_FILE_NAME
import com.moyu.core.config.BadgeConfigParser
import com.moyu.core.config.BattlePassConfigParser
import com.moyu.core.config.BuffConfigParser
import com.moyu.core.config.COMBINEDBUFF_FILE_NAME
import com.moyu.core.config.COMMON_FILE_NAME
import com.moyu.core.config.CommonConfigParser
import com.moyu.core.config.ConfigHolder
import com.moyu.core.config.DAY_REWARD_FILE_NAME
import com.moyu.core.config.DIALOG_FILE_NAME
import com.moyu.core.config.DRAW_FILE_NAME
import com.moyu.core.config.DayRewardConfigParser
import com.moyu.core.config.DialogConfigParser
import com.moyu.core.config.DrawItemConfigParser
import com.moyu.core.config.EVENT_FILE_NAME
import com.moyu.core.config.EventConfigParser
import com.moyu.core.config.GIFT_FILE_NAME
import com.moyu.core.config.GameTaskConfigParser
import com.moyu.core.config.GiftConfigParser
import com.moyu.core.config.HALO_SKILL_FILE_NAME
import com.moyu.core.config.HERO_FILE_NAME
import com.moyu.core.config.HERO_SKILL_FILE_NAME
import com.moyu.core.config.HeroConfigParser
import com.moyu.core.config.LUCKY_FILE_NAME
import com.moyu.core.config.LuckyConfigParser
import com.moyu.core.config.POOL_FILE_NAME
import com.moyu.core.config.POPULATION_LEVEL_FILE_NAME
import com.moyu.core.config.PVP_FILE_NAME
import com.moyu.core.config.PoolConfigParser
import com.moyu.core.config.PvpConfigParser
import com.moyu.core.config.RACE_FILE_NAME
import com.moyu.core.config.REPUTATION_LEVEL_FILE_NAME
import com.moyu.core.config.RaceConfigParser
import com.moyu.core.config.ReputationLevelConfigParser
import com.moyu.core.config.SCROLL_FILE_NAME
import com.moyu.core.config.SELL_FILE_NAME
import com.moyu.core.config.SIGN_FILE_NAME
import com.moyu.core.config.SKILL_FILE_NAME
import com.moyu.core.config.SPECIAL_SKILL_FILE_NAME
import com.moyu.core.config.STORY_FILE_NAME
import com.moyu.core.config.ScrollConfigParser
import com.moyu.core.config.SellConfigParser
import com.moyu.core.config.SignConfigParser
import com.moyu.core.config.SkillConfigParser
import com.moyu.core.config.StoryConfigParser
import com.moyu.core.config.TALENT_FILE_NAME
import com.moyu.core.config.TALENT_SKILL_FILE_NAME
import com.moyu.core.config.TASK_FILE_NAME
import com.moyu.core.config.TCG_AWARD_FILE_NAME
import com.moyu.core.config.TCG_CARD_FILE_NAME
import com.moyu.core.config.TCG_CARD_TYPE_FILE_NAME
import com.moyu.core.config.TOWER_FILE_NAME
import com.moyu.core.config.TURNTABLE_FILE_NAME
import com.moyu.core.config.TalentConfigParser
import com.moyu.core.config.TcgAwardConfigParser
import com.moyu.core.config.TcgCardConfigParser
import com.moyu.core.config.TcgCardTypeConfigParser
import com.moyu.core.config.TowerConfigParser
import com.moyu.core.config.TurnTableConfigParser
import com.moyu.core.config.UNLOCK_FILE_NAME
import com.moyu.core.config.UnlockConfigParser
import com.moyu.core.config.VIP_FILE_NAME
import com.moyu.core.config.VipConfigParser
import com.moyu.core.config.WAR_PASS2_FILE_NAME
import com.moyu.core.config.WAR_PASS_FILE_NAME
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.withContext

object ConfigManager {
    val configLoaders = listOf(
        ConfigLoader(BUFF_FILE_NAME, BuffConfigParser()),
        ConfigLoader(SKILL_FILE_NAME, SkillConfigParser(), false),
        ConfigLoader(HALO_SKILL_FILE_NAME, SkillConfigParser()),
        ConfigLoader(TALENT_SKILL_FILE_NAME, SkillConfigParser()),
        ConfigLoader(HERO_SKILL_FILE_NAME, SkillConfigParser(), false),
        ConfigLoader(SPECIAL_SKILL_FILE_NAME, SkillConfigParser()),
        ConfigLoader(ADVENTURE_SKILL_FILE_NAME, SkillConfigParser()),
        ConfigLoader(RACE_FILE_NAME, RaceConfigParser()),
        ConfigLoader(REPUTATION_LEVEL_FILE_NAME, ReputationLevelConfigParser()),
        ConfigLoader(POPULATION_LEVEL_FILE_NAME, ReputationLevelConfigParser()),
        ConfigLoader(COMMON_FILE_NAME, CommonConfigParser()),
        ConfigLoader(TALENT_FILE_NAME, TalentConfigParser()),
        ConfigLoader(SELL_FILE_NAME, SellConfigParser(), true),
        ConfigLoader(TASK_FILE_NAME, GameTaskConfigParser()),
        ConfigLoader(UNLOCK_FILE_NAME, UnlockConfigParser()),
        ConfigLoader(GIFT_FILE_NAME, GiftConfigParser()),
        ConfigLoader(TCG_CARD_TYPE_FILE_NAME, TcgCardTypeConfigParser()),
        ConfigLoader(TCG_CARD_FILE_NAME, TcgCardConfigParser()),
        ConfigLoader(TCG_AWARD_FILE_NAME, TcgAwardConfigParser()),
        ConfigLoader(SCROLL_FILE_NAME, ScrollConfigParser(), false),
        ConfigLoader(ALLY_FILE_NAME, AllyConfigParser(), false),
        ConfigLoader(DIALOG_FILE_NAME, DialogConfigParser()),
        ConfigLoader(EVENT_FILE_NAME, EventConfigParser()),
        ConfigLoader(POOL_FILE_NAME, PoolConfigParser()),
        ConfigLoader(BADGE_FILE_NAME, BadgeConfigParser()),
        ConfigLoader(SIGN_FILE_NAME, SignConfigParser()),
        ConfigLoader(VIP_FILE_NAME, VipConfigParser()),
        ConfigLoader(COMBINEDBUFF_FILE_NAME, BuffConfigParser()),
        ConfigLoader(HERO_FILE_NAME, HeroConfigParser()),
        ConfigLoader(STORY_FILE_NAME, StoryConfigParser()),
        ConfigLoader(WAR_PASS_FILE_NAME, BattlePassConfigParser()),
        ConfigLoader(DAY_REWARD_FILE_NAME, DayRewardConfigParser()),
        ConfigLoader(WAR_PASS2_FILE_NAME, BattlePassConfigParser()),
        ConfigLoader(TURNTABLE_FILE_NAME, TurnTableConfigParser()),
        ConfigLoader(LUCKY_FILE_NAME, LuckyConfigParser()),
        ConfigLoader(PVP_FILE_NAME, PvpConfigParser()),
        ConfigLoader(DRAW_FILE_NAME, DrawItemConfigParser()),
        ConfigLoader(TOWER_FILE_NAME, TowerConfigParser()),
)

    suspend fun loadConfigs(configHolder: ConfigHolder) {
        // 并行加载所有配置文件以提高性能
        withContext(Dispatchers.IO) {
            configLoaders.map { loader ->
                async {
                    loader.loadConfig()
                    configHolder.setGameConfig(loader.getKey(), loader.getConfig())
                }
            }.awaitAll()
        }
    }
}