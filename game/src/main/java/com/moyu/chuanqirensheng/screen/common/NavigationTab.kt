package com.moyu.chuanqirensheng.screen.common

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.pager.PagerState
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.zIndex
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.ui.theme.tabButtonHeight
import kotlinx.coroutines.launch

@Composable
fun NavigationTab(
    pageState: PagerState,
    titles: List<String>,
    redIcons: List<Boolean> = listOf(false, false, false, false, false, false)
) {
    val scope = rememberCoroutineScope()
    Row(
        modifier = Modifier
            .zIndex(999f)
            .paint(
                painterResource(id = R.drawable.common_tab), contentScale = ContentScale.FillBounds
            )
    ) {
        titles.forEachIndexed { index, title ->
            EffectButton(modifier = Modifier.weight(1f), onClick = {
                scope.launch {
                    pageState.animateScrollToPage(index)
                }
            }) {
                GameTitle(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(tabButtonHeight),
                    title,
                    selected = index == pageState.currentPage,
                    redIcons[index],
                    textStyle = if (titles.size >= 5) MaterialTheme.typography.h3 else MaterialTheme.typography.h2
                )
            }
        }
    }
}