package com.moyu.chuanqirensheng.screen.skill

import androidx.compose.ui.graphics.Color
import com.moyu.chuanqirensheng.text.SKILL_LABELS
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.isAdventure
import com.moyu.core.model.skill.isHeroSkill

fun Skill.getElementTypeRes(): Int {
    return if (isAdventure()) {
        getImageResourceDrawable("card_adventure_type_$elementType")
    } else if (isHeroSkill()) {
        getImageResourceDrawable("player_attribute_$elementType")
    } else {
        getImageResourceDrawable("card_skill_type_$elementType")
    }

}

fun getSkillDescColor(current: Boolean): Color {
    return if (current) Color.White else Color.Gray
}