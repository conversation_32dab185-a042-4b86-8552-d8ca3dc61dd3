package com.moyu.chuanqirensheng.screen.dialog

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.award.toAward
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.event.isWorldEvent
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.ui.theme.dialogWidth
import com.moyu.chuanqirensheng.ui.theme.paddingHugePlus
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.core.GameCore
import com.moyu.core.model.event.Event
import com.moyu.core.music.SoundEffect
import com.moyu.core.util.toThreeDigits
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun EventPassDialog(show: MutableState<Event?>) {
    show.value?.let { event ->
        val award = remember {
            event.toAward(true).copy(showQuestion = false)
        }
        LaunchedEffect(Unit) {
            GameCore.instance.onBattleEffect(SoundEffect.EventWin)
        }
        CommonDialog(
            title = if (event.isWorldEvent()) stringResource(R.string.world_event_cast) else stringResource(
                id = R.string.the
            ) + BattleManager.adventureProps.value.age.toThreeDigits() + stringResource(
                R.string.year_end
            ),
            onDismissRequest = {
                if (show.value != null) {
                    show.value = null
                    GameApp.globalScope.launch(Dispatchers.Main) {
                        AwardManager.gainAward(award)
                        EventManager.gotoNextEvent(event, true)
                    }
                }
            }) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = paddingMedium),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Spacer(modifier = Modifier.size(paddingMedium))
                Text(
                    modifier = Modifier.fillMaxWidth(),
                    text = event.winText,
                    style = MaterialTheme.typography.h3,
                    color = Color.Black
                )
                Spacer(modifier = Modifier.size(paddingMedium))
                Text(
                    modifier = Modifier.fillMaxWidth(),
                    text = stringResource(id = R.string.next_turn_award),
                    style = MaterialTheme.typography.h3,
                    color = Color.Black
                )
                AwardList(modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.CenterHorizontally), award = award)
                Spacer(modifier = Modifier.weight(1f))
                GameButton(
                    text = stringResource(id = R.string.see_you_again),
                    buttonStyle = ButtonStyle.Orange,
                    buttonSize = ButtonSize.Medium,
                    onClick = {
                        if (show.value != null) {
                            show.value = null
                            GameApp.globalScope.launch(Dispatchers.Main) {
                                AwardManager.gainAward(award)
                                EventManager.gotoNextEvent(event, true)
                            }
                        }
                    })
                Spacer(modifier = Modifier.size(paddingHugePlus))
            }
            ForeverGif(Modifier
                .width(dialogWidth * 0.75f), "complete_", 7, needGap = true)
        }
    }
}


