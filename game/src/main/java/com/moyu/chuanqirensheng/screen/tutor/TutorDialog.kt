@file:OptIn(ExperimentalLayoutApi::class)

package com.moyu.chuanqirensheng.screen.tutor

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.dialog.CommonDialog
import com.moyu.chuanqirensheng.text.tutorText
import com.moyu.chuanqirensheng.text.tutorTitle
import com.moyu.chuanqirensheng.ui.theme.PageTitleColor
import com.moyu.chuanqirensheng.ui.theme.paddingMediumMinis
import com.moyu.chuanqirensheng.ui.theme.paddingSmallPlus
import com.moyu.chuanqirensheng.ui.theme.tutorTitleHeight
import com.moyu.chuanqirensheng.ui.theme.tutorTitleWidth

@Composable
fun TutorDialog(tutorDialog: MutableState<Boolean>) {
    val selectedIndex = remember {
        mutableStateOf(0)
    }
    if (tutorDialog.value) {
        CommonDialog(
            title = GameApp.instance.getWrapString(R.string.tutor),
            onDismissRequest = { tutorDialog.value = false },
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState()),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                FlowRow(
                    modifier = Modifier,
                    horizontalArrangement = Arrangement.Start,
                    overflow = FlowRowOverflow.Visible,
                ) {
                    tutorTitle.forEachIndexed { index, s ->
                        EffectButton(onClick = {
                            selectedIndex.value = index
                        }) {
                            Box(
                                modifier = Modifier.size(tutorTitleWidth, tutorTitleHeight),
                                contentAlignment = Alignment.Center
                            ) {
                                Image(
                                    modifier = Modifier.fillMaxSize(),
                                    contentScale = ContentScale.FillBounds,
                                    painter = painterResource(id = if (selectedIndex.value == index) R.drawable.common_tag1 else R.drawable.common_tag2),
                                    contentDescription = null
                                )
                                Text(
                                    text = s,
                                    style = MaterialTheme.typography.h4,
                                    color = PageTitleColor,
                                    textAlign = TextAlign.Center
                                )
                            }
                        }
                    }
                }
                Text(
                    text = tutorText[selectedIndex.value],
                    style = MaterialTheme.typography.h3,
                    color = Color.Black,
                    modifier = Modifier.padding(horizontal = paddingMediumMinis).padding(top = paddingSmallPlus)
                )
            }
        }
    }
}
