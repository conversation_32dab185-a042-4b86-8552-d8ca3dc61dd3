package com.moyu.chuanqirensheng.screen.dialog

import android.graphics.Bitmap
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.stringResource
import coil.compose.rememberAsyncImagePainter
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.datastore.KEY_SHARE_AWARD_IMAGE
import com.moyu.chuanqirensheng.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.datastore.setBooleanValueByKeySync
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.ui.theme.eventCardBigHeight
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.util.shareImage
import com.moyu.core.model.sell.Award
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@Composable
fun ShareImageDialog(unlock: MutableState<Bitmap?>) {
    unlock.value?.let {
        CommonDialog(
            frame = null,
            heightInDp = eventCardBigHeight * 2.20f,
            title = stringResource(R.string.unlock_feature),
            onDismissRequest = { unlock.value = null }) {
            Column(
                Modifier
                    .fillMaxSize(), horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Image(
                    modifier = Modifier.weight(1f),
                    contentScale = ContentScale.Fit,
                    painter = rememberAsyncImagePainter(model = it),
                    contentDescription = null
                )
                Spacer(modifier = Modifier.size(paddingMedium))
                val gotAward = getBooleanFlowByKey(KEY_SHARE_AWARD_IMAGE)
                GameButton(
                    buttonStyle = ButtonStyle.Red,
                    text = if (gotAward) stringResource(id = R.string.share) else stringResource(
                        R.string.share_and_reward
                    ), onClick = {
                        shareImage(it)

                        GameApp.globalScope.launch {
                            delay(1000)
                            if (!getBooleanFlowByKey(KEY_SHARE_AWARD_IMAGE)) {
                                setBooleanValueByKeySync(KEY_SHARE_AWARD_IMAGE, true)
                                Award(key = repo.gameCore.getShareKeys()).apply {
                                    AwardManager.gainAward(this)
                                    Dialogs.awardDialog.value = this
                                }
                            }
                        }
                    })
            }
        }
    }
}