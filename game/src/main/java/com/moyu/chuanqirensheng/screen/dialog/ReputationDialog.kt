package com.moyu.chuanqirensheng.screen.dialog

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.story.StoryManager
import com.moyu.chuanqirensheng.logic.story.toReputationName
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.ui.theme.DARK_RED
import com.moyu.chuanqirensheng.ui.theme.imageHugeLite
import com.moyu.chuanqirensheng.ui.theme.padding1
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingSmallPlus
import com.moyu.chuanqirensheng.ui.theme.reputationDialogLabelBarHeight
import com.moyu.chuanqirensheng.ui.theme.reputationDialogLabelBarWidth
import com.moyu.chuanqirensheng.ui.theme.reputationItemHeight
import com.moyu.chuanqirensheng.ui.theme.reputationItemWidth
import com.moyu.chuanqirensheng.util.getImageResourceDrawable

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun ReputationDialog(showDungeon: MutableState<Boolean>) {
    showDungeon.value.takeIf { showDungeon.value }?.let {
        DisposableEffect(Unit) {
            onDispose {
                BattleManager.setReputationUnNew()
            }
        }
        CommonDialog(title = stringResource(R.string.reputation_status), onDismissRequest = {
            showDungeon.value = false
        }) {
            FlowRow(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(bottom = paddingSmallPlus)
                    .padding(horizontal = paddingMedium)
                    .verticalScroll(rememberScrollState()),
                maxItemsInEachRow = 3,
                overflow = FlowRowOverflow.Visible,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                BattleManager.toReputationLevelData().forEachIndexed { index, reputationLevel ->
                    val name = (index + 1).toReputationName() ?: ""
                    val currentExp =
                        BattleManager.reputations[index] - (reputationLevel.expTotal)
                    Column(
                        modifier = Modifier
                            .size(reputationItemWidth, reputationItemHeight)
                            .padding(paddingMedium),
                        horizontalAlignment = Alignment.CenterHorizontally,
                    ) {
                        EffectButton(onClick = {
                            Dialogs.storyDetailDialog.value = StoryManager.stories[index]
                        }) {
                            Image(
                                painter = painterResource(id = R.drawable.prestige_frame),
                                contentDescription = null
                            )
                            Image(
                                painter = painterResource(getImageResourceDrawable("story${index + 1}_logo")),
                                modifier = Modifier
                                    .size(imageHugeLite)
                                    .graphicsLayer {
                                        translationX = -padding1.toPx()
                                        translationY = -paddingSmallPlus.toPx()
                                    },
                                contentDescription = null
                            )
                        }
                        Text(
                            text = reputationLevel.name,
                            color = if (reputationLevel.level == 1) DARK_RED else Color.Black,
                            style = MaterialTheme.typography.h6
                        )
                        Text(
                            text = "${currentExp}/${reputationLevel.exp}",
                            color = Color.Black,
                            style = MaterialTheme.typography.h6
                        )
                        Box(
                            contentAlignment = Alignment.Center,
                            modifier = Modifier.padding(top = paddingSmallPlus)
                        ) {
                            Image(
                                painter = painterResource(id = R.drawable.code_frame),
                                contentDescription = null,
                                contentScale = ContentScale.FillBounds,
                                modifier = Modifier.size(
                                    reputationDialogLabelBarWidth,
                                    reputationDialogLabelBarHeight
                                )
                            )
                            Text(
                                text = name, style = MaterialTheme.typography.h6,
                                textAlign = TextAlign.Center)
                        }
                    }
                }
            }
        }
    }
}
