package com.moyu.chuanqirensheng.screen.filter

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.button.QUICK_GAP
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.filterHeight
import com.moyu.chuanqirensheng.ui.theme.filterWidth
import com.moyu.chuanqirensheng.ui.theme.paddingTiny


@Composable
fun <T> FilterLayout(
    modifier: Modifier,
    show: MutableState<Boolean>,
    filter: SnapshotStateList<ItemFilter<T>>,
    filterList: List<ItemFilter<T>>
) {
    if (show.value) {
        Box(modifier = Modifier
            .fillMaxSize()
            .clickable {
                show.value = false
            }
            .background(B50))
    }
    AnimatedVisibility(
        modifier = modifier, visible = show.value
    ) {
        Column(modifier = Modifier.verticalScroll(rememberScrollState())) {
            repeat(filterList.size) { selectIndex ->
                val selected = filter.contains(filterList[selectIndex])
                EffectButton(clickGap = QUICK_GAP, onClick = {
                    if (filter.contains(filterList[selectIndex])) {
                        filter.remove(filterList[selectIndex])
                    } else {
                        filter.removeAll {
                            filterList[selectIndex].excludeTypes.intersect(it.excludeTypes.toSet())
                                .isNotEmpty()
                        }
                        filter.add(filterList[selectIndex])
                    }
                }) {
                    Box(
                        contentAlignment = Alignment.Center,
                        modifier = Modifier.padding(bottom = paddingTiny)
                    ) {
                        val res = if (selected) R.drawable.common_button_orange
                        else R.drawable.common_button_1_new
                        Image(
                            modifier = Modifier.size(filterWidth, filterHeight),
                            painter = painterResource(res),
                            contentDescription = null
                        )
                        Text(
                            text = filterList[selectIndex].name,
                            style = MaterialTheme.typography.h3,
                            color = filterList[selectIndex].color
                        )
                    }
                }
            }
        }
    }
}