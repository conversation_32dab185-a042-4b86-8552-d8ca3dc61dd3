package com.moyu.chuanqirensheng.screen.dialog

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.core.content.ContextCompat
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.cloud.bill.BillingManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.core.model.unlock.Unlock


@Composable
fun AiFaDianDialog(unlock: MutableState<Unlock?>) {
    unlock.value?.let {
        CommonDialog(title = stringResource(R.string.unlock_feature), onDismissRequest = { unlock.value = null }) {
            Column(
                Modifier
                    .fillMaxSize()
                    .padding(horizontal = paddingMedium)
                    .verticalScroll(rememberScrollState())
            ) {
                if (GameApp.instance.canShowAifadian()) {
                    Text(text = it.desc, style = MaterialTheme.typography.h3, color = Color.Black)
                }
                Spacer(modifier = Modifier.weight(1f))
                Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceEvenly) {
                    GameButton(text = stringResource(R.string.go_unlock), onClick = {
                        val uri: Uri = Uri.parse(it.url)
                        if (it.url != "0") {
                            if (GameApp.instance.canShowAifadian()) {
                                val intent = Intent(Intent.ACTION_VIEW, uri)
                                if (!GameApp.instance.resources.getBoolean(R.bool.has_billing)) {
                                    ContextCompat.startActivity(
                                        GameApp.instance.activity,
                                        intent,
                                        Bundle()
                                    )
                                } else {
                                    BillingManager.consume(it.googleItemId)
                                    unlock.value = null
                                    Dialogs.aiFaDianDialog.value = null
                                }
                            } else {
                                GameApp.instance.doJumpQQ()
                            }
                        } else {
                            GameApp.instance.getWrapString(R.string.no_link_target).toast()
                        }
                    })
                }
            }
        }
    }
}