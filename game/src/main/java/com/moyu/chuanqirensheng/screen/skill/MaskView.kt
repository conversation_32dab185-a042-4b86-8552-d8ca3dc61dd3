package com.moyu.chuanqirensheng.screen.skill

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.common.getTextStyle
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.ui.theme.paddingTiny

@Composable
fun MaskView(
    modifier: Modifier,
    text: String,
    itemSize: ItemSize,
    maskPadding: Dp = paddingTiny
) {
    Box(
        modifier = modifier
            .width(itemSize.frameSize)
            .padding(maskPadding), contentAlignment = Alignment.Center
    ) {
        Box(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .fillMaxWidth()
                .height(itemSize.frameSize / 3)
                .clip(
                    RoundedCornerShape(
                        0.dp, 0.dp, paddingSmall, paddingSmall
                    )
                )
                .background(B50)
        )
        Text(
            text = text,
            style = itemSize.getTextStyle(),
            textAlign = TextAlign.Center,
            softWrap = false,
            overflow = TextOverflow.Clip,
            maxLines = 1
        )
    }
}