package com.moyu.chuanqirensheng.screen.arena

import androidx.compose.animation.core.FastOutLinearInEasing
import androidx.compose.animation.core.TweenSpec
import androidx.compose.animation.core.animateIntAsState
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import com.moyu.chuanqirensheng.logic.speed.GameSpeedManager
import com.moyu.chuanqirensheng.ui.theme.gapHugePlusPlus
import com.moyu.chuanqirensheng.ui.theme.roleEffectHeight
import com.moyu.chuanqirensheng.ui.theme.roleEffectWidth
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.model.action.ActionStateType
import com.moyu.core.model.damage.DamageType
import com.moyu.core.model.role.Role

val beingDamageEffect = hashMapOf(
    DamageType.DamageType1 to Pair("attack1_", 6),
    DamageType.DamageType2 to Pair("attack2_", 22),
    DamageType.DamageType3 to Pair("attack3_", 18),
    DamageType.DamageType4 to Pair("attack4_", 12),
    DamageType.DamageType5 to Pair("attack5_", 10),
)

val beingHealEffect = Pair("hot_", 11)
val suckBloodEffect = Pair("blood_", 7)

val beingDispelEffect = Pair("dispel_", 6)
val deathEffect = Pair("dead_", 8)

@Composable
fun RoleStatusBeingAttackGif(roleGetter: () -> Role) {
    val role = roleGetter()
    val effectPair = when {
        role.hasState(ActionStateType.BeingAttack) -> {
            val damageType =
                role.getStateList()
                    .firstOrNull { it.type == ActionStateType.BeingAttack }?.damage?.type
            beingDamageEffect[damageType]
        }

        role.hasState(ActionStateType.BeingHeal) -> {
            val healState = role.getStateList().firstOrNull { it.type == ActionStateType.BeingHeal }
            val isSuckBlood = healState?.healResult?.healStatus?.isSuckBlood == true
            if (isSuckBlood) suckBloodEffect else beingHealEffect
        }

        role.hasState(ActionStateType.BeingBuff) -> {
            val buffState = role.getStateList().firstOrNull { it.type == ActionStateType.BeingBuff }
            buffState?.buff?.let {
                Pair(it.targetEffect, it.targetEffectNum)
            }
        }

        role.hasState(ActionStateType.BeingDispel) -> {
            beingDispelEffect
        }
        role.hasState(ActionStateType.Death) -> {
            deathEffect
        }
        else -> null
    }
    val isAttack = role.hasState(ActionStateType.DoAttack)
    val effectIndex by animateIntAsState(
        targetValue = (effectPair?.second) ?: 1,
        animationSpec = TweenSpec(
            durationMillis = if (effectPair == null) 0 else GameSpeedManager.animDuration().toInt(),
            easing = FastOutLinearInEasing
        ), label = ""
    )
    effectPair?.let {
        // 直接根据伤害类型播放
        Image(
            modifier = Modifier
                .size(roleEffectWidth, roleEffectHeight)
                .graphicsLayer {
                    if (isAttack) {
                        translationY = if (role.isPlayerSide()) {
                            effectIndex * -gapHugePlusPlus.toPx()
                        } else {
                            effectIndex * gapHugePlusPlus.toPx()
                        }
                        if (!role.isPlayerSide()) {
                            rotationX = 180f
                        }
                        clip = false
                    }
                },
            contentScale = ContentScale.FillHeight,
            painter = painterResource(
                id = getImageResourceDrawable(
                    "${it.first}${if (isAttack) 1 else effectIndex}"
                )
            ),
            contentDescription = null
        )
    }
}