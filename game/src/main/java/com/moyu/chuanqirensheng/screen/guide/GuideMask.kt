package com.moyu.chuanqirensheng.screen.guide

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.repeatable
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.Dp
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.datastore.KEY_GUIDE_INDEX
import com.moyu.chuanqirensheng.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.event.detail.SINGLE_BATTLE_PLAY
import com.moyu.chuanqirensheng.logic.guide.BATTLE_GUIDE_START
import com.moyu.chuanqirensheng.logic.guide.GuideManager
import com.moyu.chuanqirensheng.music.MusicManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.ally.AllyCardsRow
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.SettingItem
import com.moyu.chuanqirensheng.screen.common.SettingRow
import com.moyu.chuanqirensheng.screen.common.SettingRowItem
import com.moyu.chuanqirensheng.screen.common.settingRowItems
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.screen.dialog.SelectAllyData
import com.moyu.chuanqirensheng.screen.login.InitAllyCardLayout
import com.moyu.chuanqirensheng.screen.login.InitSkillCardLayout
import com.moyu.chuanqirensheng.screen.role.CountryInfoView
import com.moyu.chuanqirensheng.screen.role.CurrentElements
import com.moyu.chuanqirensheng.ui.theme.B65
import com.moyu.chuanqirensheng.ui.theme.bigButtonHeight
import com.moyu.chuanqirensheng.ui.theme.bigButtonWidth
import com.moyu.chuanqirensheng.ui.theme.createCountryPanelHeight
import com.moyu.chuanqirensheng.ui.theme.gapHuge
import com.moyu.chuanqirensheng.ui.theme.gapHugePlus
import com.moyu.chuanqirensheng.ui.theme.gapLarge
import com.moyu.chuanqirensheng.ui.theme.gapMedium
import com.moyu.chuanqirensheng.ui.theme.imageHuge
import com.moyu.chuanqirensheng.ui.theme.imageHugeLiteFrame
import com.moyu.chuanqirensheng.ui.theme.imageLargePlus
import com.moyu.chuanqirensheng.ui.theme.padding0
import com.moyu.chuanqirensheng.ui.theme.padding40
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.paddingHuge
import com.moyu.chuanqirensheng.ui.theme.paddingLarge
import com.moyu.chuanqirensheng.ui.theme.paddingLargePlus
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.ui.theme.roleInfoHeight
import com.moyu.chuanqirensheng.ui.theme.settingBgHeight
import com.moyu.chuanqirensheng.ui.theme.singleRoleHeight
import com.moyu.chuanqirensheng.util.composeDp
import com.moyu.chuanqirensheng.util.immersionBarHeightInDp
import com.moyu.core.GameCore
import com.moyu.core.music.SoundEffect


@Composable
fun GuideMask() {
    if (GuideManager.showGuide.value) {
        val modifier = when (GuideManager.guideIndex.value) {
            in listOf(6, 7, 8) -> { // 无需高亮，但是需要点击
                Modifier
                    .fillMaxSize()
                    .clickable {
                        MusicManager.playSound(SoundEffect.Click)
                        GuideManager.onQuitFirstGuide()
                    }
            }

            0 -> {
                Modifier
                    .fillMaxSize()
                    .clickable {
                        repo.clickStart()
                    }
            }

            2 -> {
                Modifier
                    .fillMaxSize()
                    .clickable {
                        repo.startGame(false)
                        GuideManager.onEnterGame()
                        GameCore.instance.onBattleEffect(SoundEffect.StartGame)
                    }
            }

            9 -> {
                Modifier
                    .fillMaxSize()
                    .background(B65)
                    .clickable {
                        Dialogs.gameAllyDialog.value = true
                        GuideManager.guideIndex.value += 1
                        setIntValueByKey(KEY_GUIDE_INDEX, GuideManager.guideIndex.value)
                    }
            }

            BATTLE_GUIDE_START -> {
                Modifier
                    .fillMaxSize()
                    .background(B65)
                    .clickable {
                        GuideManager.guideIndex.value += 1
                        GuideManager.showGuide.value = false
                        Dialogs.selectAllyToBattleDialog.value = SelectAllyData(select = {
                            BattleManager.selectAllyToBattle(it)
                        }) {
                            true
                        }
                    }
            }

            else -> { // 高亮，其实就是背景加上一个遮罩
                Modifier
                    .fillMaxSize()
                    .background(B65)
                    .clickable {
                        MusicManager.playSound(SoundEffect.Click)
                        GuideManager.onQuitFirstGuide()
                    }
            }
        }

        Column(
            modifier = modifier, horizontalAlignment = Alignment.CenterHorizontally
        ) {
            when (GuideManager.guideIndex.value) {
                0 -> {
                    Column(
                        modifier = Modifier.fillMaxSize(),
                        verticalArrangement = Arrangement.Center,
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Spacer(modifier = Modifier.height(250.composeDp()))
                        GuidePointAndText(
                            text = stringResource(R.string.guide1),
                            offsetX = gapHuge,
                            offsetY = -gapMedium
                        )
                    }
                }

                1 -> {
                    Spacer(modifier = Modifier.height(290.composeDp()))
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(createCountryPanelHeight)
                    ) {
                        InitAllyCardLayout()
                        Box(modifier = Modifier
                            .fillMaxSize()
                            .clickable {
                                MusicManager.playSound(SoundEffect.Click)
                                GuideManager.onQuitFirstGuide()
                            })
                    }
                    Spacer(modifier = Modifier.height(paddingMedium))
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(createCountryPanelHeight)
                    ) {
                        InitSkillCardLayout()
                        Box(modifier = Modifier
                            .fillMaxSize()
                            .clickable {
                                MusicManager.playSound(SoundEffect.Click)
                                GuideManager.onQuitFirstGuide()
                            })
                    }
                    GuidePointAndText(
                        text = stringResource(R.string.guide2), handType = HandType.NO_HAND
                    )
                }

                2 -> {
                    Spacer(modifier = Modifier.weight(1f))
                    GuidePointAndText(
                        text = stringResource(R.string.guide3),
                        handType = HandType.DOWN_HAND,
                        offsetX = gapHuge
                    )
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = paddingLargePlus),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        Spacer(modifier = Modifier.size(bigButtonWidth, bigButtonHeight))
                        GameButton(
                            text = stringResource(R.string.start_game),
                            buttonStyle = ButtonStyle.Orange,
                            buttonSize = ButtonSize.Big,
                            onClick = {
                                if (BattleManager.getGameAllies().isEmpty()) {
                                    GameApp.instance.getWrapString(R.string.ally_tips).toast()
                                } else if (BattleManager.getGameAllies().size < 3) {
                                    Dialogs.alertDialog.value =
                                        CommonAlert(
                                            content = GameApp.instance.getWrapString(R.string.ally_tips_contents),
                                            onConfirm = {
                                                repo.startGame(false)
                                                GameCore.instance.onBattleEffect(SoundEffect.StartGame)
                                            })
                                } else {
                                    repo.startGame(false)
                                    GuideManager.onEnterGame()
                                    GameCore.instance.onBattleEffect(SoundEffect.StartGame)
                                }
                            })
                    }
                }

                3 -> {
                    CountryInfoView(
                        Modifier
                            .fillMaxWidth()
                            .height(roleInfoHeight + immersionBarHeightInDp())
                    )
                    GuidePointAndText(text = stringResource(R.string.guide4), offsetX = gapHugePlus)
                }

                4 -> {
                    Column(modifier = Modifier.fillMaxWidth()) {
                        Spacer(modifier = Modifier.height(117.composeDp()))
                        CurrentElements(
                            Modifier
                                .fillMaxWidth()
                                .padding(paddingMedium)
                        )
                        Spacer(modifier = Modifier.size(paddingHuge))
                        GuidePointAndText(
                            text = stringResource(R.string.guide5),
                        )
                    }
                }

                5 -> {
                    Spacer(modifier = Modifier.weight(1f))
                    Column(modifier = Modifier.fillMaxWidth()) {
                        GuidePointAndText(
                            text = stringResource(R.string.guide6),
                            handType = HandType.DOWN_HAND
                        )
                        Box {
                            SettingRow(modifier = Modifier, settingItems = settingRowItems)
                            Box(modifier = Modifier
                                .fillMaxWidth()
                                .height(settingBgHeight)
                                .clickable {
                                    MusicManager.playSound(SoundEffect.Click)
                                    GuideManager.onQuitFirstGuide()
                                })
                        }
                    }
                    Spacer(modifier = Modifier.size(padding6))
                }

                6 -> {
                    Spacer(modifier = Modifier.weight(1f))
                    GuidePointAndText(
                        text = stringResource(R.string.guide7),
                        offsetX = -gapLarge,
                        offsetY = padding0
                    )
                    Spacer(modifier = Modifier.height(160.composeDp()))
                }

                7 -> { // 指向年份
                    Spacer(modifier = Modifier.height(270.composeDp()))
                    GuidePointAndText(text = stringResource(R.string.guide8))
                }

                8 -> {
                    Spacer(modifier = Modifier.weight(1f))
                    GuidePointAndText(
                        text = stringResource(R.string.guide9),
                        offsetX = -gapLarge,
                        offsetY = padding0
                    )
                    Spacer(modifier = Modifier.height(160.composeDp()))
                }

                9 -> {
                    Spacer(modifier = Modifier.weight(1f))
                    GuidePointAndText(
                        text = stringResource(R.string.guide10),
                        handType = HandType.DOWN_HAND,
                        offsetX = (-180).composeDp()
                    )
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.Start
                    ) {
                        SettingRowItem(
                            settingItem = SettingItem(
                                name = { GameApp.instance.getWrapString(R.string.allies) },
                                icon = { R.drawable.bottom_menu1 },
                                redIcon = { BattleManager.allyGameData.any { it.new } },
                                action = {
                                    Dialogs.gameAllyDialog.value = true
                                    GuideManager.guideIndex.value += 1
                                    setIntValueByKey(KEY_GUIDE_INDEX, GuideManager.guideIndex.value)
                                },
                            )
                        )
                    }
                    Spacer(modifier = Modifier.size(paddingSmall))
                }

                BATTLE_GUIDE_START -> {
                    Spacer(modifier = Modifier.weight(1f))
                    Column(Modifier
                        .fillMaxWidth()
                        .graphicsLayer {
                            translationY = padding40.toPx()
                        }, verticalArrangement = Arrangement.SpaceEvenly) {
                        AllyCardsRow(
                            modifier = Modifier
                                .height(singleRoleHeight)
                                .fillMaxWidth(),
                            allies = BattleManager.getBattleAllies().filter { !it.isDead() },
                            capacity = if (EventManager.selectedEvent.value?.play == SINGLE_BATTLE_PLAY) 1 else 3,
                            textColor = Color.White,
                            showName = true,
                            showHp = true
                        ) {
                            GuideManager.guideIndex.value += 1
                            GuideManager.showGuide.value = false
                            Dialogs.selectAllyToBattleDialog.value = SelectAllyData(select = {
                                BattleManager.selectAllyToBattle(it)
                            }) {
                                true
                            }
                        }
                        GuidePointAndText(text = GameApp.instance.getWrapString(R.string.guide15))
                    }
                }
            }
        }
    }
}

enum class HandType(val value: Int) {
    UP_HAND(1),
    DOWN_HAND(2),
    NO_HAND(3)
}

@Composable
fun GuidePointAndText(
    text: String,
    handType: HandType = HandType.UP_HAND,
    offsetX: Dp? = null,
    offsetY: Dp? = null
) {
    // 创建一个动画状态，用于处理跳动效果
    val animatedOffsetY = remember { Animatable(0f) }

    // 当组件进入或手指类型改变时，触发跳动动画
    LaunchedEffect(Unit) {
        animatedOffsetY.animateTo(
            targetValue = -20f, // 设置动画的目标值
            animationSpec = repeatable( // 使用repeatable重复动画
                iterations = 999, // 设置动画无限重复
                animation = tween(
                    durationMillis = 500, // 设置动画持续时间
                    easing = FastOutSlowInEasing // 设置动画的缓动效果
                ),
                repeatMode = RepeatMode.Reverse // 设置动画反向重复
            )
        )
    }
    when (handType) {
        HandType.UP_HAND -> {
            Image(
                modifier = Modifier
                    .height(imageLargePlus)
                    .graphicsLayer {
                        translationY =
                            (offsetY?.toPx() ?: -gapMedium.toPx()) + animatedOffsetY.value
                        translationX = offsetX?.toPx() ?: gapLarge.toPx()
                        clip = false
                    },
                contentScale = ContentScale.FillHeight,
                painter = painterResource(R.drawable.guide_icon),
                contentDescription = ""
            )
            Spacer(modifier = Modifier.size(paddingLarge))
            Text(modifier = Modifier
                .graphicsLayer {
                    translationY = (offsetY?.toPx()
                        ?: -gapMedium.toPx()) + (-paddingLargePlus.toPx()) //paddingHugeLite.toPx() 文字背景上移
                }
                .paint(
                    painterResource(R.drawable.dialog_frame),
                    contentScale = ContentScale.FillBounds
                )
                .padding(paddingLarge),
                text = text,
                style = MaterialTheme.typography.h3,
                color = Color.Black)
        }

        HandType.DOWN_HAND -> {
            Text(
                modifier = Modifier
                    .paint(
                        painterResource(R.drawable.dialog_frame),
                        contentScale = ContentScale.FillBounds
                    )
                    .padding(paddingLarge),
                text = text,
                style = MaterialTheme.typography.h3,
                color = Color.Black
            )
            Image(
                modifier = Modifier
                    .height(imageHugeLiteFrame)
                    .graphicsLayer {
                        translationX = offsetX?.toPx() ?: paddingLarge.toPx()
                        translationY = animatedOffsetY.value
                        clip = false
                    },
                contentScale = ContentScale.FillHeight,
                painter = painterResource(R.drawable.guide_icon2),
                contentDescription = ""
            )
        }

        HandType.NO_HAND -> {
            Spacer(modifier = Modifier.size(imageHuge + paddingLarge))
            Text(modifier = Modifier
                .graphicsLayer {
                    translationY = offsetY?.toPx() ?: -gapLarge.toPx()
                }
                .paint(
                    painterResource(R.drawable.dialog_frame),
                    contentScale = ContentScale.FillBounds
                )
                .padding(paddingLarge),
                text = text,
                style = MaterialTheme.typography.h3,
                color = Color.Black)
        }
    }
}