package com.moyu.chuanqirensheng.screen.life

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.monthcard.MonthCardManager
import com.moyu.chuanqirensheng.logic.sell.SellManager
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.common.NavigationTab
import com.moyu.chuanqirensheng.screen.more.VipScreen
import com.moyu.chuanqirensheng.screen.sell.SellPage
import com.moyu.chuanqirensheng.screen.sell.SellScreen
import com.moyu.chuanqirensheng.screen.sell.SellStoryScreen


@Composable
fun SellAllScreen(initTab: Int = 0) {
    val listTabItems = remember {
        mutableStateListOf(
            GameApp.instance.getWrapString(R.string.shop),
            GameApp.instance.getWrapString(R.string.gift),
            GameApp.instance.getWrapString(R.string.story_shop),
            GameApp.instance.getWrapString(R.string.vip)
        )
    }
    val pagerState = rememberPagerState(initialPage = initTab) {
        listTabItems.size
    }
    LaunchedEffect(Unit) {
        MonthCardManager.init()
    }
    GameBackground(title = stringResource(R.string.menu3)) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            NavigationTab(pagerState, listTabItems, SellManager.getReds())
            HorizontalPager(
                state = pagerState,
            ) { page ->
                when (page) {
                    0 -> SellScreen()
                    1 -> SellPage(listOf(23, 31))
                    2 -> SellStoryScreen()
                    else -> VipScreen()
                }
            }
        }
    }
}