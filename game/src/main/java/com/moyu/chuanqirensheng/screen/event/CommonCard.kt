package com.moyu.chuanqirensheng.screen.event

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import com.moyu.chuanqirensheng.screen.effect.StrokedText
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.debug.EventIdTag
import com.moyu.chuanqirensheng.screen.common.CardSize
import com.moyu.chuanqirensheng.screen.common.getTextStyle
import com.moyu.chuanqirensheng.screen.sensor.ShadowedView
import com.moyu.chuanqirensheng.ui.theme.padding1
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding3
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.util.getImageResourceDrawable


@Composable
fun CommonCard2(
    modifier: Modifier = Modifier,
    cardSize: CardSize,
    title: String,
    icon: String,
    titleColor: Color = Color.White,
    frame: Int = R.drawable.main_frame_1,
) {
    ShadowedView(cardSize = cardSize) {
        Box(modifier.size(cardSize.width, cardSize.height)) {
            Image(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(padding1)
                    .clip(RoundedCornerShape(cardSize.getRadius())),
                contentScale = ContentScale.Crop,
                painter = painterResource(id = getImageResourceDrawable(icon)),
                contentDescription = title
            )
            Image(
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.FillBounds,
                painter = painterResource(id = frame),
                contentDescription = null
            )
            CardTitleView2(
                Modifier
                    .align(Alignment.BottomCenter), title, cardSize, titleColor
            )
        }
    }
}

@Composable
fun CommonCard(
    modifier: Modifier = Modifier,
    cardSize: CardSize,
    title: String,
    icon: String,
    id: Int,
    titleColor: Color = Color.White,
    frame: Int = R.drawable.event_card_frame,
) {
    ShadowedView(modifier = modifier, cardSize = cardSize) {
        Box(modifier.size(cardSize.width, cardSize.height)) {
            Image(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = padding4, vertical = padding2)
                    .clip(RoundedCornerShape(cardSize.getRadius())),
                contentScale = ContentScale.Crop,
                painter = painterResource(id = getImageResourceDrawable(icon)),
                contentDescription = null
            )
            Image(
                modifier = Modifier.fillMaxSize().scale(1.04f),
                contentScale = ContentScale.FillBounds,
                painter = painterResource(id = frame),
                contentDescription = null
            )
            CardTitleView(
                Modifier
                    .align(Alignment.BottomCenter), title, cardSize, titleColor
            )
            EventIdTag(modifier = Modifier.align(Alignment.Center), id, cardSize)
        }
    }
}

@Composable
fun CardTitleView(modifier: Modifier, title: String, cardSize: CardSize, titleColor: Color) {
    Box(
        modifier = modifier.fillMaxWidth(),
        contentAlignment = Alignment.BottomCenter
    ) {
        Image(
            modifier = Modifier.fillMaxWidth().padding(horizontal = padding3),
            contentScale = ContentScale.FillWidth,
            painter = painterResource(id = R.drawable.card_label),
            contentDescription = null
        )
        Box(modifier = Modifier.height(cardSize.height / 6f), contentAlignment = Alignment.Center) {
            StrokedText(
                text = title,
                style = cardSize.getTextStyle(),
                textAlign = TextAlign.Center,
                color = titleColor
            )
        }
    }
}


@Composable
fun CardTitleView2(modifier: Modifier, title: String, cardSize: CardSize, titleColor: Color) {
    Box(
        modifier = modifier.fillMaxWidth(),
        contentAlignment = Alignment.TopCenter
    ) {
        StrokedText(
            modifier = Modifier
                .padding(bottom = cardSize.height / 16f),
            text = title,
            style = cardSize.getTextStyle(),
            textAlign = TextAlign.Center,
            color = titleColor
        )
    }
}