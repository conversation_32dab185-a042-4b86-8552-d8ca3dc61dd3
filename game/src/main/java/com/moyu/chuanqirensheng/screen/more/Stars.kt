package com.moyu.chuanqirensheng.screen.more

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.Dp
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.ui.theme.cardStarSize
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding42

val starRes = listOf(
    R.drawable.card_star0,
    R.drawable.card_star1,
    <PERSON>.drawable.card_star2,
    <PERSON>.drawable.card_star3,
    R.drawable.card_star4,
    R.drawable.card_star5,
)

@Composable
fun Stars(modifier: Modifier = Modifier, stars: Int, starHeight: Dp = cardStarSize) {
    Image(
        painter = painterResource(id = starRes[stars]),
        contentDescription = null,
        modifier = modifier.height(
            starHeight
        ),
        contentScale = ContentScale.FillHeight
    )
}


@Composable
fun ClickableStars(modifier: Modifier = Modifier, starHeight: Dp = padding42, callback: (Int) -> Unit) {
    val rank = remember {
        mutableStateOf(0)
    }
    Row(modifier = modifier, horizontalArrangement = Arrangement.spacedBy(padding4)) {
        repeat(5) {
            EffectButton(onClick = {
                rank.value = it
                callback(it + 1)
            }) {
                Image(
                    painter = painterResource(id = if (rank.value > it) R.drawable.star else R.drawable.star2),
                    contentDescription = stringResource(R.string.star_level),
                    modifier = Modifier.height(
                        starHeight
                    ),
                    contentScale = ContentScale.FillHeight
                )
            }
        }
    }
}