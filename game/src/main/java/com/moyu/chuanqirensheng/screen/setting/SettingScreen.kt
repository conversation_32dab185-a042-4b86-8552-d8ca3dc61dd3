package com.moyu.chuanqirensheng.screen.setting

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.datastore.KEY_INVISIBLE_IN_RANK
import com.moyu.chuanqirensheng.datastore.KEY_MUTE_DIALOG
import com.moyu.chuanqirensheng.datastore.KEY_MUTE_MUSIC
import com.moyu.chuanqirensheng.datastore.KEY_MUTE_SOUND
import com.moyu.chuanqirensheng.datastore.KEY_TEXT_FIXED_COLOR
import com.moyu.chuanqirensheng.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.logic.event.EventManager.uploadRanks
import com.moyu.chuanqirensheng.logic.setting.SettingManager
import com.moyu.chuanqirensheng.music.MusicManager
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.sub.remoteab.RemoteAbConfigManager
import com.moyu.chuanqirensheng.ui.theme.imageMedium
import com.moyu.chuanqirensheng.ui.theme.paddingLarge
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingMediumPlus
import kotlinx.coroutines.launch


@Composable
fun SettingScreen() {
    var uploadRanks = false
    GameBackground(title = stringResource(R.string.setting)) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .paint(
                    painterResource(id = R.drawable.common_page_frame),
                    contentScale = ContentScale.FillBounds
                )
                .padding(paddingLarge)
        ) {
            SettingItem(
                getBooleanFlowByKey(KEY_MUTE_MUSIC), stringResource(
                                    R.string.turn_off_music)
            ) {
                setBooleanValueByKey(KEY_MUTE_MUSIC, it)
                MusicManager.switchMuteMusic()
            }
            Spacer(modifier = Modifier.size(paddingMediumPlus))
            SettingItem(
                getBooleanFlowByKey(KEY_MUTE_SOUND), stringResource(
                                    R.string.turn_off_sound)
            ) {
                setBooleanValueByKey(KEY_MUTE_SOUND, it)
                MusicManager.switchMuteSound()
            }
            Spacer(modifier = Modifier.size(paddingMediumPlus))
            SettingItem(
                getBooleanFlowByKey(KEY_MUTE_DIALOG, RemoteAbConfigManager.muteDialog()), stringResource(
                    R.string.turn_off_dialog)
            ) {
                setBooleanValueByKey(KEY_MUTE_DIALOG, it)
                SettingManager.switchMuteDialog()
            }
            Spacer(modifier = Modifier.size(paddingMediumPlus))
            SettingItem(
                getBooleanFlowByKey(KEY_INVISIBLE_IN_RANK),
                content = stringResource(R.string.setting_hide_electric)
            ) {
                setBooleanValueByKey(KEY_INVISIBLE_IN_RANK, it)
                if (it && !uploadRanks) {
                    uploadRanks()
                    uploadRanks = true
                }
            }
            Spacer(modifier = Modifier.size(paddingMediumPlus))
            SettingItem(
                getBooleanFlowByKey(KEY_TEXT_FIXED_COLOR),
                content = stringResource(R.string.setting_text_color)
            ) {
                setBooleanValueByKey(KEY_TEXT_FIXED_COLOR, it)
            }
            Spacer(modifier = Modifier.weight(1f))
        }
    }
}


@Composable
fun SettingItem(checked: Boolean, content: String, onChecked: suspend (Boolean) -> Unit) {
    EffectButton(onClick = {
        GameApp.globalScope.launch {
            onChecked(!checked)
        }
    }) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            Image(
                modifier = Modifier.size(imageMedium),
                painter = painterResource(id = if (checked) R.drawable.setup_2 else R.drawable.setup_1),
                contentDescription = if (checked) stringResource(R.string.game_selected) else stringResource(
                    R.string.game_not_selected
                )
            )
            Spacer(modifier = Modifier.size(paddingMedium))
            Text(
                text = content.trimIndent(), style = MaterialTheme.typography.h3
            )
        }
    }
}