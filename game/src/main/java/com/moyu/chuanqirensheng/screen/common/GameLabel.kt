package com.moyu.chuanqirensheng.screen.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.ui.theme.labelHeight
import com.moyu.chuanqirensheng.ui.theme.labelWidth
import com.moyu.chuanqirensheng.ui.theme.paddingTiny

@Composable
fun GameLabel(modifier: Modifier = Modifier, content: @Composable BoxScope.() -> Unit) {
    Box(modifier = modifier.size(labelWidth, labelHeight), contentAlignment = Alignment.Center) {
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillWidth,
            painter = painterResource(id = R.drawable.scroll), contentDescription = null
        )
        Box(
            modifier = Modifier
                .fillMaxWidth().padding(bottom = paddingTiny),
            contentAlignment = Alignment.Center
        ) {
            content()
        }
    }
}