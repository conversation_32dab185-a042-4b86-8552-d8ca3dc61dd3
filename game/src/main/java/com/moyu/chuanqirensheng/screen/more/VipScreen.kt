package com.moyu.chuanqirensheng.screen.more

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import com.moyu.chuanqirensheng.screen.effect.StrokedText
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.core.content.ContextCompat
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.award.VipManager
import com.moyu.chuanqirensheng.logic.sell.SELL_KEY_INDEX
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.ally.SingleDetailAllyCard
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.CurrentElectricPoint
import com.moyu.chuanqirensheng.screen.common.GameLabel
import com.moyu.chuanqirensheng.screen.common.VipLevel
import com.moyu.chuanqirensheng.screen.dialog.AwardList
import com.moyu.chuanqirensheng.screen.dialog.defaultParam
import com.moyu.chuanqirensheng.ui.theme.Yellow60
import com.moyu.chuanqirensheng.ui.theme.cheatDecHeight
import com.moyu.chuanqirensheng.ui.theme.cheatFrameHeight
import com.moyu.chuanqirensheng.ui.theme.gapMedium
import com.moyu.chuanqirensheng.ui.theme.imageHugeFrame
import com.moyu.chuanqirensheng.ui.theme.labelHeight
import com.moyu.chuanqirensheng.ui.theme.labelWidth
import com.moyu.chuanqirensheng.ui.theme.padding1
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.ui.theme.padding80
import com.moyu.chuanqirensheng.ui.theme.paddingHugeLite
import com.moyu.chuanqirensheng.ui.theme.paddingLarge
import com.moyu.chuanqirensheng.ui.theme.paddingLargePlus
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingMediumMinis
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.ui.theme.paddingSmallPlus
import com.moyu.chuanqirensheng.ui.theme.paddingTiny
import com.moyu.chuanqirensheng.util.gotoSellWithTabIndex
import com.moyu.core.model.Vip
import com.moyu.core.model.sell.toAward
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch


@Composable
fun VipScreen() {
    val infiniteTransition = rememberInfiniteTransition(label = "")
    val angle by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = tween(10000, easing = LinearEasing)
        ), label = ""
    )

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .paint(
                painterResource(id = R.drawable.common_page_frame),
                contentScale = ContentScale.FillBounds
            )
    ) {
        Spacer(modifier = Modifier.size(paddingMedium))
        GameLabel(modifier = Modifier.size(labelWidth, labelHeight).align(Alignment.CenterHorizontally)) {
            StrokedText(text = stringResource(id = R.string.vip), style = MaterialTheme.typography.h2, color = Color.White)
        }
        CurrentElectricPoint(Modifier.align(Alignment.CenterHorizontally))
        Spacer(modifier = Modifier.size(paddingMedium))
        Box(modifier = Modifier.fillMaxSize()) {
            Image(
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .height(cheatDecHeight)
                    .graphicsLayer {
                        translationX = gapMedium.toPx()
                        translationY = -gapMedium.toPx()
                        rotationZ = angle
                    }
                    .scale(1.8f),
                contentScale = ContentScale.FillHeight,
                painter = painterResource(id = R.drawable.effect_shine),
                contentDescription = null
            )
            val pool = repo.gameCore.getVipPool()
            LazyColumn(
                modifier = Modifier.align(Alignment.CenterStart).padding(bottom = padding80),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                items(pool.size) { index ->
                    if (index == 0) {
                        Spacer(modifier = Modifier.size(paddingMedium))
                    }
                    Spacer(modifier = Modifier.size(paddingMedium))
                    Column {
                        OneCheatItem(pool[index]) {
                            GameApp.globalScope.launch(Dispatchers.Main) {
                                val unlocked = VipManager.getVipLevel() >= pool[index].level
                                if (unlocked) {
                                    VipManager.gain(pool[index])
                                } else {
                                    GameApp.instance.getWrapString(R.string.vip_level_not_enough)
                                        .toast()
                                }
                            }
                        }
                    }
                }
            }
            CurrentCheatItem(
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .graphicsLayer {
                        translationY = -gapMedium.toPx()
                    }
            )
        }
        Spacer(modifier = Modifier.size(paddingMedium))
    }
}

@Composable
fun CurrentCheatItem(modifier: Modifier) {
    Column(
        modifier = modifier
            .padding(end = paddingLargePlus)
            .scale(0.9f)
            .graphicsLayer {
                translationX = paddingHugeLite.toPx()
            },
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        val hero = repo.gameCore.getAllyById(repo.gameCore.getVipRaceCard()).copy(peek = true)
        EffectButton(onClick = {
            Dialogs.allyDetailDialog.value = hero
        }) {
            SingleDetailAllyCard(ally = hero)
        }
        Spacer(modifier = Modifier.size(paddingMedium))
        GameButton(
            text = stringResource(R.string.get_electric),
            buttonSize = ButtonSize.Big,
            buttonStyle = ButtonStyle.Red,
        ) {
            if (GameApp.instance.canShowAifadian()) {
                if (GameApp.instance.resources.getBoolean(R.bool.has_billing)) {
                    // 谷歌内购
                    gotoSellWithTabIndex(SELL_KEY_INDEX)
                } else {
                    val uri: Uri =
                        Uri.parse(GameApp.instance.resources.getString(R.string.aifadian))
                    val intent = Intent(Intent.ACTION_VIEW, uri)
                    ContextCompat.startActivity(GameApp.instance.activity, intent, Bundle())
                }
            } else {
                GameApp.instance.doJumpQQ()
            }
        }
        Spacer(modifier = Modifier.size(paddingSmall))
    }
}

@Composable
fun OneCheatItem(cheat: Vip, callback: () -> Unit) {
    EffectButton(
        modifier = Modifier.size(cheatFrameHeight, cheatFrameHeight + padding8), onClick = callback
    ) {
        VipAwards(cheat, callback)
        VipLevel(
            modifier = Modifier
                .align(Alignment.TopStart)
                .offset(y = -paddingLarge),
            cheatLevel = cheat.level
        )

    }
}

@Composable
fun VipAwards(cheat: Vip, callback: () -> Unit) {
    val unlocked = VipManager.getVipLevel() >= cheat.level
    Box(contentAlignment = Alignment.Center) {
        if (unlocked) {
            Image(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingTiny),
                contentScale = ContentScale.FillBounds,
                painter = painterResource(id = R.drawable.battlepass_get_reward),
                contentDescription = null
            )
        }
        val colorFilter = if (unlocked) {
            ColorFilter.tint(
                Yellow60, BlendMode.SrcAtop
            )
        } else {
            null
        }
        Image(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingMediumMinis),
            contentScale = ContentScale.FillBounds,
            colorFilter = colorFilter,
            painter = painterResource(id = R.drawable.battlepass_frame),
            contentDescription = null
        )
        if (cheat.effectType == 1) {
            val award = cheat.toAward()
            AwardList(
                Modifier.graphicsLayer {
                    translationX = -padding1.toPx()
                },
                award = award,
                param = defaultParam.copy(
                    peek = true,
                    textColor = Color.White,
                    callback = if (unlocked) callback else null
                ),
            )
        } else {
            StrokedText(
                modifier = Modifier
                    .width(imageHugeFrame)
                    .padding(paddingSmall),
                text = cheat.desc,
                style = MaterialTheme.typography.h4,
                textAlign = TextAlign.Center
            )
        }
        if (unlocked) {
            Box(
                modifier = Modifier
                    .align(Alignment.TopCenter)
                    .padding(top = paddingSmallPlus),
                contentAlignment = Alignment.Center
            ) {
                if (VipManager.isThisLevelGained(cheat)) {
                    Image(
                        modifier = Modifier
                            .width(imageHugeFrame + paddingSmallPlus),
                        contentScale = ContentScale.FillWidth,
                        painter = painterResource(id = R.drawable.battlepass_get),
                        contentDescription = null
                    )
                    StrokedText(
                        text = stringResource(id = R.string.already_got),
                        style = MaterialTheme.typography.h3
                    )
                }
            }
        } else {
            // null
        }
    }
}