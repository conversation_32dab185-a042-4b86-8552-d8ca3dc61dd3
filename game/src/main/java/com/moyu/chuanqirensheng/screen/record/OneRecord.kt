package com.moyu.chuanqirensheng.screen.record

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.model.record.AllRankData
import com.moyu.chuanqirensheng.ui.theme.imageMedium
import com.moyu.chuanqirensheng.ui.theme.paddingLargePlus
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.ui.theme.paddingTiny
import com.moyu.chuanqirensheng.ui.theme.rankIndexOneDigitHeight
import com.moyu.chuanqirensheng.ui.theme.rankIndexOneDigitWidth
import com.moyu.chuanqirensheng.ui.theme.recordFrameHeight80
import com.moyu.chuanqirensheng.util.getRankNumber


@Composable
fun SingleRecord(
    deathRole: AllRankData,
    index: Int,
    content: @Composable BoxScope.(AllRankData, Int) -> Unit,
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(recordFrameHeight80)  // 983 * 183
    ) {
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(id = R.drawable.common_frame_focus2),
            contentDescription = null
        )
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = paddingLargePlus),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {

            Row(
                verticalAlignment = Alignment.CenterVertically,
            ) {

                Box(
                    contentAlignment = Alignment.Center, modifier = Modifier.size(imageMedium)
                ) {
                    val imageIcon = when (index) {
                        1 -> R.drawable.rank_1
                        2 -> R.drawable.rank_2
                        3 -> R.drawable.rank_3
                        else -> null
                    }
                    imageIcon?.let {
                        Image(
                            modifier = Modifier.size(imageMedium).offset(y= -paddingTiny),
                            painter = painterResource(it),
                            contentDescription = null
                        )
                    }
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Center
                    ) {
                        getRankNumber(index).toList().forEach {
                            it?.let { numberDrawable ->
                                Image(
                                    contentScale = ContentScale.Fit,
                                    modifier = Modifier.size(
                                        rankIndexOneDigitWidth,
                                        rankIndexOneDigitHeight
                                    ),
                                    painter = painterResource(numberDrawable),
                                    contentDescription = index.toString(),
                                )
                            }
                        }
                    }
                }
                Spacer(modifier = Modifier.padding(paddingSmall))
                Text(text = deathRole.userName, style = MaterialTheme.typography.h2)
            }
            Box {
                content(deathRole, index)
            }
        }
    }
}
