package com.moyu.chuanqirensheng.screen.record

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.rank.RankManager
import com.moyu.chuanqirensheng.model.record.AllRankData
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.core.model.sell.Award
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

val famousRanks = mutableStateOf(emptyList<AllRankData>())

const val FAMOUS_TYPE = 10

@Composable
fun FamousScreen() {
    LaunchedEffect(Unit) {
        RankManager.init()
        famousRanks.value = emptyList()
    }
    GameBackground(title = stringResource(R.string.famous_title)) {
        RankPage(FAMOUS_TYPE, famousRanks) { rankData, rankIndex ->
            EffectButton(onClick = {
                GameApp.globalScope.launch(Dispatchers.Main) {
                    if (RankManager.famousLiked.value >= 5) {
                        GameApp.instance.getWrapString(R.string.top_like_count_one_day).toast()
                    } else {
                        RankManager.famousLiked.value += 1
                        val award = Award(diamond = repo.gameCore.getFamousDiamond())
                        Dialogs.awardDialog.value = award
                        AwardManager.gainAward(award)
                    }
                }
            }) {
                Image(
                    modifier = Modifier.size(imageLarge),
                    painter = painterResource(id = R.drawable.common_menu_vote),
                    contentDescription = stringResource(R.string.like)
                )
            }
        }
    }
}