package com.moyu.chuanqirensheng.screen.login

import androidx.compose.animation.core.FastOutLinearInEasing
import androidx.compose.animation.core.TweenSpec
import androidx.compose.animation.core.animateIntAsState
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.ui.theme.imageHuge
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.util.RANDOM
import com.moyu.core.util.nextIntClosure
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch


@Composable
fun DiceView(modifier: Modifier, autoDiceOnce: Boolean = true, onEachFrame: () -> Unit) {
    val dice = remember {
        mutableIntStateOf(0)
    }
    LaunchedEffect(Unit) {
        if (autoDiceOnce) {
            delay(10)
            dice.intValue = RANDOM.nextIntClosure(1, 6)
        }
    }
    val scope = rememberCoroutineScope()
    val diceFrame by animateIntAsState(
        targetValue = when (dice.intValue > 0) {
            false -> 1
            else -> 22
        },
        animationSpec = TweenSpec(
            durationMillis = if (dice.intValue == 0) 0 else 500,
            easing = FastOutLinearInEasing
        ), label = ""
    )
    val diceDrawable = if (dice.intValue > 0) {
        if (diceFrame <= 19) {
            getImageResourceDrawable("dice_$diceFrame")
        } else {
            getImageResourceDrawable("dice_${dice.intValue}_${diceFrame - 19}")
        }
    } else R.drawable.dice_icon

    EffectButton(modifier = modifier.width(imageHuge), onClick = {
        scope.launch {
            dice.intValue = 0
            delay(10)
            dice.intValue = RANDOM.nextIntClosure(1, 6)
        }
    }) {
        if (dice.intValue > 0) {
            onEachFrame()
        }
        Image(
            modifier = Modifier
                .fillMaxWidth()
                .align(Alignment.BottomEnd),
            contentScale = ContentScale.FillWidth,
            painter = painterResource(id = diceDrawable),
            contentDescription = stringResource(R.string.random_country_name)
        )
    }
}