package com.moyu.chuanqirensheng.screen.dialog

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.feature.draw.DrawManager


@Composable
fun DrawPoolDialog(show: MutableState<Boolean>) {
    if (show.value) {
        val award = DrawManager.getAwardPool()
        CommonDialog(title = stringResource(R.string.pool_title), onDismissRequest = { show.value = false }) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState()),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                AwardList(award = award)
            }
        }
    }
}