package com.moyu.chuanqirensheng.screen.event

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.event.forceLose
import com.moyu.chuanqirensheng.logic.event.forceWin
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.ally.AllyCardsRow
import com.moyu.chuanqirensheng.screen.arena.BattleFieldLayout
import com.moyu.chuanqirensheng.screen.arena.EnemiesRow
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.SettingColumn
import com.moyu.chuanqirensheng.screen.common.SettingRow
import com.moyu.chuanqirensheng.screen.common.settingBattleItems
import com.moyu.chuanqirensheng.screen.common.settingRowItems
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.screen.dialog.SelectAllyData
import com.moyu.chuanqirensheng.ui.theme.gapHugeMinus
import com.moyu.chuanqirensheng.ui.theme.hugeButtonHeight
import com.moyu.chuanqirensheng.ui.theme.paddingHugePlus
import com.moyu.chuanqirensheng.ui.theme.singleRoleHeight
import com.moyu.core.model.ally.Ally
import com.moyu.core.model.event.Event

@Composable
fun BattleLayout(
    event: Event,
    extraTitle: String = "",
    capacity: Int = 3,
    allyFilter: (Ally) -> Boolean = { true },
    prepareBattleCallback: () -> Unit = {}
) {
    Box(modifier = Modifier.fillMaxSize()) {
        EventManager.getOrCreateHandler(event).apply {
            if (!eventFinished.value) {
                if (repo.inBattle.value) {
                    BattleFieldLayout(
                        roleMinions = { repo.allies },
                        enemyMinions = { repo.enemies },
                    )
                } else {
                    PrepareBattleLayout(
                        allyFilter = allyFilter, extraTitle = extraTitle, capacity = capacity
                    ) {
                        if (BattleManager.getBattleAllies().filter { !it.isDead() }
                                .none(allyFilter)) {
                            Dialogs.alertDialog.value =
                                CommonAlert(
                                    content = GameApp.instance.getWrapString(R.string.no_role_to_battle_tips),
                                    onConfirm = {
                                        EventManager.doEventResult(event = event, result = false)
                                    })
                        } else {
                            if (event.forceWin()) {
                                EventManager.doEventBattleResult(event, true)
                            } else if (event.forceLose()) {
                                EventManager.doEventBattleResult(event, false)
                            } else {
                                if (!repo.enemies.isEmpty() && !repo.enemies.all { it.isOver() }) {
                                    val allies = BattleManager.getBattleRoles().take(capacity)
                                    repo.setCurrentAllies(allies)
                                    prepareBattleCallback()
                                    repo.startBattle()
                                }
                            }
                        }
                    }
                }
            }
        }
        SettingColumn(
            Modifier
                .align(Alignment.CenterStart),
            settingBattleItems
        )
        SettingRow(
            Modifier
                .align(Alignment.BottomCenter)
                .graphicsLayer {
                    translationY = paddingHugePlus.toPx()
                }, settingRowItems
        )
    }
}

@Composable
fun PrepareBattleLayout(
    extraTitle: String = "",
    allyFilter: (Ally) -> Boolean = { true },
    capacity: Int = 3,
    start: () -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally, verticalArrangement = Arrangement.Center
    ) {
        EnemiesRow { repo.enemies }
        Spacer(modifier = Modifier.size(gapHugeMinus))
        EffectButton(onClick = {
            start()
        }) {
            Image(
                modifier = Modifier.height(hugeButtonHeight),
                contentScale = ContentScale.FillHeight,
                painter = painterResource(id = R.drawable.common_large_button),
                contentDescription = null
            )
            Text(
                text = stringResource(R.string.start_battle),
                style = MaterialTheme.typography.h1,
                color = Color.Black,
                textAlign = TextAlign.Center
            )
        }
        Spacer(modifier = Modifier.size(gapHugeMinus))
        Spacer(modifier = Modifier.height(singleRoleHeight))
        AllyCardsRow(modifier = Modifier
            .height(singleRoleHeight)
            .fillMaxWidth(),
            allies = BattleManager.getBattleAllies().filter { !it.isDead() }.filter(allyFilter),
            capacity = capacity,
            showName = true,
            canDrag = true,
            showHp = true,
            allyClick = {
                BattleManager.selectAllyToBattle(it)
            }) {
            Dialogs.selectAllyToBattleDialog.value = SelectAllyData(extraString = extraTitle, filter = allyFilter, select = {
                BattleManager.selectAllyToBattle(it)
            }) {
                start()
                true
            }
        }
    }
}