package com.moyu.chuanqirensheng.screen.sell

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.draw.scale
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.core.content.ContextCompat
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.ad.AdHolder
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.cloud.bill.BillingManager
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.feature.holiday.HolidayLotteryManager
import com.moyu.chuanqirensheng.feature.holiday.HolidayManager
import com.moyu.chuanqirensheng.feature.monthcard.MonthCardManager
import com.moyu.chuanqirensheng.feature.newTask.SevenDayManager
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.sell.SellManager
import com.moyu.chuanqirensheng.logic.sell.getPriceTextWithUnit
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.ButtonType
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.dialog.AwardList
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.screen.dialog.ForeverGif
import com.moyu.chuanqirensheng.screen.dialog.defaultParam
import com.moyu.chuanqirensheng.screen.effect.payGif
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.W50
import com.moyu.chuanqirensheng.ui.theme.imageMedium
import com.moyu.chuanqirensheng.ui.theme.padding1
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding100
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding170
import com.moyu.chuanqirensheng.ui.theme.padding180
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding3
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.padding360
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding51
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding60
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.ui.theme.padding84
import com.moyu.chuanqirensheng.ui.theme.paddingLarge
import com.moyu.chuanqirensheng.ui.theme.paddingLargePlus
import com.moyu.chuanqirensheng.ui.theme.paddingMediumPlus
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.ui.theme.paddingTiny
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.millisToHoursMinutesSeconds
import com.moyu.chuanqirensheng.util.millisToMidnight
import com.moyu.chuanqirensheng.util.refreshNetTime
import com.moyu.core.model.sell.Sell
import com.moyu.core.model.sell.toAward
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch


@Composable
fun OneSellItem(sell: Sell, scale: Float = 1f) {
    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        if (sell.isMonthCard()) {
            val refresh = remember {
                mutableIntStateOf(0)
            }
            LaunchedEffect(refresh) {
                SellManager.init()
            }
            val leftUpdateTime = remember {
                mutableLongStateOf(0L)
            }
            val leftStorage = remember {
                mutableStateOf(MonthCardManager.getLeftDay(sell.id))
            }
            LaunchedEffect(refresh) {
                refreshNetTime()
                if (isNetTimeValid()) {
                    while (true) {
                        leftUpdateTime.longValue = millisToMidnight(getCurrentTime())
                        leftStorage.value = MonthCardManager.getLeftDay(sell.id)
                        if (leftUpdateTime.longValue <= 1000) {
                            delay(1000)
                            // 修改这个，上面的LauncherEffect会刷新任务
                            refresh.intValue += 1
                        }
                        delay(500)
                    }
                }
            }
            Box(
                modifier = Modifier
                    .size(padding360, padding170)
                    .shadow(
                        elevation = padding16, shape = RoundedCornerShape(padding8)
                    )
                    .clip(RoundedCornerShape(padding8)) // Clip only the background
                    .paint(
                        painterResource(R.drawable.sell_month_card_frame),
                        contentScale = ContentScale.FillBounds
                    )
            ) {
                Row(
                    modifier = Modifier.size(padding360, padding170), // Match size but no clipping
                    horizontalArrangement = Arrangement.SpaceAround
                ) {
                    Column(
                        Modifier.fillMaxHeight(), verticalArrangement = Arrangement.SpaceEvenly
                    ) {
                        Text(
                            text = sell.name,
                            style = MaterialTheme.typography.h1,
                            modifier = Modifier
                                .scale(1.2f)
                                .padding(start = padding12, top = padding8)
                        )
                        Row(
                            Modifier
                                .size(padding180, padding26)
                                .paint(
                                    painterResource(R.drawable.sell_label),
                                    contentScale = ContentScale.FillBounds
                                )
                                .graphicsLayer {
                                    translationX = -padding12.toPx()
                                },
                            horizontalArrangement = Arrangement.Center,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                modifier = Modifier.graphicsLayer {
                                    translationX = padding10.toPx()
                                },
                                text = stringResource(R.string.month_card_tips),
                                style = MaterialTheme.typography.h5
                            )
                        }
                        Row(Modifier.animateContentSize()) {
                            if (!MonthCardManager.canCardGainDailyAward(sell.id)) {
                                Column {
                                    Text(
                                        text = stringResource(R.string.month_card_immediate),
                                        style = MaterialTheme.typography.h5
                                    )
                                    AwardList(
                                        Modifier,
                                        award = sell.toAward().copy(electric = 0),
                                        param = defaultParam.copy(
                                            textColor = Color.White,
                                            itemSize = ItemSize.MediumPlus,
                                            minLine = 1,
                                            showName = false
                                        ),
                                        mainAxisAlignment = Arrangement.Center
                                    )
                                }
                                Spacer(Modifier.size(padding4))
                                Spacer(
                                    Modifier
                                        .size(padding2, padding60)
                                        .clip(RoundedCornerShape(50f))
                                        .background(W50)
                                )
                                Spacer(Modifier.size(padding4))
                            }
                            Box(contentAlignment = Alignment.Center) {
                                Column {
                                    Text(
                                        text = if (MonthCardManager.getItemById(sell.id)
                                                ?.expired() != false || MonthCardManager.canCardGainDailyAward(
                                                sellId = sell.id
                                            )
                                        ) stringResource(R.string.month_card_day) else stringResource(
                                            R.string.task_refresh_left_time
                                        ) + leftUpdateTime.longValue.millisToHoursMinutesSeconds(),
                                        style = MaterialTheme.typography.h5
                                    )
                                    AwardList(
                                        award = repo.gameCore.getPoolById(sell.itemId2).toAward(),
                                        param = defaultParam.copy(
                                            textColor = Color.White,
                                            itemSize = ItemSize.MediumPlus,
                                            minLine = 1,
                                            showName = false
                                        ),
                                        mainAxisAlignment = Arrangement.Center
                                    )
                                }
                            }
                            if (MonthCardManager.canCardGainDailyAward(sell.id)) {
                                Spacer(Modifier.size(padding4))
                                GameButton(
                                    modifier = Modifier.graphicsLayer {
                                        translationY = padding19.toPx()
                                    },
                                    text = stringResource(R.string.gain_award),
                                    buttonStyle = ButtonStyle.Orange,
                                    buttonSize = ButtonSize.MediumMinus,
                                    textColor = Color.White,
                                ) {
                                    GameApp.globalScope.launch(Dispatchers.Main) {
                                        MonthCardManager.gainDailyAward(sell)
                                    }
                                }
                            }
                        }
                    }
                    Column(
                        modifier = Modifier.fillMaxHeight().width(padding100), // Allow overflow
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.SpaceEvenly
                    ) {
                        Spacer(Modifier.size(padding6))
                        Box(contentAlignment = Alignment.Center) {
                            Image(
                                modifier = Modifier
                                    .size(padding84)
                                    .scale(1.2f), // Ensure it renders above other elements
                                painter = painterResource(getImageResourceDrawable(sell.pic)),
                                contentDescription = null
                            )

                            leftStorage.value?.let {
                                Text(
                                    text = stringResource(R.string.time_left) + it + stringResource(
                                        R.string.time_left_day
                                    ),
                                    style = MaterialTheme.typography.h3,
                                    modifier = Modifier
                                        .clip(RoundedCornerShape(padding2))
                                        .background(B50)
                                        .padding(horizontal = padding3, vertical = padding1)
                                )
                            }
                            if (sell.desc2 != "0") {
                                Box(
                                    contentAlignment = Alignment.Center,
                                    modifier = Modifier
                                        .align(Alignment.TopStart)
                                        .offset(x = -padding12, y = -paddingMediumPlus)
                                ) {
                                    Image(
                                        painter = painterResource(id = R.drawable.shop_discount),
                                        contentDescription = null,
                                        modifier = Modifier.size(padding51)
                                    )
                                    Text(
                                        text = sell.desc2,
                                        style = MaterialTheme.typography.h5,
                                        modifier = Modifier.offset(
                                            x = paddingTiny, y = -paddingSmall
                                        )
                                    )
                                }
                            }
                        }
                        Box {
                            SellButton(sell)
                        }
                    }
                }
            }
        } else if (sell.isExpGift()) {
            Row(
                Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                Box(Modifier.width(ItemSize.HugeTalent.frameSize)) {
                    ForeverGif(
                        modifier = Modifier
                            .size(ItemSize.HugeTalent.itemSize),
                        resource = payGif.gif,
                        num = payGif.count,
                        needGap = true
                    )
                    Image(
                        modifier = Modifier
                            .fillMaxWidth()
                            .align(Alignment.BottomCenter)
                            .graphicsLayer {
                                translationY = padding22.toPx()
                            },
                        contentScale = ContentScale.FillWidth,
                        painter = painterResource(id = R.drawable.shop_draw_frame),
                        contentDescription = null
                    )
                    Image(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(padding8),
                        contentScale = ContentScale.FillWidth,
                        painter = painterResource(id = getImageResourceDrawable(sell.pic)),
                        contentDescription = null
                    )
                    if (sell.desc2 != "0") {
                        Box(
                            contentAlignment = Alignment.Center,
                            modifier = Modifier
                                .align(Alignment.TopCenter)
                                .offset(x = -paddingLargePlus)
                        ) {
                            Image(
                                painter = painterResource(id = R.drawable.shop_discount),
                                contentDescription = null,
                                modifier = Modifier.size(imageMedium)
                            )
                            Text(
                                text = sell.desc2,
                                style = MaterialTheme.typography.h5,
                                modifier = Modifier.offset(x = paddingTiny, y = -paddingSmall)
                            )
                        }
                    }
                    if (sell.storage < 5000) {
                        Text(
                            modifier = Modifier
                                .align(Alignment.BottomCenter)
                                .graphicsLayer {
                                    translationY = padding30.toPx()
                                },
                            text = stringResource(R.string.sell_storage_tips, sell.storage),
                            style = MaterialTheme.typography.h3,
                            color = Color.White,
                            textAlign = TextAlign.Center
                        )
                    }
                }
                Column(Modifier.graphicsLayer {
                    translationY = padding12.toPx()
                }, horizontalAlignment = Alignment.CenterHorizontally) {
                    TextLabel2(
                        text = sell.name,
                        labelSize = LabelSize.Large,
                        frame = R.drawable.common_frame3
                    )
                    AwardList(
                        // todo 商店里不要显示电力，获得时候有就行
                        award = sell.toAward().copy(electric = 0),
                        mainAxisAlignment = Arrangement.spacedBy(padding22),
                        param = defaultParam.copy(textColor = Color.White)
                    )
                    Box {
                        SellButton(sell)
                    }
                }
            }
        } else {
            SingleNormalChest(Modifier.scale(scale), sell)
            SellButton(sell)
        }
        Spacer(modifier = Modifier.size(paddingLarge))
    }
}

@Composable
fun SellButton(sell: Sell) {
    GameButton(
        text = if (sell.storage <= 0) stringResource(R.string.sold_out) else {
            if (sell.isAifadian()) {
                sell.getPriceTextWithUnit()
            } else sell.price.toString()
        },
        buttonSize = ButtonSize.MediumMinus,
        buttonStyle = ButtonStyle.Orange,
        buttonType = if (sell.storage <= 0) ButtonType.Normal
        else if (sell.isAifadian()) ButtonType.AiFaDian
        else if (sell.isAd()) ButtonType.Ad
        else if (sell.isPvpMoney()) ButtonType.Pvp
        else if (sell.isKeyMoney()) ButtonType.Key
        else ButtonType.Diamond,
        enabled = sell.storage > 0,
        onClick = {
            if (sell.storage <= 0) {
                GameApp.instance.getWrapString(R.string.sold_out).toast()
            } else if (sell.isHoliday() && !HolidayLotteryManager.canDoCheap()) {
                GameApp.instance.getWrapString(R.string.lottery_run_out).toast()
            } else if (sell.isDiamondMoney() && AwardManager.diamond.value < sell.price) {
                GiftManager.onDiamondNotEnough()
                GameApp.instance.getWrapString(R.string.diamond_not_enough).toast()
            } else if (sell.isKeyMoney() && AwardManager.key.value < sell.price) {
                GiftManager.onKeyNotEnough()
                GameApp.instance.getWrapString(R.string.key_not_enough).toast()
            } else if (sell.isPvpMoney() && AwardManager.pvpDiamond.value < sell.price) {
                GameApp.instance.getWrapString(R.string.pvp_diamond_not_enough).toast()
            } else {
                if (sell.isAd()) {
                    GameApp.globalScope.launch(Dispatchers.Main) {
                        AdHolder.playAd(sell.id.toString()) {
                            if (sell.isSevenDay()) {
                                SevenDayManager.openPackage(sell)
                            } else if (sell.isHoliday()) {
                                HolidayManager.openPackage(sell)
                            } else if (sell.isTower()) {
                                TowerManager.openPackage(sell)
                            } else {
                                SellManager.openSellChest(sell)
                            }
                        }
                    }
                } else if (sell.isAifadian()) {
                    if (GameApp.instance.resources.getBoolean(R.bool.has_billing)) {
                        // 谷歌内购
                        GameApp.globalScope.launch(Dispatchers.Main) {
                            BillingManager.prepay(sell = sell) {
                                GameApp.globalScope.launch(Dispatchers.Main) {
                                    SellManager.dealAfterPay(sell, sell.toAward())
                                }
                            }
                        }
                    } else {
                        if (sell.desc == "0") {
                            GameApp.instance.getWrapString(R.string.cant_get_yet).toast()
                        } else {
                            if (GameApp.instance.canShowAifadian()) {
                                val uri: Uri = Uri.parse(sell.desc)
                                val intent = Intent(Intent.ACTION_VIEW, uri)
                                ContextCompat.startActivity(
                                    GameApp.instance.activity, intent, Bundle()
                                )
                            } else {
                                GameApp.instance.getWrapString(R.string.cant_get_yet).toast()
                            }
                        }
                    }
                } else {
                    val award = sell.toAward()
                    val already5Stars = award.outAllies.firstOrNull()?.mainId?.let { mainId ->
                        repo.allyManager.data.any { it.mainId == mainId && it.star == it.starLimit }
                    } ?: award.outSkills.firstOrNull()?.mainId?.let { mainId ->
                        repo.skillManager.data.any { it.mainId == mainId && it.level == 5 }
                    } ?: award.outHeroes.firstOrNull()?.mainId?.let { mainId ->
                        repo.heroManager.data.any { it.mainId == mainId && it.level == 5 }
                    } ?: false
                    if (already5Stars) {
                        (GameApp.instance.getWrapString(R.string.already_max_gained) + sell.name + GameApp.instance.getWrapString(
                            R.string.no_need_buy
                        )).toast()
                    } else {
                        Dialogs.alertDialog.value = CommonAlert(
                            title = GameApp.instance.getWrapString(R.string.confirm_buy),
                            content = GameApp.instance.getWrapString(R.string.confirm_buy2) + sell.name + "?",
                            onConfirm = {
                                GameApp.globalScope.launch(Dispatchers.Main) {
                                    if (sell.isSevenDay()) {
                                        SevenDayManager.openPackage(sell)
                                    } else if (sell.isHoliday()) {
                                        HolidayManager.openPackage(sell)
                                    } else if (sell.isMonthCard()) {
                                        MonthCardManager.openPackage(sell)
                                    } else if (sell.isTower()) {
                                        TowerManager.openPackage(sell)
                                    } else {
                                        SellManager.openSellChest(sell)
                                    }
                                }
                            })
                    }
                }
            }
        })
}
