package com.moyu.chuanqirensheng.screen.dialog

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.core.content.ContextCompat
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.cloud.bill.BillingManager
import com.moyu.chuanqirensheng.cloud.bill.KeyItems
import com.moyu.chuanqirensheng.cloud.bill.keyOfAifadian
import com.moyu.chuanqirensheng.cloud.bill.keyOfGoogleBill
import com.moyu.chuanqirensheng.cloud.bill.keyOfWechatPay
import com.moyu.chuanqirensheng.logic.sell.SellManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.ui.theme.imageSmallPlusFrame
import com.moyu.chuanqirensheng.ui.theme.paddingHugePlusPlus
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.smallDialogHeight
import com.moyu.core.model.sell.toAward
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun GetKeyDialog(unlock: MutableState<Boolean>) {
    if (unlock.value) {
        CommonDialog(
            onDismissRequest = { unlock.value = false },
            frame = R.drawable.common_big_pad2,
            noPadding = true,
            heightInDp = smallDialogHeight
        ) {
            Column(
                Modifier
                    .fillMaxSize()
                    .offset(y = -paddingHugePlusPlus)
                    .padding(horizontal = paddingMedium)
                    .verticalScroll(rememberScrollState())

            ) {
                val list =
                    if (GameApp.instance.resources.getBoolean(R.bool.has_billing)) {
                        if (GameApp.instance.resources.getBoolean(R.bool.has_google_service)) {
                            keyOfGoogleBill
                        } else {
                            keyOfWechatPay
                        }
                    } else keyOfAifadian.mapIndexed { index, keyItems ->
                        keyItems.copy(url = repo.gameCore.getGetKeyUrls()[index])
                    }
                Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceEvenly) {
                    KeyItem(list[0])
                    KeyItem(list[1])
                    KeyItem(list[2])
                }
                Spacer(modifier = Modifier.size(paddingMedium))
                Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceEvenly) {
                    KeyItem(list[3])
                    KeyItem(list[4])
                    list.getOrNull(5)?.let {
                        KeyItem(list[5])
                    }
                }
            }
        }
    }
}

@Composable
fun KeyItem(keyItems: KeyItems) {
    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        Box {
            SingleAwardItem(
                name = stringResource(id = R.string.key_title),
                drawable = R.drawable.common_key,
                num = keyItems.text,
                param = defaultParam.copy(
                    itemSize = ItemSize.LargePlus,
                    showName = false,
                    numInFrame = true
                )
            )
            if (keyItems.discount.isNotEmpty()) {
                Box(
                    modifier = Modifier
                        .align(Alignment.TopStart)
                        .graphicsLayer {
                            clip = false
                        }, contentAlignment = Alignment.Center
                ) {
                    Image(
                        modifier = Modifier.size(imageSmallPlusFrame),
                        painter = painterResource(R.drawable.shop_discount),
                        contentDescription = keyItems.discount
                    )
                    Text(
                        text = keyItems.discount,
                        style = MaterialTheme.typography.body1,
                        textAlign = TextAlign.Center
                    )
                }
            }
        }

        GameButton(
            text = if (!GameApp.instance.resources.getBoolean(R.bool.has_billing)) stringResource(id = R.string.buy) else keyItems.dollar,
            buttonSize = ButtonSize.Small,
            buttonStyle = ButtonStyle.Orange,
            textColor = Color.White
        ) {
            if (GameApp.instance.resources.getBoolean(R.bool.has_billing)) {
                GameApp.globalScope.launch(Dispatchers.Main) {
                    val sell = repo.gameCore.getSellPool()
                        .first { it.id == keyItems.url.toInt() }
                    BillingManager.prepay(sell = sell) {
                        GameApp.globalScope.launch(Dispatchers.Main) {
                            SellManager.dealAfterPay(sell, sell.toAward())
                        }
                    }
                }
            } else {
                val uri: Uri = Uri.parse(keyItems.url)
                val intent = Intent(Intent.ACTION_VIEW, uri)
                ContextCompat.startActivity(GameApp.instance.activity, intent, Bundle())
            }
        }
    }
}
