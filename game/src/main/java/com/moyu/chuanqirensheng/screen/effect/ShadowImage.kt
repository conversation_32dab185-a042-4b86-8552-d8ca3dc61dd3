package com.moyu.chuanqirensheng.screen.effect

import androidx.compose.foundation.Image
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.TransformOrigin
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.Dp
import com.moyu.chuanqirensheng.ui.theme.padding0

@Composable
fun ShadowImage(
    modifier: Modifier = Modifier,
    imageResource: Int,
    clipSize: Dp = padding0,
) {
    // 斜向剪影
    Image(
        modifier = modifier
            .graphicsLayer {
                // 压扁和倾斜效果
                scaleY = 0.5f
                rotationZ = 10f
                transformOrigin = TransformOrigin(0.5f, 1f) // 设置旋转中心为底部中心
            }
            .clip(RoundedCornerShape(clipSize)),
        alignment = Alignment.TopCenter,
        painter = painterResource(id = imageResource),
        colorFilter = ColorFilter.tint(Color.Black, BlendMode.SrcIn),
        contentDescription = null
    )
    Image(
        modifier = modifier
            .clip(RoundedCornerShape(clipSize)),
        alignment = Alignment.TopCenter,
        painter = painterResource(id = imageResource),
        contentDescription = null
    )
}