package com.moyu.chuanqirensheng.screen.dialog

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.feature.mode.isTower
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.feature.pvp.PvpManager.currentTarget
import com.moyu.chuanqirensheng.logic.battle.BattleManager.updateAlliesAfterBattle
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.event.fatalEventPlayIds
import com.moyu.chuanqirensheng.model.record.AllRankData
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.GameDataManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.paddingLarge
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.util.PVP_CHOOSE_ENEMY_SCREEN
import com.moyu.chuanqirensheng.util.TOWER_SCREEN
import com.moyu.chuanqirensheng.util.goto
import com.moyu.core.model.role.Role

@Composable
fun GameLoseDialog(result: MutableState<List<Role>>) {
    result.value.takeIf { it.isNotEmpty() }?.let { roles ->
        var relived = false
        LaunchedEffect(Unit) {
            Dialogs.infoDialog.value = false
            GiftManager.getDisplayGifts(isBattle = true).firstOrNull { !it.dialogShowed }?.let {
                Dialogs.giftDetailDialog.value = it
            }
        }
        DisposableEffect(Unit) {
            onDispose {
                if (!relived && !repo.gameMode.value.isPvp() && !repo.gameMode.value.isTower()) {
                    updateAlliesAfterBattle(roles.filter { it.isPlayerSide() })
                    EventManager.doEventBattleResult(EventManager.selectedEvent.value, false)
                }
            }
        }
        CommonDialog(title = stringResource(R.string.battle_failed), onDismissRequest = {
            result.value = emptyList()
            if (repo.gameMode.value.isPvp()) {
                currentTarget.value = AllRankData(System.currentTimeMillis())
                goto(PVP_CHOOSE_ENEMY_SCREEN)
            } else if (repo.gameMode.value.getGameMode().isTower()) {
                goto(TOWER_SCREEN)
            }
        }) {
            Column(
                Modifier
                    .fillMaxSize()
                    .padding(horizontal = paddingMedium)
            ) {
                Spacer(modifier = Modifier.size(paddingMedium))
                if (repo.gameMode.value.isPvp()) {
                    Text(
                        text = stringResource(R.string.failed_to, PvpManager.currentTarget.value.userName),
                        style = MaterialTheme.typography.h3,
                        color = Color.Black
                    )
                    Spacer(modifier = Modifier.size(padding4))
                } else {
                    roles.first { !it.isPlayerSide() }.let {
                        Text(
                            text = stringResource(R.string.failed_to, it.getRace().name),
                            style = MaterialTheme.typography.h3,
                            color = Color.Black
                        )
                        Spacer(modifier = Modifier.size(paddingSmall))
                    }
                    Spacer(modifier = Modifier.size(paddingMedium))
                    roles.filter { it.isPlayerSide() }.filter { it.isDeath() }.forEach {
                        Text(
                            text = stringResource(
                                R.string.die_in_battle, it.getRace().name
                            ), style = MaterialTheme.typography.h3, color = Color.Black
                        )
                        Spacer(modifier = Modifier.size(paddingSmall))
                    }
                }
                Spacer(modifier = Modifier.weight(1f))
                Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceEvenly) {
                    GameButton(
                        text = stringResource(R.string.statistic),
                        buttonStyle = ButtonStyle.Blue
                    ) {
                        Dialogs.statisticView.value = true
                    }
                    GameButton(text = stringResource(id = R.string.battle_report), buttonStyle = ButtonStyle.Orange) {
                        Dialogs.infoDialog.value = true
                    }
                    if (GameDataManager.canRelive() && !repo.gameMode.value.isPvp() && !repo.gameMode.value.isTower()) {
                        GameButton(
                            text = stringResource(R.string.force_win_title),
                            buttonStyle = ButtonStyle.Red
                        ) {
                            GameDataManager.tryRelive {
                                relived = true
                                result.value = emptyList()
                                if (EventManager.selectedEvent.value?.play in fatalEventPlayIds) {
                                    // 宿敌胜利弹窗不一样
                                    Dialogs.fatalEnemyDialog.value = EventManager.selectedEvent.value
                                } else {
                                    updateAlliesAfterBattle(roles.filter { it.isPlayerSide() }.filter { !it.isDeath() })
                                    EventManager.doEventBattleResult(EventManager.selectedEvent.value, true)
                                }
                            }
                        }
                    }
                }
                Spacer(modifier = Modifier.size(paddingLarge))
            }
        }
    }
}