package com.moyu.chuanqirensheng.screen.equip

import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.screen.dialog.AwardUIParam
import com.moyu.chuanqirensheng.screen.dialog.SingleAwardItem
import com.moyu.chuanqirensheng.screen.dialog.defaultParam
import com.moyu.core.model.property.AdventureProps

@Composable
fun AdventureProps.DropPropertyLine(param: AwardUIParam = defaultParam) {
    this.science.takeIf { it != 0 }?.let {
        SingleAwardItem(
            drawable = R.drawable.player_attribute_1,
            name = stringResource(id = R.string.role_prop1),
            num = (if (it > 0) "+" else "") + it,
            param = param.copy(),
        ) {
            BattleManager.adventureProps.value.science >= it
        }
    }

    this.politics.takeIf { it != 0 }?.let {
        SingleAwardItem(
            drawable = R.drawable.player_attribute_2,
            name = stringResource(id = R.string.role_prop2),
            num = (if (it > 0) "+" else "") + it,
            param = param,
        ) {
            BattleManager.adventureProps.value.politics >= it
        }
    }
    this.military.takeIf { it != 0 }?.let {
        SingleAwardItem(
            drawable = R.drawable.player_attribute_3,
            name = stringResource(id = R.string.role_prop3),
            num = (if (it > 0) "+" else "") + it,
            param = param,
        ) {
            BattleManager.adventureProps.value.military >= it
        }
    }
    this.religion.takeIf { it != 0 }?.let {
        SingleAwardItem(
            drawable = R.drawable.player_attribute_4,
            name = stringResource(id = R.string.role_prop4),
            num = (if (it > 0) "+" else "") + it,
            param = param,
        ) {
            BattleManager.adventureProps.value.religion >= it
        }
    }
    this.commerce.takeIf { it != 0 }?.let {
        SingleAwardItem(
            drawable = R.drawable.player_attribute_5,
            name = stringResource(id = R.string.role_prop5),
            num = (if (it > 0) "+" else "") + it,
            param = param,
        ) {
            BattleManager.adventureProps.value.commerce >= it
        }
    }
    this.art.takeIf { it != 0 }?.let {
        SingleAwardItem(
            drawable = R.drawable.player_attribute_6,
            name = stringResource(id = R.string.role_prop6),
            num = (if (it > 0) "+" else "") + it,
            param = param,
        ) {
            BattleManager.adventureProps.value.art >= it
        }
    }
    this.population.takeIf { it != 0 }?.let {
        SingleAwardItem(
            drawable = R.drawable.player_attribute_7,
            name = stringResource(id = R.string.role_prop7),
            num = (if (it > 0) "+" else "") + it,
            param = param,
        ) {
            BattleManager.adventureProps.value.population >= it
        }
    }
}