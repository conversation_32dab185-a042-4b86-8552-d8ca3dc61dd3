package com.moyu.chuanqirensheng.screen.dialog

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.core.content.ContextCompat
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.story.Ending
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.ui.theme.paddingMedium

@Composable
fun EndingDetailDialog(show: MutableState<Ending?>) {
    show.value?.let { ending ->
        CommonDialog(
            title = stringResource(id = R.string.life_record),
            onDismissRequest = {
                show.value = null
            }) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = paddingMedium),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Spacer(modifier = Modifier.size(paddingMedium))
                Text(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                        .verticalScroll(rememberScrollState()),
                    text = ending.endingText,
                    style = MaterialTheme.typography.h4, color = Color.Black
                )
                Spacer(modifier = Modifier.size(paddingMedium))
                if (!GameApp.instance.resources.getBoolean(R.bool.has_google_service)) {
                    GameButton(
                        text = stringResource(id = R.string.share),
                        buttonStyle = ButtonStyle.Orange
                    ) {
                        val myClipboard =
                            GameApp.instance.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                        val myClip = ClipData.newPlainText("text", ending.endingText)
                        myClipboard.setPrimaryClip(myClip)
                        val uri: Uri =
                            Uri.parse(GameApp.instance.resources.getString(R.string.main_page_url))
                        val intent = Intent(Intent.ACTION_VIEW, uri)
                        ContextCompat.startActivity(
                            GameApp.instance.activity,
                            intent,
                            Bundle()
                        )
                    }
                }
            }
        }
    }
}