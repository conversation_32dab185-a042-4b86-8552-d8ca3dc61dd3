package com.moyu.chuanqirensheng.screen.common

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.guide.GuideManager
import com.moyu.chuanqirensheng.logic.speed.GameSpeedManager
import com.moyu.chuanqirensheng.music.MusicManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.button.QUICK_GAP
import com.moyu.chuanqirensheng.ui.theme.paddingLarge
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.ui.theme.settingSize
import com.moyu.chuanqirensheng.util.getImageResourceDrawable

val liftInfo = SettingItem(
    name = { GameApp.instance.getWrapString(R.string.life_info) },
    icon = { R.drawable.battle_icon_report },
    action = { Dialogs.extraInfoDialog.value = true },
)

val battleInfo = SettingItem(
    name = { GameApp.instance.getWrapString(R.string.battle_info) },
    icon = { R.drawable.battle_icon_report },
    action = { Dialogs.infoDialog.value = true },
)

val music = SettingItem(
    name = {
        if (MusicManager.muteMusic)
            GameApp.instance.getWrapString(R.string.music_paused)
        else GameApp.instance.getWrapString(R.string.music_on)
    },
    icon = { if (MusicManager.muteMusic) R.drawable.battle_icon_musicoff else R.drawable.battle_icon_musicon },
    action = { MusicManager.switchMuteMusic() },
)

val sound = SettingItem(
    name = {
        if (MusicManager.muteSound)
            GameApp.instance.getWrapString(R.string.sound_effect_paused)
        else GameApp.instance.getWrapString(R.string.sound_effect_on)
    },
    icon = { if (MusicManager.muteSound) R.drawable.battle_icon_soundoff else R.drawable.battle_icon_soundon },
    action = { MusicManager.switchMuteSound() },
)

val pause = SettingItem(
    name = { GameSpeedManager.getCurrentSpeed().description },
    icon = { getImageResourceDrawable(GameSpeedManager.getCurrentSpeed().icon) },
    action = { GameSpeedManager.nextSpeed() },
)

val tutor = SettingItem(
    name = { GameApp.instance.getWrapString(R.string.tutor) },
    icon = { R.drawable.battle_icon_tutorial },
    action = { Dialogs.tutorDialog.value = true },
)

val settingItems = mutableStateListOf(
    liftInfo,
    music,
    sound,
    tutor,
)

val settingBattleItems = mutableStateListOf(
    battleInfo,
    music,
    sound,
    pause,
    tutor,
)

data class SettingItem(
    val name: () -> String,
    val icon: () -> Int,
    val redIcon: () -> Boolean = { false },
    val action: () -> Unit,
)


@Composable
fun SettingColumn(modifier: Modifier, settings: SnapshotStateList<SettingItem>) {
    Box(
        modifier = modifier
            .width(settingSize)
            .height((settingSize + paddingSmall) * settings.size - paddingSmall)
            .animateContentSize(),
        contentAlignment = Alignment.Center
    ) {
        Image(
            modifier = Modifier
                .fillMaxHeight()
                .padding(vertical = paddingLarge),
            contentScale = ContentScale.FillHeight,
            painter = painterResource(id = R.drawable.battle_menu_chain),
            contentDescription = null
        )
        Column {
            settings.forEach {
                EffectButton(clickGap = QUICK_GAP, onClick = {
                    if (GuideManager.guideIndex.value == 7 && GuideManager.showGuide.value) {
                        GameApp.instance.getWrapString(R.string.quit_guide_tips).toast()
                    } else {
                        it.action()
                        // TODO 刷新UI
                        val list = settings.toList()
                        settings.clear()
                        settings.addAll(list)
                    }
                }) {
                    Column {
                        Image(
                            modifier = Modifier.size(settingSize),
                            painter = painterResource(id = it.icon()),
                            contentDescription = it.name()
                        )
                        Spacer(modifier = Modifier.size(paddingSmall))
                    }
                }
            }
        }
    }
}