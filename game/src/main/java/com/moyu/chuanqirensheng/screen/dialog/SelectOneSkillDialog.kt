package com.moyu.chuanqirensheng.screen.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.datastore.KEY_GUIDE_INDEX
import com.moyu.chuanqirensheng.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.guide.FIRST_GUIDE_END
import com.moyu.chuanqirensheng.logic.guide.GuideManager
import com.moyu.chuanqirensheng.repository.Dialogs.allyDetailDialog
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.filter.CommonFilterView
import com.moyu.chuanqirensheng.screen.filter.FilterLayout
import com.moyu.chuanqirensheng.screen.filter.ItemFilter
import com.moyu.chuanqirensheng.screen.filter.skillFilterList
import com.moyu.chuanqirensheng.screen.guide.GuidePointAndText
import com.moyu.chuanqirensheng.screen.guide.HandType
import com.moyu.chuanqirensheng.screen.skill.SingleSkillView
import com.moyu.chuanqirensheng.ui.theme.B65
import com.moyu.chuanqirensheng.ui.theme.detailBigHeight
import com.moyu.chuanqirensheng.ui.theme.gapMedium
import com.moyu.chuanqirensheng.ui.theme.gapSmallPlus
import com.moyu.chuanqirensheng.ui.theme.paddingLargePlus
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingMediumPlus
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.util.composeDp
import com.moyu.chuanqirensheng.util.pixelToDp
import com.moyu.chuanqirensheng.util.screenWidthInPixel
import com.moyu.core.GameCore
import com.moyu.core.model.skill.Skill
import com.moyu.core.music.SoundEffect

@Composable
fun SelectOneSkillDialog(showDungeon: MutableState<Pair<(Skill) -> Boolean, (Skill) -> Unit>?>) {
    showDungeon.value?.let { pair ->
        val showFilter = remember {
            mutableStateOf(false)
        }
        val filter = remember {
            mutableStateListOf<ItemFilter<Skill>>()
        }
        val callback = pair.second
        val list = BattleManager.getGameSkills().filter(pair.first).filter { skill ->
            filter.all { it.filter.invoke(skill) }
        }
        CommonDialog(
            title = stringResource(R.string.select_skill),
            onDismissRequest = {
                if (GuideManager.guideIndex.value == 12) {
                    GameCore.instance.onBattleEffect(SoundEffect.EquipAnything)
                    GuideManager.guideIndex.value  = FIRST_GUIDE_END
                    callback(list.first())
                    showDungeon.value = null
                    allyDetailDialog.value = null // 关闭军团卡详情弹窗，直接反馈回事件选择界面
                    GuideManager.showGuide.value = false // 暂时关闭引导，后面的引导需要触发条件
                    GameApp.instance.getWrapString(R.string.guide_skill_tips).toast()
                } else {
                    showDungeon.value = null
                }
            }) {
            Column(modifier = Modifier.fillMaxSize()) {
                Row(
                    Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.End
                ) {
                    CommonFilterView(
                        Modifier.padding(end = paddingMedium), showFilter
                    )
                }
                LazyVerticalGrid(
                    modifier = Modifier.fillMaxSize(),
                    columns = GridCells.Fixed(4),
                    content = {
                        items(list.size) { index ->
                            val ally = list[index]
                            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                                SingleSkillView(skill = ally, itemSize = ItemSize.LargePlus)
                                Spacer(modifier = Modifier.size(paddingSmall))
                                GameButton(
                                    text = stringResource(id = R.string.do_select),
                                    buttonStyle = ButtonStyle.Orange,
                                    buttonSize = ButtonSize.Small,
                                    onClick = {
                                        callback(ally)
                                        showDungeon.value = null
                                    })
                                Spacer(modifier = Modifier.size(paddingLargePlus))
                            }
                        }
                    })
            }
            FilterLayout(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(top = gapSmallPlus, end = paddingMedium),
                show = showFilter,
                filter = filter,
                filterList = skillFilterList
            )
            if (GuideManager.guideIndex.value == 12) {
                if (list.firstOrNull() == null) {
                    GuideManager.guideIndex.value = FIRST_GUIDE_END
                    GuideManager.showGuide.value = false
                    setIntValueByKey(KEY_GUIDE_INDEX, GuideManager.guideIndex.value)
                } else {
                    Column(
                        Modifier
                            .requiredSize(
                                screenWidthInPixel.pixelToDp(),
                                detailBigHeight
                            ) //使用 requiredSize 可以超过父元素大小
                            .background(B65)
                            .clickable {
                                GameCore.instance.onBattleEffect(SoundEffect.EquipAnything)
                                GuideManager.guideIndex.value  = FIRST_GUIDE_END
                                callback(list.first())
                                showDungeon.value = null
                                allyDetailDialog.value = null // 关闭军团卡详情弹窗，直接反馈回事件选择界面
                                GuideManager.showGuide.value = false // 暂时关闭引导，后面的引导需要触发条件
                                GameApp.instance.getWrapString(R.string.guide_skill_tips).toast()
                            },
                    ) {
                        Column(
                            Modifier
                                .fillMaxWidth()
                                .padding(start = 45.composeDp(), top = 72.composeDp())
                        )
                        {
                            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                                SingleSkillView(
                                    skill = list.first(),
                                    itemSize = ItemSize.LargePlus,
                                    textColor = Color.White,
                                )
                                Spacer(modifier = Modifier.size(paddingSmall))
                                GameButton(text = stringResource(id = R.string.do_select), buttonStyle = ButtonStyle.Orange, buttonSize = ButtonSize.Small, onClick = {
                                    GameCore.instance.onBattleEffect(SoundEffect.EquipAnything)
                                    GuideManager.guideIndex.value  = FIRST_GUIDE_END
                                    callback(list.first())
                                    showDungeon.value = null
                                    allyDetailDialog.value = null // 关闭军团卡详情弹窗，直接反馈回事件选择界面
                                    GuideManager.showGuide.value = false // 暂时关闭引导，后面的引导需要触发条件
                                    GameApp.instance.getWrapString(R.string.guide_skill_tips).toast()
                                })
                            }
                            GuidePointAndText(
                                text = stringResource(R.string.guide13),
                                handType = HandType.UP_HAND,
                                offsetX = gapMedium, offsetY = -paddingMediumPlus
                            )
                        }
                    }
                }
            }
        }
    }
}


