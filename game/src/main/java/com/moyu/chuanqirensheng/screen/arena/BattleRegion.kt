package com.moyu.chuanqirensheng.screen.arena

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import com.moyu.chuanqirensheng.logic.ally.getRegionDrawable
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.ui.theme.W30
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.ui.theme.paddingTiny
import com.moyu.core.model.ally.Ally
import com.moyu.core.model.role.Role
import java.lang.Integer.max


@Composable
fun BattleRegion(
    role: Role,
    ally: Ally,
    itemSize: ItemSize,
    selectCallBack: (Ally) -> Unit
) {
    val maxHp = role.getDefaultProperty().hp
    val currentHp = role.getCurrentProperty().hp
    EffectButton(modifier = Modifier, onClick = {
        selectCallBack(ally)
    }) {
        FlowRow(modifier = Modifier.size(itemSize.frameSize + paddingTiny, itemSize.frameSize),
            overflow = FlowRowOverflow.Visible) {
            val showUnits = max(1, (currentHp.toDouble() / maxHp.toDouble() * 9 + 0.5).toInt())
            repeat(showUnits) {
                Image(
                    modifier = Modifier
                        .size(itemSize.frameSize / 3)
                        .clip(
                            RoundedCornerShape(paddingSmall)
                        )
                        .background(W30)
                        ,
                    painter = painterResource(ally.getRegionDrawable()),
                    contentDescription = null,
                )
            }
        }
    }
}