package com.moyu.chuanqirensheng.screen.life

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.logic.story.StoryManager
import com.moyu.chuanqirensheng.logic.tcg.TcgManager
import com.moyu.chuanqirensheng.ui.theme.gapMedium
import com.moyu.chuanqirensheng.ui.theme.paddingHuge
import com.moyu.chuanqirensheng.ui.theme.paddingLarge
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.util.composeDp

@Composable
fun RecordPage() {
    val endings = StoryManager.endings
    Column(
        modifier = Modifier
            .fillMaxSize()
            .paint(
                painterResource(id = R.drawable.common_page_frame),
                contentScale = ContentScale.FillBounds
            )
            .padding(horizontal = paddingMedium),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        if (endings.isEmpty()) {
            Spacer(modifier = Modifier.size(gapMedium))
            Text(text = stringResource(R.string.no_records_yet), style = MaterialTheme.typography.h2)
        } else {
            Spacer(modifier = Modifier.size(paddingMedium))
            LazyVerticalGrid(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
                columns = GridCells.Fixed(2),
                horizontalArrangement = Arrangement.spacedBy(paddingHuge),
                verticalArrangement = Arrangement.spacedBy(paddingLarge)
            ) {
                items(endings.size) { index ->
                    val ending = endings[index]
                    SingleEndingCard(
                        ending = ending
                    )
                }
            }
        }
    }
}
