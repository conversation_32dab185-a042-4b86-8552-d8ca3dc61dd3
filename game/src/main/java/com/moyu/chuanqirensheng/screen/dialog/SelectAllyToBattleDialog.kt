package com.moyu.chuanqirensheng.screen.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.datastore.KEY_BATTLE_DONE
import com.moyu.chuanqirensheng.datastore.KEY_GUIDE_INDEX
import com.moyu.chuanqirensheng.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.guide.FIRST_GUIDE_END
import com.moyu.chuanqirensheng.logic.guide.GuideManager
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.ally.SingleAllyView
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.filter.CommonFilterView
import com.moyu.chuanqirensheng.screen.filter.FilterLayout
import com.moyu.chuanqirensheng.screen.filter.ItemFilter
import com.moyu.chuanqirensheng.screen.filter.allyFilterList
import com.moyu.chuanqirensheng.screen.guide.GuidePointAndText
import com.moyu.chuanqirensheng.screen.guide.HandType
import com.moyu.chuanqirensheng.ui.theme.B35
import com.moyu.chuanqirensheng.ui.theme.detailBigHeight
import com.moyu.chuanqirensheng.ui.theme.gapSmallPlus
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding300
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding60
import com.moyu.chuanqirensheng.ui.theme.paddingLargePlus
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.util.pixelToDp
import com.moyu.chuanqirensheng.util.screenWidthInPixel
import com.moyu.core.model.ally.Ally

data class SelectAllyData(
    val extraString: String = "",
    val filter: (Ally) -> Boolean = { true },
    val select: (Ally) -> Unit = { },
    val start: () -> Boolean,
)

@Composable
fun SelectAllyToBattleDialog(show: MutableState<SelectAllyData?>) {
    show.value?.let { pair ->
        val showFilter = remember {
            mutableStateOf(false)
        }
        val filter = remember {
            mutableStateListOf<ItemFilter<Ally>>()
        }
        val allies = BattleManager.getAliveGameAllies().filter(pair.filter).filter { ally ->
            filter.all { it.filter.invoke(ally) }
        }.sortedBy {
            // 出战和装配了技能的靠前
            (if (it.selectedToBattle) -99 else 0) - it.equipSkills.size
        }
        val showGuide1 = !GuideManager.battleDone.value && allies.none { it.selectedToBattle }
        val showGuide2 = !GuideManager.battleDone.value && allies.any { it.selectedToBattle }
        CommonDialog(
            title = stringResource(id = R.string.select_ally) + pair.extraString,
            onDismissRequest = {
                if (GuideManager.battleDone.value) {
                    show.value = null
                } else {
                    GameApp.instance.getWrapString(R.string.guide_block_quit).toast()
                }
            }) {
            Column(modifier = Modifier.fillMaxSize()) {
                Row(
                    Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.End
                ) {
                    CommonFilterView(
                        Modifier.padding(end = paddingMedium), showFilter
                    )
                }
                LazyVerticalGrid(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f),
                    columns = GridCells.Fixed(4),
                    content = {
                        items(allies.size) { index ->
                            val ally = allies[index]
                            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                                SingleAllyView(
                                    ally = ally,
                                    extraInfo = if (ally.selectedToBattle) stringResource(R.string.in_battle) else "",
                                    showHp = true,
                                    itemSize = ItemSize.LargePlus,
                                )
                                GameButton(
                                    text = if (BattleManager.isAllyInBattle(ally)) stringResource(id = R.string.cancel) else stringResource(
                                        id = R.string.do_select
                                    ),
                                    buttonSize = ButtonSize.Small,
                                    onClick = {
                                        pair.select(ally)
                                    })
                                Spacer(modifier = Modifier.size(paddingLargePlus))
                            }
                        }
                    })
                Row(
                    Modifier
                        .fillMaxWidth()
                        .graphicsLayer {
                            translationY = padding12.toPx()
                        }, horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    if (allies.any { it.selectedToBattle }) {
                        GameButton(text = stringResource(R.string.one_click_unselect_battle)) {
                            BattleManager.oneShotUnSelect()
                        }
                    } else {
                        GameButton(text = stringResource(R.string.one_click_select_battle)) {
                            BattleManager.oneShotSelect(pair.filter)
                        }
                    }
                    GameButton(
                        text = stringResource(id = R.string.start_battle),
                        buttonStyle = ButtonStyle.Orange
                    ) {
                        if (pair.start()) show.value = null
                    }
                }
            }
            FilterLayout(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(top = gapSmallPlus, end = paddingMedium),
                show = showFilter,
                filter = filter,
                filterList = allyFilterList
            )
            if (showGuide1) {
                Column(
                    Modifier
                        .requiredSize(
                            screenWidthInPixel.pixelToDp(),
                            detailBigHeight
                        ) //使用 requiredSize 可以超过父元素大小
                        .background(B35)
                        .clickable {
                            BattleManager.oneShotSelect(pair.filter)
                        }.padding(horizontal = padding36),
                ) {
                    Spacer(modifier = Modifier.weight(1f))
                    Column(
                        modifier = Modifier
                            .align(Alignment.Start)
                            .width(padding300),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        GuidePointAndText(
                            text = GameApp.instance.getWrapString(R.string.guide16),
                            handType = HandType.DOWN_HAND,
                            offsetX = -padding60
                        )
                    }
                    Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceEvenly) {
                        GameButton(text = stringResource(R.string.one_click_select_battle)) {
                            BattleManager.oneShotSelect(pair.filter)
                        }
                        GameButton(
                            modifier = Modifier.alpha(0f),
                            text = stringResource(id = R.string.start_battle),
                            buttonStyle = ButtonStyle.Orange
                        ) {
                            // 引导故意不响应不显示
                        }
                    }
                    Spacer(modifier = Modifier.size(padding60))
                }
            }
            if (showGuide2) {
                Column(
                    Modifier
                        .requiredSize(
                            screenWidthInPixel.pixelToDp(),
                            detailBigHeight
                        ) //使用 requiredSize 可以超过父元素大小
                        .background(B35)
                        .clickable {
                            GuideManager.guideIndex.value = FIRST_GUIDE_END
                            GuideManager.showGuide.value = false
                            GuideManager.battleDone.value = true
                            setBooleanValueByKey(KEY_BATTLE_DONE, true)
                            setIntValueByKey(
                                KEY_GUIDE_INDEX,
                                GuideManager.guideIndex.value
                            )
                            pair.start()
                            show.value = null
                        }.padding(horizontal = padding36),
                ) {
                    Spacer(modifier = Modifier.weight(1f))
                    Column(
                        modifier = Modifier
                            .align(Alignment.End)
                            .width(padding300),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        GuidePointAndText(
                            text = GameApp.instance.getWrapString(R.string.guide17),
                            handType = HandType.DOWN_HAND,
                            offsetX = padding60
                        )
                    }
                    Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceEvenly) {
                        GameButton(
                            modifier = Modifier.alpha(0f),
                            text = stringResource(R.string.one_click_select_battle)
                        ) {
                            // 引导故意不响应不显示
                        }
                        GameButton(
                            text = stringResource(id = R.string.start_battle),
                            buttonStyle = ButtonStyle.Orange
                        ) {
                            GuideManager.guideIndex.value = FIRST_GUIDE_END
                            GuideManager.showGuide.value = false
                            GuideManager.battleDone.value = true
                            setBooleanValueByKey(KEY_BATTLE_DONE, true)
                            setIntValueByKey(
                                KEY_GUIDE_INDEX,
                                GuideManager.guideIndex.value
                            )
                            pair.start()
                            show.value = null
                        }
                    }
                    Spacer(modifier = Modifier.size(padding60))
                }
            }
        }
    }
}


