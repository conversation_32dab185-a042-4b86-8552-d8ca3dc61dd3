package com.moyu.chuanqirensheng.screen.dialog

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.info.InfoLayout
import com.moyu.chuanqirensheng.ui.theme.paddingMedium

@Composable
fun InfoDialog(info: MutableState<Boolean>) {
    if (info.value) {
        CommonDialog(
            title = stringResource(R.string.battle_info),
            onDismissRequest = { info.value = false }) {
            Column(
                modifier = Modifier
                    .fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                InfoLayout(Modifier, repo.battleInfo)
                Spacer(modifier = Modifier.size(paddingMedium))
            }
        }
    }
}