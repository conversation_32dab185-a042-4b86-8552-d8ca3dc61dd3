package com.moyu.chuanqirensheng.screen.dialog

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.equip.SearchView
import com.moyu.chuanqirensheng.screen.filter.CommonFilterView
import com.moyu.chuanqirensheng.screen.filter.FilterLayout
import com.moyu.chuanqirensheng.screen.filter.ItemFilter
import com.moyu.chuanqirensheng.screen.filter.advSkillFilterList
import com.moyu.chuanqirensheng.screen.skill.SingleSkillView
import com.moyu.chuanqirensheng.text.getSkillElementName
import com.moyu.chuanqirensheng.ui.theme.gapSmallPlus
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.core.model.skill.Skill

@Composable
fun AdventureSkillDialog(showDungeon: MutableState<Boolean>) {
    showDungeon.value.takeIf { showDungeon.value }?.let {
        DisposableEffect(Unit) {
            onDispose {
                BattleManager.setAdvSkillUnNew()
            }
        }
        val search = remember {
            mutableStateOf("")
        }
        val showFilter = remember {
            mutableStateOf(false)
        }
        val filter = remember {
            mutableStateListOf<ItemFilter<Skill>>()
        }
        val list = BattleManager.getAdventureSkills().filter {
            if (search.value.isNotEmpty()) {
                it.name.contains(search.value)
            } else true
        }.filter { ally ->
            filter.all { it.filter.invoke(ally) }
        }.sortedBy { it.elementType }
        CommonDialog(
            title = stringResource(R.string.adventure_skill_card),
            onDismissRequest = {
                showDungeon.value = false
            }) {
            Column(
                modifier = Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                if (BattleManager.getAdventureSkills().size > 8) {
                    Row(
                        Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Spacer(modifier = Modifier.size(paddingSmall))
                        SearchView(search)
                        CommonFilterView(
                            Modifier.padding(end = paddingMedium), showFilter
                        )
                    }
                }
                Spacer(modifier = Modifier.size(paddingMedium))
                LazyVerticalGrid(modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
                    columns = GridCells.Fixed(4),
                    content = {
                        items(list.size) { index ->
                            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                                Spacer(modifier = Modifier.size(paddingSmall))
                                val skill = list[index]
                                SingleSkillView(
                                    skill = skill.copy(extraInfo = skill.elementType.getSkillElementName() + "\n" + repo.getAdventureSkillTips(skill)),
                                    showName = true,
                                    showRed = true,
                                    itemSize = ItemSize.LargePlus
                                )
                                Spacer(modifier = Modifier.size(paddingSmall))
                            }
                        }
                    })
            }
            FilterLayout(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(top = gapSmallPlus, end = paddingMedium),
                show = showFilter,
                filter = filter,
                filterList = advSkillFilterList
            )
        }
    }
}


