package com.moyu.chuanqirensheng.screen.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.ui.theme.White80
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.paddingLarge
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingSmallPlus

@Composable
fun GameTitle(
    modifier: Modifier = Modifier,
    title: String,
    selected: Boolean = true,
    red: Boolean,
    textStyle: TextStyle = MaterialTheme.typography.h2
) {
    Box(modifier = modifier, contentAlignment = Alignment.Center) {
        if (selected) {
            Image(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .graphicsLayer {
                        translationY = paddingSmallPlus.toPx()
                    },
                contentScale = ContentScale.FillWidth,
                painter = painterResource(
                    id = R.drawable.common_tab_focus
                ),
                contentDescription = null
            )
        } else {
            Spacer(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .fillMaxWidth()
            )
        }
        Box {
            Text(
                text = title,
                style = textStyle,
                fontWeight = FontWeight.Bold,
                color = if (selected) Color.White else White80,
                maxLines = 1,
                overflow = TextOverflow.Visible,
                softWrap = false,
            )
            if (red) {
                Image(
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .size(imageSmall)
                        .graphicsLayer {
                            translationY = -paddingMedium.toPx()
                            translationX = paddingLarge.toPx()
                            clip = false
                        },
                    painter = painterResource(R.drawable.red_icon),
                    contentDescription = null
                )
            }
        }
    }
}