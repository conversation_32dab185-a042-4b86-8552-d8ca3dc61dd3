package com.moyu.chuanqirensheng.screen.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.datastore.KEY_GUIDE_INDEX
import com.moyu.chuanqirensheng.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.guide.GuideManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.ally.AllyPropView
import com.moyu.chuanqirensheng.screen.ally.AllySkillsView
import com.moyu.chuanqirensheng.screen.ally.AllyStarUpView
import com.moyu.chuanqirensheng.screen.ally.SingleDetailAllyCard
import com.moyu.chuanqirensheng.screen.guide.GuidePointAndText
import com.moyu.chuanqirensheng.screen.skill.EmptyIconView
import com.moyu.chuanqirensheng.ui.theme.B85
import com.moyu.chuanqirensheng.ui.theme.eventCardBigHeight
import com.moyu.chuanqirensheng.ui.theme.gapHugeMinus
import com.moyu.chuanqirensheng.ui.theme.paddingMediumPlus
import com.moyu.chuanqirensheng.ui.theme.paddingSmallPlus
import com.moyu.chuanqirensheng.ui.theme.paddingTiny
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.isBattle

@Composable
fun RoleDetailDialog(show: MutableState<Role?>) {
    show.value?.let { role ->
        CommonDialog(title = "",
            frame = null,
            noPadding = true,
            heightInDp = eventCardBigHeight * 2.10f,
            clickFrame = { show.value = null },
            onDismissRequest = {
                show.value = null
            }) {
            Column(
                Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                val ally = repo.gameCore.getAllyPool().first { it.id == role.getRace().id }
                Box(Modifier.height(eventCardBigHeight), contentAlignment = Alignment.Center) {
                    AllySkillsView(
                        Modifier
                            .fillMaxWidth()
                            .padding(horizontal = paddingTiny),
                        ally = ally,
                        role = role
                    )
                    SingleDetailAllyCard(ally, role = role)
                }
                AllyPropView(ally = ally, role = role)
            }
        }
    }
}