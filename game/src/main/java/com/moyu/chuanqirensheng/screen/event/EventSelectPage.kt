package com.moyu.chuanqirensheng.screen.event

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.event.triggerEvent
import com.moyu.chuanqirensheng.logic.record.SaveManager
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.CardSize
import com.moyu.chuanqirensheng.screen.equip.SearchView
import com.moyu.chuanqirensheng.screen.filter.CommonFilterView
import com.moyu.chuanqirensheng.screen.filter.FilterLayout
import com.moyu.chuanqirensheng.screen.filter.ItemFilter
import com.moyu.chuanqirensheng.screen.filter.eventFilterList
import com.moyu.chuanqirensheng.ui.theme.gapHugePlus
import com.moyu.chuanqirensheng.ui.theme.gapSmallPlus
import com.moyu.chuanqirensheng.ui.theme.paddingHugeLite
import com.moyu.chuanqirensheng.ui.theme.paddingLarge
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.core.model.event.Event
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun EventSelectPage() {
    val events = EventManager.getNextEvents()
    val showFilter = remember {
        mutableStateOf(false)
    }
    val filter = remember {
        mutableStateListOf<ItemFilter<Event>>()
    }
    LaunchedEffect(EventManager.getUsedEvents().size) {
        if (BattleManager.adventureProps.value.age > 0) {
            SaveManager.onSelections(events)
        }
    }
    Box(modifier = Modifier.fillMaxSize()) {
        Column(modifier = Modifier.fillMaxSize()) {
            Column(modifier = Modifier.weight(1f)) {
                if (DebugManager.allEvent) {
                    val search = remember {
                        mutableStateOf("")
                    }
                    val list = events.filter {
                        if (search.value.isNotEmpty()) {
                            it.name.contains(search.value)
                        } else true
                    }.filter { ally ->
                        filter.all { it.filter.invoke(ally) }
                    }
                    Row(
                        Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Spacer(modifier = Modifier.size(paddingSmall))
                        SearchView(search)
                        CommonFilterView(
                            Modifier.padding(end = paddingMedium), showFilter
                        )
                    }
                    LazyVerticalGrid(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(horizontal = paddingLarge),
                        horizontalArrangement = Arrangement.SpaceEvenly,
                        columns = GridCells.Fixed(3),
                        content = {
                            items(list.size) {
                                Column {
                                    SingleEventCard(CardSize.Medium,
                                        event = list[it]
                                    )
                                    Spacer(modifier = Modifier.size(paddingLarge))
                                    if (it == list.size - 1) {
                                        Spacer(modifier = Modifier.size(gapHugePlus))
                                    }
                                }
                            }
                        }
                    )
                } else {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly,
                    ) {
                        events.forEach { event ->
                            SingleEventCard(
                                cardSize = CardSize.Medium, event = event
                            )
                        }
                    }
                    if (!events.any { triggerEvent(it, false) } && events.isNotEmpty()) {
                        // 所有事件都不可执行，或者没有事件
                        Spacer(modifier = Modifier.size(paddingHugeLite))
                        GameButton(
                            modifier = Modifier.align(Alignment.CenterHorizontally),
                            text = stringResource(R.string.ending_life),
                            buttonStyle = ButtonStyle.Red,
                            onClick = {
                                GameApp.globalScope.launch(Dispatchers.Main) {
                                    EventManager.gotoNextEvent(null, false)
                                }
                            })
                    }
                }
            }
        }
        FilterLayout(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(top = gapSmallPlus, end = paddingMedium),
            show = showFilter,
            filter = filter,
            filterList = eventFilterList
        )
    }
}