package com.moyu.chuanqirensheng.screen.life

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.logic.hero.getCardQualityFrame
import com.moyu.chuanqirensheng.logic.tcg.TcgManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.CardSize
import com.moyu.chuanqirensheng.screen.event.CommonCard2
import com.moyu.chuanqirensheng.ui.theme.gapMedium
import com.moyu.chuanqirensheng.ui.theme.gapSmall
import com.moyu.chuanqirensheng.ui.theme.paddingMediumPlus
import com.moyu.chuanqirensheng.ui.theme.paddingSmall

@Composable
fun TcgPage() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .paint(
                painterResource(id = R.drawable.common_page_frame),
                contentScale = ContentScale.FillBounds
            ),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        if (TcgManager.tcgCards.isEmpty()) {
            Spacer(modifier = Modifier.size(gapMedium))
            Text(text = stringResource(R.string.no_tcg_toast), style = MaterialTheme.typography.h2)
            Spacer(modifier = Modifier.weight(1f))
        } else {
            Spacer(modifier = Modifier.size(paddingMediumPlus))
            LazyVerticalGrid(
                modifier = Modifier.weight(1f),
                columns = GridCells.Fixed(3)
            ) {
                items(TcgManager.tcgCards.size) {
                    val item = TcgManager.tcgCards[it]
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier = Modifier.padding(
                            horizontal = paddingSmall,
                            vertical = paddingSmall
                        )
                    ) {
                        CommonCard2(
                            cardSize = CardSize.Small,
                            title = item.name,
                            icon = item.pic,
                            frame = item.quality.getCardQualityFrame()
                        )
                        Spacer(modifier = Modifier.size(paddingMediumPlus))
                    }
                }
            }
        }
        Spacer(modifier = Modifier.size(gapSmall))
        GameButton(
            text = stringResource(R.string.tcg_award),
            textColor = Color.White,
            redIcon = TcgManager.hasRed(),
            buttonSize = ButtonSize.Big,
            buttonStyle = ButtonStyle.Orange,
        ) {
            Dialogs.tcgAwardDetail.value = true
        }
        Spacer(modifier = Modifier.size(gapSmall))
    }
}