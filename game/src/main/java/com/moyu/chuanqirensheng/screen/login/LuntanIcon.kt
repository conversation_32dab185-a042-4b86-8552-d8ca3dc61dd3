package com.moyu.chuanqirensheng.screen.login

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.datastore.KEY_GAME_TASK_PROGRESS
import com.moyu.chuanqirensheng.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.logic.task.FOREVER
import com.moyu.chuanqirensheng.logic.task.TaskEvent
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.ui.theme.paddingMediumPlus
import com.moyu.chuanqirensheng.util.LUNTAN_SCREEN
import com.moyu.chuanqirensheng.util.goto

@Composable
fun LuntanIcon(modifier: Modifier) {
    if (GameApp.instance.resources.getBoolean(
            R.bool.luntan_activity
        ) && getIntFlowByKey(FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.AGE.id + "_1") >= 100
    ) {
        Column(horizontalAlignment = Alignment.CenterHorizontally, modifier = modifier) {
            EffectButton(modifier = Modifier.graphicsLayer {
                translationY = paddingMediumPlus.toPx()
            }, onClick = {
                goto(LUNTAN_SCREEN)
            }) {
                Image(
                    modifier = Modifier
                        .height(ItemSize.Huge.itemSize),
                    painter = painterResource(id = R.drawable.taptap_icon),
                    contentScale = ContentScale.FillHeight,
                    contentDescription = null
                )
            }
            Text(text = stringResource(R.string.luntan_page), style = MaterialTheme.typography.h4)
        }
    }
}
