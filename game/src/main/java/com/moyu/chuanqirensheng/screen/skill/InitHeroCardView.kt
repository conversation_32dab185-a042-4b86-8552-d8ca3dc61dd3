package com.moyu.chuanqirensheng.screen.skill

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.unlock.UnlockManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.ally.MAX_HERO_SIZE
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.effect.GifView
import com.moyu.chuanqirensheng.screen.equip.SingleHeroView
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.paddingTiny
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch


@Composable
fun InitHeroCardView(modifier: Modifier) {
    FlowRow(
        modifier = modifier,
        horizontalArrangement = Arrangement.SpaceEvenly,
        overflow = FlowRowOverflow.Visible,
        maxItemsInEachRow = 5
    ) {
        val skills = BattleManager.getGameHeroes()
        var skillCount = 0
        repeat(MAX_HERO_SIZE) { index ->
            val unlock = UnlockManager.getInitHeroUnlockByIndex(index)
            val unlocked = UnlockManager.getUnlockedFlow(unlock)
            Box(modifier = Modifier.padding(vertical = paddingTiny)) {
                val gif = remember {
                    mutableStateOf(false)
                }
                skills.getOrNull(skillCount)?.takeIf { unlocked }?.let { skill ->
                    skillCount += 1
                    SingleHeroView(
                        hero = skill,
                        showName = false,
                        itemSize = ItemSize.LargePlus
                    ) {
                        GameApp.globalScope.launch(Dispatchers.Main) {
                            BattleManager.selectHeroToGame(it)
                        }
                    }
                    GifView(modifier = Modifier.size(ItemSize.LargePlus.frameSize), gif.value, 8, "equipon_")
                } ?: run {
                    gif.value = true
                    Box(contentAlignment = Alignment.Center) {
                        EmptyIconView(itemSize = ItemSize.LargePlus) {
                            if (unlocked) {
                                Dialogs.selectHeroToGameDialog.value = true
                            } else {
                                unlock.desc.toast()
                            }
                        }
                        if (!unlocked) {
                            Image(
                                modifier = Modifier.size(imageLarge),
                                painter = painterResource(R.drawable.common_lock),
                                contentDescription = stringResource(id = R.string.locked),
                            )
                        }
                    }
                }
            }
        }
    }
}