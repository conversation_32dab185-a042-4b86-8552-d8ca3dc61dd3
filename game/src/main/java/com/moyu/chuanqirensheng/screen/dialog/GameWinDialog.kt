package com.moyu.chuanqirensheng.screen.dialog

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.feature.mode.isTower
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.feature.pvp.PvpManager.currentTarget
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.model.record.AllRankData
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.Dialogs.statisticView
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.paddingLarge
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.util.PVP_CHOOSE_ENEMY_SCREEN
import com.moyu.chuanqirensheng.util.TOWER_SCREEN
import com.moyu.chuanqirensheng.util.goto
import com.moyu.core.model.role.Role

@Composable
fun GameWinDialog(result: MutableState<List<Role>>) {
    result.value.takeIf { it.isNotEmpty() }?.let { roles ->
        LaunchedEffect(Unit) {
            Dialogs.infoDialog.value = false
        }
        DisposableEffect(Unit) {
            onDispose {
                if (!repo.gameMode.value.isPvp() && !repo.gameMode.value.isPvp()) {
                    EventManager.doEventBattleResult(EventManager.selectedEvent.value, true)
                }
            }
        }
        CommonDialog(title = stringResource(R.string.battle_win), onDismissRequest = {
            result.value = emptyList()
            if (repo.gameMode.value.isPvp()) {
                currentTarget.value = AllRankData(System.currentTimeMillis())
                goto(PVP_CHOOSE_ENEMY_SCREEN)
            } else if (repo.gameMode.value.getGameMode().isTower()) {
                goto(TOWER_SCREEN)
            }
        }) {
            Column(
                Modifier
                    .fillMaxSize()
                    .padding(horizontal = paddingMedium)) {
                Spacer(modifier = Modifier.size(paddingMedium))
                if (repo.gameMode.value.isPvp()) {
                    Text(
                        text = stringResource(R.string.win_over, PvpManager.currentTarget.value.userName),
                        style = MaterialTheme.typography.h3,
                        color = Color.Black
                    )
                    Spacer(modifier = Modifier.size(padding4))
                } else {
                    roles.filter { !it.isPlayerSide() }.forEach {
                        Text(
                            text = stringResource(R.string.win_over, it.getRace().name),
                            style = MaterialTheme.typography.h3,
                            color = Color.Black
                        )
                        Spacer(modifier = Modifier.size(paddingSmall))
                    }
                    Spacer(modifier = Modifier.size(paddingMedium))
                    roles.filter { it.isPlayerSide() }.filter { it.isDeath() }.forEach {
                        Text(
                            text = stringResource(id = R.string.die_in_battle, it.getRace().name),
                            style = MaterialTheme.typography.h3,
                            color = Color.Black
                        )
                        Spacer(modifier = Modifier.size(paddingSmall))
                    }
                }
                Spacer(modifier = Modifier.weight(1f))
                Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceEvenly) {
                    GameButton(text = stringResource(R.string.statistic), buttonStyle = ButtonStyle.Blue){
                        statisticView.value = true
                    }
                    GameButton(text = stringResource(id = R.string.battle_report), buttonStyle = ButtonStyle.Orange) {
                        Dialogs.infoDialog.value = true
                    }
                }
                Spacer(modifier = Modifier.size(paddingLarge))
            }
        }
    }
}