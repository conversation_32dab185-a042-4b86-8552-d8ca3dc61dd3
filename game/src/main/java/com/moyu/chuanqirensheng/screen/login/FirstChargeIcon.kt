package com.moyu.chuanqirensheng.screen.login

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp.Companion.instance
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.ui.theme.imageTinyPlus
import com.moyu.chuanqirensheng.ui.theme.paddingMediumPlus
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.millisToHoursMinutesSeconds
import com.moyu.core.model.sell.Gift
import kotlinx.coroutines.delay

@Composable
fun FirstChargeIcon(modifier: Modifier = Modifier) {
    val refreshIndex = remember {
        mutableIntStateOf(0)
    }
    // todo 为了在计时结束触发刷新
    if (isNetTimeValid() && instance.canShowAifadian()) {
        // 礼包刷新条件：时间到了；买东西了；看广告了，以及默认的打开首页
        val gifts = remember {
            mutableStateOf(emptyList<Gift>())
        }
        val showGift = remember(gifts.value) {
            derivedStateOf {
                gifts.value.firstOrNull { !it.dialogShowed }
            }
        }
        LaunchedEffect(refreshIndex.intValue, AwardManager.electric.value, AwardManager.adNum.value) {
            gifts.value = GiftManager.getDisplayGifts()
        }
        gifts.value.forEach {
            Column(horizontalAlignment = Alignment.CenterHorizontally, modifier = modifier) {
                EffectButton(modifier = Modifier.graphicsLayer {
                    translationY = paddingMediumPlus.toPx()
                }, onClick = {
                    Dialogs.giftDetailDialog.value = it
                }) {
                    Image(
                        modifier = Modifier
                            .height(ItemSize.LargePlus.itemSize),
                        painter = painterResource(id = getImageResourceDrawable(it.icon)),
                        contentScale = ContentScale.FillHeight,
                        contentDescription = null
                    )
                }
                Spacer(modifier = Modifier.size(paddingSmall))
                Text(
                    modifier = Modifier.width(ItemSize.LargePlus.frameSize),
                    text = it.name,
                    maxLines = 2,
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.h5
                )
                if (it.limitTime != 0 && it.displayTime != 0L && isNetTimeValid()) {
                    UpdateTimeText(it) {
                        refreshIndex.intValue += 1
                    }
                }
            }
        }
        showGift.value?.let {
            Dialogs.giftDetailDialog.value = it
        }
    }
}


@Composable
fun UpdateTimeText(gift: Gift, callback: () -> Unit) {
    val currentTime = remember {
        mutableLongStateOf(0L)
    }
    LaunchedEffect(Unit) {
        if (isNetTimeValid()) {
            while (true) {
                currentTime.longValue = getCurrentTime()
                if (currentTime.longValue >= gift.displayTime + gift.limitTime * 60 * 1000L) {
                    callback()
                    break
                }
                delay(500)
            }
        }
    }
    if (currentTime.longValue < gift.displayTime + gift.limitTime * 60 * 1000L) {
        Text(
            text = ((gift.displayTime + gift.limitTime * 60 * 1000L) - currentTime.longValue).millisToHoursMinutesSeconds(),
            style = MaterialTheme.typography.h6,
        )
    }
}
