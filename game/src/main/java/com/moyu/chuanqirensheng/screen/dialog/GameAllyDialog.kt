package com.moyu.chuanqirensheng.screen.dialog

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.datastore.KEY_GUIDE_INDEX
import com.moyu.chuanqirensheng.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.guide.FIRST_GUIDE_END
import com.moyu.chuanqirensheng.logic.guide.GuideManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.screen.ally.SingleAllyView
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.equip.SearchView
import com.moyu.chuanqirensheng.screen.filter.CommonFilterView
import com.moyu.chuanqirensheng.screen.filter.CommonOrderView
import com.moyu.chuanqirensheng.screen.filter.FilterLayout
import com.moyu.chuanqirensheng.screen.filter.ItemFilter
import com.moyu.chuanqirensheng.screen.filter.OrderLayout
import com.moyu.chuanqirensheng.screen.filter.allyFilterList
import com.moyu.chuanqirensheng.screen.filter.allyOrderList
import com.moyu.chuanqirensheng.screen.guide.GuidePointAndText
import com.moyu.chuanqirensheng.screen.guide.HandType
import com.moyu.chuanqirensheng.ui.theme.B65
import com.moyu.chuanqirensheng.ui.theme.detailBigHeight
import com.moyu.chuanqirensheng.ui.theme.gapSmallPlus
import com.moyu.chuanqirensheng.ui.theme.imageMedium
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.util.composeDp
import com.moyu.chuanqirensheng.util.pixelToDp
import com.moyu.chuanqirensheng.util.screenWidthInPixel
import com.moyu.core.model.ally.Ally

@Composable
fun GameAllyDialog(showDungeon: MutableState<Boolean>) {
    showDungeon.value.takeIf { showDungeon.value }?.let {
        DisposableEffect(Unit) {
            onDispose {
                BattleManager.setAllyUnNew()
            }
        }
        val search = remember {
            mutableStateOf("")
        }
        val showFilter = remember {
            mutableStateOf(false)
        }
        val filter = remember {
            mutableStateListOf<ItemFilter<Ally>>()
        }
        val showOrder = remember {
            mutableStateOf(false)
        }
        val order = remember {
            mutableStateOf(allyOrderList.first())
        }
        val list = BattleManager.getGameAllies().filter {
            if (search.value.isNotEmpty()) {
                it.name.contains(search.value)
            } else true
        }.filter { ally ->
            filter.all { it.filter.invoke(ally) }
        }.sortedByDescending { order.value.order?.invoke(it) }.let {
            it.sortedByDescending { it.selectedToBattle }
        }
        CommonDialog(title = stringResource(id = R.string.ally_card), onDismissRequest = {
            if (GuideManager.guideIndex.value == 10) {
                showDungeon.value = false
                Dialogs.allyDetailDialog.value = list.firstOrNull()
                GuideManager.toNextGuide()
            } else {
                showDungeon.value = false
            }
        }) {
            Column(
                modifier = Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                if (BattleManager.getGameAllies().size > 8) {
                    Row(
                        Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        EffectButton(
                            modifier = Modifier.padding(start = paddingMedium), onClick = {
                                Dialogs.alertDialog.value = CommonAlert(
                                    title = GameApp.instance.getWrapString(R.string.unequip_all),
                                    content = GameApp.instance.getWrapString(R.string.unequip_all_tips),
                                    onConfirm = {
                                        BattleManager.skillGameData.filter { it.equipAllyUuid.isNotEmpty() }.forEach {
                                            BattleManager.unEquipSkillToAlly(it)
                                        }
                                    }
                                )
                            }) {
                            Image(
                                modifier = Modifier.size(imageMedium),
                                painter = painterResource(id = R.drawable.icon_unequip),
                                contentDescription = null
                            )
                        }
                        CommonOrderView(
                            Modifier, showOrder
                        )
                        SearchView(search)
                        CommonFilterView(
                            Modifier.padding(end = paddingMedium), showFilter
                        )
                    }
                }
                Spacer(modifier = Modifier.size(paddingMedium))
                LazyVerticalGrid(modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
                    columns = GridCells.Fixed(4),
                    content = {
                        items(list.size) { index ->
                            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                                Spacer(modifier = Modifier.size(paddingSmall))
                                val ally = list[index]
                                SingleAllyView(
                                    ally = ally,
                                    showName = true,
                                    showHp = true,
                                    showRed = true,
                                    itemSize = ItemSize.LargePlus,
                                    extraInfo = if (ally.selectedToBattle) stringResource(R.string.in_battle)
                                    else if (ally.isDead()) stringResource(R.string.died)
                                    else if (ally.isHurt()) stringResource(R.string.hurt) else ""
                                )
                                Spacer(modifier = Modifier.size(paddingSmall))
                            }
                        }
                    })
            }
            if (GuideManager.guideIndex.value == 10) {
                if (list.firstOrNull() == null) {
                    // 异常情况，直接结束前期引导
                    GuideManager.guideIndex.value = FIRST_GUIDE_END
                    setIntValueByKey(KEY_GUIDE_INDEX, GuideManager.guideIndex.value)
                } else {
                    Column(
                        Modifier
                            .requiredSize(
                                screenWidthInPixel.pixelToDp(),
                                detailBigHeight
                            ) //使用 requiredSize 可以超过父元素大小
                            .background(B65)
                            .clickable {
                                showDungeon.value = false
                                Dialogs.allyDetailDialog.value = list.firstOrNull()
                                GuideManager.toNextGuide()
                            },
                    ) {
                        Column(
                            Modifier
                                .fillMaxWidth()
                                .padding(start = 44.composeDp(), top = 88.composeDp())
                        ) {
                            Box {
                                val ally = list.first()
                                SingleAllyView(
                                    ally = ally,
                                    showName = true,
                                    showHp = true,
                                    textColor = Color.White,
                                    itemSize = ItemSize.LargePlus,
                                    extraInfo = if (ally.selectedToBattle) stringResource(R.string.in_battle)
                                    else if (ally.isDead()) stringResource(R.string.died)
                                    else if (ally.isHurt()) stringResource(R.string.hurt) else ""
                                ) {
                                    showDungeon.value = false
                                    Dialogs.allyDetailDialog.value = it
                                    GuideManager.toNextGuide()
                                }
                            }
                            GuidePointAndText(
                                text = stringResource(R.string.guide11),
                                handType = HandType.UP_HAND,
                            )
                        }
                    }
                }
            }
            OrderLayout(
                modifier = Modifier
                    .align(Alignment.TopStart)
                    .padding(top = gapSmallPlus, end = paddingMedium),
                show = showOrder,
                filter = order,
                filterList = allyOrderList
            )
            FilterLayout(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(top = gapSmallPlus, end = paddingMedium),
                show = showFilter,
                filter = filter,
                filterList = allyFilterList
            )
        }
    }
}