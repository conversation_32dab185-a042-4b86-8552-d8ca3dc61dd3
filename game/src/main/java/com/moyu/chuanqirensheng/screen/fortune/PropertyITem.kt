package com.moyu.chuanqirensheng.screen.fortune

import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.SizeTransform
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextOverflow
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.common.addAnimationVertical
import com.moyu.chuanqirensheng.ui.theme.*
import com.moyu.core.util.percentValueToDotWithNoDigits
import com.moyu.core.util.percentValueToDotWithOneDigits
import kotlin.math.roundToInt


@Composable
fun PropertyItem(
    icon: Int,
    getProperty: () -> Double,
    isBoost: () -> Double = { 0.0 },
    getTips: () -> String,
    name: String,
    showPercent: Boolean = false,
    showBoost: Boolean = false,
    frameDrawable: Int = R.drawable.card_attribute_frame,
    textStyle: TextStyle = MaterialTheme.typography.h5,
    showName: Boolean = true,
) {
    Box(Modifier
        .padding(vertical = paddingTiny)
        .size(propertyBigWidth, propertyBigHeight).clickable { getTips().toast() }) {
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(id = frameDrawable),
            contentDescription = null
        )
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = paddingTiny),
            verticalAlignment = Alignment.CenterVertically
        ) {
            val showNum = if (showBoost && isBoost() > 0) isBoost() else getProperty()
            val text = if (showPercent) showNum.percentValueToDotWithNoDigits() else "${showNum.roundToInt()}"
            val showText = if (showBoost && isBoost() > 0) "+$text" else text
            Image(
                modifier = Modifier.size(propertyBigImageSize),
                painter = painterResource(icon),
                contentDescription = null
            )
            Spacer(modifier = Modifier.size(paddingSmallPlus))
            val boostColor = if (showBoost) {
                if (isBoost() == 0.0) Color.White else if (isBoost() > 0) DarkGreen else DARK_RED
            } else {
                Color.White
            }
            val realName = if (showName) name else ""
            Row {
                AnimatedContent(modifier = Modifier.weight(1f), targetState = showText,
                    transitionSpec = {
                        addAnimationVertical(duration = 600).using(
                            SizeTransform(clip = true)
                        )
                    }, label = ""
                ) { target ->
                    Text(
                        text = "$realName $target",
                        style = textStyle,
                        color = boostColor,
                        maxLines = 1,
                        softWrap = false,
                        overflow = TextOverflow.Visible,
                    )
                }
            }
        }
    }
}