package com.moyu.chuanqirensheng.screen.dialog

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.equip.SingleHeroView
import com.moyu.chuanqirensheng.ui.theme.paddingLargePlus
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.core.model.skill.Skill

@Composable
fun SelectOneHeroDialog(showDungeon: MutableState<Pair<(Skill) -> Boolean, (Skill) -> Unit>?>) {
    showDungeon.value?.let { pair ->
        val filter = pair.first
        val callback = pair.second
        CommonDialog(
            title = stringResource(R.string.select_hero),
            onDismissRequest = {
                showDungeon.value = null
            }) {
            LazyVerticalGrid(modifier = Modifier.fillMaxSize(),
                columns = GridCells.Fixed(4),
                content = {
                    val list = BattleManager.getGameHeroes().filter(filter)
                    items(list.size) { index ->
                        val ally = list[index]
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            SingleHeroView(hero = ally, itemSize = ItemSize.LargePlus) {
                                callback(it)
                                showDungeon.value = null
                            }
                            Spacer(modifier = Modifier.size(paddingSmall))
                            GameButton(text = stringResource(id = R.string.do_select), buttonStyle = ButtonStyle.Red, buttonSize = ButtonSize.Small, onClick = {
                                callback(ally)
                                showDungeon.value = null
                            })
                            Spacer(modifier = Modifier.size(paddingLargePlus))
                        }
                    }
                })
        }
    }
}


