package com.moyu.chuanqirensheng.screen.fortune

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.ally.AllyPage
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.common.NavigationTab
import com.moyu.chuanqirensheng.screen.equip.HeroPage
import com.moyu.chuanqirensheng.screen.skill.SkillPage


@Composable
fun FortuneScreen() {
    val listTabItems = listOf( stringResource(R.string.ally_card), stringResource(
            R.string.skill_card), stringResource(R.string.equip_card)
                )
    val pagerState = rememberPagerState {
        listTabItems.size
    }
    GameBackground(title = stringResource(R.string.my_fortune)) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            NavigationTab(
                pagerState, listTabItems, listOf(
                    repo.allyManager.haveNew(),
                    repo.skillManager.haveNew(),
                    repo.heroManager.haveNew()
                )
            )
            HorizontalPager(
                state = pagerState,
            ) { page ->
                when (page) {
                    0 -> AllyPage()
                    1 -> SkillPage()
                    else -> HeroPage()
                }
            }
        }
    }
}