package com.moyu.chuanqirensheng.screen.dialog

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.cloud.MAX_AGE
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.award.VipManager
import com.moyu.chuanqirensheng.logic.award.toAward
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.event.isStartingEvent2
import com.moyu.chuanqirensheng.logic.event.isStartingEvent3
import com.moyu.chuanqirensheng.logic.record.RecordManager
import com.moyu.chuanqirensheng.logic.record.SaveManager
import com.moyu.chuanqirensheng.logic.story.Ending
import com.moyu.chuanqirensheng.music.MusicManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.ui.theme.paddingHuge
import com.moyu.chuanqirensheng.ui.theme.paddingHugePlus
import com.moyu.chuanqirensheng.ui.theme.paddingLarge
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.core.music.SoundEffect
import kotlinx.coroutines.launch

@Composable
fun EndingDialog(show: MutableState<Ending?>) {
    show.value?.let { ending ->
        val award = remember {
            ending.toAward().let {
                if (VipManager.isDoubleEndingAward()) {
                    it.copy(
                        key = it.key * 2,
                        diamond = it.diamond * 2,
                        extraKey = it.key,
                        extraDiamond = it.diamond
                    )
                } else it
            }
        }
        LaunchedEffect(Unit) {
            SaveManager.clearSave()
            repo.gameCore.onBattleEffect(SoundEffect.GameOver)
            MusicManager.stopAll()
        }
        CommonDialog(
            title = if (ending.age >= MAX_AGE) stringResource(id = R.string.all_win) else stringResource(R.string.die_good),
            onDismissRequest = {
                if (show.value != null) {
                    show.value = null
                    repo.onGameOver(ending)
                    GameApp.globalScope.launch {
                        AwardManager.gainAward(award)
                    }
                }
            }) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = paddingHuge)
                    .padding(top = paddingMedium)
                    .verticalScroll(rememberScrollState()),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    modifier = Modifier.fillMaxWidth(),
                    text = BattleManager.countryName.value + "，" + stringResource(R.string.lasted) + BattleManager.adventureProps.value.age + stringResource(
                        R.string.age
                    ) + "，" + stringResource(R.string.score_rank) + ending.rank + "%" + stringResource(
                        R.string.score_rank2
                    ),
                    style = MaterialTheme.typography.h3,
                    color = Color.Black
                )
                Spacer(modifier = Modifier.size(paddingMedium))
                Text(
                    modifier = Modifier.fillMaxWidth(),
                    text = stringResource(R.string.king) + GameApp.instance.getUserName(),
                    style = MaterialTheme.typography.h3,
                    color = Color.Black
                )
                Text(
                    modifier = Modifier.fillMaxWidth(),
                    text = stringResource(R.string.country_location) + EventManager.getSucceededEvents()
                        .first { it.isStartingEvent2() }.name,
                    style = MaterialTheme.typography.h3,
                    color = Color.Black
                )
                Text(
                    modifier = Modifier.fillMaxWidth(),
                    text = stringResource(R.string.country_religion) + EventManager.getSucceededEvents()
                        .first { it.isStartingEvent3() }.name,
                    style = MaterialTheme.typography.h3,
                    color = Color.Black
                )
                Spacer(modifier = Modifier.size(paddingMedium))
                Row(modifier = Modifier.fillMaxWidth()) {
                    Text(
                        modifier = Modifier.weight(1f),
                        text = stringResource(R.string.country_kill) + RecordManager.recordData.defeatEnemyRecord.size,
                        style = MaterialTheme.typography.h3,
                        color = Color.Black
                    )
                    Text(
                        modifier = Modifier.weight(1f),
                        text = stringResource(R.string.country_tech) + RecordManager.recordData.getAdventureCardRecord.filter { it == 1 }.size,
                        style = MaterialTheme.typography.h3,
                        color = Color.Black
                    )
                }
                Spacer(modifier = Modifier.size(paddingSmall))
                Row(modifier = Modifier.fillMaxWidth()) {
                    Text(
                        modifier = Modifier.weight(1f),
                        text = stringResource(R.string.country_policy) + RecordManager.recordData.getAdventureCardRecord.filter { it == 2 }.size,
                        style = MaterialTheme.typography.h3,
                        color = Color.Black
                    )
                    Text(
                        modifier = Modifier.weight(1f),
                        text = stringResource(R.string.country_build) + RecordManager.recordData.getAdventureCardRecord.filter { it == 4 }.size,
                        style = MaterialTheme.typography.h3,
                        color = Color.Black
                    )
                }
                Spacer(modifier = Modifier.size(paddingSmall))
                Row(modifier = Modifier.fillMaxWidth()) {
                    Text(
                        modifier = Modifier.weight(1f),
                        text = stringResource(R.string.country_get_religion) + RecordManager.recordData.getAdventureCardRecord.filter { it == 3 }.size,
                        style = MaterialTheme.typography.h3,
                        color = Color.Black
                    )
                }
                Spacer(modifier = Modifier.size(paddingLarge))
                Text(
                    modifier = Modifier.fillMaxWidth(),
                    text = stringResource(R.string.next_turn_award),
                    style = MaterialTheme.typography.h3,
                    color = Color.Black
                )
                Spacer(modifier = Modifier.size(paddingMedium))
                if (award.isEmpty()) {
                    Text(
                        modifier = Modifier.fillMaxWidth(),
                        text = stringResource(id = R.string.none),
                        style = MaterialTheme.typography.h3,
                        color = Color.Black
                    )
                } else {
                    AwardList(modifier = Modifier.fillMaxWidth(), award = award)
                }
                Spacer(modifier = Modifier.size(paddingHugePlus))
                Spacer(modifier = Modifier.weight(1f))
                Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceEvenly) {
                    GameButton(
                        text = stringResource(R.string.left),
                        buttonStyle = ButtonStyle.Blue
                    ) {
                        if (show.value != null) {
                            show.value = null
                            repo.onGameOver(ending)
                            GameApp.globalScope.launch {
                                AwardManager.gainAward(award)
                            }
                        }
                    }
                    GameButton(
                        text = stringResource(R.string.life_record),
                        buttonStyle = ButtonStyle.Orange
                    ) {
                        if (show.value != null) {
                            show.value = null
                            repo.onGameOver(ending)
                            Dialogs.storyDialog.value = ending
                            GameApp.globalScope.launch {
                                AwardManager.gainAward(award)
                            }
                        }
                    }
                }
            }
        }
    }
}