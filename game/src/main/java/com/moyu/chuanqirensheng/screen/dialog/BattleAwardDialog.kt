@file:OptIn(ExperimentalLayoutApi::class)

package com.moyu.chuanqirensheng.screen.dialog

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.core.model.sell.Award

@Composable
fun BattleAwardDialog(switch: MutableState<Award?>) {
    switch.value?.let { role ->
        CommonDialog(title = stringResource(R.string.battle_awards),
            onDismissRequest = {
                switch.value = null
            }) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = paddingSmall)
                    .verticalScroll(rememberScrollState())
            ) {
                FlowRow(
                    Modifier.padding(paddingMedium),
                    horizontalArrangement = Arrangement.Start,
                    overflow = FlowRowOverflow.Visible,
                ) {
                    AwardList(award = role)
                }
            }
        }
    }
}