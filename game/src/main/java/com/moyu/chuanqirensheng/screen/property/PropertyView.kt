@file:OptIn(ExperimentalLayoutApi::class)

package com.moyu.chuanqirensheng.screen.property

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.screen.equip.RolePropertyLine
import com.moyu.core.model.property.AdventureProps


@Composable
fun RolePropertyViewInGame(modifier: Modifier = Modifier, property: AdventureProps) {
    Column(modifier = modifier) {
        FlowRow(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.Start,
            overflow = FlowRowOverflow.Visible,
            maxItemsInEachRow = 2,
        ) {
            property
                .RolePropertyLine(
                    originProperty = property,
                    countStart = 1,
                    countEnd = 5
                )
        }
        FlowRow(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.Start,
            overflow = FlowRowOverflow.Visible,
            maxItemsInEachRow = 3
        ) {
            property
                .RolePropertyLine(
                    originProperty = property,
                    countStart = 5,
                )
        }
    }
}