package com.moyu.chuanqirensheng.screen.dialog

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.more.MoreItem
import com.moyu.chuanqirensheng.screen.more.OneItem
import com.moyu.chuanqirensheng.ui.theme.challengeDialogHeight
import com.moyu.chuanqirensheng.ui.theme.paddingLarge
import com.moyu.chuanqirensheng.ui.theme.paddingSmallPlus
import com.moyu.chuanqirensheng.util.CHALLENGE_RANK_SCREEN
import com.moyu.chuanqirensheng.util.goto
import com.moyu.chuanqirensheng.util.gotoChallengeTask
import com.moyu.chuanqirensheng.util.gotoSell
import com.moyu.chuanqirensheng.util.gotoStory
import com.moyu.core.GameCore
import com.moyu.core.music.SoundEffect
import kotlinx.coroutines.launch


val items = listOf(
    MoreItem(
        GameApp.instance.getWrapString(R.string.challenge_rank),
        { goto(CHALLENGE_RANK_SCREEN) },
        "more_icon_challenge"
    ),
    MoreItem(
        GameApp.instance.getWrapString(R.string.challenge_task),
        { gotoChallengeTask() },
        "challenge_task_icon"
    ),
    MoreItem(
        GameApp.instance.getWrapString(R.string.challenge_shop),
        { gotoSell() },
        "challenge_shop_icon"
    ),
)

@Composable
fun ChallengeGameDialog(show: MutableState<Boolean>) {
    if (show.value) {
        CommonDialog(
            title = stringResource(R.string.daily_challenge),
            heightInDp = challengeDialogHeight,
            onDismissRequest = { show.value = false }) {
            Column(
                Modifier
                    .fillMaxSize()
                    .padding(horizontal = paddingLarge)
                    .verticalScroll(rememberScrollState()),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Spacer(modifier = Modifier.size(paddingSmallPlus))
                Column(Modifier.fillMaxWidth()) {
                    Text(text = stringResource(R.string.today_challenge), style = MaterialTheme.typography.h3)
                    Spacer(modifier = Modifier.size(paddingSmallPlus))
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Text(text = stringResource(R.string.challenge_barrier), style = MaterialTheme.typography.h4)
//                        BattleManager.getTodayChallenge().effectAdventure.map {
//                            repo.gameCore.getSkillById(
//                                it
//                            )
//                        }.forEach {
//                            SingleSkillView(skill = it)
//                            Spacer(modifier = Modifier.size(paddingSmall))
//                        }
                    }
//                    Row(verticalAlignment = Alignment.CenterVertically) {
//                        Text(text = stringResource(R.string.enemy_enhance), style = MaterialTheme.typography.h4)
//                        BattleManager.getTodayChallenge().effectBattle.map {
//                            repo.gameCore.getSkillById(
//                                it
//                            )
//                        }.forEach {
//                            SingleSkillView(skill = it)
//                            Spacer(modifier = Modifier.size(paddingSmall))
//                        }
//                    }
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        items.forEach {
                            OneItem(
                                it,
                                ItemSize.Large
                            ) {
                                show.value = false
                            }
                        }

                    }
                    Spacer(modifier = Modifier.size(paddingSmallPlus))
                }
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    GameButton(text = stringResource(R.string.change_story_bag), buttonStyle = ButtonStyle.Orange, onClick = {
                        show.value = false
                        GameApp.globalScope.launch {
                            gotoStory()
                        }
                    })
                    GameButton(text = stringResource(R.string.start_life)) {
                        show.value = false
                        repo.startGame(true)
                        GameCore.instance.onBattleEffect(SoundEffect.StartGame)
                    }
                }
                Spacer(modifier = Modifier.size(paddingSmallPlus))
            }
        }
    }
}