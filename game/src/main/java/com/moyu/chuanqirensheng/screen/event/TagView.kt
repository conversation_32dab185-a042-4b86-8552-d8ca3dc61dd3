package com.moyu.chuanqirensheng.screen.event

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import com.moyu.chuanqirensheng.screen.effect.StrokedText
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.ui.theme.paddingSmallPlus


@Composable
fun TagView(modifier: Modifier = Modifier, text: String) {
    Box(modifier = modifier, contentAlignment = Alignment.Center) {
//        Image(
//            modifier = Modifier.width(tagWidth),
//            painter = painterResource(id = R.drawable.common_label),
//            contentDescription = null
//        )
        StrokedText(
            modifier = Modifier.padding(bottom = paddingSmallPlus, end = paddingSmall),
            text = text,
            maxLines = 1,
            style = if (text.length >= 5) MaterialTheme.typography.h4 else MaterialTheme.typography.h3
        )
    }
}