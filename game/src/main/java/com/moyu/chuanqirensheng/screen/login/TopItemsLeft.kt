package com.moyu.chuanqirensheng.screen.login

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.role.UserImageView
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.ui.theme.userHeadHeight
import com.moyu.chuanqirensheng.ui.theme.userHeadWidth


@Composable
fun TopItemsLeft(modifier: Modifier = Modifier) {
    Column(modifier.padding(start = paddingSmall)) {
        EffectButton(modifier = modifier.size(userHeadWidth, userHeadHeight), onClick = {
            if (!GameApp.instance.hasLogin()) {
                GameApp.instance.login(GameApp.instance.activity)
            }
        }) {
            UserImageView(
                modifier = Modifier.align(Alignment.CenterStart),
                headRes = GameApp.instance.getAvatarUrl(),
                name = GameApp.instance.getUserName()?: stringResource(R.string.not_login)
            )
        }
    }
}