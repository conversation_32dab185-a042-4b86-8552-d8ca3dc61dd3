package com.moyu.chuanqirensheng.screen.dialog

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.life.SingleImageCard
import com.moyu.chuanqirensheng.ui.theme.cardHeight
import com.moyu.chuanqirensheng.ui.theme.cardWidth
import com.moyu.chuanqirensheng.ui.theme.chartHeight
import com.moyu.chuanqirensheng.ui.theme.chartWidth
import com.moyu.chuanqirensheng.ui.theme.paddingHuge
import com.moyu.chuanqirensheng.ui.theme.paddingLarge
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.logic.identifier.Identifier
import com.moyu.core.model.damage.DamageResult
import com.moyu.core.model.heal.HealResult
import com.moyu.core.model.skill.Skill
import me.bytebeats.views.charts.bar.BarChart
import me.bytebeats.views.charts.bar.BarChartData
import me.bytebeats.views.charts.bar.render.label.SimpleLabelDrawer
import me.bytebeats.views.charts.bar.render.xaxis.SimpleXAxisDrawer
import me.bytebeats.views.charts.bar.render.yaxis.SimpleYAxisDrawer
import kotlin.random.Random

@Composable
fun StatisticDialog(show: MutableState<Boolean>) {
    if (show.value) {
        CommonDialog(title = stringResource(R.string.statistics), onDismissRequest = { show.value = false }) {
            Column(
                modifier = Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                GameStatisticView()
            }
        }
    }
}

@Composable
fun GameStatisticView() {
    val totalDamage =
        repo.battle.value.getDamageResultsBySide(Identifier.player()).sumOf { it.damageValue.finalDamage }
    val totalBeingDamage =
        repo.battle.value.getDamageResultsBySide(Identifier.enemy()).sumOf { it.damageValue.finalDamage }
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Top,
        modifier = Modifier.verticalScroll(rememberScrollState())
    ) {
        StatisticCards(true)
        Spacer(modifier = Modifier.height(paddingSmall))
        StatisticCards(false)
        Spacer(modifier = Modifier.height(paddingSmall))
        Row(
            horizontalArrangement = Arrangement.SpaceBetween,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = paddingMedium)
        ) {
            Text(text = stringResource(R.string.total_damage) + totalDamage, style = MaterialTheme.typography.h4, color = Color.Black)
            Text(text = stringResource(R.string.gain_damages) + totalBeingDamage, style = MaterialTheme.typography.h4, color = Color.Black)
        }
        Spacer(modifier = Modifier.height(paddingMedium))
        GameStatisticChart()
    }
}

@Composable
fun StatisticCards(myStatistic: Boolean) {
    val damageData =
        repo.battle.value.getDamageResultsBySide(if (myStatistic) Identifier.player() else Identifier.enemy())
            .mostDamageSkill()
    val healData =
        repo.battle.value.getHealResultsBySide(if (myStatistic) Identifier.player() else Identifier.enemy())
            .healToBarViewData()
    val castData =
        repo.battle.value.getCastSkillsBySide(if (myStatistic) Identifier.player() else Identifier.enemy())
            .castToBarChartData()

    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        SingleStatisticCard(
            if (myStatistic) stringResource(R.string.best_dps_skill) else stringResource(R.string.best_dps_skill_enemy), damageData
        )
        SingleStatisticCard(
            if (myStatistic) stringResource(R.string.best_heal) else stringResource(R.string.best_heal_enemy), healData
        )
        SingleStatisticCard(
            if (myStatistic) stringResource(R.string.best_release) else stringResource(R.string.best_release_enemy), castData
        )
    }
}

@Composable
fun SingleStatisticCard(title: String, data: Pair<Skill, Int>?) {
    Box(contentAlignment = Alignment.Center) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Text(text = title, style = MaterialTheme.typography.h5, color = Color.Black)
            SingleImageCard(
                Modifier.size(cardWidth, cardHeight),
                getImageResourceDrawable(data?.first?.icon ?: "main_frame_1"),
                data?.first?.name ?: ""
            )
            Text(text = data?.second?.toString() ?: stringResource(id = R.string.none), style = MaterialTheme.typography.h6, color = Color.Black, textAlign = TextAlign.Center)
        }
    }
}

private fun List<HealResult>.healToBarViewData(): Pair<Skill, Int>? {
    val healListGrouped =
        this.filter { it.healSkill.id > 0 }.groupBy { it.healSkill.mainId }.values.map {
            Pair(it.first().healSkill, it.sumOf { damage -> damage.healValue })
        }
    return healListGrouped.maxByOrNull { it.second }
}

private fun List<DamageResult>.mostDamageSkill(): Pair<Skill, Int>? {
    val damageListGrouped =
        this.filter { it.damageSkill.id > 0 }.groupBy { it.damageSkill.mainId }.values.map {
            Pair(it.first().damageSkill, it.sumOf { damage -> damage.damageValue.finalDamage })
        }
    return damageListGrouped.maxByOrNull { it.second }
}

private fun List<Skill>.castToBarChartData(): Pair<Skill, Int>? {
    val skillListGrouped = this.filter { it.id > 0 }.groupBy { it.mainId }.values.map {
        Pair(it.first(), it.count())
    }
    return skillListGrouped.maxByOrNull { it.second }
}


@Composable
fun GameStatisticChart() {
    val damageBarData =
        repo.battle.value.getDamageResultsBySide(Identifier.player()).damageToBarChartData()
    val healBarData = repo.battle.value.getHealResultsBySide(Identifier.player()).healToBarChartData()
    val labelDrawer = SimpleLabelDrawer(
        drawLocation = SimpleLabelDrawer.DrawLocation.Inside,
        labelTextSize = MaterialTheme.typography.body1.fontSize,
        labelTextColor = Color.Black,
    )
    val xAxisDrawer = SimpleXAxisDrawer(
        axisLineColor = Color.Black
    )
    val yAxisDrawer = SimpleYAxisDrawer(
        labelTextSize = MaterialTheme.typography.body1.fontSize,
        labelTextColor = Color.Black,
        axisLineColor = Color.Black,
        labelValueFormatter = { value -> "%.0f".format(value) },
    )

    val enemyDamageBarData =
        repo.battle.value.getDamageResultsBySide(Identifier.enemy()).damageToBarChartData()
    val enemyHealBarData = repo.battle.value.getHealResultsBySide(Identifier.enemy()).healToBarChartData()
    Column {
        if (damageBarData.isNotEmpty()) {
            Text(text = stringResource(R.string.ally_damage), style = MaterialTheme.typography.h3, color = Color.Black)
            Spacer(modifier = Modifier.size(paddingLarge))
            BarChart(
                modifier = Modifier.size(chartWidth, chartHeight),
                barChartData = BarChartData(bars = damageBarData),
                labelDrawer = labelDrawer,
                xAxisDrawer = xAxisDrawer,
                yAxisDrawer = yAxisDrawer,
            )
        }
        Spacer(modifier = Modifier.size(paddingHuge))
        if (healBarData.isNotEmpty()) {
            Text(text = stringResource(R.string.ally_heal), style = MaterialTheme.typography.h3, color = Color.Black)
            Spacer(modifier = Modifier.size(paddingLarge))
            BarChart(
                modifier = Modifier.size(chartWidth, chartHeight),
                barChartData = BarChartData(bars = healBarData),
                labelDrawer = labelDrawer,
                xAxisDrawer = xAxisDrawer,
                yAxisDrawer = yAxisDrawer,
            )
        }
        Spacer(modifier = Modifier.size(paddingHuge))

        if (enemyDamageBarData.isNotEmpty()) {
            Text(text = stringResource(R.string.enemy_damage), style = MaterialTheme.typography.h3, color = Color.Black)
            Spacer(modifier = Modifier.size(paddingLarge))
            BarChart(
                modifier = Modifier.size(chartWidth, chartHeight),
                barChartData = BarChartData(bars = enemyDamageBarData),
                labelDrawer = labelDrawer,
                xAxisDrawer = xAxisDrawer,
                yAxisDrawer = yAxisDrawer,
            )
        }
        Spacer(modifier = Modifier.size(paddingHuge))
        if (enemyHealBarData.isNotEmpty()) {
            Text(text = stringResource(R.string.enemy_heal), style = MaterialTheme.typography.h3, color = Color.Black)
            Spacer(modifier = Modifier.size(paddingLarge))
            BarChart(
                modifier = Modifier.size(chartWidth, chartHeight),
                barChartData = BarChartData(bars = enemyHealBarData),
                labelDrawer = labelDrawer,
                xAxisDrawer = xAxisDrawer,
                yAxisDrawer = yAxisDrawer,
            )
        }
        Spacer(modifier = Modifier.size(paddingHuge))
    }
}

private fun List<HealResult>.healToBarChartData(): List<BarChartData.Bar> {
    return this.groupBy { it.healSkill.mainId }.entries.map { entries ->
        BarChartData.Bar(label = entries.value.first().healSkill.name,
            value = entries.value.map { it.healValue }.reduce { acc, i -> acc + i }.toFloat(),
            color = randomColor()
        )
    }
}

private fun List<DamageResult>.damageToBarChartData(): List<BarChartData.Bar> {
    return this.groupBy { it.damageSkill.mainId }.entries.map { entries ->
        BarChartData.Bar(label = entries.value.first().damageSkill.name,
            value = entries.value.map { it.rawDamage }.reduce { acc, i -> acc + i }.toFloat(),
            color = randomColor()
        )
    }
}

private var colors = mutableListOf(
    Color(0XFFF44336),
    Color(0XFFE91E63),
    Color(0XFF9C27B0),
    Color(0XFF673AB7),
    Color(0XFF3F51B5),
    Color(0XFF03A9F4),
    Color(0XFF009688),
    Color(0XFFCDDC39),
    Color(0XFFFFC107),
    Color(0XFFFF5722),
    Color(0XFF795548),
    Color(0XFF9E9E9E),
    Color(0XFF607D8B),
)

fun randomColor(): Color {
    val idx = Random.Default.nextInt(colors.size - 1)
    return colors[idx]
}