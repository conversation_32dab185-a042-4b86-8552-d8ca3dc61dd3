package com.moyu.chuanqirensheng.screen.dialog

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.core.content.ContextCompat
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.ui.theme.RaceNameColor
import com.moyu.chuanqirensheng.ui.theme.alertHeight
import com.moyu.chuanqirensheng.ui.theme.alertWidth
import com.moyu.chuanqirensheng.ui.theme.detailBigHeight
import com.moyu.chuanqirensheng.ui.theme.gameAlertDialogTitleHeight
import com.moyu.chuanqirensheng.ui.theme.padding48
import com.moyu.chuanqirensheng.ui.theme.paddingHuge
import com.moyu.chuanqirensheng.ui.theme.paddingHugePlusPlus
import com.moyu.chuanqirensheng.ui.theme.paddingLarge
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.core.licenseLink
import com.moyu.core.privacyLink


data class CommonAlert(
    val title: String = GameApp.instance.getWrapString(R.string.please_confirm),
    val content: String = "",
    val cancelText: String = GameApp.instance.getWrapString(R.string.cancel),
    val confirmText: String = GameApp.instance.getWrapString(R.string.confirm),
    val onlyConfirm: Boolean = false,
    val heightInDp: Dp = detailBigHeight,
    val frame: Int = R.drawable.common_window,
    val extraContent: @Composable ColumnScope.() -> Unit = {},
    val onConfirm: () -> Unit = {},
    val onCancel: () -> Unit = {},
    val onDismiss: () -> Unit = {}
)

@Composable
fun CommonAlertDialog(switch: MutableState<CommonAlert?>) {
    switch.value?.let { alert ->
        CommonDialog(alert.title, frame = alert.frame, heightInDp = alert.heightInDp, onDismissRequest = {
            alert.onDismiss()
            switch.value = null
        }) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier
                    .padding(horizontal = paddingLarge)
            ) {
                Spacer(modifier = Modifier.size(paddingMedium))
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        modifier = Modifier
                            .verticalScroll(
                                rememberScrollState()
                            ),
                        text = alert.content,
                        style = MaterialTheme.typography.h3,
                        color = Color.Black
                    )
                    alert.extraContent.invoke(this)
                }
                Spacer(modifier = Modifier.size(paddingSmall))
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    if (!alert.onlyConfirm) {
                        GameButton(text = alert.cancelText, buttonStyle = ButtonStyle.Gray) {
                            alert.onCancel()
                            switch.value = null
                        }
                    }
                    GameButton(text = alert.confirmText, buttonStyle = ButtonStyle.Orange) {
                        alert.onConfirm()
                        switch.value = null
                    }
                }
                Spacer(modifier = Modifier.size(paddingLarge))
            }
        }
    }
}

@Composable
fun GameAlertDialog(
    switch: MutableState<Boolean>,
    title: String = "",
    content: String,
    onlyConfirm: Boolean = false,
    confirmText: String = GameApp.instance.getWrapString(R.string.confirm),
    cancelText: String = GameApp.instance.getWrapString(R.string.cancel),
    cancel: () -> Unit = {},
    confirm: () -> Unit = {}
) {
    if (switch.value) {
        Dialog(
            onDismissRequest = { switch.value = false },
            properties = DialogProperties(
                usePlatformDefaultWidth = false
            )
        ) {
            Box(
                modifier = Modifier.size(alertWidth, alertHeight)
            ) {
                Image(
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.FillBounds,
                    painter = painterResource(id = R.drawable.common_window_small),
                    contentDescription = null
                )
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier
                        .padding(horizontal = paddingHuge)
                        .padding(bottom = padding48)
                ) {
                    Box(
                        modifier = Modifier.height(gameAlertDialogTitleHeight),
                        contentAlignment = Alignment.BottomCenter
                    ) {
                        Text(
                            text = title,
                            style = MaterialTheme.typography.h1,
                            color = Color.Black
                        )
                    }
                    Spacer(modifier = Modifier.size(paddingHuge))
                    Text(
                        modifier = Modifier
                            .weight(1f)
                            .padding(top = paddingMedium),
                        text = content,
                        style = MaterialTheme.typography.h3,
                        color = Color.Black
                    )

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        if (!onlyConfirm) {
                            GameButton(text = cancelText, buttonStyle = ButtonStyle.Orange) {
                                cancel()
                                switch.value = false
                            }
                        }
                        GameButton(text = confirmText, buttonStyle = ButtonStyle.Blue) {
                            confirm()
                            switch.value = false
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun PrivacyAlertDialog(
    switch: MutableState<Boolean>,
    quit: () -> Unit,
    confirm: () -> Unit,
) {
    if (switch.value) {
        Dialog(
            onDismissRequest = { switch.value = false },
            properties = DialogProperties(
                usePlatformDefaultWidth = false,
                dismissOnBackPress = false,
                dismissOnClickOutside = false,
            )
        ) {
            Box(
                modifier = Modifier.size(alertWidth, alertHeight)
            ) {
                Image(
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.FillBounds,
                    painter = painterResource(id = R.drawable.common_window_small),
                    contentDescription = null
                )
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier
                        .padding(horizontal = paddingHuge)
                        .padding(bottom = padding48)
                ) {
                    Box(
                        modifier = Modifier.height(gameAlertDialogTitleHeight),
                        contentAlignment = Alignment.BottomCenter
                    ) {
                        Text(
                            text = stringResource(R.string.agreements_content),
                            style = MaterialTheme.typography.h1,
                            color = Color.Black
                        )
                    }
                    Spacer(modifier = Modifier.size(paddingHuge))
                    Text(
                        text = stringResource(R.string.agreements_tips),
                        style = MaterialTheme.typography.h3,
                        color = Color.Black
                    )
                    Spacer(modifier = Modifier.size(paddingSmall))
                    Row(modifier = Modifier.fillMaxWidth()) {
                        Text(
                            modifier = Modifier.clickable {
                                val uri: Uri = Uri.parse(privacyLink)
                                val intent = Intent(Intent.ACTION_VIEW, uri)
                                ContextCompat.startActivity(
                                    GameApp.instance.activity,
                                    intent,
                                    Bundle()
                                )
                            },
                            text = stringResource(R.string.privacy_agreements),
                            style = MaterialTheme.typography.h3,
                            textDecoration = TextDecoration.Underline,
                            color = RaceNameColor
                        )
                        Spacer(modifier = Modifier.size(paddingSmall))
                        Text(
                            modifier = Modifier.clickable {
                                val uri: Uri = Uri.parse(licenseLink)
                                val intent = Intent(Intent.ACTION_VIEW, uri)
                                ContextCompat.startActivity(
                                    GameApp.instance.activity,
                                    intent,
                                    Bundle()
                                )
                            },
                            text = stringResource(R.string.agreements),
                            style = MaterialTheme.typography.h3,
                            textDecoration = TextDecoration.Underline,
                            color = RaceNameColor
                        )
                    }
                    Spacer(modifier = Modifier.weight(1f))
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        GameButton(
                            text = stringResource(R.string.quit),
                            buttonStyle = ButtonStyle.Gray
                        ) {
                            quit()
                            switch.value = false
                        }
                        GameButton(
                            text = stringResource(R.string.agree),
                            buttonStyle = ButtonStyle.Orange
                        ) {
                            confirm()
                            switch.value = false
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun PermissionAlertDialog(
    switch: MutableState<Boolean>,
    confirm: () -> Unit,
) {
    if (switch.value) {
        LaunchedEffect(Unit) {
            // todo 不需要权限弹窗，则直接回调confirm
            if (!GameApp.instance.resources.getBoolean(
                    R.bool.need_privacy_check)) {
                switch.value = false
                confirm()
            }
        }
        Dialog(
            onDismissRequest = { switch.value = false },
            properties = DialogProperties(
                usePlatformDefaultWidth = false,
                dismissOnBackPress = false,
                dismissOnClickOutside = false,
            )
        ) {
            Box(
                modifier = Modifier.size(alertWidth, alertHeight)
            ) {
                Image(
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.FillBounds,
                    painter = painterResource(id = R.drawable.common_window_small),
                    contentDescription = null
                )
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier
                        .padding(horizontal = paddingHuge)
                        .padding(bottom = padding48)
                ) {
                    Box(
                        modifier = Modifier.height(gameAlertDialogTitleHeight),
                        contentAlignment = Alignment.BottomCenter
                    ) {
                        Text(
                            text = stringResource(R.string.permission_content),
                            style = MaterialTheme.typography.h1,
                            color = Color.Black
                        )
                    }
                    Column(
                        horizontalAlignment = Alignment.Start,
                    ) {
                        Spacer(modifier = Modifier.size(paddingHugePlusPlus))
                        if (GameApp.instance.resources.getBoolean(R.bool.has_google_service)) {
                            Text(
                                text = stringResource(R.string.permission_tips_google),
                                style = MaterialTheme.typography.h3,
                                color = Color.Black
                            )
                        } else {
                            Text(
                                text = stringResource(R.string.permission_tips),
                                style = MaterialTheme.typography.h3,
                                color = Color.Black
                            )
                            Spacer(modifier = Modifier.size(paddingMedium))
                            Text(
                                text = stringResource(R.string.associated_boot),
                                style = MaterialTheme.typography.h4,
                                color = RaceNameColor
                            )
                            Spacer(modifier = Modifier.size(paddingSmall))
                            Text(
                                text = stringResource(R.string.read_write_permission),
                                style = MaterialTheme.typography.h4,
                                color = RaceNameColor
                            )
                            Spacer(modifier = Modifier.size(paddingSmall))
                            Text(
                                text = stringResource(R.string.location_permission),
                                style = MaterialTheme.typography.h4,
                                color = RaceNameColor
                            )
                            Spacer(modifier = Modifier.size(paddingSmall))
                            Text(
                                text = stringResource(R.string.app_list_permission),
                                style = MaterialTheme.typography.h4,
                                color = RaceNameColor
                            )
                        }
                    }
                    Spacer(modifier = Modifier.weight(1f))
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        GameButton(
                            text = stringResource(id = R.string.confirm),
                            buttonStyle = ButtonStyle.Orange
                        ) {
                            switch.value = false
                            confirm()
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun GuideDialog(switch: MutableState<String>) {
    if (switch.value.isNotEmpty()) {
        Dialog(
            onDismissRequest = { switch.value = "" },
            properties = DialogProperties(
                dismissOnBackPress = true,
                dismissOnClickOutside = true,
                usePlatformDefaultWidth = false
            )
        ) {
            Box(
                modifier = Modifier
                    .size(alertWidth, alertHeight)
                    .padding(paddingLarge),
                contentAlignment = Alignment.Center
            ) {
            }
        }
    }
}