package com.moyu.chuanqirensheng.screen.antiaddic

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.*
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.cloud.privacy.PrivacyManager
import com.moyu.chuanqirensheng.datastore.KEY_NEED_SHOW_PERMISSION
import com.moyu.chuanqirensheng.datastore.KEY_NEED_SHOW_PRIVACY
import com.moyu.chuanqirensheng.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.ui.theme.*

// todo translate
@Composable
fun AntiAddictDialog(confirm: () -> Unit = {}) {
    val privacyNeedShow = getBooleanFlowByKey(KEY_NEED_SHOW_PRIVACY, true)
    val permissionNeedShow = getBooleanFlowByKey(KEY_NEED_SHOW_PERMISSION, true)
    if (!privacyNeedShow && !permissionNeedShow) {
        if (!GameApp.instance.antiAddictPassed().value && !PrivacyManager.antiAddictVerified) {
            Dialog(
                onDismissRequest = { },
                properties = DialogProperties(
                    dismissOnBackPress = false,
                    dismissOnClickOutside = false,
                    usePlatformDefaultWidth = false
                )
            ) {
                Box(
                    modifier = Modifier
                        .size(alertWidth, alertHeight)
                        .padding(paddingLarge)
                ) {
                    Image(
                        modifier = Modifier.fillMaxSize(),
                        contentScale = ContentScale.FillBounds,
                        painter = painterResource(id = R.drawable.common_window_small),
                        contentDescription = null
                    )
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier = Modifier
                            .padding(horizontal = paddingHuge)
                            .padding(bottom = padding48)
                    ) {
                        Box(
                            modifier = Modifier.height(gameAlertDialogTitleHeight),
                            contentAlignment = Alignment.BottomCenter
                        ) {
                            Text(
                                text = "防沉迷认证",
                                style = MaterialTheme.typography.h1,
                                color = Color.Black
                            )
                        }
                        Spacer(modifier = Modifier.size(paddingHugePlus))
                        Text(
                            text = "根据国家法律法规要求，必须完成实名认证后，才可以进入游戏。请点击按钮认证：",
                            style = MaterialTheme.typography.h3,
                            color = Color.Black
                        )
                        Spacer(modifier = Modifier.weight(1f))
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceEvenly,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            GameButton(text = "防沉迷认证", buttonStyle = ButtonStyle.Orange) {
                                confirm()
                            }
                        }
                        Spacer(modifier = Modifier.size(paddingHuge))
                    }
                }
            }
        }
    }
}