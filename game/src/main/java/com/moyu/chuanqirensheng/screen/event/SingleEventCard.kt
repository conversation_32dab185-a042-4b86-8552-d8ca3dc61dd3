package com.moyu.chuanqirensheng.screen.event

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.logic.award.toAward
import com.moyu.chuanqirensheng.logic.event.EventConditionLayout
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.event.isStartingEvent
import com.moyu.chuanqirensheng.logic.event.isWorldEvent
import com.moyu.chuanqirensheng.logic.event.triggerEvent
import com.moyu.chuanqirensheng.logic.skill.getRealDescColorful
import com.moyu.chuanqirensheng.logic.skill.toSkillAnnotatedString
import com.moyu.chuanqirensheng.repository.Dialogs.eventDetailDialog
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.CardSize
import com.moyu.chuanqirensheng.screen.common.ClickExpandLayout
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.sensor.ShadowedView
import com.moyu.chuanqirensheng.text.playNameMap
import com.moyu.chuanqirensheng.ui.theme.DARK_RED
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingSmallPlus
import com.moyu.core.model.event.Event
import com.moyu.core.model.skill.isAdventure
import com.moyu.core.util.RANDOM
import com.moyu.core.util.nextIntClosure
import com.wajahatkarim.flippable.FlipAnimationType
import com.wajahatkarim.flippable.Flippable
import com.wajahatkarim.flippable.FlippableController
import kotlinx.coroutines.delay


@Composable
fun SingleEventCard(cardSize: CardSize, event: Event) {
    Column(modifier = Modifier, horizontalAlignment = Alignment.CenterHorizontally) {
        val flipControl = remember {
            mutableStateOf(FlippableController())
        }
        val visible = remember(event.id) {
            mutableStateOf(false)
        }
        LaunchedEffect(event.id) {
            visible.value = false
            flipControl.value.flipToFront()
            delay(300)
            visible.value = true
            delay(RANDOM.nextIntClosure(100, 600).toLong())
            flipControl.value.flipToBack()
            // todo 有时候翻不过去，再翻一次，保证翻过去了
            delay(300L)
            flipControl.value.flipToBack()
        }
        TopCardView(event, cardSize, flipControl.value, visible.value)
        AnimatedVisibility(
            modifier = Modifier.size(cardSize.width, cardSize.height * 0.8f),
            visible = visible.value
        ) {
            BottomCardView(
                modifier = Modifier.size(cardSize.width, cardSize.height * 0.8f),
                event,
                visible.value
            )
        }
    }
}

@Composable
fun BottomCardView(modifier: Modifier, event: Event, visible: Boolean) {
    Box(modifier = modifier) {
        InnerBottomCard(event, visible)
    }
}

@Composable
fun InnerBottomCard(event: Event, visible: Boolean) {
    Box(contentAlignment = Alignment.Center) {
        Image(
            modifier = Modifier
                .fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(id = R.drawable.event_play_frame),
            contentDescription = null
        )
        if (visible) {
            Column(
                modifier = Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                TagView(
                    modifier = Modifier.graphicsLayer {
                        translationY = -paddingMedium.toPx()
                    },
                    text = playNameMap[event.play] ?: ""
                )
                Text(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                        .padding(horizontal = paddingMedium)
                        .verticalScroll(rememberScrollState()),
                    text = event.toAward(true).skills.firstOrNull { it.isAdventure() }
                        ?.getRealDescColorful(MaterialTheme.typography.h5.toSpanStyle())
                        ?: event.showText.toSkillAnnotatedString(MaterialTheme.typography.h5.toSpanStyle()),
                    style = MaterialTheme.typography.h5
                )
                if (!event.isStartingEvent() && event.condition != 0) {
                    EventConditionLayout(
                        event = event,
                        itemSize = ItemSize.Small,
                        textColor = Color.White
                    )
                }
                GameButton(
                    text = stringResource(id = R.string.do_select),
                    enabled = triggerEvent(event, false),
                    buttonSize = ButtonSize.MediumMinus,
                    buttonStyle = ButtonStyle.Orange
                ) {
                    if (event.isStartingEvent() || event.isWorldEvent()) {
                        EventManager.selectEvent(event)
                    } else {
                        eventDetailDialog.value = event
                    }
                }
                Spacer(modifier = Modifier.size(paddingMedium))
            }
        }
    }
}

@Composable
fun TopCardView(
    event: Event,
    cardSize: CardSize,
    flipControl: FlippableController,
    visible: Boolean
) {
    if (visible) {
        ShadowedView(
            modifier = Modifier.graphicsLayer { translationX = -paddingSmallPlus.toPx() },
            cardSize = cardSize
        ) {
            Flippable(
                modifier = Modifier.size(cardSize.width, cardSize.height),
                flipDurationMs = 300,
                flipOnTouch = false,
                flipController = flipControl,
                flipEnabled = true,
                frontSide = {
                    SingleCardBack(
                        modifier = Modifier.size(cardSize.width, cardSize.height),
                        drawableRes = R.drawable.card_back
                    )
                },
                backSide = {
                    ClickExpandLayout {
                        CommonCard(
                            cardSize = cardSize,
                            title = event.name,
                            icon = event.pic,
                            titleColor = if (event.isBad()) DARK_RED else Color.White
                        )
                    }
                },
                contentAlignment = Alignment.TopCenter,
                onFlippedListener = {
                },
                autoFlip = false,
                flipAnimationType = FlipAnimationType.HORIZONTAL_CLOCKWISE
            )
        }

    } else {
        Spacer(modifier = Modifier.size(cardSize.width, cardSize.height))
    }
}

@Composable
fun SingleCardBack(modifier: Modifier, drawableRes: Int) {
    Image(
        painterResource(id = drawableRes),
        modifier = modifier.clip(RoundedCornerShape(paddingMedium)),
        contentDescription = null
    )
}