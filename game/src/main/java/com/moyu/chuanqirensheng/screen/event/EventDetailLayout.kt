package com.moyu.chuanqirensheng.screen.event

import androidx.compose.foundation.layout.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.ui.theme.paddingMediumPlus
import com.moyu.core.model.event.Event


/**
1：【决斗类】2：【试炼类】3：【问答类】4：【奇遇类】
5：【商店类】6：【复活类】7：【探索类】8：【恢复类】
9：【修炼类】10：【军团卡升星类】11：【技能升星类】12：【史诗人物升星类】
13：【换军团卡类】14：【换技能类】15：【换史诗人物类】16：【附魔类】
21：【普通战斗】22：【宿敌战斗】23：【诅咒之战】24：【镜像之战】
25：【种族之战】26：【诸神之战】27：【保卫之战】28：【末日之战】
 */
@Composable
fun EventDetailLayout(modifier: Modifier, event: Event) {
    Box(modifier = modifier) {
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally) {
            Spacer(modifier = Modifier.size(paddingMediumPlus))
            EventManager.getOrCreateHandler(event).Layout(event)
        }
    }
}