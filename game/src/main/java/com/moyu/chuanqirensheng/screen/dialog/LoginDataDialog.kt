package com.moyu.chuanqirensheng.screen.dialog

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.core.content.ContextCompat
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.datastore.KEY_LOGIN_MESSAGE_ID
import com.moyu.chuanqirensheng.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.ui.theme.paddingHuge

@Composable
fun LoginFailedGameDialog() {
    val savedMessageId = getIntFlowByKey(KEY_LOGIN_MESSAGE_ID, 0)
    val show =
        !GameApp.instance.loginData.value.verified
                || (!GameApp.newUser && GameApp.instance.loginData.value.show && savedMessageId < GameApp.instance.loginData.value.messageId)
    if (show) {
        CommonDialog(
            title = stringResource(R.string.login_result),
            onDismissRequest = {
                if (GameApp.instance.loginData.value.verified) {
                    GameApp.instance.loginData.value =
                        GameApp.instance.loginData.value.copy(show = false)
                    setIntValueByKey(KEY_LOGIN_MESSAGE_ID, GameApp.instance.loginData.value.messageId)
                } else {
                    GameApp.instance.getWrapString(R.string.please_shut_down).toast()
                }
            },
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.padding(horizontal = paddingHuge)
            ) {
                Spacer(modifier = Modifier.size(paddingHuge))
                Text(
                    text = GameApp.instance.loginData.value.dialogText,
                    style = MaterialTheme.typography.h3,
                    color = Color.Black
                )
                Spacer(modifier = Modifier.weight(1f))
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    val buttonText = GameApp.instance.loginData.value.buttonText.ifEmpty { stringResource(
                        id = R.string.confirm
                    ) }
                    GameButton(text = buttonText, buttonStyle = ButtonStyle.Orange) {
                        if (GameApp.instance.loginData.value.buttonLink.isNotEmpty()) {
                            val uri: Uri = Uri.parse(GameApp.instance.loginData.value.buttonLink)
                            val intent = Intent(Intent.ACTION_VIEW, uri)
                            ContextCompat.startActivity(GameApp.instance.activity, intent, Bundle())
                        }
                        if (GameApp.instance.loginData.value.verified) {
                            GameApp.instance.loginData.value =
                                GameApp.instance.loginData.value.copy(show = false)
                            setIntValueByKey(KEY_LOGIN_MESSAGE_ID, GameApp.instance.loginData.value.messageId)
                        } else {
                            GameApp.instance.getWrapString(R.string.login_fail_tips).toast()
                        }
                    }
                }
                Spacer(modifier = Modifier.size(paddingHuge))
            }
        }
    }
}