@file:OptIn(ExperimentalLayoutApi::class, ExperimentalLayoutApi::class)

package com.moyu.chuanqirensheng.screen.sell

import androidx.compose.foundation.gestures.animateScrollBy
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.datastore.KEY_SHOP_SCROLL
import com.moyu.chuanqirensheng.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.feature.lucky.AdManager
import com.moyu.chuanqirensheng.feature.lucky.ui.AdRewardBar
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.award.VipManager
import com.moyu.chuanqirensheng.logic.sell.SellManager
import com.moyu.chuanqirensheng.logic.sell.preCondition
import com.moyu.chuanqirensheng.logic.sell.shopTitles
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.button.RefreshButton
import com.moyu.chuanqirensheng.screen.common.CurrentDiamondPoint
import com.moyu.chuanqirensheng.screen.common.CurrentKeyPoint
import com.moyu.chuanqirensheng.screen.common.GameLabel
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.screen.effect.VerticalScrollbar
import com.moyu.chuanqirensheng.ui.theme.gapSmallPlus
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.ui.theme.padding96
import com.moyu.chuanqirensheng.ui.theme.paddingLargePlus
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingSmallPlus
import com.moyu.chuanqirensheng.ui.theme.shopItemHeight
import com.moyu.chuanqirensheng.ui.theme.shopItemWidth
import kotlinx.coroutines.delay


@Composable
fun SellScreen() {
    val shopChests = SellManager.items.filter {
        // storageType是永久限量，storage是剩余数量
        it.storage > 0 || it.storageType != 2
    }
    val scrollState = rememberScrollState()
    LaunchedEffect(Unit) {
        AdManager.refreshAdMoney()
        SellManager.init()
        if (!getBooleanFlowByKey(KEY_SHOP_SCROLL)) {
            delay(1500)
            scrollState.animateScrollBy(1600f)
            setBooleanValueByKey(KEY_SHOP_SCROLL, true)
        }
    }
    Column(
        modifier = Modifier
            .fillMaxSize()
            .paint(
                painterResource(id = R.drawable.common_page_frame),
                contentScale = ContentScale.FillBounds
            ),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Spacer(modifier = Modifier.size(paddingMedium))
        Row(
            Modifier
                .align(Alignment.End)
                .padding(end = paddingSmallPlus)
        ) {
            CurrentDiamondPoint(showPlus = true, showFrame = true)
            Spacer(modifier = Modifier.size(paddingSmallPlus))
            CurrentKeyPoint(showPlus = true, showFrame = true)
        }
        Box(
            Modifier
                .fillMaxWidth()
                .weight(1f)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(scrollState),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Spacer(Modifier.size(padding4))
                AdRewardBar(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(padding96)
                        .padding(horizontal = padding36),
                    currentValue = AwardManager.adMoney.value,
                    maxValue = AdManager.luckyList.maxOf { it.price }
                )
                shopTitles.forEachIndexed { index, triple ->
                    val itemType = triple.second
                    val title = triple.first
                    val num = triple.third
                    if (shopChests.filter { it.type == itemType }.filter { it.preCondition() }
                            .isNotEmpty()) {
                        Spacer(modifier = Modifier.size(paddingLargePlus))
                        GameLabel {
                            if (index in listOf(1, 2, 3)) {
                                // 免费商品/皮肤/故事不支持刷新
                                val cost = VipManager.getShopRefreshCost()
                                RefreshButton(
                                    Modifier
                                        .align(Alignment.CenterEnd)
                                        .padding(end = paddingLargePlus),
                                    text = stringResource(id = R.string.refresh)
                                ) {
                                    val refreshedNum = when (itemType) {
                                        2 -> SellManager.refresh1Count.value
                                        3 -> SellManager.refresh2Count.value
                                        else -> SellManager.refresh3Count.value
                                    }
                                    val limit = VipManager.getRealShopRefreshLimit()
                                    if (refreshedNum >= limit) {
                                        (GameApp.instance.getWrapString(R.string.reach_max_refresh) + limit + GameApp.instance.getWrapString(
                                            R.string.reach_max_refresh2
                                        )).toast()
                                    } else {
                                        Dialogs.alertDialog.value = CommonAlert(
                                            title = GameApp.instance.getWrapString(R.string.refresh_shop),
                                            content = GameApp.instance.getWrapString(R.string.refresh_cost) + cost + GameApp.instance.getWrapString(
                                                R.string.refresh_cost2
                                            ),
                                            confirmText = GameApp.instance.getWrapString(R.string.refresh),
                                            onConfirm = {
                                                if (AwardManager.key.value >= cost) {
                                                    AwardManager.gainKey(-cost)
                                                    SellManager.refreshSellItemByType(itemType)
                                                } else {
                                                    GiftManager.onKeyNotEnough()
                                                    GameApp.instance.getWrapString(R.string.key_not_enough)
                                                        .toast()
                                                }
                                            }
                                        )
                                    }
                                }
                            }
                            Text(
                                text = title,
                                style = MaterialTheme.typography.h2,
                                color = Color.Black
                            )
                        }
                        FlowRow(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceEvenly,
                            overflow = FlowRowOverflow.Visible,
                            maxItemsInEachRow = 3
                        ) {
                            shopChests.filter { it.type == itemType }.filter { it.preCondition() }
                                .take(num).forEach {
                                    OneSellItem(it)
                                }
                            if (itemType == 5 || itemType == 6 || itemType == 7) {
                                // todo
                                Box(
                                    modifier = Modifier.size(shopItemWidth, shopItemHeight),
                                )
                                Box(
                                    modifier = Modifier.size(shopItemWidth, shopItemHeight),
                                )
                            }
                            if (index == 0) {
                                // todo 广告商品，加一个奖池按钮
                                GameButton(
                                    modifier = Modifier.padding(top = padding8),
                                    text = stringResource(R.string.check_award_pool),
                                    buttonStyle = ButtonStyle.Orange
                                ) {
                                    Dialogs.adPoolDialog.value = true
                                }
                            }
                        }
                    }
                    Spacer(modifier = Modifier.size(gapSmallPlus))
                }
            }
            VerticalScrollbar(
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .fillMaxHeight()
                    .width(padding16),
                scrollState = scrollState
            )
        }
    }
}