package com.moyu.chuanqirensheng.screen.equip

import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.screen.fortune.PropertyItem
import com.moyu.core.model.property.Property
import java.lang.Double.max

@Composable
fun Property.MainPropertyLine(
    originProperty: Property = this,
    showBoost: Boolean = false,
    countStart: Int = 0,
    countEnd: Int = 100,
    textStyle: TextStyle = MaterialTheme.typography.body1,
    showZero: Boolean = true,
) {
    var count = 1
    if (count in countStart until countEnd) {
        if (showZero || attack != 0) PropertyItem(
            icon = R.drawable.battle_attribute_1,
            getProperty = { max(0.0, attack.toDouble()) },
            name = stringResource(R.string.attack),
            getTips = { GameApp.instance.getWrapString(R.string.attack_tips) },
            isBoost = { (attack.toDouble() - originProperty.attack) },
            showBoost = showBoost,
            textStyle = textStyle,
            frameDrawable = R.drawable.card_attribute_frame2
        )
    }
    count += 1
    if (count in countStart until countEnd) {
        if (showZero || defenses.first() != 0) PropertyItem(
            icon = R.drawable.battle_attribute_2,
            getProperty = { max(0.0, defenses.first().toDouble()) },
            name = stringResource(R.string.defense),
            getTips = { GameApp.instance.getWrapString(R.string.defense_tips) },
            isBoost = { (defenses.first().toDouble() - originProperty.defenses.first()) },
            showBoost = showBoost,
            textStyle = textStyle,
            frameDrawable = R.drawable.card_attribute_frame2
        )
    }
    count += 1
    if (count in countStart until countEnd) {
        if (showZero || hp != 0) PropertyItem(
            icon = R.drawable.battle_attribute_3,
            getProperty = { hp.toDouble() },
            name = stringResource(R.string.hp),
            getTips = { GameApp.instance.getWrapString(R.string.hp_tips) },
            isBoost = { (hp.toDouble() - originProperty.hp) },
            showBoost = showBoost,
            textStyle = textStyle,
            frameDrawable = R.drawable.card_attribute_frame2
        )
    }
    count += 1
    if (count in countStart until countEnd) {
        if (showZero || getRealFatalRate() != 0.0) PropertyItem(
            icon = R.drawable.battle_attribute_4,
            getProperty = { max(0.0, getRealFatalRate()) },
            name = stringResource(R.string.fatal_rate),
            getTips = { GameApp.instance.getWrapString(R.string.fatal_rate_tips) },
            showPercent = true,
            isBoost = { (getRealFatalRate() - originProperty.getRealFatalRate()) },
            showBoost = showBoost,
            textStyle = textStyle,
            frameDrawable = R.drawable.card_attribute_frame2
        )
    }
    count += 1
    if (count in countStart until countEnd) {
        if (showZero || getRealFatalDamage() != 0.0) PropertyItem(
            icon = R.drawable.battle_attribute_5,
            getProperty = { max(0.0, getRealFatalDamage()) },
            name = stringResource(R.string.fatal_damage),
            getTips = { GameApp.instance.getWrapString(R.string.fatal_damage_tips) },
            showPercent = true,
            isBoost = { (getRealFatalDamage() - originProperty.getRealFatalDamage()) },
            showBoost = showBoost,
            textStyle = textStyle,
            frameDrawable = R.drawable.card_attribute_frame2
        )
    }
    count += 1
    if (count in countStart until countEnd) {
        if (showZero || getRealDodgeRate() != 0.0) PropertyItem(
            icon = R.drawable.battle_attribute_6,
            getProperty = { max(0.0, getRealDodgeRate()) },
            name = stringResource(R.string.dodge),
            getTips = { GameApp.instance.getWrapString(R.string.dodge_tips) },
            showPercent = true,
            isBoost = { (getRealDodgeRate() - originProperty.getRealDodgeRate()) },
            showBoost = showBoost,
            textStyle = textStyle,
            frameDrawable = R.drawable.card_attribute_frame2
        )
    }
    count += 1
    if (count in countStart until countEnd) {
        if (showZero || getRealSuckBloodRate() != 0.0) PropertyItem(icon = R.drawable.battle_attribute_7,
            getProperty = { max(0.0, getRealSuckBloodRate()) },
            name = stringResource(R.string.suck_blood),
            getTips = { GameApp.instance.getWrapString(R.string.suck_blood_tips) },
            showPercent = true,
            isBoost = { (getRealSuckBloodRate() - originProperty.getRealSuckBloodRate()) },
            showBoost = showBoost,
            textStyle = textStyle,
            frameDrawable = R.drawable.card_attribute_frame2
        )
    }
    count += 1
    if (count in countStart until countEnd) {
        if (showZero || speed != 0) PropertyItem(
            icon = R.drawable.battle_attribute_8,
            getProperty = { max(0.0, speed.toDouble()) },
            name = stringResource(R.string.speed),
            getTips = { GameApp.instance.getWrapString(R.string.speed_tips) },
            isBoost = { (speed.toDouble() - originProperty.speed) },
            showBoost = showBoost,
            textStyle = textStyle,
            frameDrawable = R.drawable.card_attribute_frame2
        )
    }
}