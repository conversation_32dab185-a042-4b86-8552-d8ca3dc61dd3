package com.moyu.chuanqirensheng.screen.dialog

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.screen.more.ClickableStars
import com.moyu.chuanqirensheng.sub.review.GameReviewManager
import com.moyu.chuanqirensheng.ui.theme.paddingLargePlus
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingSmallPlus

@Composable
fun GameReviewDialog(info: MutableState<Boolean>) {
    if (info.value) {
        CommonDialog(
            title = stringResource(R.string.rank_star_title),
            onDismissRequest = { info.value = false }) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = paddingSmallPlus),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Spacer(modifier = Modifier.size(paddingLargePlus))
                Text(
                    text = stringResource(R.string.review_text),
                    style = MaterialTheme.typography.h2,
                    color = Color.Black
                )
                Spacer(modifier = Modifier.size(paddingMedium))
                ClickableStars {
                    GameReviewManager.doRankStar(it)
                }
            }
        }
    }
}