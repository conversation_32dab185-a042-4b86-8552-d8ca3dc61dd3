package com.moyu.chuanqirensheng.screen.dialog

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.moyu.chuanqirensheng.BuildConfig
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.cloud.saver.GameSave
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.alertHeight
import com.moyu.chuanqirensheng.ui.theme.alertWidth
import com.moyu.chuanqirensheng.ui.theme.gameAlertDialogTitleHeight
import com.moyu.chuanqirensheng.ui.theme.padding48
import com.moyu.chuanqirensheng.ui.theme.paddingHuge
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.util.date2TimeStamp
import kotlinx.coroutines.delay

@Composable
fun UseCloudSaverDialog(cloudSaverDialog: MutableState<GameSave?>) {
    cloudSaverDialog.value?.let { cloudSave ->
        LaunchedEffect(Unit) {
            if (BuildConfig.FLAVOR.contains("Lite")) {
                delay(5000)
            } else {
                delay(1000)
            }
            GameApp.instance.useThisCloudSave(cloudSave)
            cloudSaverDialog.value = null
        }
        Dialog(
            onDismissRequest = { cloudSaverDialog.value = null },
            properties = DialogProperties(
                dismissOnBackPress = false,
                dismissOnClickOutside = false,
                usePlatformDefaultWidth = false
            )
        ) {
            Box(
                modifier = Modifier.size(alertWidth, alertHeight)
            ) {
                Image(
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.FillBounds,
                    painter = painterResource(R.drawable.common_window_small),
                    contentDescription = null
                )
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier
                        .padding(horizontal = paddingHuge)
                        .padding(bottom = padding48)
                ) {
                    Box(
                        modifier = Modifier.height(gameAlertDialogTitleHeight),
                        contentAlignment = Alignment.BottomCenter
                    ) {
                        Text(
                            text = stringResource(R.string.user_cloud_save_title),
                            style = MaterialTheme.typography.h2,
                            color = Color.Black
                        )
                    }
                    Spacer(modifier = Modifier.size(paddingHuge))
                    Column(
                        modifier = Modifier
                            .clip(RoundedCornerShape(paddingSmall))
                            .background(B50)
                            .padding(paddingMedium),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        Text(text = stringResource(R.string.save_upload_time), style = MaterialTheme.typography.h3)
                        Text(
                            text = date2TimeStamp(cloudSave.timestamp),
                            style = MaterialTheme.typography.h3
                        )
                    }
                    Spacer(modifier = Modifier.weight(1f))
                    Row(
                        Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly,
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        if (BuildConfig.FLAVOR.contains("Lite")) {
                            GameButton(
                                text = stringResource(id = R.string.cancel),
                                buttonStyle = ButtonStyle.Orange,
                                onClick = {
                                    cloudSaverDialog.value = null
                                })
                            Spacer(modifier = Modifier.size(paddingMedium))
                        }
                        GameButton(
                            text = stringResource(R.string.use),
                            onClick = { })
                    }
                }
            }
        }
    }
}