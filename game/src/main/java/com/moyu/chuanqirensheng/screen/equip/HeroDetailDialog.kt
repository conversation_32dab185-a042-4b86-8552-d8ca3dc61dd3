package com.moyu.chuanqirensheng.screen.equip

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.skill.canStarUp
import com.moyu.chuanqirensheng.logic.skill.empire
import com.moyu.chuanqirensheng.logic.skill.getCardFrameDrawable
import com.moyu.chuanqirensheng.logic.skill.getRealDescColorful
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.CardSize
import com.moyu.chuanqirensheng.screen.common.CommonBar
import com.moyu.chuanqirensheng.screen.common.GameLabel
import com.moyu.chuanqirensheng.screen.dialog.CommonDialog
import com.moyu.chuanqirensheng.screen.effect.StrokedText
import com.moyu.chuanqirensheng.screen.effect.StrokedTextAnnotated
import com.moyu.chuanqirensheng.screen.effect.newTurnEffectState
import com.moyu.chuanqirensheng.screen.effect.restartEffect
import com.moyu.chuanqirensheng.screen.effect.starUpEffect
import com.moyu.chuanqirensheng.screen.more.Stars
import com.moyu.chuanqirensheng.screen.sensor.ShadowedView
import com.moyu.chuanqirensheng.thread.gameDispatcher
import com.moyu.chuanqirensheng.ui.theme.cardStarBigSize
import com.moyu.chuanqirensheng.ui.theme.eventCardBigHeight
import com.moyu.chuanqirensheng.ui.theme.eventCardBigWidth
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding165
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding24
import com.moyu.chuanqirensheng.ui.theme.padding260
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding40
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.ui.theme.padding80
import com.moyu.chuanqirensheng.ui.theme.paddingLarge
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.ui.theme.paddingSmallPlus
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.isHeroSkill
import kotlinx.coroutines.launch

@Composable
fun HeroDetailDialog(show: MutableState<Skill?>) {
    show.value?.let { skill ->
        CommonDialog(title = "",
            frame = null,
            noPadding = true,
            heightInDp = eventCardBigHeight * 2.3f,
            clickFrame = { show.value = null },
            onDismissRequest = {
                show.value = null
            }) {
            Column(
                Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Spacer(modifier = Modifier.size(paddingSmall))
                ShadowedView(cardSize = CardSize.Large) {
                    SingleDetailHeroCard(
                        Modifier.size(eventCardBigWidth, eventCardBigHeight),
                        skill
                    )
                }
                HeroPropView(skill = skill)
                if (skill.canStarUp() && !repo.gameMode.value.isPvp()) {
                    Spacer(modifier = Modifier.size(padding22))
                    HeroStarUpView(skill = skill)
                }
            }
        }
    }
}

@Composable
fun SingleDetailHeroCard(modifier: Modifier = Modifier, skill: Skill) {
    Box(modifier.size(eventCardBigWidth, eventCardBigHeight)) {
        Image(
            modifier = Modifier
                .fillMaxSize()
                .padding(vertical = padding4, horizontal = padding10)
                .padding(bottom = paddingSmallPlus)
                .clip(RoundedCornerShape(eventCardBigWidth / 22)),
            contentScale = ContentScale.Crop,
            alignment = Alignment.TopCenter,
            painter = painterResource(id = getImageResourceDrawable(skill.icon)),
            contentDescription = skill.name
        )
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(id = skill.getCardFrameDrawable()),
            contentDescription = null
        )
        Stars(
            modifier = Modifier
                .align(Alignment.TopCenter)
                .graphicsLayer {
                    translationY = -padding8.toPx()
                },
            stars = skill.level,
            starHeight = cardStarBigSize
        )
        HeroTitleView(
            Modifier
                .align(Alignment.BottomCenter)
                .height(padding80)
                .padding(bottom = paddingLarge),
            skill
        )
    }
}

@Composable
fun HeroTitleView(modifier: Modifier, skill: Skill) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        StrokedText(
            text = skill.name,
            style = MaterialTheme.typography.h1,
            textAlign = TextAlign.Center
        )
    }
}
@Composable
fun HeroPropView(modifier: Modifier = Modifier, skill: Skill) {
    Box(
        modifier = modifier.height(eventCardBigHeight / 1.4f)
    ) {
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(id = R.drawable.common_big_frame),
            contentDescription = null
        )
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = padding6)
                .verticalScroll(
                    rememberScrollState()

                ),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.SpaceEvenly
        ) {
            Spacer(Modifier.size(padding6))
            val skills = if (skill.isHeroSkill()) {
                repo.gameCore.getSkillPool().filter { it.mainId == skill.mainId }
            } else listOf(skill)
            skills.forEach {
                if (skill.level == it.level) {
                    // 让 getRealDescColorful() 返回一个 AnnotatedString
                    val descAnnotatedString = it.getRealDescColorful()
                    // 用 buildAnnotatedString 把普通字符串和 AnnotatedString 拼在一起
                    val combinedText = buildAnnotatedString {
                        append("Lv.${it.level} ")
                        append(descAnnotatedString)
                    }
                    StrokedTextAnnotated(
                        modifier = Modifier.align(Alignment.Start),
                        text = combinedText,
                        style = MaterialTheme.typography.h4,
                    )
                } else {
                    StrokedText(
                        modifier = Modifier.align(Alignment.Start),
                        text = "Lv.${it.level} " + it.getRealDescColorful(),
                        style = MaterialTheme.typography.h4,
                        color = Color.Gray
                    )
                }
            }
            Spacer(Modifier.size(padding10))
        }
        if (skill.empire() != "0") {
            GameLabel(
                Modifier
                    .align(Alignment.BottomCenter)
                    .size(padding165, padding40)
                    .graphicsLayer {
                        translationY = padding24.toPx()
                    }) {
                StrokedText(text = skill.empire(), style = MaterialTheme.typography.h5)
            }
        }
    }
}

@Composable
fun HeroStarUpView(modifier: Modifier = Modifier, skill: Skill) {
    val scroll = repo.gameCore.getHeroById(skill.id)
    Box(
        modifier = modifier.height(eventCardBigHeight / 3f),
        contentAlignment = Alignment.Center
    ) {
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(id = R.drawable.common_small_frame),
            contentDescription = null
        )
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(paddingMedium),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.SpaceEvenly
        ) {
            val haveNextLevel = scroll.star < scroll.starLimit
            if (haveNextLevel) {
                CommonBar(
                    modifier = Modifier
                        .width(padding260)
                        .padding(horizontal = padding8)
                        .height(padding36),
                    currentValue = skill.num - 1,
                    maxValue = scroll.starUpNum,
                    emptyRes = R.drawable.exp_empty,
                    fullRes = R.drawable.exp_full,
                    style = MaterialTheme.typography.h4,
                    extraText = stringResource(R.string.same_card)
                )
            }
            GameButton(text = if (haveNextLevel) stringResource(R.string.star_up) else stringResource(
                R.string.star_max
            ),
                buttonStyle = ButtonStyle.Red,
                enabled = !skill.peek && scroll.starUpNum != 0 && (skill.num - 1) >= scroll.starUpNum && scroll.star != scroll.starLimit && AwardManager.diamond.value >= scroll.starUpResourceNum,
                onClick = {
                    if (skill.peek) {
                        GameApp.instance.getWrapString(R.string.peek_tips).toast()
                    } else {
                        restartEffect(newTurnEffectState, starUpEffect)
                        GameApp.globalScope.launch(gameDispatcher) {
                            Dialogs.heroDetailDialog.value = repo.heroManager.upgrade(skill)
                        }
                    }
                })
        }
    }
}