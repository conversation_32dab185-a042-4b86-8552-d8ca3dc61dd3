package com.moyu.chuanqirensheng.screen.talent

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.skill.getRealDescColorful
import com.moyu.chuanqirensheng.logic.talent.TalentManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.DiamondPoint
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.dialog.CommonDialog
import com.moyu.chuanqirensheng.screen.effect.dialogEffectState
import com.moyu.chuanqirensheng.screen.effect.restartEffect
import com.moyu.chuanqirensheng.screen.effect.starUpEffect
import com.moyu.chuanqirensheng.screen.skill.SingleSkillView
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.paddingLargePlus
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.talent.Talent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlin.math.max

@Composable
fun TalentDetailDialog(show: MutableState<Talent?>) {
    show.value?.let { talent ->
        val talentLevel = TalentManager.talents[talent.mainId] ?: 0
        val showTalentLevel = max(1, talentLevel)
        val showTalent = repo.gameCore.getTalentPool().first { it.level == showTalentLevel && it.mainId == talent.mainId }
        val locked = TalentManager.getLockInfoByTalent(showTalent).first
        val skill = repo.gameCore.getSkillById(talent.talentSkill)
        CommonDialog(
            title = talent.name + "${talentLevel}/${showTalent.levelLimit}",
            clickFrame = { show.value = null },
            onDismissRequest = {
                show.value = null
            }) {
            Column(
                Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Spacer(modifier = Modifier.size(paddingSmall))
                SingleSkillView(
                    skill = skill,
                    showName = false,
                    itemSize = ItemSize.HugeTalent,
                    locked = locked,
                    colorFilter = if (locked) ColorFilter.tint(
                        B50, BlendMode.SrcAtop
                    ) else null,
                    showStars = false,
                    frame = R.drawable.talent_frame,
                    imageClip = RoundedCornerShape(ItemSize.HugeTalent.itemSize / 2f)
                ) { }
                val skills = repo.gameCore.getSkillPool().filter { it.mainId == skill.mainId }
                Spacer(modifier = Modifier.size(paddingLargePlus))
                Column(
                    Modifier
                        .fillMaxWidth()
                        .weight(1f)
                        .verticalScroll(rememberScrollState())
                        .padding(horizontal = paddingLargePlus)
                ) {
                    skills.filter { it.level >= showTalentLevel && it.level < showTalentLevel + 15 }.forEach {
                        Text(
                            text = "Lv.${it.level}:" + it.getRealDescColorful(MaterialTheme.typography.h3.toSpanStyle()),
                            style = MaterialTheme.typography.h3,
                            color = if (it.level == talentLevel) Color.Black else Color.Gray
                        )
                        Spacer(modifier = Modifier.size(paddingSmall))
                    }
                }
                TalentStarUpView(skill = skill)
            }
        }
    }
}

@Composable
fun TalentStarUpView(modifier: Modifier = Modifier, skill: Skill) {
    val talentLevel = TalentManager.talents[skill.mainId] ?: 0
    val nextLevel = talentLevel + 1
    val scroll = repo.gameCore.getTalentPool().first { it.talentSkill == skill.id }
    val nextScroll = repo.gameCore.getTalentPool().firstOrNull { it.mainId == scroll.mainId && it.level == nextLevel }
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Spacer(modifier = Modifier.size(paddingSmall))
        val haveNextLevel = scroll.level < scroll.levelLimit
        Row(verticalAlignment = Alignment.CenterVertically) {
            nextScroll?.let {
                DiamondPoint(cost = nextScroll.cost, color = Color.Black)
            }
        }
        Spacer(modifier = Modifier.size(paddingSmall))
        val (locked, toast) = TalentManager.getLockInfoByTalent(scroll)
        GameButton(text = if (talentLevel == 0) stringResource(id = R.string.learn) else if (haveNextLevel) stringResource(
            id = R.string.star_up
        ) else stringResource(id = R.string.star_max),
            buttonStyle = ButtonStyle.Orange,
            enabled = scroll.level != scroll.levelLimit && AwardManager.diamond.value >= (nextScroll?.cost
                ?: 0),
            locked = locked,
            onClick = {
                if (locked) {
                    toast.toast()
                } else if (!haveNextLevel) {
                    GameApp.instance.getWrapString(R.string.star_max).toast()
                } else if (AwardManager.diamond.value < (nextScroll?.cost?: 0)) {
                    GiftManager.onDiamondNotEnough()
                    GameApp.instance.getWrapString(R.string.diamond_not_enough).toast()
                } else {
                    GameApp.globalScope.launch(Dispatchers.Main) {
                        restartEffect(dialogEffectState, starUpEffect)
                        Dialogs.detailTalentDialog.value = TalentManager.upgradeTalent(scroll, nextScroll?.cost?: 0)
                    }
                }
            })
    }
}
