package com.moyu.chuanqirensheng.screen.life

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.logic.story.Ending
import com.moyu.chuanqirensheng.logic.story.StoryManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.ui.theme.B35
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingSmall


@Composable
fun SingleEndingCard(ending: Ending?) {
    EffectButton(onClick = {
        Dialogs.endingDetailDialog.value = ending
    }) {

        Image(
            modifier = Modifier.fillMaxWidth(),
            contentScale = ContentScale.FillWidth,
            alignment = Alignment.Center,
            painter = painterResource(R.drawable.dialog_frame),
            contentDescription = null
        )
        // 删除按钮
        EffectButton(modifier = Modifier
            .size(imageSmall)
            .align(Alignment.TopEnd),
            onClick = {
                ending?.let { StoryManager.delete(ending) }
            }
        ) {
            Image(painter = painterResource(id = R.drawable.menu8_exit), contentDescription = stringResource(
                id = R.string.delete
            ))
        }
        // end 删除按钮
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(start = paddingMedium),
            horizontalAlignment = Alignment.Start,
        ) {
            ending?.let {
                Text(
                    text = ending.countryName + stringResource(R.string.lasted) + ending.age + stringResource(
                        R.string.age
                    ),
                    style = MaterialTheme.typography.h3,
                    color = Color.Black,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                Text(
                    text = ending.dieReason,
                    style = MaterialTheme.typography.h3,
                    color = Color.Black
                )
                Text(
                    text = stringResource(R.string.country_location) + ending.location,
                    style = MaterialTheme.typography.h3,
                    color = Color.Black
                )
                Text(
                    text = stringResource(R.string.country_religion) + ending.religion,
                    style = MaterialTheme.typography.h3,
                    color = Color.Black
                )
            } ?: Text(
                text = stringResource(R.string.no_record),
                style = MaterialTheme.typography.h3,
                color = Color.Black
            )
        }
    }
}


@Composable
fun SingleImageCard(modifier: Modifier, res: Int, text: String) {
    EffectButton(modifier = modifier, onClick = {}) {
        Image(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingSmall)
                .clip(
                    RoundedCornerShape(
                        paddingSmall
                    )
                ),
            contentScale = ContentScale.Crop,
            alignment = Alignment.TopCenter,
            painter = painterResource(id = res),
            contentDescription = null
        )
        Column(
            modifier = Modifier
                .fillMaxSize()
                .fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Box(Modifier.weight(3f)) {}
            Column(
                Modifier
                    .weight(2f)
                    .padding(paddingSmall)
                    .background(B35)
                    .fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Spacer(modifier = Modifier.size(paddingMedium))
                Text(text = text, style = MaterialTheme.typography.h6)
            }
        }
    }
}
