package com.moyu.chuanqirensheng.screen.dialog

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.logic.award.toBadgeTip
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.common.getTextStyle
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.util.getImageResourceDrawable

@Composable
fun ItemsDialog(showDungeon: MutableState<Boolean>) {
    showDungeon.value.takeIf { showDungeon.value }?.let {
        DisposableEffect(Unit) {
            onDispose {
                BattleManager.setBadgeUnNew()
            }
        }
        CommonDialog(title = stringResource(R.string.items_in_bag), onDismissRequest = {
            showDungeon.value = false
        }) {
            Column(
                modifier = Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                val list = BattleManager.badges.mapIndexed { index, i -> Pair(index, i) }
                    .filter { it.second > 0 }
                LazyVerticalGrid(modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
                    columns = GridCells.Fixed(4),
                    content = {
                        items(list.size) { index ->
                            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                                Spacer(modifier = Modifier.size(paddingSmall))
                                OneBadgeItem(
                                    list[index].first,
                                    list[index].second
                                )
                                Spacer(modifier = Modifier.size(paddingSmall))
                            }
                        }
                    })
            }
        }
    }
}

@Composable
fun OneBadgeItem(
    type: Int,
    value: Int,
    itemSize: ItemSize = ItemSize.LargePlus,
) {
    val badge = repo.gameCore.getBadgePool().first { it.id == type + 1 }
    Column(modifier = Modifier, horizontalAlignment = Alignment.CenterHorizontally) {
        EffectButton(onClick = {
            badge.id.toBadgeTip().toast()
        }) {
            Image(
                modifier = Modifier.size(itemSize.frameSize),
                painter = painterResource(R.drawable.item_quality_3),
                contentDescription = null,
            )
            Image(
                modifier = Modifier.size(itemSize.itemSize),
                painter = painterResource(id = getImageResourceDrawable(badge.pic)),
                contentDescription = badge.name + "x$value"
            )
        }
        Text(
            text = badge.name + "x$value", style = itemSize.getTextStyle(),
            maxLines = 2,
            minLines = 2,
            overflow = TextOverflow.Clip,
            textAlign = TextAlign.Center,
            color = Color.Black
        )
    }
}

