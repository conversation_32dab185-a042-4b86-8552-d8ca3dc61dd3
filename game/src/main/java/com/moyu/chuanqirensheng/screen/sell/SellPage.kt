package com.moyu.chuanqirensheng.screen.sell

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import com.moyu.chuanqirensheng.screen.effect.StrokedText
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.award.VipManager
import com.moyu.chuanqirensheng.logic.sell.SELL_KEY_INDEX
import com.moyu.chuanqirensheng.logic.sell.SellManager
import com.moyu.chuanqirensheng.logic.sell.preCondition
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.button.RefreshButton
import com.moyu.chuanqirensheng.screen.common.CurrentDiamondPoint
import com.moyu.chuanqirensheng.screen.common.CurrentKeyPoint
import com.moyu.chuanqirensheng.screen.common.CurrentPvpPoint
import com.moyu.chuanqirensheng.screen.common.CurrentRealMoneyPoint
import com.moyu.chuanqirensheng.screen.common.GameLabel
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.ui.theme.gapLarge
import com.moyu.chuanqirensheng.ui.theme.labelHeight
import com.moyu.chuanqirensheng.ui.theme.labelWidth
import com.moyu.chuanqirensheng.ui.theme.padding14
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding8


@Composable
fun SellPage(
    itemType: Int,
    showPvpDiamond: Boolean = false,
    topContent: @Composable ColumnScope.() -> Unit = {}
) {
    LaunchedEffect(Unit) {
        SellManager.init()
    }
    val shopChests =
        SellManager.items.filter { it.type == itemType && it.preCondition() }
            .sortedBy { it.order }.filter {
                // storageType是永久限量，storage是剩余数量
                it.storage > 0 || it.storageType != 2
            }.filter {
                // 钥匙商店，如果是谷歌商店，不显示没有谷歌商品id的商品
                if (it.isAifadian()) {
                    it.googleItemId != "0" || !GameApp.instance.resources.getBoolean(R.bool.has_billing)
                } else true
            }
    Column(
        Modifier.paint(
            painterResource(id = R.drawable.common_page_frame),
            contentScale = ContentScale.FillBounds
        )
    ) {
        Spacer(modifier = Modifier.size(padding8))
        Row(
            modifier = Modifier
                .padding(start = padding14)
        ) {
            if (showPvpDiamond) {
                CurrentPvpPoint(
                    showPlus = true,
                    showFrame = true
                )
            } else {
                CurrentKeyPoint(
                    showPlus = true,
                    showFrame = true
                )
                CurrentDiamondPoint(
                    showPlus = true,
                    showFrame = true
                )
                if (!GameApp.instance.resources.getBoolean(R.bool.has_billing)) {
                    CurrentRealMoneyPoint(
                        showPlus = true,
                        showFrame = true
                    )
                }
            }
        }
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(
                    rememberScrollState()
                ),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            topContent()
            shopChests.groupBy { it.title }.keys.forEach { title ->
                val items = shopChests.filter { it.type == itemType }
                    .filter { it.title == title }
                GameLabel(Modifier.size(labelWidth, labelHeight)) {
                    if (items.any { it.canRefresh }) {
                        val cost = VipManager.getShopRefreshCost()
                        RefreshButton(
                            Modifier
                                .align(Alignment.CenterEnd)
                                .padding(end = padding19),
                            stringResource(id = R.string.refresh)
                        ) {
                            val refreshedNum = when (itemType) {
                                1 -> SellManager.refresh1Count.intValue
                                else -> SellManager.refresh2Count.intValue
                            }
                            val limit = VipManager.getRealShopRefreshLimit()
                            if (refreshedNum >= limit) {
                                (GameApp.instance.getWrapString(R.string.reach_max_refresh) + limit + GameApp.instance.getWrapString(
                                    R.string.reach_max_refresh2
                                )).toast()
                            } else {
                                Dialogs.alertDialog.value = CommonAlert(
                                    title = GameApp.instance.getWrapString(R.string.refresh_shop),
                                    content = GameApp.instance.getWrapString(R.string.refresh_cost) + cost + GameApp.instance.getWrapString(
                                        R.string.refresh_cost2
                                    ),
                                    confirmText = GameApp.instance.getWrapString(R.string.refresh),
                                    onConfirm = {
                                        if (AwardManager.key.value >= cost) {
                                            AwardManager.gainKey(-cost)
                                            SellManager.refreshSellItemByType(itemType)
                                        } else {
                                            GiftManager.onKeyNotEnough()
                                            GameApp.instance.getWrapString(R.string.key_not_enough)
                                                .toast()
                                        }
                                    }
                                )
                            }
                        }
                    }
                    StrokedText(
                        text = title,
                        style = MaterialTheme.typography.h2,
                        textAlign = TextAlign.Center,
                        color = Color.White
                    )
                }
                FlowRow(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly,
                    overflow = FlowRowOverflow.Visible,
                    maxItemsInEachRow = 3
                ) {
                    items
                        .sortedBy { if (itemType == SELL_KEY_INDEX) it.price else it.priceType }
                        .forEach {
                            OneSellItem(it)
                        }
                }
            }
            Spacer(modifier = Modifier.size(gapLarge))
        }
    }
}