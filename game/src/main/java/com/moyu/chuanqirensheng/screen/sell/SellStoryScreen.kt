@file:OptIn(ExperimentalLayoutApi::class, ExperimentalLayoutApi::class)

package com.moyu.chuanqirensheng.screen.sell

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.logic.story.StoryManager
import com.moyu.chuanqirensheng.screen.life.SingleStorySell
import com.moyu.chuanqirensheng.ui.theme.eventCardHeight
import com.moyu.chuanqirensheng.ui.theme.paddingMediumPlus


@Composable
fun SellStoryScreen() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .paint(
                painterResource(id = R.drawable.common_page_frame),
                contentScale = ContentScale.FillBounds
            ),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Spacer(modifier = Modifier.size(paddingMediumPlus))
        FlowRow(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly,
            overflow = FlowRowOverflow.Visible,
            maxItemsInEachRow = 3
        ) {
            StoryManager.stories.filter { it.sellId != 0 }.forEach { story->
                SingleStorySell(
                    modifier = Modifier.height(eventCardHeight).weight(1f),
                    story = story,
                )
            }
        }
    }
}