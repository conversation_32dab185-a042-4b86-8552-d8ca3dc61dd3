package com.moyu.chuanqirensheng.screen.common

import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.SizeTransform
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.zIndex
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.battlepass.BattlePass2Manager
import com.moyu.chuanqirensheng.feature.battlepass.BattlePassManager
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.award.VipManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.ui.theme.DARK_RED
import com.moyu.chuanqirensheng.ui.theme.cheatBarHeight
import com.moyu.chuanqirensheng.ui.theme.cheatBarWidth
import com.moyu.chuanqirensheng.ui.theme.elementHeight
import com.moyu.chuanqirensheng.ui.theme.elementWidth
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.imageSmallPlus
import com.moyu.chuanqirensheng.ui.theme.imageSmallPlusFrame
import com.moyu.chuanqirensheng.ui.theme.imageTinyPlus
import com.moyu.chuanqirensheng.ui.theme.moneyHeight
import com.moyu.chuanqirensheng.ui.theme.moneyWidth
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.ui.theme.paddingTiny

const val UNLOCK_AIFADIAN_LEVEL = 3


@Composable
fun CurrentPassPoint(modifier: Modifier = Modifier) {
    val money = BattlePassManager.getCurrentLevelWarPass()
    val level = BattlePassManager.getCurrentWarPass()?.level ?: 0
    val season = BattlePassManager.getPassSeason()
    val nextLevelValue =
        repo.gameCore.getBattlePassPool().filter { it.level == level + 1 && it.season == season }
            .firstOrNull()?.exp ?: 1000
    Row(modifier = modifier, verticalAlignment = Alignment.CenterVertically) {
        Box(
            Modifier
                .size(imageLarge)
                .graphicsLayer {
                    translationX = paddingMedium.toPx()
                }
                .zIndex(10f), contentAlignment = Alignment.Center) {
            VipLevel(cheatLevel = level, frame = painterResource(id = R.drawable.battlepass_level_frame))
        }
        CommonBar(
            modifier = Modifier.size(cheatBarWidth, cheatBarHeight),
            currentValue = money,
            maxValue = nextLevelValue,
            fullRes = R.drawable.battlepass_level_line,
            textColor = Color.White,
            style = MaterialTheme.typography.h6
        )
        Spacer(modifier = Modifier.size(paddingSmall))
        InfoIcon {
            GameApp.instance.getWrapString(R.string.battle_pass_content).toast()
        }
        Spacer(modifier = Modifier.size(paddingSmall))
        EffectButton(onClick = {
            BattlePassManager.switchSeason()
        }) {
            Text(text = stringResource(R.string.switch_war_pass), style = MaterialTheme.typography.h3, color = Color.Yellow)
        }
    }
}

@Composable
fun CurrentPass2Point(modifier: Modifier = Modifier) {
    val money = BattlePass2Manager.getCurrentLevelWarPass()
    val level = BattlePass2Manager.getCurrentWarPass()?.level ?: 0
    val season = BattlePass2Manager.getPassSeason()
    val nextLevelValue =
        repo.gameCore.getBattlePass2Pool().filter { it.level == level + 1 && it.season == season }
            .firstOrNull()?.exp ?: 1000
    Row(modifier = modifier, verticalAlignment = Alignment.CenterVertically) {
        Box(
            Modifier
                .size(imageLarge)
                .graphicsLayer {
                    translationX = paddingMedium.toPx()
                }
                .zIndex(10f), contentAlignment = Alignment.Center) {
            VipLevel(cheatLevel = level, frame = painterResource(id = R.drawable.battlepass_level_frame))
        }
        CommonBar(
            modifier = Modifier.size(cheatBarWidth, cheatBarHeight),
            currentValue = money,
            maxValue = nextLevelValue,
            fullRes = R.drawable.battlepass_level_line,
            textColor = Color.White,
            style = MaterialTheme.typography.h6
        )
        Spacer(modifier = Modifier.size(paddingSmall))
        InfoIcon {
            GameApp.instance.getWrapString(R.string.battle_pass2_content).toast()
        }
    }
}

@Composable
fun InfoIcon(modifier: Modifier = Modifier, callback: () -> Unit) {
    EffectButton(modifier = modifier, onClick = callback) {
        Image(
            modifier = Modifier.size(imageSmallPlus),
            painter = painterResource(id = R.drawable.common_information),
            contentDescription = stringResource(R.string.explain)
        )
    }
}


@Composable
fun VipLevel(modifier: Modifier = Modifier, cheatLevel: Int, frame :Painter = painterResource(id = R.drawable.cheat_level_frame)) {
    Box(modifier = modifier.size(imageLarge), contentAlignment = Alignment.Center) {
        Image(
            modifier = Modifier.size(imageLarge),
            painter = painterResource(R.drawable.cheat_level_frame),
            contentDescription = null
        )
        Text(text = "Lv$cheatLevel", style = MaterialTheme.typography.h3)
    }
}


@Composable
fun CurrentElectricPoint(
    modifier: Modifier = Modifier
) {
    val money = AwardManager.electric.value
    val level = VipManager.getVipLevelData()
    val nextLevelValue = if (money == 0) repo.gameCore.getVipPool().first() else
        repo.gameCore.getVipPool().firstOrNull { it.level == level.level + 1 } ?: repo.gameCore.getVipPool().last()
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center,
        modifier = Modifier
            .fillMaxWidth()
            .then(modifier)
    ) {
        VipLevel(cheatLevel = if (money == 0) 0 else level.level)
        CommonBar(
            modifier = Modifier.size(cheatBarWidth, cheatBarHeight),
            currentValue = money - (nextLevelValue.num - nextLevelValue.currentNum),
            maxValue = nextLevelValue.currentNum,
            fullRes = R.drawable.cheat_level_line,
            textColor = Color.White,
            style = MaterialTheme.typography.h6
        )
        InfoIcon {
            if (GameApp.instance.canShowAifadian()) {
                if (GameApp.instance.resources.getBoolean(R.bool.has_billing)) {
                    // 谷歌内购
                    Dialogs.getKeyDialog.value = true
                    GameApp.instance.getWrapString(R.string.electric_content).toast()
                } else {
                    GameApp.instance.getWrapString(R.string.electric_content).toast()
                }
            } else {
                GameApp.instance.getWrapString(R.string.no_electric).toast()
            }
        }
    }
}

@Composable
fun CurrentDiamondPoint(
    modifier: Modifier = Modifier, showPlus: Boolean = false, showFrame: Boolean = false
) {
    val money = AwardManager.diamond.value
    Box(modifier = modifier.size(moneyWidth, moneyHeight), contentAlignment = Alignment.Center) {
        if (showFrame) {
            Image(
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.FillBounds,
                painter = painterResource(id = R.drawable.event_condition_label),
                contentDescription = stringResource(id = R.string.diamond_title)
            )
        }
        Row(modifier = modifier, verticalAlignment = Alignment.CenterVertically) {
            Spacer(modifier = Modifier.size(paddingSmall))
            Image(
                modifier = Modifier.size(imageSmallPlus),
                painter = painterResource(id = R.drawable.common_medal),
                contentDescription = null
            )
            Spacer(modifier = Modifier.size(paddingSmall))
            AnimatedContent(modifier = Modifier.weight(1f), targetState = money, transitionSpec = {
                addAnimationVertical(duration = 600).using(
                    SizeTransform(clip = true)
                )
            }, label = "") { target ->
                Text(
                    text = target.toString(),
                    style = MaterialTheme.typography.h4,
                    textAlign = TextAlign.End
                )
            }
            if (showPlus) {
                Spacer(modifier = Modifier.size(paddingTiny))
                EffectButton(onClick = {
                    Dialogs.moneyTransferDialog.value = true
                }) {
                    Image(
                        modifier = Modifier.size(imageTinyPlus),
                        painter = painterResource(R.drawable.new_gold_buy),
                        contentDescription = stringResource(id = R.string.gain_diamond)
                    )
                }
            }
            Spacer(modifier = Modifier.size(paddingMedium))
        }
    }
}

@Composable
fun DiamondPoint(cost: Int, color: Color = Color.Black) {
    val money = AwardManager.diamond.value
    val enough = money >= cost
    Row(verticalAlignment = Alignment.CenterVertically) {
        Image(
            modifier = Modifier.size(imageSmallPlus),
            painter = painterResource(id = R.drawable.common_medal),
            contentDescription = stringResource(id = R.string.diamond_title)
        )
        Spacer(modifier = Modifier.size(paddingTiny))
        Text(
            text = cost.toString(),
            style = MaterialTheme.typography.h4,
            color = if (enough) color else DARK_RED
        )
    }
}


@Composable
fun CurrentKeyPoint(
    modifier: Modifier = Modifier, showPlus: Boolean = false, showFrame: Boolean = false
) {
    val money = AwardManager.key.value
    Box(modifier = modifier.size(moneyWidth, moneyHeight), contentAlignment = Alignment.Center) {
        if (showFrame) {
            Image(
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.FillBounds,
                painter = painterResource(id = R.drawable.event_condition_label),
                contentDescription = stringResource(id = R.string.key_title)
            )
        }
        Row(modifier = modifier, verticalAlignment = Alignment.CenterVertically) {
            Spacer(modifier = Modifier.size(paddingSmall))
            Image(
                modifier = Modifier.size(imageSmallPlus),
                painter = painterResource(id = R.drawable.common_key),
                contentDescription = null
            )
            Spacer(modifier = Modifier.size(paddingSmall))
            AnimatedContent(modifier = Modifier.weight(1f), targetState = money, transitionSpec = {
                addAnimationVertical(duration = 600).using(
                    SizeTransform(clip = true)
                )
            }, label = "") { target ->
                Text(
                    text = target.toString(),
                    style = MaterialTheme.typography.h4,
                    textAlign = TextAlign.End
                )
            }
            if (showPlus) {
                Spacer(modifier = Modifier.size(paddingTiny))
                EffectButton(onClick = {
                    if (GameApp.instance.canShowAifadian()) {
                        Dialogs.getKeyDialog.value = true
                    } else {
                        GameApp.instance.doJumpQQ()
                    }
                }) {
                    Image(
                        modifier = Modifier.size(imageTinyPlus),
                        painter = painterResource(R.drawable.new_gold_buy),
                        contentDescription = stringResource(id = R.string.get_key)
                    )
                }
            }
            Spacer(modifier = Modifier.size(paddingMedium))
        }
    }
}

@Composable
fun KeyPoint(cost: Int) {
    val money = AwardManager.key.value
    val enough = money >= cost
    Row(verticalAlignment = Alignment.CenterVertically) {
        Image(
            modifier = Modifier.size(imageSmallPlus),
            painter = painterResource(id = R.drawable.common_key),
            contentDescription = stringResource(id = R.string.key_title)
        )
        Spacer(modifier = Modifier.size(paddingTiny))
        Text(
            text = cost.toString(),
            style = MaterialTheme.typography.h4,
            color = if (enough) Color.White else DARK_RED
        )
    }
}

@Composable
fun CommonElementPoint(
    modifier: Modifier = Modifier, money: String, toast: String, drawable: Int
) {
    EffectButton(
        modifier = modifier.size(elementWidth, elementHeight), onClick = {
            toast.toast()
        }
    ) {
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(id = R.drawable.event_condition_label),
            contentDescription = toast.take(2) // todo，读屏只读前两个字即可，否则太啰嗦
        )
        Row(modifier = Modifier.fillMaxSize(), verticalAlignment = Alignment.CenterVertically) {
            Image(
                modifier = Modifier
                    .size(imageSmallPlus)
                    .scale(1.3f),
                contentScale = ContentScale.FillHeight,
                painter = painterResource(id = drawable),
                contentDescription = null
            )
            AnimatedContent(modifier = Modifier.weight(1f), targetState = money, transitionSpec = {
                addAnimationVertical(duration = 600).using(
                    SizeTransform(clip = true)
                )
            }, label = "") { target ->
                Text(
                    modifier = Modifier.weight(1f),
                    text = target,
                    style = MaterialTheme.typography.h6,
                    textAlign = TextAlign.End,
                    maxLines = 1,
                    overflow = TextOverflow.Visible,
                    softWrap = false,
                )
            }
            Spacer(modifier = Modifier.size(paddingSmall))
        }
    }
}


@Composable
fun LotteryPoint(cost: Int) {
    val money = AwardManager.lotteryMoney.value
    val enough = money >= cost
    Row(verticalAlignment = Alignment.CenterVertically) {
        Image(
            modifier = Modifier.size(imageSmallPlus),
            painter = painterResource(id = R.drawable.lottery_money),
            contentDescription = null
        )
        Spacer(modifier = Modifier.size(paddingTiny))
        Text(
            text = cost.toString(),
            style = MaterialTheme.typography.h4,
            color = if (enough) Color.White else Color.Red
        )
    }
}

@Composable
fun CurrentLotteryMoney(
    modifier: Modifier = Modifier, showPlus: Boolean = false, showFrame: Boolean = false
) {
    val money = AwardManager.lotteryMoney.value
    Box(modifier = modifier.size(moneyWidth, moneyHeight), contentAlignment = Alignment.Center) {
        if (showFrame) {
            Image(
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.FillBounds,
                painter = painterResource(id = R.drawable.event_condition_label),
                contentDescription = null
            )
        }
        Row(modifier = modifier, verticalAlignment = Alignment.CenterVertically) {
            Spacer(modifier = Modifier.size(paddingSmall))
            Image(
                modifier = Modifier.size(imageSmallPlus),
                painter = painterResource(id = R.drawable.lottery_money),
                contentDescription = null
            )
            Spacer(modifier = Modifier.size(paddingSmall))
            AnimatedContent(modifier = Modifier.weight(1f), targetState = money,
                transitionSpec = {
                    addAnimationVertical(duration = 1200).using(
                        SizeTransform(clip = true)
                    )
                }) { target ->
                Text(
                    text = target.toString(),
                    style = MaterialTheme.typography.h4,
                    textAlign = TextAlign.End
                )
            }
            Spacer(modifier = Modifier.size(paddingMedium))
        }
    }
}


@Composable
fun CurrentPvpScore(
    modifier: Modifier = Modifier, showPlus: Boolean = false, showFrame: Boolean = false
) {
    val money = PvpManager.pvpScore.value
    Box(modifier = modifier.size(moneyWidth, moneyHeight), contentAlignment = Alignment.Center) {
        if (showFrame) {
            Image(
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.FillBounds,
                painter = painterResource(id = R.drawable.event_condition_label),
                contentDescription = stringResource(id = R.string.pvp_score)
            )
        }
        Row(modifier = Modifier, verticalAlignment = Alignment.CenterVertically) {
            Image(
                modifier = Modifier
                    .size(imageSmallPlusFrame)
                    .graphicsLayer {
                        translationX = -padding2.toPx()
                    },
                painter = painterResource(id = R.drawable.pvp_score_icon),
                contentDescription = null
            )
            Spacer(modifier = Modifier.size(padding4))
            AnimatedContent(modifier = Modifier.weight(1f), targetState = money, transitionSpec = {
                addAnimationVertical(duration = 600).using(
                    SizeTransform(clip = true)
                )
            }, label = "") { target ->
                Text(
                    text = target.toString(),
                    style = MaterialTheme.typography.h3,
                    textAlign = TextAlign.Center
                )
            }
            Spacer(modifier = Modifier.size(padding26))
        }
    }
}


@Composable
fun PvpPoint(cost: Int, color: Color = Color.White) {
    val money = AwardManager.pvpDiamond.value
    val enough = money >= cost
    Row(verticalAlignment = Alignment.CenterVertically) {
        Image(
            modifier = Modifier.size(imageSmallPlusFrame),
            painter = painterResource(id = R.drawable.pvp_diamond_icon),
            contentDescription = stringResource(id = R.string.pvp_diamond)
        )
        Spacer(modifier = Modifier.size(padding6))
        Text(
            text = cost.toString(),
            style = if (cost >= 10000) MaterialTheme.typography.h3 else MaterialTheme.typography.h2,
            color = if (enough) color else Color.Red
        )
    }
}


@Composable
fun CurrentPvpPoint(
    modifier: Modifier = Modifier, showPlus: Boolean = false, showFrame: Boolean = false
) {
    val money = AwardManager.pvpDiamond.value
    Box(modifier = modifier.size(moneyWidth, moneyHeight), contentAlignment = Alignment.Center) {
        if (showFrame) {
            Image(
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.FillBounds,
                painter = painterResource(id = R.drawable.event_condition_label),
                contentDescription = stringResource(id = R.string.pvp_diamond)
            )
        }
        Row(modifier = Modifier, verticalAlignment = Alignment.CenterVertically) {
            Image(
                modifier = Modifier
                    .size(imageSmallPlusFrame)
                    .graphicsLayer {
                        translationX = -padding2.toPx()
                    },
                painter = painterResource(id = R.drawable.pvp_diamond_icon),
                contentDescription = null
            )
            Spacer(modifier = Modifier.size(padding4))
            AnimatedContent(modifier = Modifier.weight(1f), targetState = money, transitionSpec = {
                addAnimationVertical(duration = 600).using(
                    SizeTransform(clip = true)
                )
            }, label = "") { target ->
                Text(
                    text = target.toString(),
                    style = MaterialTheme.typography.h3,
                    textAlign = TextAlign.Center
                )
            }
            Spacer(modifier = Modifier.size(padding26))
        }
    }
}


@Composable
fun HolidayPoint(cost: Int) {
    val money = AwardManager.holidayMoney.value
    val enough = money >= cost
    Row(verticalAlignment = Alignment.CenterVertically) {
        Image(
            modifier = Modifier.size(imageSmallPlus),
            painter = painterResource(id = R.drawable.holiday_money),
            contentDescription = null
        )
        Spacer(modifier = Modifier.size(paddingTiny))
        Text(
            text = cost.toString(),
            style = MaterialTheme.typography.h4,
            color = if (enough) Color.White else Color.Red
        )
    }
}

@Composable
fun CurrentHolidayMoney(
    modifier: Modifier = Modifier, showPlus: Boolean = false, showFrame: Boolean = false
) {
    val money = AwardManager.holidayMoney.value
    Box(modifier = modifier.size(moneyWidth, moneyHeight), contentAlignment = Alignment.Center) {
        if (showFrame) {
            Image(
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.FillBounds,
                painter = painterResource(id = R.drawable.event_condition_label),
                contentDescription = null
            )
        }
        Row(modifier = modifier, verticalAlignment = Alignment.CenterVertically) {
            Spacer(modifier = Modifier.size(paddingSmall))
            Image(
                modifier = Modifier.size(imageSmallPlus),
                painter = painterResource(id = R.drawable.holiday_money),
                contentDescription = null
            )
            Spacer(modifier = Modifier.size(paddingSmall))
            AnimatedContent(modifier = Modifier.weight(1f), targetState = money,
                transitionSpec = {
                    addAnimationVertical(duration = 1200).using(
                        SizeTransform(clip = true)
                    )
                }) { target ->
                Text(
                    text = target.toString(),
                    style = MaterialTheme.typography.h4,
                    textAlign = TextAlign.End
                )
            }
            Spacer(modifier = Modifier.size(paddingMedium))
        }
    }
}
