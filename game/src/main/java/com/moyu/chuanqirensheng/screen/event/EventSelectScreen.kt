package com.moyu.chuanqirensheng.screen.event

import androidx.compose.animation.AnimatedContent
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.debug.EventDebugButton
import com.moyu.chuanqirensheng.feature.stage.StageManager
import com.moyu.chuanqirensheng.screen.common.SettingRow
import com.moyu.chuanqirensheng.screen.common.settingRowItems
import com.moyu.chuanqirensheng.screen.effect.GifView
import com.moyu.chuanqirensheng.screen.effect.turnGif
import com.moyu.chuanqirensheng.screen.role.CountryInfoView
import com.moyu.chuanqirensheng.ui.theme.B35
import com.moyu.chuanqirensheng.ui.theme.eventTopLayoutHeight
import com.moyu.chuanqirensheng.ui.theme.padding180
import com.moyu.chuanqirensheng.ui.theme.padding28
import com.moyu.chuanqirensheng.ui.theme.paddingSmall

@Composable
fun EventSelectScreen() {
    Box {
        val switch = remember {
            mutableStateOf(false)
        }
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.Crop,
            painter = painterResource(StageManager.getCurrentBg()),
            contentDescription = null
        )
        Spacer(Modifier.fillMaxSize().background(B35))
        Column(
            modifier = Modifier.fillMaxSize()
        ) { // 左侧空出一些，避开setting
            CountryInfoView(
                Modifier
                    .fillMaxWidth()
                    .height(eventTopLayoutHeight)
            )
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .graphicsLayer {
                        translationY = -padding28.toPx()
                    }, contentAlignment = Alignment.Center
            ) {
                EventLabelView()
                Box(Modifier.align(Alignment.CenterEnd)) {
                    EventDebugButton()
                }
            }
            if (DebugManager.allEvent) {
                EventDebugSelectPage()
            } else {
                EventSelectPage(switch)
            }
        }
        GifView(modifier = Modifier.padding(top = padding180).align(Alignment.TopCenter), enabled = true, gifCount = turnGif.count, gifDrawable = turnGif.gif, pace = turnGif.pace)
        AnimatedContent(
            targetState = switch.value || DebugManager.allEvent,
            label = ""
        ) { target ->
            if (target) {
                Box(Modifier.fillMaxSize()) {
                    SettingRow(
                        Modifier
                            .align(Alignment.BottomCenter)
                            .padding(bottom = paddingSmall), settingRowItems
                    )
                }
            }
        }
    }
}
