package com.moyu.chuanqirensheng.screen.dialog

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.award.VipManager
import com.moyu.chuanqirensheng.logic.award.toAward
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.ui.theme.paddingHugePlus
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.core.GameCore
import com.moyu.core.model.event.Event
import com.moyu.core.model.sell.toAward
import com.moyu.core.music.SoundEffect
import com.moyu.core.util.toThreeDigits
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun EventFailDialog(show: MutableState<Event?>) {
    show.value?.let { event ->
        val award = remember {
            event.toAward(false).copy(showQuestion = false)
        }
        LaunchedEffect(Unit) {
            GameCore.instance.onBattleEffect(SoundEffect.EventFail)
            AwardManager.gainAward(award)
        }
        CommonDialog(
            title = stringResource(id = R.string.the) + BattleManager.adventureProps.value.age.toThreeDigits() + stringResource(
                            R.string.year_end),
            onDismissRequest = {
                if (show.value != null) {
                    show.value = null
                    GameApp.globalScope.launch(Dispatchers.Main) {
                        EventManager.gotoNextEvent(event, false)
                    }
                }
            }) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = paddingMedium),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Spacer(modifier = Modifier.size(paddingMedium))
                Text(
                    text = event.loseText,
                    style = MaterialTheme.typography.h3,
                    color = Color.Black
                )
                Text(
                    modifier = Modifier.fillMaxWidth(),
                    text = stringResource(id = R.string.punish),
                    style = MaterialTheme.typography.h3,
                    color = Color.Black
                )
                AwardList(modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.CenterHorizontally), award = award)
                Spacer(modifier = Modifier.weight(1f))
                GameButton(
                    text = stringResource(R.string.see_you_again),
                    buttonStyle = ButtonStyle.Orange,
                    buttonSize = ButtonSize.Medium,
                    onClick = {
                        if (show.value != null) {
                            show.value = null
                            GameApp.globalScope.launch(Dispatchers.Main) {
                                EventManager.gotoNextEvent(event, false)
                            }
                        }
                    })
                Spacer(modifier = Modifier.size(paddingHugePlus))
            }
        }
    }
}


