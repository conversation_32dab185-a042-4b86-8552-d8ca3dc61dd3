package com.moyu.chuanqirensheng.screen.event

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.gapSmall
import com.moyu.chuanqirensheng.util.getImageResourceDrawable

@Composable
fun EventDetailScreen() {
    GameBackground(
        title = EventManager.selectedEvent.value?.name?:"",
        background = getImageResourceDrawable(EventManager.selectedEvent.value?.bgPic?.takeIf { it != "0" }
            ?: "0"),
        bgMask = B50
    ) {
        EventManager.selectedEvent.value?.let {
            EventLayout(event = it) {
                EventDetailLayout(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                        .padding(bottom = gapSmall), event = it
                )
            }
        }
    }
}