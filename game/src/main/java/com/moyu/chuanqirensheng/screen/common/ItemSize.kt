package com.moyu.chuanqirensheng.screen.common

import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.Dp
import com.moyu.chuanqirensheng.ui.theme.eventCardBigHeight
import com.moyu.chuanqirensheng.ui.theme.eventCardBigWidth
import com.moyu.chuanqirensheng.ui.theme.eventCardHeight
import com.moyu.chuanqirensheng.ui.theme.eventCardWidth
import com.moyu.chuanqirensheng.ui.theme.imageHuge
import com.moyu.chuanqirensheng.ui.theme.imageHugeFrame
import com.moyu.chuanqirensheng.ui.theme.imageHugeLite
import com.moyu.chuanqirensheng.ui.theme.imageHugeLiteFrame
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.imageLargePlus
import com.moyu.chuanqirensheng.ui.theme.imageMedium
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.imageSmallPlus
import com.moyu.chuanqirensheng.ui.theme.padding40
import com.moyu.chuanqirensheng.ui.theme.padding48
import com.moyu.chuanqirensheng.ui.theme.tcgItemHeight
import com.moyu.chuanqirensheng.ui.theme.tcgItemWidth

enum class ItemSize(val frameSize: Dp, val itemSize: Dp) {
    Small(imageSmallPlus, imageSmall),
    Medium(imageMedium, imageSmallPlus),
    MediumPlus(padding48, padding40),
    Large(imageLargePlus, imageLarge),
    LargePlus(imageHugeLiteFrame, imageLargePlus),
    Huge(imageHuge, imageHugeLite),
    HugeTalent(imageHugeFrame, imageHugeLite),
}

@Composable
fun ItemSize.getTextStyle(): TextStyle {
    return when (this) {
        ItemSize.Small -> MaterialTheme.typography.h6
        ItemSize.Medium -> MaterialTheme.typography.h6
        ItemSize.MediumPlus -> MaterialTheme.typography.h6
        ItemSize.Large -> MaterialTheme.typography.h5
        ItemSize.LargePlus -> MaterialTheme.typography.h5
        else -> MaterialTheme.typography.h3
    }
}


enum class CardSize(val width: Dp, val height: Dp) {
    Small(tcgItemWidth, tcgItemHeight),
    Medium(eventCardWidth, eventCardHeight),
    Large(eventCardBigWidth, eventCardBigHeight),;
    fun getRadius(): Dp {
        return width / 22
    }
}

@Composable
fun CardSize.getTextStyle(): TextStyle {
    return when (this) {
        CardSize.Small -> MaterialTheme.typography.h6
        CardSize.Medium -> MaterialTheme.typography.h4
        CardSize.Large -> MaterialTheme.typography.h2
    }
}