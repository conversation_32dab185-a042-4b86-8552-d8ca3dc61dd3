package com.moyu.chuanqirensheng.screen.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.button.QUICK_GAP
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.paddingMediumPlus
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.ui.theme.paddingSmallPlus
import com.moyu.chuanqirensheng.ui.theme.settingBgHeight
import com.moyu.chuanqirensheng.ui.theme.settingSize
import com.moyu.core.model.skill.isAdventure
import com.moyu.core.model.skill.isBattleSkill

val settingRowItems = mutableStateListOf(
    SettingItem(
        name = { GameApp.instance.getWrapString(R.string.allies) },
        icon = { R.drawable.bottom_menu1 },
        redIcon = { BattleManager.allyGameData.any { it.new } },
        action = { Dialogs.gameAllyDialog.value = true },
    ),
    SettingItem(
        name = { GameApp.instance.getWrapString(R.string.adventure_skill) },
        icon = { R.drawable.bottom_menu2 },
        redIcon = { BattleManager.skillGameData.filter { it.isAdventure() }.any { it.new } },
        action = { Dialogs.adventureSkillDialog.value = true },
    ),
    SettingItem(
        name = { GameApp.instance.getWrapString(R.string.battle_skill) },
        icon = { R.drawable.bottom_menu3 },
        redIcon = { BattleManager.skillGameData.filter { it.isBattleSkill() }.any { it.new } },
        action = { Dialogs.battleSkillDialog.value = true },
    ),
    SettingItem(
        name = { GameApp.instance.getWrapString(R.string.hero) },
        icon = { R.drawable.bottom_menu4 },
        redIcon = { BattleManager.heroGameData.any { it.new } },
        action = { Dialogs.equipDialog.value = true },
    ),
    SettingItem(
        name = { GameApp.instance.getWrapString(R.string.item) },
        icon = { R.drawable.bottom_menu5 },
        redIcon = { BattleManager.badgeNew.value },
        action = { Dialogs.itemsDialog.value = true },
    ),
    SettingItem(
        name = { GameApp.instance.getWrapString(R.string.reputation) },
        icon = { R.drawable.bottom_menu6 },
        redIcon = { BattleManager.reputationNew.value },
        action = { Dialogs.reputationDialog.value = true },
    ),
)

@Composable
fun SettingRow(modifier: Modifier, settingItems: List<SettingItem>) {
    Row(
        modifier = modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        settingItems.forEach { settingItem ->
            Box(modifier = Modifier.weight(1f)) {
                SettingRowItem(settingItem)
            }
        }
    }
}

@Composable
fun SettingRowItem(settingItem: SettingItem) {
    EffectButton(modifier = Modifier
        .padding(
            vertical = paddingSmall
        ), clickGap = QUICK_GAP, onClick = {
        settingItem.action()
    }) {
        Image(
            modifier = Modifier.height(settingBgHeight),
            contentScale = ContentScale.FillHeight,
            painter = painterResource(id = R.drawable.event_menu_frame),
            contentDescription = null
        )
        Column(
            Modifier
                .graphicsLayer {
                    translationY = -paddingSmallPlus.toPx()
                },
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Image(
                modifier = Modifier.size(settingSize),
                painter = painterResource(id = settingItem.icon()),
                contentDescription = null
            )
            Text(
                text = settingItem.name(),
                style = MaterialTheme.typography.h3,
                maxLines = 1,
                overflow = TextOverflow.Visible
            )
        }
        if (settingItem.redIcon()) {
            Image(
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(bottom = paddingMediumPlus)
                    .size(imageSmall)
                    .graphicsLayer { translationX = paddingSmall.toPx() },
                painter = painterResource(R.drawable.red_icon),
                contentDescription = null
            )
        }
    }
}