@file:OptIn(ExperimentalLayoutApi::class, ExperimentalLayoutApi::class)

package com.moyu.chuanqirensheng.screen.dialog

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.ally.SingleAllyView
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.DiamondPoint
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.equip.MainPropertyLine
import com.moyu.chuanqirensheng.ui.theme.DarkGreen
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.ui.theme.paddingSmallPlus
import com.moyu.core.model.ally.Ally
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun AllyStarUpDialog(show: MutableState<Ally?>) {
    show.value?.let { ally ->
        val nextAlly = repo.gameCore.getAllyPool()
            .firstOrNull { it.mainId == ally.mainId && it.star == ally.star + 1 }
        CommonDialog(title = stringResource(id = R.string.star_up), onDismissRequest = {
            show.value = null
        }) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = paddingMedium)
                    .verticalScroll(rememberScrollState()),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                nextAlly?.let {
                    SingleAllyView(ally = nextAlly, showName = true, itemSize = ItemSize.LargePlus) { }
                    Spacer(modifier = Modifier.size(paddingSmall))
                    Column(Modifier.fillMaxWidth()) {
                        Text(
                            text = stringResource(R.string.star_up_preview),
                            style = MaterialTheme.typography.h3,
                            color = Color.Black
                        )
                    }
                    Spacer(modifier = Modifier.size(paddingSmall))
                    val nextRole = BattleManager.getRoleByAlly(nextAlly)

                    val role = BattleManager.getRoleByAlly(ally)
                    FlowRow(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly,
                        overflow = FlowRowOverflow.Visible,
                        maxItemsInEachRow = 2
                    ) {
                        nextRole.getCurrentProperty().MainPropertyLine(
                            originProperty = role.getCurrentProperty(), showBoost = true
                        )
                    }
                    Spacer(modifier = Modifier.size(paddingSmallPlus))
                    val oldSkills = role.getSkills()
                    val newSkills = nextRole.getSkills()
                    oldSkills.zip(newSkills).take(1).forEach {
                        Row {
                            Text(
                                text = "【${it.first.name}】${it.first.level}" + stringResource(R.string.stars) + "->", style = MaterialTheme.typography.h4,
                                color = Color.Black
                            )
                            Text(
                                text = "【${it.second.name}】${it.second.level}" + stringResource(
                                    R.string.stars
                                ), style = MaterialTheme.typography.h4,
                                color = if (it.first.level == it.second.level) Color.Black else DarkGreen
                            )
                        }
                        Spacer(modifier = Modifier.size(paddingSmall))
                    }
                    Spacer(modifier = Modifier.weight(1f))
                    val maxStar = ally.star >= ally.starLimit
                    val num = if (ally.inGame) {
                        BattleManager.allyGameData.filter { it.mainId == ally.mainId }.size - 1
                    } else {
                        (ally.num - 1)
                    }
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Text(
                            text = stringResource(id = R.string.same_card) + num + "/" + ally.starUpNum,
                            style = MaterialTheme.typography.h3,
                            color = Color.Black
                        )
                        if (!ally.inGame) {
                            Spacer(modifier = Modifier.size(paddingSmall))
                            DiamondPoint(cost = ally.starUpRes)
                        }
                    }
                    Spacer(modifier = Modifier.size(paddingSmall))

                    val buttonText =
                        if (maxStar) GameApp.instance.getWrapString(R.string.star_max) else if (ally.inGame) GameApp.instance.getWrapString(
                            R.string.star_up_in_game
                        ) else GameApp.instance.getWrapString(
                            R.string.star_up
                        )
                    val enabled = !ally.peek && ally.starUpNum != 0 && num >= ally.starUpNum && !maxStar && if (!ally.inGame) {
                        AwardManager.diamond.value >= ally.starUpRes
                    } else {
                        true
                    }
                    GameButton(text = buttonText,
                        buttonStyle = ButtonStyle.Orange,
                        enabled = enabled,
                        onClick = {
                            if (ally.peek) {
                                GameApp.instance.getWrapString(R.string.peek_tips).toast()
                            } else {
                                if (ally.inGame) {
                                    GameApp.globalScope.launch(Dispatchers.Main) {
                                        BattleManager.starUp(ally)?.let {
                                            show.value = null
                                            Dialogs.allyDetailDialog.value = it
                                        }
                                    }
                                } else {
                                    repo.allyManager.upgrade(ally)?.let {
                                        show.value = null
                                        Dialogs.allyDetailDialog.value = it
                                    }
                                }
                            }
                        })
                    Spacer(modifier = Modifier.size(paddingMedium))
                }?: Text(text = stringResource(R.string.star_max), style = MaterialTheme.typography.h3, color = Color.Black)
            }
        }
    }
}