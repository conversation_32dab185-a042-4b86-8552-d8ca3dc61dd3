package com.moyu.chuanqirensheng.screen.filter

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.award.toAward
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.text.getSkillElementName
import com.moyu.chuanqirensheng.ui.theme.SkillStoryColor
import com.moyu.chuanqirensheng.ui.theme.imageMedium
import com.moyu.chuanqirensheng.ui.theme.toQualityColor
import com.moyu.core.model.ally.Ally
import com.moyu.core.model.event.Event
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.isAdventure
import com.moyu.core.model.skill.isBattle
import com.moyu.core.model.skill.quality
import com.moyu.core.model.tcg.TcgCard


data class ItemOrder<T, R : Comparable<R>>(
    val name: String,
    val color: Color,
    val excludeTypes: List<Int>,
    val order: ((T)-> R)? = null,
)

val allyOrderList = listOf<ItemOrder<Ally, String>>(
    ItemOrder(GameApp.instance.getWrapString(R.string.none), Color.White, listOf(1, 2)),
    ItemOrder(GameApp.instance.getWrapString(R.string.name), Color.White, listOf(1, 2)) {
        it.name
    },
    ItemOrder(GameApp.instance.getWrapString(R.string.star_level), Color.White, listOf(1, 2)) {
        it.star.toString()
    },
    ItemOrder(GameApp.instance.getWrapString(R.string.quality), Color.White, listOf(1, 2)) {
        it.quality.toString()
    },
    ItemOrder(GameApp.instance.getWrapString(R.string.talent_gift), Color.White, listOf(1, 2)) {
        if (it.isTalentGift()) "1" else "2"
    },
)

val skillOrderList = listOf<ItemOrder<Skill, String>>(
    ItemOrder(GameApp.instance.getWrapString(R.string.none), Color.White, listOf(1, 2)),
    ItemOrder(GameApp.instance.getWrapString(R.string.name), Color.White, listOf(1, 2)) {
        it.name
    },
    ItemOrder(GameApp.instance.getWrapString(R.string.star_level), Color.White, listOf(1, 2)) {
        it.level.toString()
    },
    ItemOrder(GameApp.instance.getWrapString(R.string.quality), Color.White, listOf(1, 2)) { skill->
        val quality = repo.gameCore.getScrollPool().firstOrNull { it.id == skill.id }?.quality
        quality.toString()
    },
    ItemOrder(GameApp.instance.getWrapString(R.string.number), Color.White, listOf(1, 2)) {
        it.num.toString()
    },
)

val equipOrderList = listOf<ItemOrder<Skill, String>>(
    ItemOrder(GameApp.instance.getWrapString(R.string.none), Color.White, listOf(1, 2)),
    ItemOrder(GameApp.instance.getWrapString(R.string.name), Color.White, listOf(1, 2)) {
        it.name
    },
    ItemOrder(GameApp.instance.getWrapString(R.string.star_level), Color.White, listOf(1, 2)) {
        it.level.toString()
    },
    ItemOrder(GameApp.instance.getWrapString(R.string.quality), Color.White, listOf(1, 2)) {
        it.quality().toString()
    },
    ItemOrder(GameApp.instance.getWrapString(R.string.number), Color.White, listOf(1, 2)) {
        it.num.toString()
    },
)

data class ItemFilter<T>(
    val name: String,
    val color: Color,
    val excludeTypes: List<Int>,
    val filter: (T) -> Boolean
)

val allyFilterList = listOf<ItemFilter<Ally>>(
    ItemFilter(GameApp.instance.getWrapString(R.string.all), Color.White, listOf(1, 2, 3)) { true },
    ItemFilter(
        GameApp.instance.getWrapString(com.moyu.core.R.string.race1),
        Color.White,
        listOf(1)
    ) { card -> repo.gameCore.getRaceById(card.id).raceType == 1 },
    ItemFilter(
        GameApp.instance.getWrapString(com.moyu.core.R.string.race2),
        Color.White,
        listOf(1)
    ) { card -> repo.gameCore.getRaceById(card.id).raceType == 2 },
    ItemFilter(
        GameApp.instance.getWrapString(com.moyu.core.R.string.race3),
        Color.White,
        listOf(1)
    ) { card -> repo.gameCore.getRaceById(card.id).raceType == 3 },
    ItemFilter(
        GameApp.instance.getWrapString(com.moyu.core.R.string.race4),
        Color.White,
        listOf(1)
    ) { card -> repo.gameCore.getRaceById(card.id).raceType == 4 },
    ItemFilter(
        GameApp.instance.getWrapString(com.moyu.core.R.string.race5),
        Color.White,
        listOf(1)
    ) { card -> repo.gameCore.getRaceById(card.id).raceType == 5 },
    ItemFilter(GameApp.instance.getWrapString(R.string.quality_max), 3.toQualityColor(), listOf(2)) { card -> card.quality == 3 },
    ItemFilter(GameApp.instance.getWrapString(R.string.quality_high), 2.toQualityColor(), listOf(2)) { card -> card.quality == 2 },
    ItemFilter(GameApp.instance.getWrapString(R.string.quality_normal), 1.toQualityColor(), listOf(2)) { card -> card.quality == 1 },
    ItemFilter("0" + GameApp.instance.getWrapString(R.string.star), SkillStoryColor, listOf(3)) { card -> card.star == 0 },
    ItemFilter("1" + GameApp.instance.getWrapString(R.string.star), SkillStoryColor, listOf(3)) { card -> card.star == 1 },
    ItemFilter("2" + GameApp.instance.getWrapString(R.string.star), SkillStoryColor, listOf(3)) { card -> card.star == 2 },
    ItemFilter("3" + GameApp.instance.getWrapString(R.string.star), SkillStoryColor, listOf(3)) { card -> card.star == 3 },
    ItemFilter("4" + GameApp.instance.getWrapString(R.string.star), SkillStoryColor, listOf(3)) { card -> card.star == 4 },
    ItemFilter("5" + GameApp.instance.getWrapString(R.string.star), SkillStoryColor, listOf(3)) { card -> card.star == 5 },
)

val skillFilterList = listOf<ItemFilter<Skill>>(
    ItemFilter(GameApp.instance.getWrapString(R.string.all), Color.White, listOf(1, 2, 3)) { true },
    ItemFilter("0" + GameApp.instance.getWrapString(R.string.star), SkillStoryColor, listOf(3)) { card -> card.level == 0 },
    ItemFilter("1" + GameApp.instance.getWrapString(R.string.star), SkillStoryColor, listOf(3)) { card -> card.level == 1 },
    ItemFilter("2" + GameApp.instance.getWrapString(R.string.star), SkillStoryColor, listOf(3)) { card -> card.level == 2 },
    ItemFilter("3" + GameApp.instance.getWrapString(R.string.star), SkillStoryColor, listOf(3)) { card -> card.level == 3 },
    ItemFilter("4" + GameApp.instance.getWrapString(R.string.star), SkillStoryColor, listOf(3)) { card -> card.level == 4 },
    ItemFilter("5" + GameApp.instance.getWrapString(R.string.star), SkillStoryColor, listOf(3)) { card -> card.level == 5 },
    ItemFilter(GameApp.instance.getWrapString(R.string.skill_type1), Color.White, listOf(1)) { card -> card.elementType == 1 },
    ItemFilter(GameApp.instance.getWrapString(R.string.skill_type2), Color.White, listOf(1)) { card -> card.elementType == 2 },
    ItemFilter(GameApp.instance.getWrapString(R.string.skill_type3), Color.White, listOf(1)) { card -> card.elementType == 3 },
    ItemFilter(GameApp.instance.getWrapString(R.string.skill_type4), Color.White, listOf(1)) { card -> card.elementType == 4 },
    ItemFilter(GameApp.instance.getWrapString(R.string.skill_type5), Color.White, listOf(1)) { card -> card.elementType == 5 },
    ItemFilter(GameApp.instance.getWrapString(R.string.skill_tag1), Color.White, listOf(2)) { card -> card.skillTagIds.contains(1) },
    ItemFilter(GameApp.instance.getWrapString(R.string.skill_tag2), Color.White, listOf(2)) { card -> card.skillTagIds.contains(2) },
    ItemFilter(GameApp.instance.getWrapString(R.string.skill_tag3), Color.White, listOf(2)) { card -> card.skillTagIds.contains(3) },
    ItemFilter(GameApp.instance.getWrapString(R.string.skill_tag4), Color.White, listOf(2)) { card -> card.skillTagIds.contains(4) },
    ItemFilter(GameApp.instance.getWrapString(R.string.skill_tag5), Color.White, listOf(2)) { card -> card.skillTagIds.contains(5) },
    ItemFilter(GameApp.instance.getWrapString(R.string.skill_tag6), Color.White, listOf(2)) { card -> card.skillTagIds.contains(6) },
    ItemFilter(GameApp.instance.getWrapString(R.string.skill_tag7), Color.White, listOf(2)) { card -> card.skillTagIds.contains(7) },
    ItemFilter(GameApp.instance.getWrapString(R.string.skill_tag8), Color.White, listOf(2)) { card -> card.skillTagIds.contains(8) },
)

val advSkillFilterList = listOf<ItemFilter<Skill>>(
    ItemFilter(GameApp.instance.getWrapString(R.string.all), Color.White, listOf(1, 2, 3)) { true },
    ItemFilter(1.getSkillElementName(), Color.White, listOf(1)) { card -> card.elementType == 1 },
    ItemFilter(2.getSkillElementName(), Color.White, listOf(1)) { card -> card.elementType == 2 },
    ItemFilter(3.getSkillElementName(), Color.White, listOf(1)) { card -> card.elementType == 3 },
    ItemFilter(4.getSkillElementName(), Color.White, listOf(1)) { card -> card.elementType == 4 },
    ItemFilter(5.getSkillElementName(), Color.White, listOf(1)) { card -> card.elementType == 5 },
    ItemFilter(6.getSkillElementName(), Color.White, listOf(1)) { card -> card.elementType == 6 },
    ItemFilter(7.getSkillElementName(), Color.White, listOf(1)) { card -> card.elementType == 7 },
)

val equipFilterList = listOf<ItemFilter<Skill>>(
    ItemFilter(GameApp.instance.getWrapString(R.string.all), Color.White, listOf(1, 2, 3)) { true },
    ItemFilter(GameApp.instance.getWrapString(R.string.quality_max), 3.toQualityColor(), listOf(2)) { card -> card.quality() == 3 },
    ItemFilter(GameApp.instance.getWrapString(R.string.quality_high), 2.toQualityColor(), listOf(2)) { card -> card.quality() == 2 },
    ItemFilter(GameApp.instance.getWrapString(R.string.quality_normal), 1.toQualityColor(), listOf(2)) { card -> card.quality() == 1 },
    ItemFilter("0" + GameApp.instance.getWrapString(R.string.star), SkillStoryColor, listOf(3)) { card -> card.level == 0 },
    ItemFilter("1" + GameApp.instance.getWrapString(R.string.star), SkillStoryColor, listOf(3)) { card -> card.level == 1 },
    ItemFilter("2" + GameApp.instance.getWrapString(R.string.star), SkillStoryColor, listOf(3)) { card -> card.level == 2 },
    ItemFilter("3" + GameApp.instance.getWrapString(R.string.star), SkillStoryColor, listOf(3)) { card -> card.level == 3 },
    ItemFilter("4" + GameApp.instance.getWrapString(R.string.star), SkillStoryColor, listOf(3)) { card -> card.level == 4 },
    ItemFilter("5" + GameApp.instance.getWrapString(R.string.star), SkillStoryColor, listOf(3)) { card -> card.level == 5 },
)

val eventFilterList = listOf<ItemFilter<Event>>(
    ItemFilter("全部", Color.White, listOf(1, 2, 3, 4)) { true },
    ItemFilter("发展", Color.White, listOf(1)) { card -> card.play == 1 },
    ItemFilter("政策", Color.White, listOf(1)) { card -> card.play == 2 },
    ItemFilter("建造", Color.White, listOf(1)) { card -> card.play == 3 },
    ItemFilter("征兵", Color.White, listOf(1)) { card -> card.play == 4 },
    ItemFilter("雇佣兵营", Color.White, listOf(1)) { card -> card.play == 5 },
    ItemFilter("学院", Color.White, listOf(1)) { card -> card.play == 6 },
    ItemFilter("招募", Color.White, listOf(1)) { card -> card.play == 7 },
    ItemFilter("外交", Color.White, listOf(1)) { card -> card.play == 8 },
    ItemFilter("天灾", Color.White, listOf(1)) { card -> card.play == 9 },
    ItemFilter("危机", Color.White, listOf(1)) { card -> card.play == 10 },
    ItemFilter("谈判", Color.White, listOf(1)) { card -> card.play == 11 },
    ItemFilter("暴动", Color.White, listOf(1)) { card -> card.play == 12 },
    ItemFilter("世界局势", Color.White, listOf(1)) { card -> card.play == 31 },
    ItemFilter("抵抗入侵", Color.Green, listOf(1)) { card -> card.play == 21 },
    ItemFilter("遭遇战", Color.Green, listOf(1)) { card -> card.play == 22 },
    ItemFilter("战役", Color.Green, listOf(1)) { card -> card.play == 23 },
    ItemFilter("攻城战", Color.Green, listOf(1)) { card -> card.play == 24 },
    ItemFilter("奇袭战", Color.Green, listOf(1)) { card -> card.play == 25 },
    ItemFilter("演习", Color.Green, listOf(1)) { card -> card.play == 26 },
    ItemFilter(
        "掉落声望",
        Color.Red,
        listOf(2)
    ) { card -> card.toAward(true).reputations.any { it != 0 } },
    ItemFilter(
        "掉落特产",
        Color.Red,
        listOf(2)
    ) { card -> card.toAward(true).badges.any { it != 0 } },
    ItemFilter("掉落科技", Color.Red, listOf(2)) { card -> card.toAward(true).property.science != 0 },
    ItemFilter("掉落政治", Color.Red, listOf(22)) { card -> card.toAward(true).property.politics != 0 },
    ItemFilter("掉落军事", Color.Red, listOf(2)) { card -> card.toAward(true).property.military != 0 },
    ItemFilter("掉落宗教", Color.Red, listOf(2)) { card -> card.toAward(true).property.religion != 0 },
    ItemFilter("掉落商业", Color.Red, listOf(2)) { card -> card.toAward(true).property.commerce != 0 },
    ItemFilter("掉落艺术", Color.Red, listOf(2)) { card -> card.toAward(true).property.art != 0 },
    ItemFilter("掉落人口", Color.Red, listOf(2)) { card -> card.toAward(true).property.population != 0 },
    ItemFilter(
        "科技卡",
        Color.Black,
        listOf(4)
    ) { card -> card.toAward(true).skills.any { it.isAdventure() && it.elementType == 1 } },
    ItemFilter(
        "政策卡",
        Color.Black,
        listOf(4)
    ) { card -> card.toAward(true).skills.any { it.isAdventure() && it.elementType == 2 } },
    ItemFilter(
        "宗教卡",
        Color.Black,
        listOf(4)
    ) { card -> card.toAward(true).skills.any { it.isAdventure() && it.elementType == 3 } },
    ItemFilter(
        "建筑卡",
        Color.Black,
        listOf(4)
    ) { card -> card.toAward(true).skills.any { it.isAdventure() && it.elementType == 4 } },
    ItemFilter(
        "地理卡",
        Color.Black,
        listOf(4)
    ) { card -> card.toAward(true).skills.any { it.isAdventure() && it.elementType == 5 } },
    ItemFilter(
        "天灾卡",
        Color.Black,
        listOf(4)
    ) { card -> card.toAward(true).skills.any { it.isAdventure() && it.elementType == 6 } },
    ItemFilter(
        "危机卡",
        Color.Black,
        listOf(4)
    ) { card -> card.toAward(true).skills.any { it.isAdventure() && it.elementType == 7 } },
    ItemFilter(
        "战术卡",
        Color.Black,
        listOf(4)
    ) { card -> card.toAward(true).skills.any { it.isBattle() } },
    ItemFilter("获得史诗人物", Color.Yellow, listOf(3)) { card -> card.toAward(true).heroes.isNotEmpty() },
    ItemFilter("遗弃史诗人物", Color.Yellow, listOf(3)) { card -> card.toAward(true).loseHeroes.isNotEmpty() },
    ItemFilter("局外史诗人物", Color.Yellow, listOf(3)) { card -> card.toAward(true).outHeroes.isNotEmpty() },
    ItemFilter("获得军团卡", Color.Yellow, listOf(3)) { card -> card.toAward(true).allies.isNotEmpty() },
    ItemFilter("遗弃军团卡", Color.Yellow, listOf(3)) { card -> card.toAward(true).loseAllies.isNotEmpty() },
    ItemFilter("局外军团卡", Color.Yellow, listOf(3)) { card -> card.toAward(true).outAllies.isNotEmpty() },
    ItemFilter("获得技能", Color.Yellow, listOf(3)) { card -> card.toAward(true).skills.isNotEmpty() },
    ItemFilter("遗弃技能", Color.Yellow, listOf(3)) { card -> card.toAward(true).loseSkills.isNotEmpty() },
    ItemFilter("局外技能", Color.Yellow, listOf(3)) { card -> card.toAward(true).outSkills.isNotEmpty() },
    ItemFilter("Tcg", Color.Yellow, listOf(3)) { card -> card.toAward(true).tcgs.isNotEmpty() },
    ItemFilter("填充", Color.Yellow, listOf(3)) { card -> card.toAward(true).outHeroes.isNotEmpty() },
    ItemFilter("填充", Color.Yellow, listOf(3)) { card -> card.toAward(true).outHeroes.isNotEmpty() },
    ItemFilter("填充", Color.Yellow, listOf(3)) { card -> card.toAward(true).outHeroes.isNotEmpty() },
    ItemFilter("填充", Color.Yellow, listOf(3)) { card -> card.toAward(true).outHeroes.isNotEmpty() },
    ItemFilter("填充", Color.Yellow, listOf(3)) { card -> card.toAward(true).outHeroes.isNotEmpty() },
    ItemFilter("填充", Color.Yellow, listOf(3)) { card -> card.toAward(true).outHeroes.isNotEmpty() },
    ItemFilter("填充", Color.Yellow, listOf(3)) { card -> card.toAward(true).outHeroes.isNotEmpty() },
    ItemFilter("填充", Color.Yellow, listOf(3)) { card -> card.toAward(true).outHeroes.isNotEmpty() },
)

@Composable
fun CommonFilterView(modifier: Modifier = Modifier, showFilter: MutableState<Boolean>) {
    EffectButton(
        modifier = modifier, onClick = {
            showFilter.value = !showFilter.value
        }) {
        Image(
            modifier = Modifier.size(imageMedium),
            painter = painterResource(id = R.drawable.hero_icon_filter),
            contentDescription = stringResource(R.string.filter)
        )
    }
}

@Composable
fun CommonOrderView(modifier: Modifier = Modifier, showFilter: MutableState<Boolean>) {
    EffectButton(
        modifier = modifier, onClick = {
            showFilter.value = !showFilter.value
        }) {
        Image(
            modifier = Modifier.size(imageMedium),
            painter = painterResource(id = R.drawable.hero_icon_order),
            contentDescription = stringResource(R.string.order)
        )
    }
}

