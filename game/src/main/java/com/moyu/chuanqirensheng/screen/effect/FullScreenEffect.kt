package com.moyu.chuanqirensheng.screen.effect

import androidx.compose.animation.core.CubicBezierEasing
import androidx.compose.animation.core.Easing
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.TweenSpec
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.animateIntAsState
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.speed.GameSpeedManager
import com.moyu.chuanqirensheng.screen.common.CardSize
import com.moyu.chuanqirensheng.screen.sensor.ShadowedView
import com.moyu.chuanqirensheng.screen.skill.SingleDetailSkillCard
import com.moyu.chuanqirensheng.ui.theme.gapSmallPlus
import com.moyu.chuanqirensheng.ui.theme.paddingLargePlus
import com.moyu.chuanqirensheng.ui.theme.turnEffectWidth
import com.moyu.chuanqirensheng.ui.theme.upgradeEffectWidth
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.chuanqirensheng.util.screenWidthInPixel
import com.moyu.core.model.skill.Skill
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

val VeryFastOutSlowInEasing: Easing = CubicBezierEasing(0f, 1f, 1f, 0f)


val newTurnEffectState = mutableStateOf<Pair<String, Int>?>(null)
val dialogEffectState = mutableStateOf<Pair<String, Int>?>(null)
val castSkillEffectState = mutableStateOf(0)

val winBattleEffect = Pair("effect10_", 16)
val loseBattleEffect = Pair("effect11_", 6)
val starUpEffect = Pair("starup_", 9)
val levelUpEffect = Pair("effect4_", 33)
val turnEffect = Pair("effect9_", 9)

val cardEffects = mutableStateOf<Skill?>(null)

fun restartEffect(state: MutableState<Pair<String, Int>?>, effect: Pair<String, Int>) {
    GameApp.globalScope.launch {
        state.value = null
        delay(200)
        state.value = effect
    }
}

@Composable
fun DialogUpgradeEffect(modifier: Modifier) {
    val enabled = dialogEffectState.value != null
    val effectIndex by animateIntAsState(
        targetValue = when {
            enabled -> dialogEffectState.value!!.second
            else -> 1
        },
        animationSpec = TweenSpec(
            durationMillis = if (enabled) 500 else 0,
            easing = LinearEasing
        ),
        finishedListener = {
            dialogEffectState.value = null
        }, label = ""
    )
    dialogEffectState.value?.let { effect ->
        Image(
            modifier = modifier.width(upgradeEffectWidth),
            contentScale = ContentScale.FillWidth,
            painter = painterResource(
                id = getImageResourceDrawable(
                    "${effect.first}${effectIndex}"
                )
            ),
            contentDescription = null
        )
    }
}


@Composable
fun NewTurnEffect() {
    val enabled = newTurnEffectState.value != null
    val effectIndex by animateIntAsState(
        targetValue = when {
            enabled -> newTurnEffectState.value!!.second
            else -> 1
        },
        animationSpec = TweenSpec(
            durationMillis = if (enabled) 350 else 0,
            easing = LinearEasing
        ),
        finishedListener = {
            newTurnEffectState.value = null
        }, label = ""
    )
    newTurnEffectState.value?.let { effect ->
        Image(
            modifier = Modifier
                .width(turnEffectWidth)
                .graphicsLayer {
                    clip = false
                },
            contentScale = ContentScale.FillWidth,
            painter = painterResource(
                id = getImageResourceDrawable(
                    "${effect.first}${effectIndex}"
                )
            ),
            contentDescription = null
        )
    }
}

// todo 这里如果delay会有问题，导致后续技能不触发，非常奇怪
suspend fun setCardEffect(card: Skill) {
    cardEffects.value = card
//    delay(GameSpeedManager.longDuration() + 200)
//    cardEffects.value = null
}

@Composable
fun FullScreenDrawCardEffect() {
    val card = cardEffects.value
    val effectIndex by animateFloatAsState(
        targetValue = when {
            card != null -> 2f
            else -> 0f
        },
        animationSpec = TweenSpec(
            durationMillis = (if (card != null) (GameSpeedManager.longDuration()).toInt() else 0).toInt(),
            easing = VeryFastOutSlowInEasing
        ),
        finishedListener = {
            cardEffects.value = null
        }, label = ""
    )
    cardEffects.value?.let {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clickable { }
                .padding(bottom = paddingLargePlus),
            contentAlignment = Alignment.BottomCenter
        ) {
            Column(
                Modifier
                    .graphicsLayer {
                        translationX = screenWidthInPixel * (1f - effectIndex)
                    },
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                cardEffects.value?.let {
                    ShadowedView(
                        modifier = Modifier
                            .scale(0.6f), cardSize = CardSize . Large
                    ) {
                        SingleDetailSkillCard(
                            Modifier,
                            CardSize.Large,
                            it
                        )
                    }
                }
                Text(
                    modifier = Modifier
                        .graphicsLayer {
                            translationY = -gapSmallPlus.toPx()
                        },
                    text = stringResource(R.string.effecting),
                    style = MaterialTheme.typography.h1
                )
            }
        }
    }
}