package com.moyu.chuanqirensheng.screen.dialog

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.equip.SearchView
import com.moyu.chuanqirensheng.screen.filter.CommonFilterView
import com.moyu.chuanqirensheng.screen.filter.FilterLayout
import com.moyu.chuanqirensheng.screen.filter.ItemFilter
import com.moyu.chuanqirensheng.screen.filter.skillFilterList
import com.moyu.chuanqirensheng.screen.skill.SingleSkillView
import com.moyu.chuanqirensheng.ui.theme.gapSmallPlus
import com.moyu.chuanqirensheng.ui.theme.paddingLargePlus
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.core.model.skill.Skill
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun SelectSkillToGameDialog(show: MutableState<Boolean>) {
    show.value.takeIf { show.value }?.let {
        val search = remember {
            mutableStateOf("")
        }
        val showFilter = remember {
            mutableStateOf(false)
        }
        val filter = remember {
            mutableStateListOf<ItemFilter<Skill>>()
        }
        val list = repo.skillManager.data.filter {
            if (search.value.isNotEmpty()) {
                it.name.contains(search.value)
            } else true
        }.filter { ally ->
            filter.all { it.filter.invoke(ally) }
        }
        CommonDialog(title = stringResource(R.string.select_init_battle_skill), onDismissRequest = {
            show.value = false
        }) {
            Column(
                modifier = Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                if (list.size > 8) {
                    Row(
                        Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Spacer(modifier = Modifier.size(paddingSmall))
                        SearchView(search)
                        CommonFilterView(
                            Modifier.padding(end = paddingMedium), showFilter
                        )
                    }
                }
                Spacer(modifier = Modifier.size(paddingMedium))
                LazyVerticalGrid(modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
                    columns = GridCells.Fixed(4),
                    content = {
                        items(list.size) { index ->
                            val skill = list[index]
                            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                                SingleSkillView(skill = skill, itemSize = ItemSize.LargePlus)
                                val button = if (BattleManager.getGameSkills()
                                        .any { it.id == skill.id }
                                ) stringResource(R.string.cancel) else stringResource(id = R.string.do_select)
                                Spacer(modifier = Modifier.size(paddingSmall))
                                GameButton(text = button, buttonStyle = ButtonStyle.Orange, buttonSize = ButtonSize.Small, onClick = {
                                    GameApp.globalScope.launch(Dispatchers.Main) {
                                        BattleManager.selectToGame(skill)
                                    }
                                })
                                Spacer(modifier = Modifier.size(paddingLargePlus))
                            }
                        }
                    })
            }
            FilterLayout(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(top = gapSmallPlus, end = paddingMedium),
                show = showFilter,
                filter = filter,
                filterList = skillFilterList
            )
        }
    }
}


