package com.moyu.chuanqirensheng.screen.event

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.event.isWorldEvent
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.GameLabel
import com.moyu.chuanqirensheng.text.playNameMap
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.core.model.property.AdventureProps
import com.moyu.core.model.sell.Award
import kotlinx.coroutines.launch


@Composable
fun EventLabelView() {
    GameLabel {
        val title = EventManager.getEventSelectTitle()
        Row(verticalAlignment = Alignment.Bottom) {
            Text(
                text = title, style = MaterialTheme.typography.h1, color = Color.Black
            )
            if (EventManager.selectionEvents.firstOrNull()?.same == 1
                && EventManager.selectionEvents.firstOrNull()?.isWorldEvent() != true
            ) {
                Text(
                    text = "[" + playNameMap[EventManager.selectionEvents.first().play] + "]",
                    style = MaterialTheme.typography.h3,
                    color = Color.Black
                )
            }
        }
    }
}