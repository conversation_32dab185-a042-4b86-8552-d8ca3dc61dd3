package com.moyu.chuanqirensheng.screen.login

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import com.moyu.chuanqirensheng.screen.effect.StrokedText
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.ally.InitAllyCardsView
import com.moyu.chuanqirensheng.screen.ally.MAX_ALLY_SKILL_SIZE
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.screen.skill.InitHeroCardView
import com.moyu.chuanqirensheng.ui.theme.createCountryPanelHeight
import com.moyu.chuanqirensheng.ui.theme.padding7
import com.moyu.chuanqirensheng.ui.theme.paddingSmallPlus
import com.moyu.chuanqirensheng.ui.theme.paddingTiny
import com.moyu.core.GameCore
import com.moyu.core.music.SoundEffect
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch


@Composable
fun CreateCountryScreen() {
    GameBackground(title = stringResource(R.string.create_country)) {
        Column(
            Modifier.fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.SpaceEvenly
        ) {
            InitAllyCardLayout()
            InitHeroCardLayout()
            CreateCountryButtonLayout()
        }
    }
}

@Composable
fun CreateCountryButtonLayout() {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        GameButton(
            text = stringResource(R.string.start_game),
            buttonStyle = ButtonStyle.RoundedRed,
            buttonSize = ButtonSize.Huge,
            onClick = {
                if (BattleManager.getGameAllies().isEmpty()) {
                    GameApp.instance.getWrapString(R.string.ally_tips).toast()
                } else if (BattleManager.getGameAllies().size < 3) {
                    Dialogs.alertDialog.value =
                        CommonAlert(
                            content = GameApp.instance.getWrapString(R.string.ally_tips_contents),
                            onConfirm = {
                                repo.startGame()
                                GameCore.instance.onBattleEffect(SoundEffect.StartGame)
                            })
                } else {
                    repo.startGame()
                    GameCore.instance.onBattleEffect(SoundEffect.StartGame)
                }
            })
    }
}

@Composable
fun InitHeroCardLayout() {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .fillMaxWidth()
            .height(createCountryPanelHeight)
            .paint(
                painterResource(id = R.drawable.common_frame_big),
                contentScale = ContentScale.FillBounds
            )
    ) {
        Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
            Column {
                Spacer(modifier = Modifier.size(paddingSmallPlus))
                StrokedText(
                    text = stringResource(R.string.choose_person),
                    style = MaterialTheme.typography.h3,
                    modifier = Modifier
                        .align(Alignment.Start)
                        .padding(start = padding7)
                )
                Spacer(modifier = Modifier.size(paddingTiny))
            }
        }
        InitHeroCardView(
            modifier = Modifier.fillMaxWidth()
        )
        Spacer(modifier = Modifier.size(paddingSmallPlus))
    }
}

@Composable
fun InitAllyCardLayout() {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .fillMaxWidth()
            .height(createCountryPanelHeight)
            .paint(
                painterResource(id = R.drawable.common_frame_big),
                contentScale = ContentScale.FillBounds
            )
    ) {
        Spacer(modifier = Modifier.size(paddingSmallPlus))
        StrokedText(
            text = stringResource(R.string.choose_tips2), style = MaterialTheme.typography.h3,
            modifier = Modifier
                .align(Alignment.Start)
                .padding(start = padding7)
        )
        Spacer(modifier = Modifier.size(paddingTiny))
        InitAllyCardsView(modifier = Modifier
            .fillMaxWidth(),
            allies = BattleManager.getGameAllies(),
            capacity = MAX_ALLY_SKILL_SIZE,
            allyClick = {
                GameApp.globalScope.launch(Dispatchers.Main) {
                    BattleManager.selectToGame(it)
                }
            }) {
            Dialogs.selectAllyToGameDialog.value = true
        }
        Spacer(modifier = Modifier.size(paddingSmallPlus))
    }
}
