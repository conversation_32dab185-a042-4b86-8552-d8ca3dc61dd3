package com.moyu.chuanqirensheng.screen.equip

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.filter.CommonFilterView
import com.moyu.chuanqirensheng.screen.filter.CommonOrderView
import com.moyu.chuanqirensheng.screen.filter.FilterLayout
import com.moyu.chuanqirensheng.screen.filter.ItemFilter
import com.moyu.chuanqirensheng.screen.filter.OrderLayout
import com.moyu.chuanqirensheng.screen.filter.equipFilterList
import com.moyu.chuanqirensheng.screen.filter.equipOrderList
import com.moyu.chuanqirensheng.ui.theme.gapSmallPlus
import com.moyu.chuanqirensheng.ui.theme.padding80
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.core.model.skill.Skill

@Composable
fun HeroPage() {
    val search = remember {
        mutableStateOf("")
    }
    val showFilter = remember {
        mutableStateOf(false)
    }
    val filter = remember {
        mutableStateListOf<ItemFilter<Skill>>()
    }
    val showOrder = remember {
        mutableStateOf(false)
    }
    val order = remember {
        mutableStateOf(equipOrderList.first())
    }
    val list = repo.heroManager.data.filter {
        if (search.value.isNotEmpty()) {
            it.name.contains(search.value)
        } else true
    }.filter { ally ->
        filter.all { it.filter.invoke(ally) }
    }.sortedBy { order.value.order?.invoke(it) }
    DisposableEffect(Unit) {
        onDispose {
            repo.heroManager.setUnNew()
        }
    }
    Box(
        modifier = Modifier
            .fillMaxSize()
            .paint(
                painterResource(id = R.drawable.common_page_frame),
                contentScale = ContentScale.FillBounds
            ),
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize().padding(bottom = paddingMedium)
                .verticalScroll(rememberScrollState())
        ) {
            Spacer(modifier = Modifier.size(paddingMedium))
            Row(
                Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                CommonOrderView(
                    Modifier.padding(start = paddingMedium), showOrder
                )
                SearchView(search)
                CommonFilterView(
                    Modifier.padding(end = paddingMedium), showFilter
                )
            }
            LazyVerticalGrid(modifier = Modifier
                .weight(1f)
                .fillMaxWidth(),
                columns = GridCells.Fixed(4),
                content = {
                    items(list.size) { index ->
                        val equip = list[index]
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            Spacer(modifier = Modifier.size(paddingMedium))
                            SingleHeroView(
                                hero = equip.copy(extraInfo = if (equip.selected) stringResource(R.string.game_selected) else "",
                                ),
                                showName = true,
                                showRed = true,
                                itemSize = ItemSize.LargePlus,
                                textColor = Color.White
                            )
                            Spacer(modifier = Modifier.size(paddingMedium))
                        }
                    }
                })
            Spacer(Modifier.size(padding80))
        }
        OrderLayout(
            modifier = Modifier
                .align(Alignment.TopStart)
                .padding(top = gapSmallPlus, end = paddingMedium),
            show = showOrder,
            filter = order,
            filterList = equipOrderList
        )
        FilterLayout(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(top = gapSmallPlus, end = paddingMedium),
            show = showFilter,
            filter = filter,
            filterList = equipFilterList
        )
    }
}