package com.moyu.chuanqirensheng.screen.dialog

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.unit.Dp
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.GameSnackBar
import com.moyu.chuanqirensheng.screen.effect.DialogUpgradeEffect
import com.moyu.chuanqirensheng.screen.effect.dialogEffectState
import com.moyu.chuanqirensheng.ui.theme.bigButtonHeight
import com.moyu.chuanqirensheng.ui.theme.detailBigHeight
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.imageSmallPlus
import com.moyu.chuanqirensheng.ui.theme.padding0
import com.moyu.chuanqirensheng.ui.theme.padding48
import com.moyu.chuanqirensheng.ui.theme.paddingLarge
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingSmall

// 需要一个全局的关闭弹窗的事件
val dialogClose = mutableStateOf(0)

@Composable
fun CommonDialog(
    title: String = "",
    titleColor: Color = Color.Black,
    heightInDp: Dp = detailBigHeight,
    clickFrame: () -> Unit = {},
    extraIcon: Int? = null,
    extraIconClick: (() -> Unit)? = null,
    frame: Int? = R.drawable.common_window,
    noPadding: Boolean = false,
    onDismissRequest: () -> Unit = EMPTY_DISMISS,
    content: @Composable BoxScope.() -> Unit
) {
    DisposableEffect(Unit) {
        onDispose {
            dialogEffectState.value = null
            dialogClose.value += 1
        }
    }
    CloseHintDialog(onDismissRequest = onDismissRequest) {
        EffectButton(
            modifier = Modifier
                .padding(horizontal = if (noPadding) padding0 else paddingSmall)
                .fillMaxWidth()
                .height(heightInDp)
                .padding(
                    horizontal = if (noPadding) padding0 else paddingLarge,
                    vertical = if (noPadding) padding0 else paddingMedium
                ),
            onClick = clickFrame
        ) {
            frame?.let {
                Image(
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.FillBounds,
                    painter = painterResource(frame),
                    contentDescription = null
                )
            }
            Column(
                modifier = Modifier.padding(if (noPadding) padding0 else paddingSmall),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(bigButtonHeight)
                ) {
                    Row(
                        Modifier.align(Alignment.BottomCenter),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            modifier = Modifier,
                            text = title,
                            style = MaterialTheme.typography.h1,
                            color = titleColor
                        )
                        extraIcon?.let {
                            EffectButton(modifier = Modifier
                                .padding(start = paddingSmall)
                                .size(imageSmallPlus), onClick = {
                                extraIconClick?.invoke()
                            }) {
                                Image(
                                    painter = painterResource(id = it),
                                    contentDescription = null
                                )
                            }
                        }
                    }
                    EffectButton(modifier = Modifier.semantics {
                        contentDescription = GameApp.instance.getWrapString(R.string.close)
                    }
                        .size(imageLarge)
                        .align(Alignment.TopEnd),
                        onClick = {
                        onDismissRequest()
                    }) {

                    }
                }
                if (!noPadding) {
                    Spacer(modifier = Modifier.size(paddingLarge))
                }
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .padding(horizontal = if (noPadding) padding0 else paddingMedium),
                    content = content
                )
                if (frame != null && !noPadding) {
                    Spacer(modifier = Modifier.size(padding48))
                }
            }
            DialogUpgradeEffect(
                modifier = Modifier
                    .align(Alignment.Center)
            )
            GameSnackBar()
        }
    }
}