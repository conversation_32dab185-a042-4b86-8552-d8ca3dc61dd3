package com.moyu.chuanqirensheng.screen.ally

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.logic.ally.getFrameDrawable
import com.moyu.chuanqirensheng.logic.ally.getTouchInfo
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.arena.RoleHpWithAnim
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.common.getTextStyle
import com.moyu.chuanqirensheng.screen.more.Stars
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.hpHeight
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.imageTinyPlus
import com.moyu.chuanqirensheng.ui.theme.padding1
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.ui.theme.paddingSmallPlus
import com.moyu.chuanqirensheng.ui.theme.paddingTiny
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.model.ally.Ally
import kotlin.math.roundToInt


@Composable
fun SingleAllyView(
    ally: Ally,
    showName: Boolean = true,
    showNum: Boolean = false,
    showHp: Boolean = false,
    extraInfo: String = "",
    textColor: Color = Color.Black,
    showRed: Boolean = false,
    showEffect: Boolean = false,
    itemSize: ItemSize = ItemSize.LargePlus,
    selectCallBack: (Ally) -> Unit = { Dialogs.allyDetailDialog.value = it }
) {
    val race = repo.gameCore.getRaceById(ally.id)
    EffectButton(modifier = Modifier, onClick = {
        selectCallBack(ally)
    }) {
        Column(modifier = Modifier, horizontalAlignment = Alignment.CenterHorizontally) {
            Box(contentAlignment = Alignment.Center) {
                Image(
                    modifier = Modifier.size(itemSize.frameSize),
                    painter = painterResource(ally.getFrameDrawable()),
                    contentDescription = null,
                )
                Image(
                    modifier = Modifier
                        .size(itemSize.itemSize)
                        .clip(RoundedCornerShape(itemSize.itemSize / 12)),
                    alignment = Alignment.TopCenter,
                    contentScale = ContentScale.Crop,
                    painter = painterResource(id = getImageResourceDrawable(race.pic)),
                    contentDescription = ally.getTouchInfo()
                )
                if (extraInfo.isNotEmpty()) {
                    Box(
                        modifier = Modifier
                            .background(B50)
                            .padding(paddingSmall)
                    ) {
                        Text(text = extraInfo, style = itemSize.getTextStyle())
                    }
                }
                Stars(
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .padding(bottom = itemSize.itemSize / 12),
                    ally.star,
                    starHeight = itemSize.itemSize / 5
                )
                if (showRed && ally.new) {
                    Image(
                        modifier = Modifier
                            .align(Alignment.TopEnd)
                            .size(imageSmall),
                        painter = painterResource(R.drawable.red_icon),
                        contentDescription = null
                    )
                }
                if (ally.equipSkills.isNotEmpty()) {
                    Row(
                        Modifier
                            .align(Alignment.TopStart)
                            .padding(paddingSmallPlus)
                    ) {
                        repeat(ally.equipSkills.size) {
                            Image(
                                modifier = Modifier
                                    .size(imageTinyPlus),
                                painter = painterResource(R.drawable.card_icon),
                                contentDescription = null
                            )
                        }
                    }
                }
                if (showEffect && ally.quality >= 3) {
                    val infiniteTransition = rememberInfiniteTransition(label = "")
                    val index = infiniteTransition.animateFloat(
                        initialValue = 1f, targetValue = 50f, animationSpec = infiniteRepeatable(
                            animation = tween(1600, easing = LinearEasing),
                            repeatMode = RepeatMode.Restart,
                        ), label = ""
                    )
                    if (index.value.roundToInt() <= 15) { // 做一个间歇的效果
                        Image(
                            modifier = Modifier
                                .size(itemSize.frameSize)
                                .scale(1.3f),
                            painter = painterResource(
                                getImageResourceDrawable(
                                    "rewarditem_${index.value.roundToInt()}"
                                )
                            ),
                            contentDescription = null
                        )
                    }
                }
            }
            if (showHp) {
                Spacer(modifier = Modifier.size(padding1))
                Box(
                    modifier = Modifier.size(itemSize.frameSize * 0.88f, hpHeight)
                ) {
                    RoleHpWithAnim { BattleManager.getRoleByAlly(ally = ally) }
                }
                Spacer(modifier = Modifier.size(paddingTiny))
            }
            if (showName) {
                Spacer(modifier = Modifier.size(padding1))
                val text = if (showNum) race.name + "x${ally.num}" else race.name
                Text(
                    text = text,
                    style = itemSize.getTextStyle(),
                    maxLines = 2,
                    minLines = 2,
                    textAlign = TextAlign.Center,
                    color = textColor
                )
            }
        }
    }
}