package com.moyu.chuanqirensheng.screen.effect

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.Dp
import com.moyu.chuanqirensheng.ui.theme.padding0
import kotlinx.coroutines.delay
import kotlin.random.Random

@Composable
fun MovableImage(
    modifier: Modifier = Modifier,
    imageResource: Int,
    contentScale: ContentScale = ContentScale.Fit,
    clipSize: Dp = padding0,
) {
    // 1) 持续呼吸动画：使用 rememberInfiniteTransition
    val infiniteTransition = rememberInfiniteTransition(label = "")

    // a) 轻微缩放：1.0 ~ 1.05，往复动画
    val scale by infiniteTransition.animateFloat(
        initialValue = 1.0f,
        targetValue = 1.05f,
        animationSpec = infiniteRepeatable(
            animation = tween(
                durationMillis = 2000,
                easing = FastOutSlowInEasing
            ),
            repeatMode = RepeatMode.Reverse
        ), label = ""
    )

    // b) 轻微左右摇摆：0° ~ 2°，往复动画
    val tiltAngle by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 2f,
        animationSpec = infiniteRepeatable(
            animation = tween(
                durationMillis = 2000,
                easing = FastOutSlowInEasing
            ),
            repeatMode = RepeatMode.Reverse
        ), label = ""
    )

    // 2) 偶尔转身：用 Animatable 来做 rotationY 的关键帧动画
    val rotationY = remember { Animatable(0f) }

    // 让这个效果在组合后就一直循环执行
    LaunchedEffect(Unit) {
        while (true) {
            // 随机等待一段时间再触发“转身”
            delay(Random.nextLong(5000, 15000))
            // 从 0 旋转到 180°
            rotationY.animateTo(
                targetValue = 180f,
                animationSpec = tween(
                    durationMillis = 800,
                    easing = FastOutSlowInEasing
                )
            )
            // 暂停一下，角色背对玩家
            delay(500)
            // 再从 180° 旋转回 0°
            rotationY.animateTo(
                targetValue = 0f,
                animationSpec = tween(
                    durationMillis = 800,
                    easing = FastOutSlowInEasing
                )
            )
        }
    }

    // 3) 将以上动画值叠加到 Image 上
    Image(
        modifier = modifier
            // 微倾斜（tiltAngle），让它往左-往右轻微摆动
            .rotate(tiltAngle)
            // 呼吸缩放
            .scale(scale)
            // graphicsLayer 做 3D 翻转
            .graphicsLayer {
                this.rotationY = rotationY.value
            }
            .clip(RoundedCornerShape(clipSize)),
        alignment = Alignment.TopCenter,
        painter = painterResource(id = imageResource),
        contentScale = contentScale,
        contentDescription = null
    )
}
