package com.moyu.chuanqirensheng.screen.dialog

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import com.moyu.chuanqirensheng.screen.effect.StrokedText
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import com.moyu.chuanqirensheng.screen.ally.SingleAllyView
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.equip.SingleHeroView
import com.moyu.chuanqirensheng.screen.skill.SingleSkillView
import com.moyu.chuanqirensheng.ui.theme.detailBigHeight
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingMediumPlus
import com.moyu.core.GameCore
import com.moyu.core.model.skill.isHeroSkill
import com.moyu.core.model.story.Story

@Composable
fun StoryDetailDialog(story: MutableState<Story?>) {
    story.value?.let { it ->
        CommonDialog(
            title = it.name,
            heightInDp = detailBigHeight * 1.1f,
            onDismissRequest = { story.value = null }
        ) {
            Column(
                modifier = Modifier.padding(horizontal = paddingMedium),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                StrokedText(
                    text = it.desc,
                    style = MaterialTheme.typography.h4,
                    color = Color.White
                )
                Spacer(modifier = Modifier.size(paddingMediumPlus))
                LazyVerticalGrid(
                    modifier = Modifier.weight(1f),
                    columns = GridCells.Fixed(4),
                    verticalArrangement = Arrangement.spacedBy(paddingMedium)
                ) {
                    items(it.showCard) { item ->
                        GameCore.instance.getAllyPool().firstOrNull { it.id == item }?.let {
                            SingleAllyView(ally = it, itemSize = ItemSize.LargePlus)
                        } ?: GameCore.instance.getSkillPool().firstOrNull { it.id == item }?.let {
                            if (it.isHeroSkill()) {
                                SingleHeroView(hero = it, itemSize = ItemSize.LargePlus)
                            } else {
                                SingleSkillView(skill = it, itemSize = ItemSize.LargePlus)
                            }
                        }
                    }
                }
            }
        }
    }
}