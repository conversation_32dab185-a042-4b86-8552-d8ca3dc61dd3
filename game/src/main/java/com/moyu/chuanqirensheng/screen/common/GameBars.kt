package com.moyu.chuanqirensheng.screen.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.GenericShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.LayoutDirection
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.ui.theme.expBarHeight
import com.moyu.chuanqirensheng.ui.theme.padding1
import com.moyu.chuanqirensheng.ui.theme.paddingTiny
import com.moyu.core.util.percentValueToDotWithOneDigits


/**
 * 血条UI组件
 */
@Composable
fun HpBar(
    currentHp: Int,
    maxHp: Int,
    totalShield: Int = 0
) {
    val realMax = if (maxHp + totalShield == 0) 100 else maxHp + totalShield
    Box {
        Row {
            Box(modifier = Modifier.fillMaxSize()) {
                val hpBuilder: Path.(size: Size, layoutDirection: LayoutDirection) -> Unit =
                    { size: Size, _: LayoutDirection ->
                        this.addRect(
                            Rect(
                                0f,
                                0f,
                                size.width * currentHp.toFloat() / realMax,
                                size.height
                            )
                        )
                    }
                Image(
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(GenericShape(hpBuilder))
                        .clip(RoundedCornerShape(paddingTiny)),
                    contentScale = ContentScale.FillBounds,
                    painter = painterResource(id = R.drawable.battle_blood_full),
                    contentDescription = null
                )
                val shieldBuilder: Path.(size: Size, layoutDirection: LayoutDirection) -> Unit =
                    { size: Size, _: LayoutDirection ->
                        this.addRect(
                            Rect(
                                size.width * currentHp.toFloat() / realMax,
                                0f,
                                size.width * (currentHp + totalShield).toFloat() / realMax,
                                size.height
                            )
                        )
                    }
                Image(
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(GenericShape(shieldBuilder))
                        .clip(RoundedCornerShape(paddingTiny)),
                    contentScale = ContentScale.FillBounds,
                    painter = painterResource(id = R.drawable.battle_blood_shield_line),
                    contentDescription = null
                )
                Image(
                    modifier = Modifier
                        .fillMaxSize(),
                    contentScale = ContentScale.FillBounds,
                    painter = painterResource(id = R.drawable.battle_blood_empty),
                    contentDescription = null
                )
            }
        }
        Text(
            text = "$currentHp/$maxHp",
            style = MaterialTheme.typography.body1,
            modifier = Modifier.align(Alignment.Center),
        )
    }
}

@Composable
fun CommonBar(
    modifier: Modifier = Modifier,
    currentValue: Int,
    maxValue: Int,
    emptyRes: Int = R.drawable.battle_blood_empty,
    fullRes: Int = R.drawable.cheat_level_line,
    showNum: Boolean = true,
    textColor: Color = Color.White,
    style: TextStyle = MaterialTheme.typography.body1,
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = modifier
    ) {
        val expBuilder: Path.(size: Size, layoutDirection: LayoutDirection) -> Unit =
            { size: Size, _: LayoutDirection ->
                this.addRect(
                    Rect(
                        0f,
                        0f,
                        size.width * currentValue.toFloat() / maxValue,
                        size.height
                    )
                )
            }
        Image(
            modifier = Modifier
                .height(expBarHeight)
                .fillMaxWidth()
                .padding(padding1)
                .clip(GenericShape(expBuilder))
                .clip(RoundedCornerShape(paddingTiny))
                .align(Alignment.CenterStart),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(id = fullRes),
            contentDescription = null
        )
        Image(
            modifier = Modifier
                .height(expBarHeight)
                .fillMaxWidth(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(id = emptyRes),
            contentDescription = null
        )
        if (showNum) {
            Text(
                text = "$currentValue/$maxValue",
                style = style,
                color = textColor
            )
        }
    }
}
