package com.moyu.chuanqirensheng.screen.dialog

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.tcg.TcgManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.ui.theme.paddingLarge
import com.moyu.chuanqirensheng.ui.theme.paddingMediumPlus
import com.moyu.chuanqirensheng.ui.theme.paddingSmallPlus
import com.moyu.chuanqirensheng.ui.theme.pvpRecordFrameHeight
import com.moyu.core.model.sell.toAward
import com.moyu.core.model.tcg.TcgAward
import com.moyu.core.model.tcg.getTypeName
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun TcgAwardDetailDialog(showDungeon: MutableState<Boolean>) {
    showDungeon.value.takeIf { showDungeon.value }?.let {
        CommonDialog(title = stringResource(R.string.tcg_award), onDismissRequest = {
            showDungeon.value = false
        }) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = paddingLarge)
                    .verticalScroll(
                        rememberScrollState()
                    ), horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Spacer(modifier = Modifier.size(paddingLarge))
                val tcgAwards = repo.gameCore.getTcgAwardPool()
                tcgAwards.forEach {
                    TcgAwardRow(it)
                    Spacer(modifier = Modifier.size(paddingSmallPlus))
                }
                Spacer(modifier = Modifier.size(paddingLarge))
            }
        }
    }
}

@Composable
fun TcgAwardRow(tcgAward: TcgAward) {
    val current = TcgManager.tcgCards.count { it.type == tcgAward.type }
    Row(
        Modifier
            .fillMaxWidth()
            .height(pvpRecordFrameHeight)
            .paint(
                painterResource(id = R.drawable.common_frame_focus),
                contentScale = ContentScale.FillBounds
            ),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Spacer(modifier = Modifier.size(paddingLarge))
        Text(
            modifier = Modifier.weight(2f),
            text = stringResource(R.string.collect) + current + "/" + tcgAward.num + stringResource(
                R.string.piece
            ) + getTypeName(tcgAward.type) + stringResource(R.string.card),
            style = MaterialTheme.typography.h5,
            color = Color.Black
        )
        Spacer(modifier = Modifier.size(paddingMediumPlus))
        val award = tcgAward.toAward()
        Box(Modifier.weight(1f)) {
            AwardList(
                award = award,
                param = defaultParam.copy(
                    showName = false,
                    numInFrame = true,
                    itemSize = ItemSize.Large
                )
            )
        }
        Spacer(modifier = Modifier.size(paddingMediumPlus))
        val key = tcgAward.id
        if (AwardManager.tcgCardRewardRecords.contains(key)) {
            Text(
                text = stringResource(id = R.string.already_got), style = MaterialTheme.typography.h4, color = Color.Black
            )
        } else {
            GameButton(text = stringResource(R.string.gain),
                enabled = current >= tcgAward.num,
                buttonStyle = ButtonStyle.Orange,
                buttonSize = ButtonSize.Small,
                onClick = {
                    if (AwardManager.tcgCardRewardRecords.contains(key)) {
                        GameApp.instance.getWrapString(R.string.already_got).toast()
                    } else if (current >= tcgAward.num) {
                        GameApp.globalScope.launch(Dispatchers.Main) {
                            AwardManager.gainTcgCardReward(tcgAward, award)
                        }
                    } else {
                        GameApp.instance.getWrapString(R.string.no_collected).toast()
                    }
                })
        }
        Spacer(modifier = Modifier.size(paddingLarge))
    }
}