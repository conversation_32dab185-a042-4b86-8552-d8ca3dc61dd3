package com.moyu.chuanqirensheng.screen.login

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.logic.sell.SellManager
import com.moyu.chuanqirensheng.logic.task.TaskManager
import com.moyu.chuanqirensheng.logic.tcg.TcgManager
import com.moyu.chuanqirensheng.logic.unlock.MENU_ALLY
import com.moyu.chuanqirensheng.logic.unlock.MENU_HISTORY
import com.moyu.chuanqirensheng.logic.unlock.MENU_MORE
import com.moyu.chuanqirensheng.logic.unlock.MENU_SELL
import com.moyu.chuanqirensheng.logic.unlock.MENU_TASK
import com.moyu.chuanqirensheng.logic.unlock.UnlockManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.ui.theme.bigButtonHeight
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.padding48
import com.moyu.chuanqirensheng.ui.theme.paddingHuge
import com.moyu.chuanqirensheng.ui.theme.paddingHugePlus
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingSmallPlus
import com.moyu.chuanqirensheng.util.FORTUNE_SCREEN
import com.moyu.chuanqirensheng.util.LIFE_SCREEN
import com.moyu.chuanqirensheng.util.MORE_SCREEN
import com.moyu.chuanqirensheng.util.QUEST_SCREEN
import com.moyu.chuanqirensheng.util.goto
import com.moyu.chuanqirensheng.util.gotoSell
import com.moyu.core.model.unlock.Unlock

@Composable
fun GameMenu(modifier: Modifier) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = padding48),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(paddingHugePlus)
    ) {
        MenuButton(
            modifier = Modifier
                .height(bigButtonHeight)
                .fillMaxWidth(),
            stringResource(id = R.string.start_game),
            unlock = null,
            drawable = R.drawable.menu1
        ) {
            repo.clickStart()
        }

        Row(
            modifier = Modifier
                .fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
        ) {
            MenuButton(
                modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f),
                text = stringResource(R.string.menu1),
                frameDrawable = R.drawable.common_button_1_new,
                unlock = repo.gameCore.getUnlockById(MENU_ALLY),
                drawable = R.drawable.menu2,
                redIcon = repo.heroManager.data.any { it.new } || repo.allyManager.data.any { it.new } || repo.skillManager.data.any { it.new }
            ) {
                goto(FORTUNE_SCREEN)
            }
            MenuButton(
                modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f),
                leftPadding = UnlockManager.getUnlockedFlow(repo.gameCore.getUnlockById(MENU_ALLY)),
                frameDrawable = R.drawable.common_button_1_new,
                text = stringResource(R.string.menu2),
                unlock = repo.gameCore.getUnlockById(MENU_HISTORY),
                redIcon = TcgManager.hasRed(), drawable = R.drawable.menu3
            ) {
                goto(LIFE_SCREEN)
            }
        }
        MenuButton(
            modifier = Modifier
                .height(bigButtonHeight)
                .fillMaxWidth(),
            text = stringResource(R.string.menu3),
            unlock = repo.gameCore.getUnlockById(MENU_SELL), drawable = R.drawable.menu4,
            redIcon = SellManager.getReds().any { it }
        ) {
            gotoSell()
        }
        Row(
            horizontalArrangement = Arrangement.SpaceBetween,
            modifier = Modifier.fillMaxWidth()
        ) {
            MenuButton(
                modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f),
                text = stringResource(id = R.string.quest),
                frameDrawable = R.drawable.common_button_1_new,
                unlock = repo.gameCore.getUnlockById(MENU_TASK),
                drawable = R.drawable.menu5,
                redIcon = (TaskManager.dailyTasks + TaskManager.oneTimeTasks + TaskManager.warPassTasks).any {
                    TaskManager.getTaskDoneFlow(it) && !it.opened
                }
            ) {
                goto(QUEST_SCREEN)
            }
            MenuButton(
                modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f),
                leftPadding = UnlockManager.getUnlockedFlow(repo.gameCore.getUnlockById(MENU_TASK)),
                frameDrawable = R.drawable.common_button_1_new,
                drawable = R.drawable.menu6,
                text = stringResource(id = R.string.more),
                unlock = repo.gameCore.getUnlockById(MENU_MORE),
            ) {
                goto(MORE_SCREEN)
            }
        }
    }
}


@Composable
fun MenuButton(
    modifier: Modifier,
    text: String = "",
    unlock: Unlock?,
    frameDrawable: Int = R.drawable.common_long_button1,
    drawable: Int,
    leftPadding: Boolean = false,
    redIcon: Boolean = false,
    onclick: () -> Unit
) {
    val locked =
        unlock?.let { !UnlockManager.getUnlockedFlow(unlock) }
            ?: false
    if (!locked && leftPadding) {
        Spacer(modifier = Modifier.width(paddingHuge))
    }
    AnimatedVisibility(modifier = modifier, visible = !locked) {
        EffectButton(
            onClick = { if (locked) (unlock?.desc ?: "").toast() else onclick() },
        ) {
            Image(
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.FillBounds,
                painter = painterResource(id = frameDrawable),
                contentDescription = null
            )
            Image(
                modifier = Modifier.size(bigButtonHeight)
                    .align(Alignment.CenterStart)
                    .scale(1.2f)
                    .graphicsLayer {
                        translationX = -paddingMedium.toPx()
                    },
                painter = painterResource(id = drawable),
                contentDescription = null
            )
            if (redIcon) {
                Image(
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .size(imageSmall),
                    painter = painterResource(R.drawable.red_icon),
                    contentDescription = null
                )
            }
            Text(
                modifier = Modifier.graphicsLayer {
                    translationX = paddingSmallPlus.toPx()
                },
                text = text,
                style = MaterialTheme.typography.h1,
                color = Color.White
            )
        }
    }
}