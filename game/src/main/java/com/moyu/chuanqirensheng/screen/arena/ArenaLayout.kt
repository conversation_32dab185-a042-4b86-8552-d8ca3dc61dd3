@file:OptIn(ExperimentalLayoutApi::class)

package com.moyu.chuanqirensheng.screen.arena

import androidx.compose.animation.core.TweenSpec
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.positionInRoot
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.speed.GameSpeedManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.effect.NewTurnEffect
import com.moyu.chuanqirensheng.screen.effect.newTurnEffectState
import com.moyu.chuanqirensheng.screen.effect.turnEffect
import com.moyu.chuanqirensheng.thread.gameDispatcher
import com.moyu.chuanqirensheng.ui.theme.animateLarge
import com.moyu.chuanqirensheng.ui.theme.buffSize
import com.moyu.chuanqirensheng.ui.theme.hpWidth
import com.moyu.chuanqirensheng.ui.theme.hugeButtonHeight
import com.moyu.chuanqirensheng.ui.theme.imageHugeLite
import com.moyu.chuanqirensheng.ui.theme.oneRoleWidth
import com.moyu.chuanqirensheng.ui.theme.padding1
import com.moyu.chuanqirensheng.ui.theme.padding93
import com.moyu.chuanqirensheng.ui.theme.paddingLarge
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.singleRoleHeight
import com.moyu.chuanqirensheng.ui.theme.singleRoleWidth
import com.moyu.chuanqirensheng.util.pixelToDp
import com.moyu.core.model.action.ActionStateType
import com.moyu.core.model.role.Role
import kotlinx.coroutines.launch

@Composable
fun BattleFieldLayout(roleMinions: () -> List<Role>, enemyMinions: () -> List<Role>) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.SpaceEvenly
    ) {
        EnemiesRow(enemyMinions)
        Spacer(modifier = Modifier.size(padding93))
        TurnView(modifier = Modifier.height(hugeButtonHeight))
        Spacer(modifier = Modifier.size(padding93))
        PlayersRow(roleMinions)
    }
}

@Composable
fun EnemiesRow(roleMinions: () -> List<Role>) {
    val roles = roleMinions()
    Row(
        Modifier
            .fillMaxWidth()
            .height(singleRoleHeight), horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        repeat(3) { index ->
            roles.filter { !it.isMinion() }.getOrNull(index)?.let {
                OneRoleInBattle(Modifier.size(oneRoleWidth, singleRoleHeight)) { it }
            } ?: let {
                if (roles.filter { !it.isMinion() }.size != 1) {
                    // 有solo,solo时候居中
                    Box(Modifier.size(oneRoleWidth, singleRoleHeight))
                }
            }
        }
    }
    Row(
        Modifier
            .fillMaxWidth()
            .height(singleRoleHeight), horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        repeat(3) { index ->
            roles.filter { !it.isMinion() }.getOrNull(index)
                ?.let { master -> roles.firstOrNull { it.masterId == master.roleId && !it.isOver() } }?.let {
                OneRoleInBattle(
                    Modifier
                        .size(oneRoleWidth, singleRoleHeight)
                        .scale(0.8f)
                ) { it }
            } ?: let {
                if (roles.filter { !it.isMinion() }.size != 1) {
                    Box(
                        Modifier
                            .size(oneRoleWidth, singleRoleHeight)
                            .scale(0.8f))
                }
            }
        }
    }
}


@Composable
fun PlayersRow(roleMinions: () -> List<Role>) {
    val roles = roleMinions()
    Row(
        Modifier
            .fillMaxWidth()
            .height(singleRoleHeight), horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        repeat(3) { index ->
            roles.filter { !it.isMinion() }.getOrNull(index)
                ?.let { master -> roles.firstOrNull { it.masterId == master.roleId  && !it.isOver() } }?.let {
                OneRoleInBattle(
                    Modifier
                        .size(oneRoleWidth, singleRoleHeight)
                        .scale(0.8f)
                ) { it }
            } ?: let {
                if (roles.filter { !it.isMinion() }.size != 1) {
                    // 有solo,solo时候居中
                    Box(
                        Modifier
                            .size(oneRoleWidth, singleRoleHeight)
                            .scale(0.8f))
                }
            }
        }
    }
    Row(
        Modifier
            .fillMaxWidth()
            .height(singleRoleHeight), horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        repeat(3) { index ->
            roles.filter { !it.isMinion() }.getOrNull(index)?.let {
                OneRoleInBattle(Modifier.size(oneRoleWidth, singleRoleHeight)) { it }
            } ?: let {
                if (roles.filter { !it.isMinion() }.size != 1) {
                    // 有solo,solo时候居中
                    Box(Modifier.size(oneRoleWidth, singleRoleHeight))
                }
            }
        }
    }
}

@Composable
fun TurnView(modifier: Modifier = Modifier) {
    val turnName = repo.battleTurn.value
    Box(modifier = modifier, contentAlignment = Alignment.Center) {
        Image(
            modifier = Modifier.height(imageHugeLite),
            contentScale = ContentScale.FillHeight,
            painter = painterResource(id = R.drawable.scroll),
            contentDescription = null
        )
        Row {

            Text(
                text = stringResource(R.string.round_tips_prefix) + turnName + stringResource(id = R.string.turn_me),
                style = MaterialTheme.typography.h3,
                color = Color.Black
            )
            if (DebugManager.singleStep) {
                Text(modifier = Modifier.clickable {
                    GameApp.globalScope.launch(gameDispatcher) {
                        repo.battle.value.nextStep()
                    }
                }, text = "下一步", style = MaterialTheme.typography.h3)
            }
        }
        LaunchedEffect(turnName) {
            newTurnEffectState.value = turnEffect
        }
        NewTurnEffect()
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun OneRoleInBattle(modifier: Modifier = Modifier, role: () -> Role) {
    val alpha by animateFloatAsState(
        animationSpec = TweenSpec(
            durationMillis = GameSpeedManager.animDuration().toInt() * 4
        ), targetValue = if (role().isDeath()) 0f else 1f, label = ""
    )
    Box(
        modifier
            .graphicsLayer {
                this.alpha = alpha
            }
            .onGloballyPositioned {
                BattleManager.battleRolePositions[role().playerId()] = it
                    .positionInRoot()
                    .let {
                        try {
                            Pair(
                                it.x
                                    .toInt()
                                    .pixelToDp() - hpWidth,
                                it.y
                                    .toInt()
                                    .pixelToDp()
                            )
                        } catch (e: Exception) {
                            Pair(padding1, padding1)
                        }
                    }
            }, contentAlignment = Alignment.Center
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            if (repo.inBattle.value) {
                FlowRow(
                    Modifier
                        .width(buffSize)
                        .verticalScroll(rememberScrollState()),
                    overflow = FlowRowOverflow.Visible,
                ) {
                    role().getShowGoodBuff().forEach { buff ->
                        OneBuffGrid(buff) {
                            Dialogs.buffDetailDialog.value = Pair(it, role())
                        }
                    }
                }
            }
            Box(
                Modifier.size(singleRoleWidth, singleRoleHeight),
                contentAlignment = Alignment.Center
            ) {
                SingleRoleInBattleLayout(roleGetter = role)
            }
            if (repo.inBattle.value) {
                FlowRow(
                    Modifier
                        .width(buffSize)
                        .verticalScroll(rememberScrollState()),
                    overflow = FlowRowOverflow.Visible,
                ) {
                    role().getShowBadBuff().forEach { buff ->
                        OneBuffGrid(buff) {
                            Dialogs.buffDetailDialog.value = Pair(it, role())
                        }
                    }
                }
            }
        }
        Box(modifier = Modifier.padding(bottom = paddingLarge)) {
            RoleStatusBeingAttackGif(role)
        }
        DamageTextLayout(roleGetter = role)
    }
}

@Composable
fun DamageTextLayout(roleGetter: () -> Role) {
    val role = roleGetter()
    val damageOffset by animateDpAsState(
        animationSpec = TweenSpec(
            durationMillis = GameSpeedManager.animDuration().toInt()
        ), targetValue = when {
            role.hasState(ActionStateType.BeingAttack) -> -animateLarge
            role.hasState(ActionStateType.BeingHeal) -> -animateLarge
            role.hasState(ActionStateType.DoSkill) -> -animateLarge
            else -> 0.dp
        }, label = ""
    )
    Column(modifier = Modifier
        .fillMaxWidth()
        .graphicsLayer {
            if (damageOffset.toPx().isNaN().not()) {
                translationY = damageOffset.toPx()
            }
            if ((-damageOffset.toPx() - paddingMedium.toPx()).isNaN().not()) {
                translationX = -damageOffset.toPx() - paddingMedium.toPx()
            }
            clip = false
        }) {
        // 确保在屏幕中间靠右地方播放
        role.getStateList().forEach {
            RoleDamageOrSkillText(Modifier, it)
        }
    }
}