package com.moyu.chuanqirensheng.screen.sell

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.dialog.AwardList
import com.moyu.chuanqirensheng.screen.dialog.defaultParam
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.imageSmallPlus
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.paddingLargePlus
import com.moyu.chuanqirensheng.ui.theme.paddingMediumPlus
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.ui.theme.paddingTiny
import com.moyu.chuanqirensheng.ui.theme.shopItemHeight
import com.moyu.chuanqirensheng.ui.theme.shopItemWidth
import com.moyu.core.model.sell.Sell
import com.moyu.core.model.sell.toAward


@Composable
fun SingleNormalChest(modifier: Modifier = Modifier, sell: Sell) {
    Box(
        modifier = Modifier.size(shopItemWidth, shopItemHeight).then(modifier),
        contentAlignment = Alignment.Center
    ) {
        val chest = repo.gameCore.getPoolById(sell.itemId)
        val award = chest.toAward()
        AwardList(
            award = award.copy(showQuestion = sell.isAd()),
            param = defaultParam.copy(
                textColor = Color.White,
                itemSize = ItemSize.LargePlus,
            ),
            mainAxisAlignment = Arrangement.Center,
        )
        if (sell.storage in 2..999) {
            Text(
                modifier = Modifier
                    .graphicsLayer {
                        translationY = -padding26.toPx()
                    }.background(B50),
                text = stringResource(R.string.sell_left_tips, sell.storage),
                style = MaterialTheme.typography.h4,
                color = Color.White
            )
        }
        if (sell.desc2 != "0") {
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier
                    .align(Alignment.TopCenter)
                    .offset(x = -paddingLargePlus, y = paddingMediumPlus)
            )
            {
                Image(
                    painter = painterResource(id = R.drawable.shop_discount),
                    contentDescription = null,
                    modifier = Modifier.size(imageSmallPlus)
                )
                Text(
                    text = sell.desc2,
                    style = MaterialTheme.typography.body1,
                    modifier = Modifier.offset(x = paddingTiny, y = -paddingSmall)
                )
            }
        }
    }
}