package com.moyu.chuanqirensheng.screen.sell

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.ui.theme.padding0


@Composable
fun TextLabel2(
    modifier: Modifier = Modifier,
    labelSize: LabelSize = LabelSize.Medium2,
    frame: Int = R.drawable.shop_title,
    text: String,
    color: Color = Color.White,
    translateY: Dp = padding0
) {
    Box(
        modifier = modifier.size(labelSize.width, labelSize.height),
        contentAlignment = Alignment.Center
    ) {
        Image(
            modifier = Modifier.fillMaxSize(),
            painter = painterResource(id = frame),
            contentScale = ContentScale.FillBounds,
            contentDescription = null
        )
        Text(
            modifier = Modifier.graphicsLayer {
                this.translationY = translateY.toPx()
            },
            text = text,
            style = labelSize.getTextStyle(),
            maxLines = 2,
            textAlign = TextAlign.Center,
            color = color
        )
    }
}