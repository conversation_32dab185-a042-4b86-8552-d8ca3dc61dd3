package com.moyu.chuanqirensheng.screen.record

import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.api.getRanksApi
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.datastore.json
import com.moyu.chuanqirensheng.model.record.AllRankData
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import kotlinx.coroutines.delay
import kotlinx.serialization.builtins.ListSerializer
import timber.log.Timber


@Composable
fun RankPage(type: Int, data: MutableState<List<AllRankData>>, content: @Composable BoxScope.(AllRankData, Int) -> Unit) {
    LaunchedEffect(Unit) {
        try {
            delay(200)
            if (data.value.isEmpty()) {
                getRanksApi(
                    GameApp.instance.resources.getString(
                        R.string.platform_channel
                    ), type
                ).let {
                    data.value = json.decodeFromString(ListSerializer(AllRankData.serializer()), it.message)
                }
            }
        } catch (e: Exception) {
            Timber.e(e)
            GameApp.instance.getWrapString(R.string.net_error_retry).toast()
        }
    }
    LazyColumn(
        horizontalAlignment = Alignment.CenterHorizontally, modifier = Modifier
            .fillMaxSize()
            .paint(
                painterResource(id = R.drawable.common_page_frame),
                contentScale = ContentScale.FillBounds
            ),
        content = {
            items(data.value.size) { index ->
                Spacer(modifier = Modifier.size(paddingMedium))
                SingleRecord(data.value[index], index + 1,
                    content = content)
            }
        }
    )
}