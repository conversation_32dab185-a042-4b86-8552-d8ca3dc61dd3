package com.moyu.chuanqirensheng.screen.quest

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.award.VipManager
import com.moyu.chuanqirensheng.logic.task.TaskManager
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.dialog.AwardList
import com.moyu.chuanqirensheng.screen.dialog.defaultParam
import com.moyu.chuanqirensheng.ui.theme.paddingLarge
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.pvpRecordFrameHeight
import com.moyu.core.model.sell.toAward
import com.moyu.core.model.task.GameTask
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch


@Composable
fun SingleQuest(quest: GameTask, refresh: () -> Unit) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(pvpRecordFrameHeight)
    ) {
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(R.drawable.common_frame_focus2),
            contentDescription = null
        )
        Row(
            modifier = Modifier.fillMaxSize(),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Spacer(modifier = Modifier.size(paddingMedium))
            Column(Modifier.weight(3f), horizontalAlignment = Alignment.CenterHorizontally) {
                val postFix = TaskManager.getTaskProgressFlow(task = quest).let {
                    if (it.isNotEmpty()) {
                        "（$it）"
                    } else {
                        ""
                    }
                }
                Text(text = quest.desc + postFix, style = MaterialTheme.typography.h4)
            }
            val award = quest.toAward()
            Row(Modifier.weight(3f)) {
                AwardList(
                    award = award,
                    param = defaultParam.copy(
                        numInFrame = true,
                        showName = false,
                        itemSize = ItemSize.Large
                    )
                )
            }
            val questCompleted =
                TaskManager.getTaskDoneFlow(quest)
            GameButton(text =
            if (quest.opened) stringResource(R.string.already_got)
            else if (VipManager.isDoubleQuestAward() && quest.isDailyTask()) stringResource(
                R.string.double_gain
            ) else stringResource(R.string.gain),
                enabled = questCompleted && !quest.opened,
                buttonStyle = ButtonStyle.Orange,
                buttonSize = ButtonSize.MediumMinus,
                onClick = {
                    GameApp.globalScope.launch(Dispatchers.Main) {
                        if (!questCompleted) {
                            GameApp.instance.getWrapString(R.string.quest_not_done_toast).toast()
                        } else if (quest.opened) {
                            GameApp.instance.getWrapString(R.string.award_got_toast).toast()
                        } else {
                            TaskManager.questReward(quest, award)
                            refresh()
                        }
                    }
                })
            Spacer(modifier = Modifier.size(paddingLarge))
        }
    }
}
