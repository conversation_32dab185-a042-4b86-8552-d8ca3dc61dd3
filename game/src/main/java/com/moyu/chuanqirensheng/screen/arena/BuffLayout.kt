package com.moyu.chuanqirensheng.screen.arena

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.ui.theme.*
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.logic.buff.HOLY_SHIELD
import com.moyu.core.model.buff.Buff
import com.moyu.core.model.role.Role
import kotlin.math.roundToInt

@Composable
fun OneBuffGrid(
    buff: Buff,
    callback: (Buff) -> Unit
) {
    val imageSize = buffSize
    EffectButton(onClick = { callback(buff) }) {
        Column {
            Box {
                Image(
                    painter = painterResource(
                        id = if (buff.isGood()) R.drawable.battle_buff_frame else R.drawable.battle_debuff_frame
                    ),
                    modifier = Modifier.size(imageSize),
                    contentScale = ContentScale.FillBounds,
                    contentDescription = null
                )
                Image(
                    painter = painterResource(
                        getImageResourceDrawable(
                            buff.skill?.icon ?: ""
                        )
                    ),
                    modifier = Modifier
                        .size(imageSize).padding(padding1),
                    contentScale = ContentScale.FillBounds,
                    contentDescription = buff.skill?.name
                )
            }
            Spacer(modifier = Modifier.size(paddingTiny))
        }
    }
}