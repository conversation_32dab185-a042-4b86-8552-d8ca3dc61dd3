package com.moyu.chuanqirensheng.screen.record

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import com.moyu.chuanqirensheng.screen.effect.StrokedText
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.model.record.AllRankData
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.common.NavigationTab
import com.moyu.core.util.percentValueToDotWithOneDigits

val rankTabs = listOf(
    GameApp.instance.getWrapString(R.string.life_rank_title),
    GameApp.instance.getWrapString(R.string.ending_rank_title),
    GameApp.instance.getWrapString(R.string.kill_rank_title),
    GameApp.instance.getWrapString(R.string.talent_rank_title))

val ageData = mutableStateOf(emptyList<AllRankData>())
val endingsNumRanks = mutableStateOf(emptyList<AllRankData>())
val killEnemyRanks = mutableStateOf(emptyList<AllRankData>())
val talentNumRanks = mutableStateOf(emptyList<AllRankData>())
val tcgRanks = mutableStateOf(emptyList<AllRankData>())

@Composable
fun RankScreen() {
    LaunchedEffect(Unit) {
        ageData.value = emptyList()
        endingsNumRanks.value = emptyList()
        tcgRanks.value = emptyList()
        killEnemyRanks.value = emptyList()
        talentNumRanks.value = emptyList()
    }
    val pagerState = rememberPagerState {
        rankTabs.size
    }
    GameBackground(title = stringResource(R.string.rank_title)) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            NavigationTab(pagerState, rankTabs)
            HorizontalPager(
                state = pagerState,
            ) { page ->
                when (page) {
                    0 -> RankPage(1, ageData) { rankData, _->
                        // 历史榜，最高年龄
                        StrokedText(
                            text =  rankData.age.toString() + stringResource(id = com.moyu.core.R.string.year),
                            style = MaterialTheme.typography.h3
                        )
                    }
                    1 -> RankPage(2, endingsNumRanks) { rankData, _->
                        // 征服榜，ending数量
                        StrokedText(
                            text = stringResource(R.string.ending_content) + rankData.endingNum,
                            style = MaterialTheme.typography.h3
                        )
                    }
                    2 -> RankPage(3, killEnemyRanks) { rankData, _->
                        // 杀敌榜
                        StrokedText(
                            text = stringResource(R.string.kill_content) + rankData.killEnemy,
                            style = MaterialTheme.typography.h3
                        )
                    }
                    else -> RankPage(4, talentNumRanks) { rankData, _->
                        // 天赋榜
                        StrokedText(
                            text = stringResource(R.string.talent_level) + rankData.talentNum,
                            style = MaterialTheme.typography.h3
                        )
                    }
                }
            }
        }
    }
}