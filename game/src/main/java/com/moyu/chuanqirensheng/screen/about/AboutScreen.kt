package com.moyu.chuanqirensheng.screen.about

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import com.moyu.chuanqirensheng.screen.effect.StrokedText
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextDecoration
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.ui.theme.RaceNameColor
import com.moyu.chuanqirensheng.ui.theme.SkillStoryColor
import com.moyu.chuanqirensheng.ui.theme.paddingLarge

// todo translate
@Composable
fun AboutScreen() {
    GameBackground(
        title = stringResource(id = R.string.about)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .paint(
                    painterResource(id = R.drawable.common_page_frame),
                    contentScale = ContentScale.FillBounds
                )
                .padding(paddingLarge)
                .verticalScroll(rememberScrollState())
        ) {
            if (GameApp.instance.resources.getBoolean(R.bool.has_billing)) {
//                StrokedText(text = stringResource(R.string.produce), style = MaterialTheme.typography.h3)
//                Row(modifier = Modifier.clickable {
//                    val myClipboard =
//                        GameApp.instance.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
//                    val myClip = ClipData.newPlainText("text", "https://discord.gg/6ZKh2tQXSq")
//                    myClipboard.setPrimaryClip(myClip)
//                    ("Discord Link：https://discord.gg/6ZKh2tQXSq " + GameApp.instance.getWrapString(R.string.copied)).toast()
//                }) {
//                    StrokedText(
//                        text = stringResource(R.string.contact1) + "(Discord)：",
//                        style = MaterialTheme.typography.h3
//                    )
//                    StrokedText(
//                        text = "https://discord.gg/6ZKh2tQXSq",
//                        style = MaterialTheme.typography.h3,
//                        textDecoration = TextDecoration.Underline,
//                        color = RaceNameColor
//                    )
//                }
                Row(modifier = Modifier.clickable {
                    val myClipboard =
                        GameApp.instance.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                    val myClip = ClipData.newPlainText("text", "<EMAIL>")
                    myClipboard.setPrimaryClip(myClip)
                    ("gmail：<EMAIL> " + GameApp.instance.getWrapString(R.string.copied)).toast()
                }) {
                    StrokedText(
                        text = stringResource(R.string.contact1) + "(email)：",
                        style = MaterialTheme.typography.h3
                    )
                    StrokedText(
                        text = "<EMAIL>",
                        style = MaterialTheme.typography.h3,
                        color = RaceNameColor
                    )
                }
                Row(modifier = Modifier.clickable {
                    val myClipboard =
                        GameApp.instance.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                    val myClip = ClipData.newPlainText("text", "<EMAIL>")
                    myClipboard.setPrimaryClip(myClip)
                    ("gmail：<EMAIL> " + GameApp.instance.getWrapString(R.string.copied)).toast()
                }) {
                    StrokedText(
                        text = stringResource(R.string.contact1) + "(email)：",
                        style = MaterialTheme.typography.h3
                    )
                    StrokedText(
                        text = "<EMAIL>",
                        style = MaterialTheme.typography.h3,
                        color = RaceNameColor
                    )
                }
            }
//            StrokedText(text = stringResource(R.string.produce), style = MaterialTheme.typography.h3)
//            Row(modifier = Modifier.clickable {
//                val myClipboard =
//                    GameApp.instance.getSystemService(CLIPBOARD_SERVICE) as ClipboardManager
//                val myClip = ClipData.newPlainText("text", "136235142")
//                myClipboard.setPrimaryClip(myClip)
//                ("QQ号：136235142 " + GameApp.instance.getWrapString(R.string.copied)).toast()
//            }) {
//                StrokedText(
//                    text = stringResource(R.string.contact1) + "(QQ)：",
//                    style = MaterialTheme.typography.h3
//                )
//                StrokedText(
//                    text = "136235142",
//                    style = MaterialTheme.typography.h3,
//                    textDecoration = TextDecoration.Underline,
//                    color = RaceNameColor
//                )
//            }
//            val qq = GameApp.instance.resources.getString(
//                R.string.qq
//            )
//            Row(modifier = Modifier.clickable {
//                val myClipboard =
//                    GameApp.instance.getSystemService(CLIPBOARD_SERVICE) as ClipboardManager
//                val myClip = ClipData.newPlainText("text", qq)
//                myClipboard.setPrimaryClip(myClip)
//                "QQ群号：${qq} 已复制".toast()
//            }) {
//                StrokedText(text = "游戏官方QQ群：", style = MaterialTheme.typography.h3)
//                StrokedText(
//                    text = qq,
//                    style = MaterialTheme.typography.h3,
//                    textDecoration = TextDecoration.Underline,
//                    color = RaceNameColor
//                )
//            }
            Spacer(modifier = Modifier.size(paddingLarge))
            StrokedText(
                text = stringResource(R.string.about_us).trimIndent(),
                style = MaterialTheme.typography.h4
            )
            Spacer(modifier = Modifier.size(paddingLarge))
            StrokedText(
                modifier = Modifier.align(Alignment.CenterHorizontally),
                color = SkillStoryColor,
                text = stringResource(id = R.string.about) + "《" + stringResource(id = R.string.app_name) + "》",
                style = MaterialTheme.typography.h3
            )
            StrokedText(
                color = Color.White,
                text = stringResource(R.string.about_game).trimIndent(),
                style = MaterialTheme.typography.h4
            )
        }
    }
}