package com.moyu.chuanqirensheng.screen.dialog

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.equip.SingleHeroView
import com.moyu.chuanqirensheng.screen.skill.SingleSkillView
import com.moyu.chuanqirensheng.ui.theme.paddingSmall

@Composable
fun HeroDialog(showDungeon: MutableState<Boolean>) {
    showDungeon.value.takeIf { showDungeon.value }?.let {
        DisposableEffect(Unit) {
            onDispose {
                BattleManager.setHeroUnNew()
            }
        }
        CommonDialog(
            title = stringResource(R.string.hero),
            onDismissRequest = {
                showDungeon.value = false
            }) {
            Column(
                modifier = Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                val list = BattleManager.getGameHeroes()
                LazyVerticalGrid(modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
                    columns = GridCells.Fixed(4),
                    content = {
                        items(list.size) { index ->
                            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                                Spacer(modifier = Modifier.size(paddingSmall))
                                val skill = list[index]
                                SingleHeroView(
                                    hero = skill,
                                    showName = true,
                                    showRed = true,
                                    itemSize = ItemSize.LargePlus
                                )
                                Spacer(modifier = Modifier.size(paddingSmall))
                            }
                        }
                    })
            }
        }
    }
}