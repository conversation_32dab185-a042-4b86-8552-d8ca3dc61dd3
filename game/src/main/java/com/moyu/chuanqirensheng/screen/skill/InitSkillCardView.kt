@file:OptIn(ExperimentalLayoutApi::class)

package com.moyu.chuanqirensheng.screen.skill

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.unlock.UnlockManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.ally.MAX_ALLY_SKILL_SIZE
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.paddingTiny
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch


@Composable
fun InitSkillCardView(modifier: Modifier) {
    FlowRow(
        modifier = modifier,
        horizontalArrangement = Arrangement.SpaceEvenly,
        overflow = FlowRowOverflow.Visible,
        maxItemsInEachRow = MAX_ALLY_SKILL_SIZE / 2
    ) {
        val skills = BattleManager.getGameSkills()
        var skillCount = 0
        repeat(MAX_ALLY_SKILL_SIZE) { index ->
            val unlock = UnlockManager.getInitSkillUnlockByIndex(index)
            val unlocked = UnlockManager.getUnlockedFlow(unlock)
            Box(modifier = Modifier.padding(vertical = paddingTiny)) {
                val gif = remember {
                    mutableStateOf(false)
                }
                skills.getOrNull(skillCount)?.takeIf { unlocked }?.let { skill ->
                    skillCount += 1
                    SingleSkillView(
                        skill = skill,
                        showName = false,
                        itemSize = ItemSize.LargePlus
                    ) {
                        GameApp.globalScope.launch(Dispatchers.Main) {
                            BattleManager.selectToGame(it)
                        }
                    }
                    GifView(modifier = Modifier.size(ItemSize.LargePlus.frameSize), gif.value, 8, "equipon_")
                } ?: run {
                    gif.value = true
                    Box(contentAlignment = Alignment.Center) {
                        EmptyIconView(itemSize = ItemSize.LargePlus) {
                            if (unlocked) {
                                Dialogs.selectSkillToGameDialog.value = true
                            } else {
                                unlock.desc.toast()
                            }
                        }
                        if (!unlocked) {
                            Image(
                                modifier = Modifier.size(imageLarge),
                                painter = painterResource(R.drawable.common_lock),
                                contentDescription = stringResource(id = R.string.locked),
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun EmptyIconView(
    modifier: Modifier = Modifier,
    frame: Int = R.drawable.item_quality_0,
    itemSize: ItemSize = ItemSize.LargePlus,
    callback: () -> Unit
) {
    EffectButton(modifier = modifier, onClick = {
        callback()
    }) {
        Image(
            modifier = Modifier.size(itemSize.frameSize),
            painter = painterResource(id = frame),
            contentDescription = null
        )
        Image(
            modifier = Modifier.size(itemSize.frameSize / 2),
            painter = painterResource(id = R.drawable.common_plus),
            contentDescription = stringResource(R.string.empty_slot)
        )
    }
}


@Composable
fun IconView(
    modifier: Modifier = Modifier,
    res: Int,
    frame: Int? = R.drawable.item_quality_0,
    itemSize: ItemSize = ItemSize.LargePlus,
    name: String = "",
    callback: () -> Unit = {}
) {
    EffectButton(modifier = modifier, onClick = {
        callback()
    }) {
        frame?.let {
            Image(
                modifier = Modifier.size(itemSize.frameSize),
                painter = painterResource(id = frame),
                contentDescription = null
            )
        }
        Image(
            modifier = Modifier
                .size(itemSize.itemSize)
                .clip(RoundedCornerShape(itemSize.itemSize / 12)),
            painter = painterResource(id = res),
            contentScale = ContentScale.Crop,
            contentDescription = name
        )
    }
}