package com.moyu.chuanqirensheng.screen.skill

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.logic.skill.getFrameDrawable
import com.moyu.chuanqirensheng.logic.skill.getTouchInfo
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.common.getTextStyle
import com.moyu.chuanqirensheng.screen.more.Stars
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.ui.theme.paddingTiny
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.isAdventure
import com.moyu.core.model.skill.isTalentSkill
import com.moyu.core.model.skill.quality
import com.wajahatkarim.flippable.FlipAnimationType
import com.wajahatkarim.flippable.Flippable
import com.wajahatkarim.flippable.FlippableController
import kotlinx.coroutines.delay
import kotlin.math.roundToInt


@Composable
fun SingleSkillView(
    modifier: Modifier = Modifier,
    skill: Skill,
    itemSize: ItemSize = ItemSize.Large,
    showName: Boolean = true,
    showNum: Boolean = false,
    textColor: Color = Color.Black,
    colorFilter: ColorFilter? = null,
    frame: Int? = null,
    locked: Boolean = false,
    showRed: Boolean = false,
    showStars: Boolean = true,
    showEffect: Boolean = false,
    imageClip: Shape = RoundedCornerShape(itemSize.frameSize / 12),
    callback: (Skill) -> Unit = { Dialogs.skillDetailDialog.value = skill }
) {
    Column(modifier = modifier, horizontalAlignment = Alignment.CenterHorizontally) {
        EffectButton(onClick = {
            callback(skill)
        }) {
            Image(
                modifier = Modifier.size(itemSize.frameSize),
                painter = painterResource(frame ?: skill.getFrameDrawable()),
                contentDescription = null,
            )
            Image(
                modifier = Modifier
                    .size(itemSize.itemSize)
                    .clip(imageClip),
                contentScale = ContentScale.Crop,
                colorFilter = colorFilter,
                painter = painterResource(getImageResourceDrawable(skill.icon)),
                contentDescription = skill.getTouchInfo(),
            )
            if (skill.extraInfo.isNotEmpty()) {
                Box(
                    modifier = Modifier
                        .background(B50)
                        .padding(paddingSmall)) {
                    Text(text = skill.extraInfo, style = MaterialTheme.typography.body1, textAlign = TextAlign.Center)
                }
            }
            if (!skill.isAdventure() && !skill.isTalentSkill() && showStars) {
                Stars(
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .padding(bottom = itemSize.itemSize / 12),
                    skill.level,
                    starHeight = itemSize.itemSize / 5
                )
            }
            if (locked) {
                Image(
                    modifier = Modifier.size(itemSize.frameSize / 2),
                    painter = painterResource(R.drawable.common_lock),
                    contentDescription = stringResource(id = R.string.locked),
                )
            }
            if (showRed && skill.new) {
                Image(
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .size(imageSmall),
                    painter = painterResource(R.drawable.red_icon),
                    contentDescription = null
                )
            }
            if (showEffect && skill.quality() >= 3) {
                val infiniteTransition = rememberInfiniteTransition(label = "")
                val index = infiniteTransition.animateFloat(
                    initialValue = 1f, targetValue = 50f, animationSpec = infiniteRepeatable(
                        animation = tween(1600, easing = LinearEasing),
                        repeatMode = RepeatMode.Restart,
                    ), label = ""
                )
                if (index.value.roundToInt() <= 15) { // 做一个间歇的效果
                    Image(
                        modifier = Modifier
                            .size(itemSize.frameSize)
                            .scale(1.3f),
                        painter = painterResource(
                            getImageResourceDrawable(
                                "rewarditem_${index.value.roundToInt()}"
                            )
                        ),
                        contentDescription = null
                    )
                }
            }
        }
        if (showName) {
            Spacer(modifier = Modifier.size(paddingTiny))
            val text = if (showNum) skill.name + "x${skill.num}" else skill.name
            Text(
                text = text,
                style = itemSize.getTextStyle(),
                maxLines = 2,
                minLines = 2,
                textAlign = TextAlign.Center,
                color = textColor
            )
        }
    }
}


@Composable
fun FlipView(modifier: Modifier, delay: Long, front: @Composable () -> Unit, back: @Composable () -> Unit) {
    val flipControl = remember {
        mutableStateOf(FlippableController())
    }
    LaunchedEffect(Unit) {
        delay(delay)
        flipControl.value.flipToBack()
    }
    Flippable(
        modifier = modifier,
        flipDurationMs = 300,
        flipOnTouch = false,
        flipController = flipControl.value,
        flipEnabled = true,
        frontSide = front,
        backSide = back,
        contentAlignment = Alignment.TopCenter,
        onFlippedListener = { currentSide ->
        },
        autoFlip = false,
        autoFlipDurationMs = 300,
        flipAnimationType = FlipAnimationType.VERTICAL_CLOCKWISE
    )
}