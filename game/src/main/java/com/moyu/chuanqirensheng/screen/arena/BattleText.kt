package com.moyu.chuanqirensheng.screen.arena

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.skill.SingleSkillView
import com.moyu.chuanqirensheng.ui.theme.AllDamageShieldColor
import com.moyu.chuanqirensheng.ui.theme.HealTextColor
import com.moyu.chuanqirensheng.ui.theme.NormalDamageShieldColor
import com.moyu.chuanqirensheng.ui.theme.getTextColor
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.util.getDamageNumber
import com.moyu.chuanqirensheng.util.getHealNumber
import com.moyu.core.model.action.ActionState
import com.moyu.core.model.action.ActionStateType


@Composable
fun RoleDamageOrSkillText(
    modifier: Modifier = Modifier,
    actionState: ActionState
) {
    Column(modifier) {
        // 掉血
        if (actionState.isState(ActionStateType.BeingAttack)) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                getDamageNumber(actionState.value).forEach {
                    Image(
                        modifier = Modifier.width(if (actionState.damage?.damageStatus?.isFatal == true) padding19 else padding16).scale(1.8f),
                        painter = painterResource(id = it),
                        colorFilter = ColorFilter.tint(
                            color = actionState.damage!!.type.getTextColor().copy(0.2f),
                            BlendMode.SrcAtop
                        ),
                        contentScale = ContentScale.FillWidth,
                        contentDescription = null
                    )
                }
            }
            val normalShield =
                actionState.damage!!.damageValue.normalShieldBlockedDamage
            if (normalShield > 0) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    getDamageNumber(normalShield).forEach {
                        Image(
                            modifier = Modifier.width(padding16).scale(1.8f),
                            painter = painterResource(id = it),
                            colorFilter = ColorFilter.tint(
                                color = NormalDamageShieldColor.copy(0.2f),
                                BlendMode.SrcAtop
                            ),
                            contentScale = ContentScale.FillWidth,
                            contentDescription = null
                        )
                    }
                }
            }
            val allShield = actionState.damage!!.damageValue.allShieldBlockedDamage
            if (allShield > 0) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    getDamageNumber(allShield).forEach {
                        Image(
                            modifier = Modifier.width(padding16).scale(1.8f),
                            painter = painterResource(id = it),
                            colorFilter = ColorFilter.tint(
                                color = AllDamageShieldColor.copy(0.2f),
                                BlendMode.SrcAtop
                            ),
                            contentScale = ContentScale.FillWidth,
                            contentDescription = null
                        )
                    }
                }
            }
        }
        // 回血
        if (actionState.isState(ActionStateType.BeingHeal)) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                getHealNumber(actionState.healResult?.healValue?: 0).forEach {
                    Image(
                        modifier = Modifier.width(padding16).scale(1.8f),
                        painter = painterResource(id = it),
                        colorFilter = ColorFilter.tint(
                            color = HealTextColor.copy(0.2f),
                            BlendMode.SrcAtop
                        ),
                        contentDescription = null
                    )
                }
            }
        }
        // 普通技能
        if (actionState.isState(ActionStateType.DoSkill)) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                actionState.skill?.let {
                    SingleSkillView(
                        skill = it,
                        itemSize = ItemSize.Small,
                        showName = false
                    ) {}
                }
                Spacer(modifier = Modifier.size(padding4))
                Text(
                    text = actionState.skill?.name ?: "",
                    color = Color.White,
                    style = MaterialTheme.typography.h1,
                    maxLines = 1,
                    overflow = TextOverflow.Visible,
                    softWrap = false,
                )
            }
        }
    }
}