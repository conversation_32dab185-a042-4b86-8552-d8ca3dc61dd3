package com.moyu.chuanqirensheng.screen.equip

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.screen.fortune.PropertyItem
import com.moyu.chuanqirensheng.ui.theme.paddingMediumPlus
import com.moyu.core.model.property.AdventureProps
import com.moyu.core.model.property.EMPTY_ADV_PROPS

@Composable
fun AdventureProps.RolePropertyLine(
    originProperty: AdventureProps = EMPTY_ADV_PROPS,
    showBoost: Boolean = false,
    showEmpty: Boolean = true,
    countStart: Int = 0,
    countEnd: Int = 100,
) {
    var count = 1
    if (count in countStart until countEnd) {
        if (showEmpty || science != 0) {
            Row {
                PropertyItem(
                    icon = R.drawable.player_attribute_1,
                    getProperty = { science.toDouble() },
                    name = stringResource(R.string.role_prop1),
                    getTips = { GameApp.instance.getWrapString(R.string.prop1_tips) },
                    isBoost = { (science.toDouble() - originProperty.science) },
                    showBoost = showBoost
                )
                Spacer(modifier = Modifier.size(paddingMediumPlus))
            }
        }
    }
    count += 1
    if (count in countStart until countEnd) {
        if (showEmpty || politics != 0) {
            Row {
                PropertyItem(
                    icon = R.drawable.player_attribute_2,
                    getProperty = { politics.toDouble() },
                    name = stringResource(R.string.role_prop2),
                    getTips = { GameApp.instance.getWrapString(R.string.prop2_tips) },
                    isBoost = { (politics.toDouble() - originProperty.politics) },
                    showBoost = showBoost
                )
                Spacer(modifier = Modifier.size(paddingMediumPlus))
            }
        }
    }
    count += 1
    if (count in countStart until countEnd) {
        if (showEmpty || military != 0) {
            Row {
                PropertyItem(
                    icon = R.drawable.player_attribute_3,
                    getProperty = { military.toDouble() },
                    name = stringResource(R.string.role_prop3),
                    getTips = { GameApp.instance.getWrapString(R.string.prop3_tips) },
                    isBoost = { (military.toDouble() - originProperty.military) },
                    showBoost = showBoost
                )
                Spacer(modifier = Modifier.size(paddingMediumPlus))
            }
        }
    }
    count += 1
    if (count in countStart until countEnd) {
        if (showEmpty || religion != 0) {
            Row {
                PropertyItem(
                    icon = R.drawable.player_attribute_4,
                    getProperty = { religion.toDouble() },
                    name = stringResource(R.string.role_prop4),
                    getTips = { GameApp.instance.getWrapString(R.string.prop4_tips) },
                    isBoost = { (religion.toDouble() - originProperty.religion) },
                    showBoost = showBoost
                )
                Spacer(modifier = Modifier.size(paddingMediumPlus))
            }
        }
    }
    count += 1
    if (count in countStart until countEnd) {
        if (showEmpty || commerce != 0) {
            Row {
                PropertyItem(
                    icon = R.drawable.player_attribute_5,
                    getProperty = { commerce.toDouble() },
                    name = stringResource(R.string.role_prop5),
                    getTips = { GameApp.instance.getWrapString(R.string.prop5_tips) },
                    isBoost = { (commerce.toDouble() - originProperty.commerce) },
                    showBoost = showBoost
                )
                Spacer(modifier = Modifier.size(paddingMediumPlus))
            }
        }
    }
    count += 1
    if (count in countStart until countEnd) {
        if (showEmpty || art != 0) {
            Row {
                PropertyItem(
                    icon = R.drawable.player_attribute_6,
                    getProperty = { art.toDouble() },
                    name = stringResource(R.string.role_prop6),
                    getTips = { GameApp.instance.getWrapString(R.string.prop6_tips) },
                    isBoost = { (art.toDouble() - originProperty.art) },
                    showBoost = showBoost
                )
                Spacer(modifier = Modifier.size(paddingMediumPlus))
            }
        }
    }
    count += 1
    if (count in countStart until countEnd) {
        if (showEmpty || population != 0) {
            Row {
                PropertyItem(
                    icon = R.drawable.player_attribute_7,
                    getProperty = { population.toDouble() },
                    name = stringResource(R.string.role_prop7),
                    getTips = { GameApp.instance.getWrapString(R.string.prop7_tips) },
                    isBoost = { (population.toDouble() - originProperty.population) },
                    showBoost = showBoost
                )
                Spacer(modifier = Modifier.size(paddingMediumPlus))
            }
        }
    }
}