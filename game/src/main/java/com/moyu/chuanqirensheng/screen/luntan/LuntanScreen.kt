package com.moyu.chuanqirensheng.screen.luntan

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.core.content.ContextCompat
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.ui.theme.SkillStoryColor
import com.moyu.chuanqirensheng.ui.theme.gapSmall
import com.moyu.chuanqirensheng.ui.theme.gapSmallPlus
import com.moyu.chuanqirensheng.ui.theme.paddingLarge


@Composable
fun LuntanScreen() {
    GameBackground(
        title = stringResource(R.string.luntan_page)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .paint(
                    painterResource(id = R.drawable.common_page_frame),
                    contentScale = ContentScale.FillBounds
                )
                .padding(paddingLarge)
                .verticalScroll(rememberScrollState())
        ) {
            Spacer(modifier = Modifier.size(paddingLarge))
            Text(
                modifier = Modifier.align(Alignment.CenterHorizontally),
                color = SkillStoryColor,
                text = """论坛打卡第3天
奖励:五鬼搬运【五星】

论坛打卡第6天
奖励:鹤翼阵【五星】

论坛打卡第10天
奖励:手术医师【五星】

论坛打卡第15天
奖励:秦始皇【五星】

论坛打卡第21天
奖励:法老禁卫【五星】

论坛打卡第28天
奖励:三位一体【五星】""",
                style = MaterialTheme.typography.h3
            )
            Spacer(modifier = Modifier.size(paddingLarge))
            GameButton(
                modifier = Modifier.align(Alignment.CenterHorizontally),
                text = stringResource(id = R.string.go)
            ) {
                val uri: Uri =
                    Uri.parse("https://tap.cn/xgp7WeDs?channel=rep-rep_oxtkbmhzgfj_h5url465")
                val intent = Intent(Intent.ACTION_VIEW, uri)
                ContextCompat.startActivity(
                    GameApp.instance.activity,
                    intent,
                    Bundle()
                )
            }
            Spacer(modifier = Modifier.size(gapSmallPlus))
            Text(
                text = """找谁领取奖励？

1.打卡【tap tap】——【tap tap】首页上方搜索栏——点击搜索——在搜索栏输入:371912513——点击用户——点击关注【林荫道】——等待【林荫道】回关——可在4小时后再查看【林荫道】是否回关

2.【林荫道】回关之后——点击【tap tap】主页下方【消息】——点击上方【好友关注】——点击【好友】——在好友列表里找到【林荫道】——点击右方【私信】——发送消息【领取打卡第X天奖励】——X为第几天需自己根据进度填写天数

3.经查询并核实，确认无误后，将发放奖励

4.由于领奖人数太多，发送消息后需要等一段时间才能得到回复

5.每个tap tap账号ID只能参与1次，ID具有唯一性



如何进行论坛打卡？

1.打开tap tap——点击右下角【我的游戏】——找到游戏【帝国的第99次重生】——点击【论坛】

2.在本游戏论坛，发表一个自己的帖子，可以分享自己的游戏心得、分享日常生活、宣传中国文化等等都行

3.帖子标题为【论坛打卡第X天】，X为第几天需自己根据进度填写天数，完成发帖后在本游戏论坛随便逛两分钟，即可完成今日打卡，当打卡进度达到活动要求进度时，即可按照领奖流程进行奖励领取



注意事项有哪些？

1.在论坛发表的毫无意义的内容，将会被清除，被清除的帖子不视为有效打卡

2.帖子所在地方，非本游戏论坛，则不视为有效打卡

3.打卡不要求连续打卡，只要求打卡次数，但每天只有第一次打卡有效，当天多次打卡无效

4.通过修改之前发布过的帖子标题，伪证活动数据的行为，视为作弊，将取消获奖资格""",
                style = MaterialTheme.typography.h4
            )
        }
    }
}