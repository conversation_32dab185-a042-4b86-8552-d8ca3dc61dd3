package com.moyu.chuanqirensheng.screen.about

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextDecoration
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.dialog.InnerAwardItem
import com.moyu.chuanqirensheng.ui.theme.RaceNameColor
import com.moyu.chuanqirensheng.ui.theme.SkillLevel2Color
import com.moyu.chuanqirensheng.ui.theme.answerHeight
import com.moyu.chuanqirensheng.ui.theme.paddingHuge
import com.moyu.chuanqirensheng.ui.theme.paddingLarge
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingMediumPlus
import com.moyu.chuanqirensheng.util.USER_SHARE_GAME_SCREEN
import com.moyu.chuanqirensheng.util.goto

// todo translate
@Composable
fun AboutShareRewardsScreen() {
    GameBackground(
        title = stringResource(id = R.string.share_rewards)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .paint(
                    painterResource(id = R.drawable.common_page_frame),
                    contentScale = ContentScale.FillBounds
                )
                .padding(horizontal = paddingLarge),
        ) {
            Text(
                text = stringResource(R.string.share_award_tips),
                style = MaterialTheme.typography.h3,
                modifier = Modifier.padding(top = paddingMedium)
            )
            Section1()
            Spacer(modifier = Modifier.size(paddingHuge))
            Section2()
        }
    }
}

@Composable
private fun Section2() {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {

        Text(text = stringResource(R.string.already_shared) + "1/5", style = MaterialTheme.typography.h2)
        Column {
            (1..3).forEach {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(answerHeight),
                    contentAlignment = Alignment.Center
                ) {
                    Image(
                        modifier = Modifier.fillMaxSize(),
                        contentScale = ContentScale.FillBounds,
                        painter = painterResource(R.drawable.common_frame_focus2),
                        contentDescription = null
                    )
                    Row(
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = paddingLarge)
                    ) {
                        Text(
                            text = "玩家名字7个字", style = MaterialTheme.typography.h2,
                        )
                        if (it == 2) {
                            Text(text = stringResource(id = R.string.already_got), style = MaterialTheme.typography.h4)
                        } else {
                            GameButton(
                                textColor = Color.White,
                                text = stringResource(id = R.string.gain_award),
                                buttonStyle = ButtonStyle.Blue
                            ){
                                //TODO
                            }
                        }
                    }
                }
            }

        }
        Spacer(modifier = Modifier.size(paddingHuge))
        Text(text = stringResource(R.string.accepted_share) + "1/5", style = MaterialTheme.typography.h2)
    }
}

@Composable
private fun Section1() {
    Box(
        modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.Center
    ) {
        Image(
            painter = painterResource(id = R.drawable.dialog_frame),
            contentDescription = null,
            modifier = Modifier.fillMaxWidth(),
            contentScale = ContentScale.FillWidth
        )
        Column(
            //modifier = Modifier.background(Color.Green),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(paddingMediumPlus)
        ) {
            Row(
                //modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(paddingHuge)
            ) {
                (1..3).forEach {
                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                        // ============================================
                        // InnerAwardItem 下方可能有个间距，影响下边文字的位置 ， 调整后可以去掉下方文字的offset
                        // ============================================
                        InnerAwardItem(name = "", drawable = R.drawable.common_key)
                        Text(
                            text = "钻石", color = Color.Black, style = MaterialTheme.typography.h4,
                            modifier = Modifier.offset(y = -paddingMedium) //<<<<<<可以去掉
                        )
                    }
                }
            }
            Row(
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                Text(
                    text = stringResource(id = R.string.share_code), style = MaterialTheme.typography.h3, color = Color.Black
                )
                Spacer(modifier = Modifier.size(paddingMedium))
                Text(
                    text = GameApp.instance.getShareCode(),
                    style = MaterialTheme.typography.h3,
                    color = Color.Black
                )
                Spacer(modifier = Modifier.size(paddingMedium))
                EffectButton(onClick = {
                    val myClipboard =
                        GameApp.instance.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                    val myClip = ClipData.newPlainText("text", GameApp.instance.getShareCode())
                    myClipboard.setPrimaryClip(myClip)
                    GameApp.instance.getWrapString(R.string.copied).toast()
                }) {
                    Text(
                        text = stringResource(R.string.click_to_copy),
                        style = MaterialTheme.typography.h3,
                        color = RaceNameColor,
                        textDecoration = TextDecoration.Underline
                    )
                }
            }
            GameButton(
                modifier = Modifier.align(Alignment.CenterHorizontally),
                text = stringResource(id = R.string.share),
                buttonSize = ButtonSize.Medium,
                buttonStyle = ButtonStyle.Orange,
            ) {
                //TODO
                goto(USER_SHARE_GAME_SCREEN)
            }
        }

    }
}