package com.moyu.chuanqirensheng.screen.button

import android.view.MotionEvent
import androidx.compose.foundation.LocalIndication
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.input.pointer.pointerInteropFilter
import androidx.compose.ui.platform.LocalHapticFeedback
import com.moyu.core.GameCore
import com.moyu.core.music.SoundEffect
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber

const val CLICK_GAP = 400
const val HUGE_GAP = 500
const val LONG_PRESS_TIME = 500L
const val MEDIA_GAP = 200
const val QUICK_GAP = 200
@Transient
var globalLastClickTime = 0L


@Composable
fun EffectButton(
    modifier: Modifier = Modifier,
    onClick: () -> Unit = {},
    indicator: Boolean = true,
    enableLongClick: Boolean = false,
    longPress: (() -> Unit)? = null,
    pressing: MutableState<Boolean> = remember {
        mutableStateOf(false)
    },
    clickGap: Int = HUGE_GAP,
    content: @Composable BoxScope.() -> Unit
) {
    val lastClickTime = remember {
        mutableStateOf(0L)
    }
    val startTime = remember {
        mutableStateOf(0L)
    }
    var job: Job? = remember {
        null
    }
    val hapticFeedback = LocalHapticFeedback.current
    val coroutineScope = rememberCoroutineScope()
    if (longPress == null && !enableLongClick) {
        Box(
            modifier = modifier
                .clickable(
                    interactionSource = if (indicator) remember { MutableInteractionSource() } else MutableInteractionSource(),
                    indication = if (indicator) LocalIndication.current else null,
                    onClick = {
                        val current = System.currentTimeMillis()
                        if (current - lastClickTime.value > clickGap && current - globalLastClickTime > MEDIA_GAP) {
                            lastClickTime.value = current
                            globalLastClickTime = current
                            onClick()
                        } else {
                            Timber.e("EffectButton拦截快速点击")
                        }
                        GameCore.instance.onBattleEffect(SoundEffect.Click)
                        pressing.value = false
                    }), contentAlignment = Alignment.Center, content = content
        )
    } else {
        Box(
            modifier = modifier
                .pointerInteropFilter {
                    when (it.action) {
                        MotionEvent.ACTION_DOWN -> {
                            // User has pressed the button
                            pressing.value = true
                            startTime.value = System.currentTimeMillis()
                            job = coroutineScope.launch {
                                delay(LONG_PRESS_TIME)
                                if (pressing.value) {
                                    if (enableLongClick) {
                                        while (pressing.value) {
                                            GameCore.instance.onBattleEffect(SoundEffect.Click)
                                            onClick()
                                            delay(200)
                                        }
                                    } else {
                                        GameCore.instance.onBattleEffect(SoundEffect.Click)
                                        longPress?.invoke()
                                        longPress?.let {
                                            hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                                        }
                                        startTime.value = 0
                                        pressing.value = false
                                    }
                                }
                            }
                        }
                        MotionEvent.ACTION_UP -> {
                            if (pressing.value) {
                                val current = System.currentTimeMillis()
                                if (current - lastClickTime.value > clickGap && current - globalLastClickTime > QUICK_GAP) {
                                    lastClickTime.value = current
                                    globalLastClickTime = current
                                    onClick()
                                } else {
                                    Timber.e("EffectButton拦截快速点击")
                                }
                                GameCore.instance.onBattleEffect(SoundEffect.Click)
                            }
                            startTime.value = 0
                            pressing.value = false
                            job?.cancel()
                        }
                        MotionEvent.ACTION_CANCEL -> {
                            startTime.value = 0
                            pressing.value = false
                        }
                        MotionEvent.ACTION_OUTSIDE -> {
                            startTime.value = 0
                            pressing.value = false
                        }
                    }
                    true
                }, contentAlignment = Alignment.Center, content = content
        )
    }
}