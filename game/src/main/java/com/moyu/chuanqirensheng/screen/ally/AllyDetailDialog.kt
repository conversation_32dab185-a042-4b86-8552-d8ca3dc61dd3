@file:OptIn(ExperimentalLayoutApi::class)

package com.moyu.chuanqirensheng.screen.ally

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.zIndex
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.ally.getCardFrameDrawable
import com.moyu.chuanqirensheng.logic.ally.getElementTypeRes
import com.moyu.chuanqirensheng.logic.ally.getRaceTips
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.guide.GuideManager
import com.moyu.chuanqirensheng.logic.hero.getQualityFrame
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.CardSize
import com.moyu.chuanqirensheng.screen.common.CurrentDiamondPoint
import com.moyu.chuanqirensheng.screen.common.DiamondPoint
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.dialog.CommonDialog
import com.moyu.chuanqirensheng.screen.equip.MainPropertyLine
import com.moyu.chuanqirensheng.screen.guide.GuidePointAndText
import com.moyu.chuanqirensheng.screen.guide.HandType
import com.moyu.chuanqirensheng.screen.more.Stars
import com.moyu.chuanqirensheng.screen.role.TextLabel
import com.moyu.chuanqirensheng.screen.sensor.ShadowedView
import com.moyu.chuanqirensheng.screen.skill.EmptyIconView
import com.moyu.chuanqirensheng.screen.skill.GifView
import com.moyu.chuanqirensheng.screen.skill.IconView
import com.moyu.chuanqirensheng.screen.skill.SingleSkillView
import com.moyu.chuanqirensheng.thread.gameDispatcher
import com.moyu.chuanqirensheng.ui.theme.B65
import com.moyu.chuanqirensheng.ui.theme.W50
import com.moyu.chuanqirensheng.ui.theme.cardStarBigSize
import com.moyu.chuanqirensheng.ui.theme.eventCardBigHeight
import com.moyu.chuanqirensheng.ui.theme.eventCardBigWidth
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.imageSmallPlus
import com.moyu.chuanqirensheng.ui.theme.padding1
import com.moyu.chuanqirensheng.ui.theme.paddingHuge
import com.moyu.chuanqirensheng.ui.theme.paddingHugeLite
import com.moyu.chuanqirensheng.ui.theme.paddingLarge
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingMediumPlus
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.ui.theme.paddingSmallPlus
import com.moyu.chuanqirensheng.ui.theme.smallButtonHeight
import com.moyu.chuanqirensheng.util.composeDp
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.chuanqirensheng.util.pixelToDp
import com.moyu.chuanqirensheng.util.screenWidthInPixel
import com.moyu.core.model.ally.Ally
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.isBattle
import com.moyu.core.model.skill.isBattleSkill
import com.moyu.core.model.skill.isHalo
import com.moyu.core.model.skill.quality
import kotlinx.coroutines.launch
import java.lang.Integer.max

const val FIXED_SKILL_SIZE = 4 // 固定4个技能，专属1个，护甲，战意，普攻

@Composable
fun AllyDetailDialog(show: MutableState<Ally?>) {
    show.value?.let { ally ->
        CommonDialog(title = "",
            frame = null,
            noPadding = true,
            heightInDp = eventCardBigHeight * 2.20f,
            clickFrame = { show.value = null },
            onDismissRequest = {
                if (GuideManager.guideIndex.value == 11) {
                    Dialogs.selectOneSkillDialog.value = Pair({
                        it.isBattle() && it.equipAllyUuid.isEmpty()
                    }, { skill ->
                        Dialogs.allyDetailDialog.value =
                            BattleManager.equipSkillToAlly(
                                skill = skill, ally = ally
                            )
                    })
                    show.value = null
                    GuideManager.toNextGuide()
                } else {
                    show.value = null
                }

            }) {
            Column(
                Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                CurrentDiamondPoint(
                    Modifier
                        .align(Alignment.End)
                        .padding(end = paddingMedium),
                    showPlus = true,
                    showFrame = true
                )
                Spacer(modifier = Modifier.size(paddingSmall))
                val role = BattleManager.getRoleByAlly(ally)
                Box(Modifier.height(eventCardBigHeight), contentAlignment = Alignment.Center) {
                    AllySkillsView(
                        Modifier
                            .fillMaxWidth()
                            .padding(horizontal = paddingMediumPlus),
                        ally = ally,
                        role = role
                    )
                    ShadowedView(cardSize = CardSize.Large) {
                        SingleDetailAllyCard(ally, role = role)
                    }
                }
                AllyPropView(ally = ally, role = role)
                Spacer(modifier = Modifier.size(paddingSmallPlus))
                if (!ally.peek && !repo.gameMode.value.isPvp() && !repo.gameMode.value.isTower()) {
                    AllyStarUpView(ally = ally)
                }
            }
            if (GuideManager.guideIndex.value == 11) {
                Column(
                    Modifier
                        .requiredSize(
                            screenWidthInPixel.pixelToDp(),
                            eventCardBigHeight * 2.10f,
                        ) //使用 requiredSize 可以超过父元素大小
                        .background(B65) //B85
                        .clickable {
                            Dialogs.selectOneSkillDialog.value = Pair({
                                it.isBattle() && it.equipAllyUuid.isEmpty()
                            }, { skill ->
                                Dialogs.allyDetailDialog.value =
                                    BattleManager.equipSkillToAlly(
                                        skill = skill, ally = ally
                                    )
                            })
                            show.value = null
                            GuideManager.toNextGuide()
                        },
                ) {
                    Box(
                        modifier = Modifier.padding(
                            top = 292.composeDp(),
                            start = paddingMediumPlus,
                        ),
                    ) {
                        EmptyIconView(
                            modifier = Modifier.width(ItemSize.Large.frameSize * 1.2f),
                            itemSize = ItemSize.Large
                        ) {
                            Dialogs.selectOneSkillDialog.value = Pair({
                                it.isBattle() && it.equipAllyUuid.isEmpty()
                            }, { skill ->
                                Dialogs.allyDetailDialog.value =
                                    BattleManager.equipSkillToAlly(
                                        skill = skill, ally = ally
                                    )
                            })
                            show.value = null
                            GuideManager.toNextGuide()
                        }
                    }
                    GuidePointAndText(
                        text = stringResource(R.string.guide12),
                        handType = HandType.UP_HAND,
                        offsetY = -paddingHuge
                    )
                }
            }
        }
    }
}

@Composable
fun SingleDetailAllyCard(ally: Ally, role: Role) {
    val race = role.getRace()
    Box(Modifier.size(eventCardBigWidth, eventCardBigHeight)) {
        Image(
            modifier = Modifier
                .fillMaxSize()
                .padding(bottom = paddingSmallPlus)
                .clip(RoundedCornerShape(eventCardBigWidth / 22)),
            contentScale = ContentScale.Crop,
            alignment = Alignment.TopCenter,
            painter = painterResource(id = getImageResourceDrawable(race.pic)),
            contentDescription = race.name
        )
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(id = ally.getCardFrameDrawable()),
            contentDescription = null
        )
        AllyTitleView(
            Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = paddingLarge),
            ally
        )
        AllyTypeView(
            Modifier
                .align(Alignment.TopStart)
                .graphicsLayer {
                    translationX = -paddingLarge.toPx()
                    translationY = -paddingLarge.toPx()
                    clip = false
                }, ally
        )
    }
}

@Composable
fun AllyTitleView(modifier: Modifier, ally: Ally) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = ally.name,
            style = MaterialTheme.typography.h1,
            textAlign = TextAlign.Center
        )
        Spacer(modifier = Modifier.size(paddingSmall))
        Stars(
            stars = ally.star,
            starHeight = cardStarBigSize
        )
    }
}

@Composable
fun AllyTypeView(modifier: Modifier, ally: Ally) {
    EffectButton(modifier = modifier.size(imageLarge), onClick = {
        ally.getRaceTips().toast()
    }) {
        Image(
            modifier = Modifier.fillMaxSize(),
            painter = painterResource(id = R.drawable.card_type),
            contentDescription = ally.name
        )
        Image(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingMediumPlus)
                .graphicsLayer {
                    translationX = -padding1.toPx()
                    translationY = -padding1.toPx()
                },
            painter = painterResource(ally.getElementTypeRes()),
            contentDescription = ally.name
        )
    }
}


@Composable
fun AllyPropView(modifier: Modifier = Modifier, ally: Ally, role: Role) {
    Box(
        modifier = modifier.height(eventCardBigHeight / 2.2f)
    ) {
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(id = R.drawable.common_big_frame),
            contentDescription = null
        )
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(vertical = paddingMedium),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Row {
                Text(
                    text = "【${stringResource(id = com.moyu.core.R.string.ally)}】",
                    style = MaterialTheme.typography.h3
                )
                if (ally.empire != "0") {
                    Text(text = "【${ally.empire}】", style = MaterialTheme.typography.h3)
                }
            }
            Spacer(modifier = Modifier.size(paddingMedium))
            FlowRow(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly,
                verticalArrangement = Arrangement.spacedBy(paddingSmallPlus),
                overflow = FlowRowOverflow.Visible,
                maxItemsInEachRow = 4
            ) {
                role.getCurrentProperty().MainPropertyLine(
                    originProperty = role.getCurrentProperty(),
                    textStyle = MaterialTheme.typography.body1
                )
            }
            Spacer(modifier = Modifier.size(paddingMedium))
            Text(
                modifier = Modifier
                    .align(Alignment.Start)
                    .padding(horizontal = paddingMedium).verticalScroll(rememberScrollState()),
                text = ally.story,
                style = MaterialTheme.typography.h5,
                color = W50,
                fontFamily = FontFamily.Monospace,
                fontStyle = FontStyle.Italic
            )
        }
    }
}

@Composable
fun AllyStarUpView(modifier: Modifier = Modifier, ally: Ally) {
    Box(
        modifier = modifier.height(eventCardBigHeight / 3)
    ) {
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(id = R.drawable.common_big_frame),
            contentDescription = null
        )
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(vertical = paddingMedium),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.SpaceAround
        ) {
            Spacer(modifier = Modifier.size(paddingSmall))
            val maxStar = ally.star >= ally.starLimit
            val num = if (ally.inGame) {
                BattleManager.allyGameData.filter { it.mainId == ally.mainId }.size - 1
            } else {
                (ally.num - 1)
            }
            Row(verticalAlignment = Alignment.CenterVertically) {
                Text(
                    text = stringResource(id = R.string.same_card) + num + "/" + ally.starUpNum,
                    style = MaterialTheme.typography.h3
                )
                if (!ally.inGame) {
                    Spacer(modifier = Modifier.size(paddingSmall))
                    DiamondPoint(cost = ally.starUpRes, color = Color.White)
                }
            }
            val buttonText =
                if (maxStar) GameApp.instance.getWrapString(R.string.star_max) else if (ally.inGame) GameApp.instance.getWrapString(
                    R.string.star_up_in_game
                ) else GameApp.instance.getWrapString(
                    R.string.star_up
                )
            val enabled = !ally.peek && ally.starUpNum != 0 && num >= ally.starUpNum && !maxStar && if (!ally.inGame) {
                AwardManager.diamond.value >= ally.starUpRes
            } else {
                true
            }
            GameButton(text = buttonText,
                buttonStyle = ButtonStyle.Orange,
                enabled = enabled,
                onClick = {
                    GameApp.globalScope.launch(gameDispatcher) {
                        repo.gameCore.getAllyPool()
                            .firstOrNull { it.mainId == ally.mainId && it.star == ally.star + 1 }
                            ?.let {
                                Dialogs.allyStarUpDialog.value = ally
                            }
                    }
                })
        }
        EffectButton(modifier = Modifier
            .align(Alignment.TopEnd)
            .padding(
                paddingSmall
            ), onClick = {
            Dialogs.allyStarUpDialog.value = ally
        }) {
            Image(
                modifier = Modifier.size(imageSmallPlus),
                painter = painterResource(id = R.drawable.common_information),
                contentDescription = stringResource(id = R.string.star_up_preview)
            )
        }
    }
}

@Composable
fun AllySkillsView(modifier: Modifier, ally: Ally, role: Role) {
    // 屏蔽halo，halo机制是通过挂载到enemy身上实现的，但是不要显示
    val skills = role.getSkills().filter { !it.isHalo() }
    val maxSkillSize = FIXED_SKILL_SIZE + ally.skillNum
    FlowRow(
        modifier = modifier,
        horizontalArrangement = Arrangement.SpaceBetween,
        overflow = FlowRowOverflow.Visible,
        maxItemsInEachRow = 2
    ) {
        repeat(max(skills.size, maxSkillSize)) { index ->
            Box(
                modifier = Modifier
                    .width(ItemSize.Large.frameSize * 1.2f)
                    .padding(bottom = paddingLarge)
                    .graphicsLayer {
                        if (maxSkillSize == 7 && index % 2 == 0) {
                            translationY = -paddingHugeLite.toPx()
                        }
                    }, contentAlignment = Alignment.Center
            ) {
                val gif = remember {
                    mutableStateOf(false)
                }
                skills.getOrNull(index)?.let { skill ->
                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                        Box(contentAlignment = Alignment.Center) {
                            SingleSkillView(
                                Modifier.fillMaxWidth(),
                                skill = skill,
                                itemSize = ItemSize.Large,
                                showName = false,
                                frame = if (skill.isBattleSkill()) skill.quality()
                                    .getQualityFrame() else ally.quality.getQualityFrame()
                            )
                            GifView(
                                modifier = Modifier.size(ItemSize.Large.frameSize),
                                gif.value,
                                8,
                                "equipon_"
                            )
                        }
                        Spacer(modifier = Modifier.size(paddingMediumPlus))
                        TextLabel(
                            modifier = Modifier,
                            text = skill.name,
                            style = MaterialTheme.typography.body1
                        )
                    }
                    if (!skill.isSolidSkill() && !repo.inBattle.value) {
                        IconView(
                            Modifier
                                .align(Alignment.TopEnd)
                                .graphicsLayer {
                                    translationY = -paddingSmall.toPx()
                                },
                            res = R.drawable.menu8_exit,
                            itemSize = ItemSize.Small,
                            frame = null,
                            name = stringResource(R.string.remove)
                        ) {
                            Dialogs.allyDetailDialog.value =
                                BattleManager.unEquipSkillToAlly(skill)
                        }
                    }
                } ?: run {
                    gif.value = true
                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                        Box(contentAlignment = Alignment.Center) {
                            if (!ally.peek) {
                                val locked =
                                    ally.skillNum <= index - skills.filter { it.equipAllyUuid.isEmpty() }.size
                                EmptyIconView(
                                    modifier = Modifier.width(ItemSize.Large.frameSize * 1.2f),
                                    itemSize = ItemSize.Large
                                ) {
                                    if (repo.inBattle.value) {
                                        GameApp.instance.getWrapString(R.string.in_battle_can_not_adjust)
                                            .toast()
                                    } else if (!role.isPlayer()) {
                                        GameApp.instance.getWrapString(R.string.only_ally_can_adjust)
                                            .toast()
                                    } else if (locked) {
                                        GameApp.instance.getWrapString(R.string.unlock_tips).toast()
                                    } else if (!repo.inGame.value) {
                                        GameApp.instance.getWrapString(R.string.equip_tips).toast()
                                    } else {
                                        Dialogs.selectOneSkillDialog.value = Pair({
                                            it.isBattle() && it.equipAllyUuid.isEmpty()
                                        }, { skill ->
                                            Dialogs.allyDetailDialog.value =
                                                BattleManager.equipSkillToAlly(
                                                    skill = skill, ally = ally
                                                )
                                        })
                                    }
                                }
                                if (locked) {
                                    Image(
                                        modifier = Modifier.size(ItemSize.Large.itemSize),
                                        painter = painterResource(R.drawable.common_lock),
                                        contentDescription = null,
                                    )
                                }
                            }
                        }
                        Spacer(modifier = Modifier.height(smallButtonHeight)) // 对齐
                    }
                }
                if (!ally.peek) {
                    Image(
                        modifier = Modifier
                            .align(Alignment.BottomCenter)
                            .width(ItemSize.Large.frameSize * 1.2f)
                            .scale(1.1f)
                            .zIndex(-1f)
                            .graphicsLayer {
                                translationY = -paddingMedium.toPx()
                            }, contentScale = ContentScale.FillWidth, painter = painterResource(
                            id = R.drawable.map_base
                        ), contentDescription = null
                    )
                }
            }
        }
    }
}