package com.moyu.chuanqirensheng.screen.ally

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.repeatable
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.logic.guide.GuideManager
import com.moyu.chuanqirensheng.logic.guide.SKILL_GUIDE_START
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.equip.SearchView
import com.moyu.chuanqirensheng.screen.filter.CommonFilterView
import com.moyu.chuanqirensheng.screen.filter.CommonOrderView
import com.moyu.chuanqirensheng.screen.filter.FilterLayout
import com.moyu.chuanqirensheng.screen.filter.ItemFilter
import com.moyu.chuanqirensheng.screen.filter.OrderLayout
import com.moyu.chuanqirensheng.screen.filter.allyFilterList
import com.moyu.chuanqirensheng.screen.filter.allyOrderList
import com.moyu.chuanqirensheng.ui.theme.gapSmallPlus
import com.moyu.chuanqirensheng.ui.theme.padding80
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.core.model.ally.Ally


@Composable
fun AllyPage() {
    val search = remember {
        mutableStateOf("")
    }
    val showFilter = remember {
        mutableStateOf(false)
    }
    val filter = remember {
        mutableStateListOf<ItemFilter<Ally>>()
    }
    val showOrder = remember {
        mutableStateOf(false)
    }
    val order = remember {
        mutableStateOf(allyOrderList.first())
    }
    val list = repo.allyManager.data.filter {
        if (search.value.isNotEmpty()) {
            it.name.contains(search.value)
        } else true
    }.filter { ally ->
        filter.all { it.filter.invoke(ally) }
    }.sortedBy { order.value.order?.invoke(it) }
    DisposableEffect(Unit) {
        onDispose {
            repo.allyManager.setUnNew()
        }
    }
    Box(
        modifier = Modifier
            .fillMaxSize()
            .paint(
                painterResource(id = R.drawable.common_page_frame),
                contentScale = ContentScale.FillBounds
            ),
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize().padding(bottom = paddingMedium)
                .verticalScroll(rememberScrollState())
        ) {
            Spacer(modifier = Modifier.size(paddingMedium))
            Row(
                Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                CommonOrderView(
                    Modifier.padding(start = paddingMedium), showOrder
                )
                SearchView(search)
                CommonFilterView(
                    Modifier.padding(end = paddingMedium), showFilter
                )
            }
            val guideAlly = if (GuideManager.guideIndex.value == SKILL_GUIDE_START + 1) repo.allyManager.data.firstOrNull { it.quality > 1 } else null
            LazyVerticalGrid(modifier = Modifier
                .weight(1f)
                .fillMaxWidth(),
                columns = GridCells.Fixed(4),
                content = {
                    items(list.size) { index ->
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            Spacer(modifier = Modifier.size(paddingMedium))
                            val ally = list[index]
                            Box {
                                SingleAllyView(
                                    ally = ally,
                                    extraInfo = if (ally.selected) stringResource(R.string.game_selected) else "",
                                    showName = true,
                                    showRed = true,
                                    itemSize = ItemSize.LargePlus,
                                    textColor = Color.White
                                ) {
                                    guideAlly?.let {
                                        // 如果有引导，则无论点哪里，都是这个角色
                                        Dialogs.allyDetailDialog.value = it
                                        GuideManager.guideIndex.value = SKILL_GUIDE_START + 2
                                    }?: run {
                                        Dialogs.allyDetailDialog.value = ally
                                        GuideManager.guideIndex.value = 999
                                        GuideManager.showGuide.value = false
                                    }
                                }
                                guideAlly?.takeIf { it.mainId == ally.mainId }?.let {
                                    val animatedOffsetY = remember { Animatable(0f) }

                                    // 当组件进入或手指类型改变时，触发跳动动画
                                    LaunchedEffect(Unit) {
                                        animatedOffsetY.animateTo(
                                            targetValue = -20f, // 设置动画的目标值
                                            animationSpec = repeatable( // 使用repeatable重复动画
                                                iterations = 999, // 设置动画无限重复
                                                animation = tween(
                                                    durationMillis = 500, // 设置动画持续时间
                                                    easing = FastOutSlowInEasing // 设置动画的缓动效果
                                                ),
                                                repeatMode = RepeatMode.Reverse // 设置动画反向重复
                                            )
                                        )
                                    }
                                    Image(
                                        modifier = Modifier
                                            .size(ItemSize.LargePlus.frameSize)
                                            .graphicsLayer {
                                                translationY = ItemSize.LargePlus.frameSize.toPx() / 2 + animatedOffsetY.value
                                                translationX = ItemSize.LargePlus.frameSize.toPx() / 2
                                            },
                                        contentScale = ContentScale.FillHeight,
                                        painter = painterResource(R.drawable.guide_icon),
                                        contentDescription = ""
                                    )
                                }
                            }
                            Spacer(modifier = Modifier.size(paddingMedium))
                        }
                    }
                })
            Spacer(Modifier.size(padding80))
        }
        OrderLayout(
            modifier = Modifier
                .align(Alignment.TopStart)
                .padding(top = gapSmallPlus, end = paddingMedium),
            show = showOrder,
            filter = order,
            filterList = allyOrderList
        )
        FilterLayout(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(top = gapSmallPlus, end = paddingMedium),
            show = showFilter,
            filter = filter,
            filterList = allyFilterList
        )
    }
}