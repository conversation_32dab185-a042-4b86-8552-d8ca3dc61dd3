package com.moyu.chuanqirensheng.screen.button

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.imageMedium

@Composable
fun RefreshButton(modifier: Modifier = Modifier, text: String, callback: () -> Unit) {
    EffectButton(modifier = modifier, onClick = {
        callback()
    }) {
        Box(
            modifier = Modifier
                .size(imageLarge), contentAlignment = Alignment.Center
        ) {
            Image(
                modifier = Modifier.size(imageMedium),
                painter = painterResource(id = R.drawable.shop_icon_refresh),
                contentDescription = text
            )
        }
    }
}