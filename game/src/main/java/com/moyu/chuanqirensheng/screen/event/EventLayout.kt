package com.moyu.chuanqirensheng.screen.event

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.TweenSpec
import androidx.compose.animation.core.animateIntAsState
import androidx.compose.animation.expandHorizontally
import androidx.compose.animation.shrinkHorizontally
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.setting.SettingManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.role.UserImageView
import com.moyu.chuanqirensheng.ui.theme.answerHeight
import com.moyu.chuanqirensheng.ui.theme.answerWidth
import com.moyu.chuanqirensheng.ui.theme.dialogFrameHeight
import com.moyu.chuanqirensheng.ui.theme.gapHugePlusPlus
import com.moyu.chuanqirensheng.ui.theme.gapSmallPlus
import com.moyu.chuanqirensheng.ui.theme.paddingHuge
import com.moyu.chuanqirensheng.ui.theme.paddingLargePlus
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.core.GameCore
import com.moyu.core.model.dialog.Dialog
import com.moyu.core.model.event.Event
import com.moyu.core.music.SoundEffect
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@Composable
fun EventLayout(event: Event, content: @Composable ColumnScope.() -> Unit = {}) {
    val dialogs = remember {
        mutableStateListOf<Dialog>()
    }
    val index = remember {
        mutableIntStateOf(0)
    }
    val showAnswer = remember {
        mutableStateOf(false)
    }

    val playHandler = EventManager.getOrCreateHandler(event)
    val contentVisible = remember(event.id) {
        mutableStateOf(false)
    }
    LaunchedEffect(event.id) {
        contentVisible.value = false
        dialogs.clear()
        dialogs.addAll(repo.gameCore.getDialogPool().filter { it.mainId == event.dialogId }
            .sortedBy { it.id })
        if (dialogs.isEmpty() || SettingManager.noDialog.value) {
            if (playHandler.dialogJump) {
                playHandler.eventFinished.value = true
                delay(600)
                EventManager.doEventResult(event, true)
            }
            contentVisible.value = true
        }
        index.intValue = 0
        showAnswer.value = false
    }

    AnimatedVisibility(
        enter = expandHorizontally(),
        exit = shrinkHorizontally(), visible = contentVisible.value
    ) {
        Column(
            modifier = Modifier.fillMaxSize(), content = content
        )
    }
    if (!SettingManager.noDialog.value) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.BottomCenter
        ) {
            dialogs.getOrNull(index.intValue)?.let {
                MeetingDialogView(it) {
                    if (index.intValue >= dialogs.size - 1) {
                        dialogs.clear()
                        if (playHandler.dialogJump) {
                            GameApp.globalScope.launch(Dispatchers.Main) {
                                playHandler.eventFinished.value = true
                                delay(1200)
                                EventManager.doEventResult(event, true)
                            }
                        }
                        contentVisible.value = true
                    } else {
                        if (it.answer1 == "0") {
                            index.intValue += 1
                        } else {
                            showAnswer.value = true
                        }
                    }
                }
            }
            if (dialogs.getOrNull(index.intValue)?.answer1 != "0" && showAnswer.value) {
                Box(
                    modifier = Modifier.align(Alignment.TopCenter),
                ) {
                    if (dialogs.isNotEmpty()) {
                        Column {
                            Spacer(modifier = Modifier.size(gapHugePlusPlus))
                            OneMeetingRow(1, dialogs[index.intValue]) {
                                index.intValue += 1
                                showAnswer.value = false
                            }
                            Spacer(modifier = Modifier.size(gapSmallPlus))
                            OneMeetingRow(2, dialogs[index.intValue]) {
                                index.intValue += 1
                                showAnswer.value = false
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun OneMeetingRow(answer: Int, dialog: Dialog, doAnswer: (answer: Int) -> Unit) {
    val text = when (answer) {
        1 -> dialog.answer1
        else -> dialog.answer2
    }
    if (text != "0") {
        Box(
            Modifier.size(answerWidth, answerHeight), contentAlignment = Alignment.Center
        ) {
            EffectButton(
                onClick = {
                    doAnswer(answer)
                }
            ) {
                Image(
                    modifier = Modifier
                        .fillMaxSize(),
                    contentScale = ContentScale.FillBounds,
                    painter = painterResource(id = R.drawable.common_long_button1),
                    contentDescription = null
                )
                Text(
                    text = text,
                    style = MaterialTheme.typography.h3,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(horizontal = paddingMedium)
                )
            }
        }
    }
}

@Composable
fun MeetingDialogView(dialog: Dialog, callback: () -> Unit) {
    val text = dialog.desc
    val bubbleMyAnimEnable = remember(text) {
        mutableStateOf("")
    }
    val myBubbleProgress by animateIntAsState(
        targetValue = if (bubbleMyAnimEnable.value.isNotEmpty()) text.length else 0,
        animationSpec = TweenSpec(
            durationMillis = if (bubbleMyAnimEnable.value.isNotEmpty()) 1500 else 0,
        ),
        label = "",
    )
    LaunchedEffect(text) {
        GameCore.instance.onBattleEffect(SoundEffect.DuringDialog)
        bubbleMyAnimEnable.value = text
    }
    EffectButton(
        Modifier
            .fillMaxSize(), onClick = {
            callback()
        }) {
        Column(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(horizontal = paddingSmall)
        ) {
            UserImageView(headRes = dialog.npcPic.takeIf { it != "0" }
                ?: GameApp.instance.getAvatarUrl(),
                name = dialog.npcName.takeIf { it != "0" } ?: GameApp.instance.getUserName())
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(dialogFrameHeight),
                contentAlignment = Alignment.Center
            ) {
                Image(
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.FillBounds,
                    painter = painterResource(id = R.drawable.dialog_frame),
                    contentDescription = null,
                )
                Text(
                    modifier = Modifier
                        .align(Alignment.TopStart)
                        .padding(paddingLargePlus),
                    text = dialog.desc.take(myBubbleProgress),
                    style = MaterialTheme.typography.h2,
                    color = Color.Black
                )
                Text(
                    modifier = Modifier
                        .align(Alignment.BottomEnd)
                        .padding(paddingHuge),
                    text = ">>> " + stringResource(R.string.continue_text),
                    style = MaterialTheme.typography.h4,
                    color = Color.Black
                )
            }
        }
    }
}