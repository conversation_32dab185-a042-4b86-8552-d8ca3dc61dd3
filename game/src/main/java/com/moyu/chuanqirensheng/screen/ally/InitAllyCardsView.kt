package com.moyu.chuanqirensheng.screen.ally

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import com.moyu.chuanqirensheng.screen.effect.StrokedText
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.logic.unlock.UnlockManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.effect.GifView
import com.moyu.chuanqirensheng.screen.effect.equipGif
import com.moyu.chuanqirensheng.screen.skill.EmptyIconView
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.oneRoleWidth
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.paddingTiny
import com.moyu.core.logic.role.ALLY_ROW1_SECOND
import com.moyu.core.logic.role.positionListAllies
import com.moyu.core.model.ally.Ally

const val MAX_ALLY_SKILL_SIZE = 15
const val MAX_HERO_SIZE = 15

@Composable
fun InitAllyCardsView(
    modifier: Modifier = Modifier,
    allies: List<Ally>,
    capacity: Int,
    allyClick: (Ally) -> Unit = { Dialogs.allyDetailDialog.value = it },
    emptyClick: () -> Unit
) {
    FlowRow(
        modifier = modifier,
        horizontalArrangement = Arrangement.SpaceEvenly,
        overflow = FlowRowOverflow.Visible,
        maxItemsInEachRow = 5,
    ) {
        var skillCount = 0
        repeat(capacity) { index ->
            val unlock = UnlockManager.getInitAllyUnlockByIndex(index)
            val unlocked = UnlockManager.getUnlockedFlow(unlock)
            Box(modifier = Modifier.padding(vertical = paddingTiny)) {
                val gif = remember {
                    mutableStateOf(false)
                }
                allies.getOrNull(skillCount)?.takeIf { unlocked }?.let { ally ->
                    skillCount += 1
                    Box(contentAlignment = Alignment.Center) {
                        SingleAllyView(
                            ally = ally,
                            extraInfo = ally.extraInfo,
                            showName = false,
                            itemSize = ItemSize.LargePlus,
                            selectCallBack = allyClick
                        )
                        GifView(
                            modifier = Modifier.size(ItemSize.LargePlus.frameSize),
                            gif.value,
                            8,
                            "equipon_"
                        )
                    }
                } ?: run {
                    gif.value = true
                    Box(contentAlignment = Alignment.Center) {
                        EmptyIconView(itemSize = ItemSize.LargePlus) {
                            if (unlocked) {
                                emptyClick()
                            } else {
                                unlock.desc.toast()
                            }
                        }
                        if (!unlocked) {
                            Image(
                                modifier = Modifier.size(imageLarge),
                                painter = painterResource(R.drawable.common_lock),
                                contentDescription = stringResource(id = R.string.locked),
                            )
                        }
                    }
                }
            }
        }
    }
}


@Composable
fun AllyCardsRow(
    modifier: Modifier = Modifier,
    allies: Map<Int, Ally>,
    capacity: Int,
    showName: Boolean = false,
    showHp: Boolean = false,
    textColor: Color = Color.White,
    allyClick: (Ally) -> Unit = { Dialogs.allyDetailDialog.value = it },
    emptyClick: (Int) -> Unit
) {
    FlowRow(
        modifier = modifier, horizontalArrangement = Arrangement.SpaceEvenly,
        verticalArrangement = Arrangement.spacedBy(padding16),
        overflow = FlowRowOverflow.Visible,
        maxItemsInEachRow = 3
    ) {
        if (capacity == 1) {
            Spacer(modifier = Modifier.size(oneRoleWidth))
            AllyCard(allies, showName, showHp, textColor, allyClick, emptyClick, ALLY_ROW1_SECOND)
            Spacer(modifier = Modifier.size(oneRoleWidth))
        } else {
            positionListAllies.forEach { position ->
                AllyCard(allies, showName, showHp, textColor, allyClick, emptyClick, position)
            }
        }
    }
}


@Composable
fun AllyCard(
    allies: Map<Int, Ally>,
    showName: Boolean,
    showHp: Boolean,
    textColor: Color,
    allyClick: (Ally) -> Unit,
    emptyClick: (Int) -> Unit,
    position: Int
) {
    Box(
        modifier = Modifier.width(oneRoleWidth),
        contentAlignment = Alignment.Center
    ) {
        allies[position]?.let { ally ->
            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                Box(contentAlignment = Alignment.Center) {
                    SingleAllyView(
                        ally = ally,
                        extraInfo = ally.extraInfo,
                        showName = false,
                        textColor = textColor,
                        itemSize = ItemSize.LargePlus,
                        selectCallBack = allyClick,
                        showEffect = false,
                        showNum = false,
                        showHp = showHp
                    )
                    GifView(
                        modifier = Modifier.size(ItemSize.LargePlus.frameSize),
                        true,
                        equipGif.count,
                        equipGif.gif
                    )
                }
                if (showName) {
                    StrokedText(text = ally.name, style = MaterialTheme.typography.h5)
                }
            }
        } ?: run {
            Box(contentAlignment = Alignment.Center) {
                EmptyIconView(itemSize = ItemSize.LargePlus) {
                    emptyClick(position)
                }
            }
        }
    }
}
