package com.moyu.chuanqirensheng.screen.dialog

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.event.detail.FATAL_ENEMY
import com.moyu.chuanqirensheng.logic.event.detail.RACE_BATTLE
import com.moyu.chuanqirensheng.logic.record.RecordManager
import com.moyu.chuanqirensheng.logic.task.onTaskFatalPlay
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.ally.SingleAllyView
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.equip.SingleHeroView
import com.moyu.chuanqirensheng.screen.skill.IconView
import com.moyu.chuanqirensheng.ui.theme.gapSmall
import com.moyu.chuanqirensheng.ui.theme.paddingHuge
import com.moyu.chuanqirensheng.ui.theme.paddingHugePlus
import com.moyu.chuanqirensheng.ui.theme.paddingLarge
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.ui.theme.paddingSmallPlus
import com.moyu.core.model.event.Event
import com.moyu.core.model.sell.Award
import com.moyu.core.model.sell.toAward
import com.moyu.core.model.sell.toAwards
import com.moyu.core.util.RANDOM
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun FatalEnemyDialog(showDungeon: MutableState<Event?>) {
    showDungeon.value?.let { event ->
        CommonDialog(
            title = stringResource(R.string.how_to_deal_fatal_enemy),
            onDismissRequest = EMPTY_DISMISS
        ) {
            val select = remember {
                mutableStateOf(0)
            }
            val (selection1Name, selection1Sub) = when (event.play) {
                FATAL_ENEMY -> {
                    Pair(
                        stringResource(R.string.capture),
                        stringResource(id = R.string.fatal_sub_text1)
                    )
                }

                RACE_BATTLE -> {
                    Pair(
                        stringResource(R.string.ask_join),
                        stringResource(id = R.string.race_sub_text1)
                    )
                }

                else -> {
                    Pair(
                        stringResource(R.string.develop),
                        stringResource(id = R.string.curse_sub_text1)
                    )
                }
            }
            val (selection2Name, selection2Sub) = when (event.play) {
                FATAL_ENEMY -> {
                    Pair(
                        stringResource(R.string.stop_war_talk),
                        stringResource(id = R.string.fatal_sub_text2)
                    )
                }

                RACE_BATTLE -> {
                    Pair(
                        stringResource(R.string.rest),
                        stringResource(id = R.string.race_sub_text2)
                    )
                }

                else -> {
                    Pair(
                        stringResource(R.string.kill_all),
                        stringResource(id = R.string.curse_sub_text2)
                    )
                }
            }
            val icon1 = when (event.play) {
                FATAL_ENEMY -> {
                    R.drawable.battle_icon1
                }

                RACE_BATTLE -> {
                    R.drawable.battle_icon6
                }

                else -> {
                    R.drawable.battle_icon3
                }
            }
            val icon2 = when (event.play) {
                FATAL_ENEMY -> {
                    R.drawable.battle_icon2
                }

                RACE_BATTLE -> {
                    R.drawable.battle_icon5
                }

                else -> {
                    R.drawable.battle_icon4
                }
            }
            DisposableEffect(Unit) {
                onDispose {
                    EventManager.doEventBattleResult(EventManager.selectedEvent.value, true)
                }
            }
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = paddingMedium),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Spacer(modifier = Modifier.size(paddingHuge))
                AnimatedVisibility(visible = select.value == 0) {
                    Column {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            IconView(itemSize = ItemSize.LargePlus, res = icon1, name = selection1Name)
                            Spacer(modifier = Modifier.size(paddingSmall))
                            Column(modifier = Modifier.weight(1f)) {
                                Text(
                                    text = selection1Name,
                                    style = MaterialTheme.typography.h1,
                                    color = Color.Black
                                )
                                Spacer(modifier = Modifier.padding(paddingSmallPlus))
                                Text(
                                    text = selection1Sub,
                                    style = MaterialTheme.typography.h4,
                                    color = Color.Black
                                )
                            }
                            Spacer(modifier = Modifier.size(paddingSmall))
                            GameButton(
                                text = stringResource(id = R.string.do_select),
                                buttonSize = ButtonSize.Medium,
                                buttonStyle = ButtonStyle.Orange,
                                onClick = {
                                    if (select.value == 0) {
                                        if (event.play == FATAL_ENEMY) {
                                            GameApp.globalScope.launch(Dispatchers.Main) {
                                                RecordManager.specialDecision(0)
                                            }
                                            select.value = 1
                                        } else {
                                            showDungeon.value = null
                                            val poolId = event.playPara2[1]
                                            repo.gameCore.getPoolById(poolId.toInt())
                                                .toAward().apply {
                                                    GameApp.globalScope.launch(Dispatchers.Main) {
                                                        AwardManager.gainAward(this@apply)
                                                    }
                                                    Dialogs.awardDialog.value = this
                                                }
                                        }
                                        GameApp.globalScope.launch(Dispatchers.Main) {
                                            onTaskFatalPlay(1, event)
                                        }
                                    }
                                })
                            Spacer(modifier = Modifier.size(paddingSmall))
                        }
                        Spacer(modifier = Modifier.size(gapSmall))
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            IconView(itemSize = ItemSize.LargePlus, res = icon2, name = selection2Name)
                            Spacer(modifier = Modifier.size(paddingSmall))
                            Column(modifier = Modifier.weight(1f)) {
                                Text(
                                    text = selection2Name,
                                    style = MaterialTheme.typography.h1,
                                    color = Color.Black
                                )
                                Spacer(modifier = Modifier.padding(paddingSmallPlus))
                                Text(
                                    text = selection2Sub,
                                    style = MaterialTheme.typography.h4,
                                    color = Color.Black
                                )
                            }
                            Spacer(modifier = Modifier.size(paddingSmall))
                            GameButton(
                                text = stringResource(id = R.string.do_select),
                                buttonSize = ButtonSize.Medium,
                                buttonStyle = ButtonStyle.Orange,
                                onClick = {
                                    if (select.value == 0) {
                                        if (event.play == FATAL_ENEMY) {
                                            GameApp.globalScope.launch(Dispatchers.Main) {
                                                RecordManager.specialDecision(1)
                                            }
                                            select.value = 2
                                        } else {
                                            showDungeon.value = null
                                            val poolId = event.playPara2[2]
                                            repo.gameCore.getPoolById(poolId.toInt())
                                                .toAward().apply {
                                                    GameApp.globalScope.launch(Dispatchers.Main) {
                                                        AwardManager.gainAward(this@apply)
                                                    }
                                                    Dialogs.awardDialog.value = this
                                                }
                                        }
                                        GameApp.globalScope.launch(Dispatchers.Main) {
                                            onTaskFatalPlay(2, event)
                                        }
                                    }
                                })
                            Spacer(modifier = Modifier.size(paddingSmall))
                        }
                    }
                }
                AnimatedVisibility(visible = select.value == 1) {
                    Column {
                        Text(
                            text = stringResource(R.string.select_your_award),
                            style = MaterialTheme.typography.h3,
                            color = Color.Black
                        )
                        Spacer(modifier = Modifier.size(paddingLarge))
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceEvenly
                        ) {
                            val allies = remember {
                                repo.gameCore.getPoolById(event.playPara2.first().toInt())
                                    .toAward().allies.shuffled(
                                        RANDOM
                                    ).take(3)
                            }
                            allies.forEach {
                                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                                    SingleAllyView(
                                        ally = it,
                                        showName = true,
                                        itemSize = ItemSize.LargePlus
                                    )
                                    Spacer(modifier = Modifier.size(paddingSmall))
                                    GameButton(
                                        text = stringResource(id = R.string.do_select),
                                        buttonSize = ButtonSize.MediumMinus,
                                        buttonStyle = ButtonStyle.Orange,
                                        onClick = {
                                            showDungeon.value = null
                                            Award(allies = listOf(it)).apply {
                                                GameApp.globalScope.launch(Dispatchers.Main) {
                                                    AwardManager.gainAward(this@apply)
                                                }
                                                Dialogs.awardDialog.value = this
                                            }
                                        })
                                }
                            }
                        }
                    }
                }
                AnimatedVisibility(visible = select.value == 2) {
                    Column {
                        Text(
                            text = stringResource(R.string.choose_your_award),
                            style = MaterialTheme.typography.h3,
                            color = Color.Black
                        )
                        Spacer(modifier = Modifier.size(paddingLarge))
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceEvenly
                        ) {
                            val heroes = remember {
                                repo.gameCore.getPoolById(event.playPara2[1].toInt())
                                    .toAward().heroes.shuffled(
                                        RANDOM
                                    ).take(3)
                            }
                            heroes.forEach {
                                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                                    SingleHeroView(
                                        hero = it,
                                        showName = true,
                                        itemSize = ItemSize.LargePlus
                                    )
                                    Spacer(modifier = Modifier.size(paddingSmall))
                                    GameButton(
                                        text = stringResource(id = R.string.do_select),
                                        buttonSize = ButtonSize.MediumMinus,
                                        buttonStyle = ButtonStyle.Orange,
                                        onClick = {
                                            showDungeon.value = null
                                            Award(heroes = listOf(it)).apply {
                                                GameApp.globalScope.launch(Dispatchers.Main) {
                                                    AwardManager.gainAward(this@apply)
                                                }
                                                Dialogs.awardDialog.value = this
                                            }
                                        })
                                }
                            }
                        }
                    }
                }
                Spacer(modifier = Modifier.weight(1f))
                Spacer(modifier = Modifier.size(paddingHuge))
            }
        }
    }
}


