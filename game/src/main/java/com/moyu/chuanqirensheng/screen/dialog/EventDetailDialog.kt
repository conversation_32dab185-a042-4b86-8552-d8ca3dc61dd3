package com.moyu.chuanqirensheng.screen.dialog

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.datastore.KEY_GUIDE_INDEX
import com.moyu.chuanqirensheng.datastore.KEY_NON_BATTLE_DONE
import com.moyu.chuanqirensheng.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.logic.award.toAward
import com.moyu.chuanqirensheng.logic.award.toConditionAward
import com.moyu.chuanqirensheng.logic.event.EventConditionLayoutAward
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.event.createEnemyRole
import com.moyu.chuanqirensheng.logic.event.isBattle
import com.moyu.chuanqirensheng.logic.event.isRiotGuide
import com.moyu.chuanqirensheng.logic.event.isStartingEvent
import com.moyu.chuanqirensheng.logic.event.triggerEvent
import com.moyu.chuanqirensheng.logic.guide.BATTLE_GUIDE_START
import com.moyu.chuanqirensheng.logic.guide.FIRST_GUIDE_END
import com.moyu.chuanqirensheng.logic.guide.GuideManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.ally.SingleAllyView
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.guide.GuidePointAndText
import com.moyu.chuanqirensheng.text.playNameMap
import com.moyu.chuanqirensheng.text.playRuleMap
import com.moyu.chuanqirensheng.ui.theme.B65
import com.moyu.chuanqirensheng.ui.theme.detailBigHeight
import com.moyu.chuanqirensheng.ui.theme.hpWidth
import com.moyu.chuanqirensheng.ui.theme.imageSmallPlus
import com.moyu.chuanqirensheng.ui.theme.paddingLarge
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.ui.theme.paddingSmallPlus
import com.moyu.chuanqirensheng.ui.theme.singleRoleHeight
import com.moyu.chuanqirensheng.util.composeDp
import com.moyu.chuanqirensheng.util.pixelToDp
import com.moyu.chuanqirensheng.util.screenWidthInPixel
import com.moyu.core.model.event.Event

@Composable
fun EventDetailDialog(eventSwitch: MutableState<Event?>) {
    eventSwitch.value?.let { event ->
        CommonDialog(title = event.name,
            extraIcon = R.drawable.common_information,
            extraIconClick = {
                Dialogs.alertDialog.value = CommonAlert(
                    title = "${
                        playNameMap[event.play]?.replace(
                            "\n", ""
                        )
                    }${GameApp.instance.getWrapString(R.string.rules)}",
                    content = playRuleMap[event.play] ?: "",
                    onlyConfirm = true
                )
            },
            onDismissRequest = {
                if (GuideManager.guideIndex.value == FIRST_GUIDE_END  && event.isRiotGuide() && !GuideManager.nonBattleDone.value) {
                    GuideManager.guideIndex.value = FIRST_GUIDE_END
                    GuideManager.nonBattleDone.value = true
                    setBooleanValueByKey(KEY_NON_BATTLE_DONE, true)
                    setIntValueByKey(KEY_GUIDE_INDEX, GuideManager.guideIndex.value)

                    Dialogs.alertDialog.value = CommonAlert(
                        title = "${
                            playNameMap[event.play]?.replace(
                                "\n", ""
                            )
                        }${GameApp.instance.getWrapString(R.string.rules)}",
                        content = playRuleMap[event.play] ?: "",
                        onlyConfirm = true
                    )
                } else {
                    eventSwitch.value = null
                }
            }) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = paddingMedium)
                    .verticalScroll(rememberScrollState()),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Column(Modifier.fillMaxWidth()) {
                    Text(
                        text = event.startText,
                        style = MaterialTheme.typography.h3,
                        color = Color.Black
                    )
                    if (event.isBattle()) {
                        Spacer(modifier = Modifier.size(paddingSmallPlus))
                        Text(
                            text = stringResource(R.string.enemy),
                            style = MaterialTheme.typography.h3,
                            color = Color.Black
                        )
                        val pool = repo.gameCore.getPoolById(event.playPara1.first())
                        val enemies = pool.pool.map {
                            repo.gameCore.getRaceById(it)
                        }.map {
                            createEnemyRole(it, event)
                        }.toMutableList()
                        Row {
                            Box(
                                Modifier.size(hpWidth, singleRoleHeight),
                                contentAlignment = Alignment.Center
                            ) {
                                val role = createEnemyRole(enemies[0].getRace(), event)
                                SingleAllyView(
                                    ally = repo.gameCore.getAllyPool()
                                        .first { it.id == enemies[0].getRace().id }
                                        .copy(peek = true)
                                ) {
                                    Dialogs.roleDetailDialog.value = role
                                }
                            }
                            Spacer(modifier = Modifier.size(paddingSmallPlus))
                            enemies.getOrNull(1)?.let {
                                Box(
                                    Modifier.size(hpWidth, singleRoleHeight),
                                    contentAlignment = Alignment.Center
                                ) {
                                    val role = createEnemyRole(enemies[1].getRace(), event)
                                    SingleAllyView(ally = repo.gameCore.getAllyPool()
                                        .first { it.id == enemies[1].getRace().id }
                                        .copy(peek = true)) {
                                        Dialogs.roleDetailDialog.value = role
                                    }
                                }
                                Spacer(modifier = Modifier.size(paddingSmallPlus))
                            }
                            enemies.getOrNull(2)?.let {
                                Box(
                                    Modifier.size(hpWidth, singleRoleHeight),
                                    contentAlignment = Alignment.Center
                                ) {
                                    val role = createEnemyRole(enemies[2].getRace(), event)
                                    SingleAllyView(ally = repo.gameCore.getAllyPool()
                                        .first { it.id == enemies[2].getRace().id }
                                        .copy(peek = true)) {
                                        Dialogs.roleDetailDialog.value = role
                                    }
                                }
                            }
                        }
                    }
                    Spacer(modifier = Modifier.size(paddingSmallPlus))
                    Text(
                        text = stringResource(R.string.get_tips),
                        style = MaterialTheme.typography.h3,
                        color = Color.Black
                    )
                    AwardList(award = event.toAward(true))
                    Spacer(modifier = Modifier.size(paddingSmallPlus))
                    if (event.condition != 0) {
                        val award = event.toConditionAward()
                        val consumableAward = award.consumablePart()
                        val conditionAward = award.noneConsumablePart()
                        if (!consumableAward.isEmpty()) {
                            Row(verticalAlignment = Alignment.CenterVertically) {
                                Text(
                                    text = stringResource(R.string.event_start_condition_consume),
                                    style = MaterialTheme.typography.h3,
                                    color = Color.Black
                                )
                                Spacer(modifier = Modifier.size(paddingSmall))
                                EventConditionLayoutAward(
                                    award = consumableAward,
                                    itemSize = ItemSize.Medium,
                                    textColor = Color.Black
                                )
                            }
                        }
                        if (!conditionAward.isEmpty()) {
                            Row(verticalAlignment = Alignment.CenterVertically) {
                                if (!conditionAward.isEmpty()) {
                                    Text(
                                        text = stringResource(R.string.event_start_condition),
                                        style = MaterialTheme.typography.h3,
                                        color = Color.Black
                                    )
                                    Spacer(modifier = Modifier.size(paddingSmall))
                                    EventConditionLayoutAward(
                                        award = conditionAward,
                                        itemSize = ItemSize.Medium,
                                        textColor = Color.Black
                                    )
                                }
                            }
                        }
                    }
                }
                Spacer(modifier = Modifier.weight(1f))
                GameButton(text = stringResource(R.string.do_select),
                    enabled = triggerEvent(event, false),
                    buttonStyle = ButtonStyle.Orange,
                    onClick = {
                        if (EventManager.selectEvent(event)) {
                            if (!event.isStartingEvent()) {
                                if (GuideManager.guideIndex.value == FIRST_GUIDE_END) {
                                    if (event.isBattle() && !GuideManager.battleDone.value) {
                                        GuideManager.guideIndex.value = BATTLE_GUIDE_START
                                        GuideManager.showGuide.value = true
                                    }
                                }
                            }
                            eventSwitch.value = null
                        }
                    })
            }
            //  第一次 谈判或者暴动事件
            if (GuideManager.guideIndex.value == FIRST_GUIDE_END  && event.isRiotGuide() && !GuideManager.nonBattleDone.value) {
                Column(
                    Modifier
                        .requiredSize(
                            screenWidthInPixel.pixelToDp(), detailBigHeight
                        ) //使用 requiredSize 可以超过父元素大小
                        .background(B65) //.background(Color(0x834CAF50))
                        .clickable {
                            GuideManager.guideIndex.value = FIRST_GUIDE_END
                            GuideManager.nonBattleDone.value = true
                            setBooleanValueByKey(KEY_NON_BATTLE_DONE, true)
                            setIntValueByKey(KEY_GUIDE_INDEX, GuideManager.guideIndex.value)

                            Dialogs.alertDialog.value = CommonAlert(
                                title = "${
                                    playNameMap[event.play]?.replace(
                                        "\n", ""
                                    )
                                }${GameApp.instance.getWrapString(R.string.rules)}",
                                content = playRuleMap[event.play] ?: "",
                                onlyConfirm = true
                            )
                        },
                ) {
                    Column(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Box(
                            modifier = Modifier.padding(
                                top = 28.composeDp(), start = 120.composeDp()
                            )
                        ) {
                            //信息图标
                            EffectButton(modifier = Modifier
                                .padding(start = paddingSmall)
                                .size(imageSmallPlus), onClick = {

                                GuideManager.guideIndex.value = FIRST_GUIDE_END
                                GuideManager.nonBattleDone.value = true
                                setBooleanValueByKey(KEY_NON_BATTLE_DONE, true)
                                setIntValueByKey(KEY_GUIDE_INDEX, GuideManager.guideIndex.value)

                                Dialogs.alertDialog.value = CommonAlert(
                                    title = "${
                                        playNameMap[event.play]?.replace(
                                            "\n", ""
                                        )
                                    }${GameApp.instance.getWrapString(R.string.rules)}",
                                    content = playRuleMap[event.play] ?: "",
                                    onlyConfirm = true
                                )

                            }) {
                                Image(
                                    painter = painterResource(id = R.drawable.common_information),
                                    contentDescription = stringResource(id = R.string.explain),
                                )
                            }
                        }
                        GuidePointAndText(
                            text = stringResource(R.string.guide14),
                            offsetX = 100.composeDp(),
                            offsetY = -paddingLarge
                        )
                    }
                }
            }
        }
    }
}


