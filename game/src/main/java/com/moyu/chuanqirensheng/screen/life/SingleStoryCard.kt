package com.moyu.chuanqirensheng.screen.life

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.logic.story.StoryManager
import com.moyu.chuanqirensheng.logic.unlock.UnlockManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.ui.theme.imageHuge
import com.moyu.chuanqirensheng.ui.theme.imageHugeFramePlus
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.imageLargePlus
import com.moyu.chuanqirensheng.ui.theme.paddingLarge
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingSmallPlus
import com.moyu.chuanqirensheng.ui.theme.paddingTiny
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.model.story.Story


@Composable
fun SingleStoryCard(modifier: Modifier = Modifier, story: Story) {
    val lock = repo.gameCore.getUnlockById(story.unlockId)
    val unlocked = UnlockManager.getUnlockedFlow(lock)
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.SpaceBetween,
        modifier = modifier
            .padding(horizontal = paddingMedium, vertical = paddingMedium)
    ) {
        EffectButton(onClick = {
            Dialogs.storyDetailDialog.value = story
        }) {
            Image(
                modifier = Modifier
                    .width(imageHugeFramePlus),
                contentScale = ContentScale.FillWidth,
                painter = painterResource(id = R.drawable.prestige_frame),
                contentDescription = null
            )
            Image(
                painter = painterResource(getImageResourceDrawable(story.pic)),
                modifier = Modifier
                    .size(imageHuge)
                    .graphicsLayer {
                        translationX = -paddingTiny.toPx()
                        translationY = -paddingSmallPlus.toPx()
                    },
                contentScale = ContentScale.FillWidth,
                contentDescription = if (story.selected) stringResource(id = R.string.game_selected) else stringResource(
                    id = R.string.game_not_selected
                )
            )
            if (!unlocked) {
                Image(
                    painter = painterResource(id = R.drawable.common_lock),
                    modifier = Modifier
                        .size(imageLarge)
                        .align(Alignment.BottomEnd),
                    contentDescription = stringResource(id = R.string.locked)
                )
            } else if (story.selected) {
                Image(
                    painter = painterResource(id = R.drawable.common_choose),
                    modifier = Modifier
                        .size(imageLargePlus)
                        .align(Alignment.BottomEnd),
                    contentDescription = null
                )
            }
        }
        Text(
            modifier = Modifier.graphicsLayer {
                translationY = -paddingMedium.toPx()
            },
            text = story.name,
            style = MaterialTheme.typography.h3,
            maxLines = 1,
            overflow = TextOverflow.Visible,
            textAlign = TextAlign.Center
        )
        GameButton(
            modifier = Modifier.graphicsLayer {
                translationY = -paddingLarge.toPx()
            },
            text = stringResource(id = R.string.check),
            textColor = Color.White,
            buttonSize = ButtonSize.Medium,
            buttonStyle = ButtonStyle.Orange
        ) {
            Dialogs.storyDetailDialog.value = story
        }
    }
}
