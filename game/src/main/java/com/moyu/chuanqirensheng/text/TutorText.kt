package com.moyu.chuanqirensheng.text

import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp


val tutorTitle = listOf(
    GameApp.instance.getWrapString(R.string.tutor_title1),
    GameApp.instance.getWrapString(R.string.tutor_title2),
    GameApp.instance.getWrapString(R.string.tutor_title3),
    GameApp.instance.getWrapString(R.string.tutor_title4),
    GameApp.instance.getWrapString(R.string.tutor_title5),
    GameApp.instance.getWrapString(R.string.tutor_title6),
    GameApp.instance.getWrapString(R.string.tutor_title7),
    GameApp.instance.getWrapString(R.string.tutor_title8),
    GameApp.instance.getWrapString(R.string.tutor_title9),
    GameApp.instance.getWrapString(R.string.tutor_title10),
    GameApp.instance.getWrapString(R.string.tutor_title11),
    GameApp.instance.getWrapString(R.string.tutor_title12),
)

val tutorText = listOf(
    GameApp.instance.getWrapString(R.string.tutor1),
    GameApp.instance.getWrapString(R.string.tutor2),
    GameApp.instance.getWrapString(R.string.tutor3), 
    GameApp.instance.getWrapString(R.string.tutor4),
    GameApp.instance.getWrapString(R.string.tutor5),
    GameApp.instance.getWrapString(R.string.tutor6),
    GameApp.instance.getWrapString(R.string.tutor7),
    GameApp.instance.getWrapString(R.string.tutor8),
    GameApp.instance.getWrapString(R.string.tutor9),
    GameApp.instance.getWrapString(R.string.tutor10),
    GameApp.instance.getWrapString(R.string.tutor11),
    GameApp.instance.getWrapString(R.string.tutor12),
)