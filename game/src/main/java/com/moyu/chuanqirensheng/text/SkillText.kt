package com.moyu.chuanqirensheng.text

import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.core.AppWrapper
import com.moyu.core.model.skill.Skill


fun Skill.getTypeName(): String {
    return skillType.getSkillTypeName()
}

fun Int.getSkillTypeName(): String {
    return when (this) {
        1 -> AppWrapper.getString(R.string.skilltype_1)
        2 -> AppWrapper.getString(R.string.skilltype_2)
        3 -> AppWrapper.getString(R.string.skilltype_3)
        4 -> AppWrapper.getString(R.string.skilltype_4)
        5 -> AppWrapper.getString(R.string.skilltype_5)
        else -> AppWrapper.getString(R.string.skilltype_other)
    }
}

fun Int.getSkillElementName(): String {
    return when (this) {
        1 -> AppWrapper.getString(R.string.skill_element_1)
        2 -> AppWrapper.getString(R.string.skill_element_2)
        3 -> AppWrapper.getString(R.string.skill_element_3)
        4 -> AppWrapper.getString(R.string.skill_element_4)
        5 -> AppWrapper.getString(R.string.skill_element_5)
        6 -> AppWrapper.getString(R.string.skill_element_6)
        else -> AppWrapper.getString(R.string.skill_element_7)
    }
}

fun Int.getHeroElementName(): String {
    return when (this) {
        1 -> AppWrapper.getString(R.string.hero_element_1)
        2 -> AppWrapper.getString(R.string.hero_element_2)
        3 -> AppWrapper.getString(R.string.hero_element_3)
        4 -> AppWrapper.getString(R.string.hero_element_4)
        5 -> AppWrapper.getString(R.string.hero_element_5)
        else -> AppWrapper.getString(R.string.hero_element_6)
    }
}

val SKILL_LABELS = hashMapOf(
    1 to GameApp.instance.getWrapString(R.string.skill_label1),
    2 to GameApp.instance.getWrapString(R.string.skill_label2),
    3 to GameApp.instance.getWrapString(R.string.skill_label3),
    4 to GameApp.instance.getWrapString(R.string.skill_label4),
    5 to GameApp.instance.getWrapString(R.string.skill_label5),
    6 to GameApp.instance.getWrapString(R.string.skill_label6),
    7 to GameApp.instance.getWrapString(R.string.skill_label7),
    8 to GameApp.instance.getWrapString(R.string.skill_label8),
)