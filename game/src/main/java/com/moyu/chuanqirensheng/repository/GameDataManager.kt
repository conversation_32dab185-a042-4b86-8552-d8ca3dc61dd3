package com.moyu.chuanqirensheng.repository

import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.ad.AdHolder
import com.moyu.chuanqirensheng.ad.KEY_RELIVE_PLAY
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.datastore.KEY_GAME_COUNT
import com.moyu.chuanqirensheng.datastore.KEY_GAME_RELIVE_COUNT
import com.moyu.chuanqirensheng.datastore.KEY_GAME_TIME_COUNT
import com.moyu.chuanqirensheng.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.datastore.setLongValueByKey
import com.moyu.chuanqirensheng.model.award.Guarded
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.sub.report.KEY_TRY_PLAY
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.isSameDay


const val MAX_TIME_ONE_DAY = 50
const val MAX_RELIVE_TIME_ONE_DAY = 3

object GameDataManager {
    private val gameCount = Guarded(KEY_GAME_COUNT)
    private val reliveCount = Guarded(KEY_GAME_RELIVE_COUNT)
    fun init() {
        if (isNetTimeValid()) {
            if (!isSameDay(getLongFlowByKey(KEY_GAME_TIME_COUNT), getCurrentTime())) {
                setLongValueByKey(KEY_GAME_TIME_COUNT, getCurrentTime())
                gameCount.value = 0
                reliveCount.value = 0
            }
        }
    }

    fun tryPlay(callback: ()->Unit) {
        init()
        if (gameCount.value >= MAX_TIME_ONE_DAY) {
            Dialogs.alertDialog.value = CommonAlert(
                title = GameApp.instance.getWrapString(R.string.today_game_times_out),
                content = GameApp.instance.getWrapString(R.string.today_game_times_out_tips),
                onConfirm = {
                    AdHolder.playAd(KEY_TRY_PLAY) {
                        callback()
                    }
                }
            )
        } else {
            gameCount.value += 1
            callback()
        }
    }

    fun canRelive(): Boolean {
        init()
        return reliveCount.value < MAX_RELIVE_TIME_ONE_DAY
    }

    fun tryRelive(callback: ()->Unit) {
        init()
        if (reliveCount.value < MAX_RELIVE_TIME_ONE_DAY) {
            Dialogs.alertDialog.value = CommonAlert(
                title = GameApp.instance.getWrapString(R.string.force_win_title),
                content = GameApp.instance.getWrapString(R.string.force_win_tips),
                onConfirm = {
                    AdHolder.playAd(KEY_RELIVE_PLAY) {
                        reliveCount.value += 1
                        callback()
                    }
                }
            )
        }
    }
}