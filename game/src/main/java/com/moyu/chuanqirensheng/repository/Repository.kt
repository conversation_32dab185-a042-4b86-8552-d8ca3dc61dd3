package com.moyu.chuanqirensheng.repository

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.cloud.AntiCheatManager
import com.moyu.chuanqirensheng.cloud.CloudManager
import com.moyu.chuanqirensheng.feature.battlepass.BattlePass2Manager
import com.moyu.chuanqirensheng.feature.battlepass.BattlePassManager
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.feature.holiday.HolidayManager
import com.moyu.chuanqirensheng.feature.mode.EndlessGameMode
import com.moyu.chuanqirensheng.feature.mode.GameMode
import com.moyu.chuanqirensheng.feature.sign.SignManager
import com.moyu.chuanqirensheng.logic.ally.AllyManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.award.VipManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.guide.GuideManager
import com.moyu.chuanqirensheng.logic.hero.HeroManager
import com.moyu.chuanqirensheng.logic.judge.GameJudge
import com.moyu.chuanqirensheng.logic.judge.GameJudgeManager
import com.moyu.chuanqirensheng.logic.record.RecordManager
import com.moyu.chuanqirensheng.logic.record.SaveManager
import com.moyu.chuanqirensheng.logic.role.SkinManager
import com.moyu.chuanqirensheng.logic.sell.SellManager
import com.moyu.chuanqirensheng.logic.skill.SkillManager
import com.moyu.chuanqirensheng.logic.story.StoryManager
import com.moyu.chuanqirensheng.logic.talent.TalentManager
import com.moyu.chuanqirensheng.logic.task.TaskManager
import com.moyu.chuanqirensheng.logic.tcg.TcgManager
import com.moyu.chuanqirensheng.logic.unlock.UnlockManager
import com.moyu.chuanqirensheng.music.playerMusicByScreen
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.screen.effect.cardEffects
import com.moyu.chuanqirensheng.sub.report.ReportManager
import com.moyu.chuanqirensheng.sub.review.GameReviewManager
import com.moyu.chuanqirensheng.thread.gameDispatcher
import com.moyu.chuanqirensheng.util.CREATE_COUNTRY_SCREEN
import com.moyu.chuanqirensheng.util.goto
import com.moyu.chuanqirensheng.vibrate.VibrateManage.startVibrator
import com.moyu.core.GameCore
import com.moyu.core.logic.battle.BattleField
import com.moyu.core.logic.identifier.Identifier
import com.moyu.core.model.info.BattleInfo
import com.moyu.core.model.info.BattleInfoType
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.Skill
import com.moyu.core.music.SoundEffect
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

val repo = Repo()

class Repo(private val judge: GameJudge = GameJudgeManager()) : GameJudge by judge {
    val gameCore = GameCore(battleCallback)
    val battle = mutableStateOf(GameCore.EMPTY)
    val gameMode: MutableState<GameMode> = mutableStateOf(EndlessGameMode())
    val you = mutableStateOf(Role(roleIdentifier = Identifier.player()))
    val allies = mutableStateListOf<Role>()
    val enemies = mutableStateListOf<Role>()
    val battleInfo = mutableStateListOf<BattleInfo>()
    val lifeInfo = mutableStateListOf<BattleInfo>()
    val inGame = mutableStateOf(false)

    val battleTurn = mutableStateOf(0)

    val heroManager = HeroManager()
    val skillManager = SkillManager()
    val allyManager = AllyManager()

    val isChallenge = mutableStateOf(false)

    suspend fun doInit() {
        allyManager.init()
        heroManager.init()
        skillManager.init()
        AwardManager.init()
        SkinManager.init()
        TcgManager.init()
        SellManager.init()
        TaskManager.init()
        TalentManager.init()
        UnlockManager.init()
        EventManager.init()
        StoryManager.init()
        CloudManager.init()
        VipManager.init()
        BattlePassManager.init()
        AntiCheatManager.init()
        GameDataManager.init()
        SignManager.init()
        BattlePassManager.init()
        BattlePass2Manager.init()
        GameReviewManager.init()
        ReportManager.init()
        GiftManager.init()
        HolidayManager.init()
    }

    fun startGame(isChallenge: Boolean = false) {
        if (!repo.inGame.value) {
            GameDataManager.tryPlay {
                repo.inGame.value = true
                repo.isChallenge.value = isChallenge
                lifeInfo.clear()
                startVibrator()
                cardEffects.value = null
                ReportManager.onNewGame(0)
                EventManager.onNewGame()
                BattleManager.onNewGame()
                RecordManager.onNewGame()
                EventManager.gotoNextEvent(null, true)
            }
        }
    }

    fun continueGame() {
        repo.inGame.value = true
        lifeInfo.clear()
        startVibrator()
    }

    fun startBattle() {
        gameOver.value = false
        // 保护，防止用户作弊
        if (gameOver.value || inBattle.value) return

        GameCore.instance.onBattleEffect(SoundEffect.ExploreFinish)
        battleInfo.clear()
        inBattle.value = true
        playerMusicByScreen() // 音乐
        GameApp.globalScope.launch(gameDispatcher) {
            allies.forEach {
                it.setBuffList(emptyList())
                if (gameMode.value.isTower()) {
                    // todo https://xkff20230903033446466.pingcode.com/pjm/workitems/TyMWL_l9?
                    //#YXW-871 爬塔，战败的时候点击+号上阵框，就可以继续上阵兵种，继续打，一直重复操作可以打到敌方死亡为止
                    it.setPropertyToDefault()
                }

            }
            enemies.forEach {
                it.setBuffList(emptyList())
                if (gameMode.value.isTower()) {
                    // todo https://xkff20230903033446466.pingcode.com/pjm/workitems/TyMWL_l9?
                    //#YXW-871 爬塔，战败的时候点击+号上阵框，就可以继续上阵兵种，继续打，一直重复操作可以打到敌方死亡为止
                    it.setPropertyToDefault()
                }
            }
            battle.value = gameCore.createBattleField(mutableListOf<Role>().apply {
                addAll(allies)
                addAll(enemies)
            })
            battle.value.startBattle()
        }
    }

    fun onBattleInfo(string: String, type: BattleInfoType) {
        if (lifeInfo.size >= 300) {
            val temp = lifeInfo.takeLast(100)
            lifeInfo.clear()
            lifeInfo.addAll(temp)
        }
        lifeInfo.add(BattleInfo(content = string, type = type))
    }

    fun updateYou() {
        you.value = you.value.copy(updateId = you.value.updateId + 1)
    }

    suspend fun onBattleUpdate(battleField: BattleField) {
        withContext(Dispatchers.Main) {
            allies.clear()
            allies.addAll(battleField.getAllPlayers())
            enemies.clear()
            enemies.addAll(battleField.getAllEnemies())
            battleTurn.value = battleField.getTurn()
        }
    }

    fun setCurrentEnemies(enemies: List<Role>) {
        this.enemies.clear()
        this.enemies.addAll(enemies)
    }

    fun setCurrentAllies(allies: List<Role>) {
        this.allies.clear()
        this.allies.addAll(allies)
    }

    fun clickStart(isChallenge: Boolean = false) {
        repo.isChallenge.value = isChallenge
        GuideManager.onEnterGame()
        // 如果有存档，弹窗
        GameApp.globalScope.launch(Dispatchers.Main) {
            if (SaveManager.haveSaver()) {
                Dialogs.alertDialog.value = CommonAlert(
                    title = GameApp.instance.getWrapString(R.string.continue_or_not),
                    content = GameApp.instance.getWrapString(R.string.continue_contents),
                    confirmText = GameApp.instance.getWrapString(R.string.continue_game),
                    cancelText = GameApp.instance.getWrapString(R.string.new_game),
                    onConfirm = {
                        GameApp.globalScope.launch(Dispatchers.Main) {
                            SaveManager.recreateGame()
                            repo.continueGame()
                        }
                    }, onCancel = {
                        SaveManager.clearSave()
                        BattleManager.init()
                        if (repo.isChallenge.value) {
                            Dialogs.challengeGameDialog.value = true
                        } else {
                            goto(CREATE_COUNTRY_SCREEN)
                        }
                    }
                )
            } else {
                BattleManager.init()
                if (isChallenge) {
                    Dialogs.challengeGameDialog.value = true
                } else {
                    goto(CREATE_COUNTRY_SCREEN)
                }
            }
        }
    }

    fun getAdventureSkillTips(skill: Skill): String {
        return if (you.value.getGraveSkills().any { it.id == skill.id }) {
            GameApp.instance.getWrapString(R.string.effected)
        } else if (skill.grave != -1) {
            GameApp.instance.getWrapString(R.string.effect_multiple_times)
        } else {
            GameApp.instance.getWrapString(R.string.not_effect)
        }
    }

    fun isCurrentEnemyEmptyOrDead(): Boolean {
        return enemies.none { !it.isDeath() } || enemies.isEmpty()
    }
}