package com.moyu.chuanqirensheng.repository

import android.graphics.Bitmap
import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.cloud.saver.GameSave
import com.moyu.chuanqirensheng.logic.story.Ending
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.screen.dialog.SelectAllyData
import com.moyu.core.model.ally.Ally
import com.moyu.core.model.buff.Buff
import com.moyu.core.model.event.Event
import com.moyu.core.model.role.Role
import com.moyu.core.model.sell.Award
import com.moyu.core.model.sell.Gift
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skin.Skin
import com.moyu.core.model.story.Story
import com.moyu.core.model.talent.Talent
import com.moyu.core.model.tcg.TcgCard
import com.moyu.core.model.unlock.Unlock

const val SNACK_LEVEL_UP_STRING = "snack_level_up"
const val SNACK_SKILL_COMPOSED = "snack_skill_composed"

fun String.toast() {
    Dialogs.snackbar.value = this
}

object Dialogs {
    val giftDetailDialog = mutableStateOf<Gift?>(null)
    val payBlockingDialog = mutableStateOf<String?>(null)
    val warPassUnlockDialog = mutableStateOf(false)
    val warPass2UnlockDialog = mutableStateOf(false)
    val errorOrderDialog = mutableStateOf(false)
    val roleDetailDialog = mutableStateOf<Role?>(null)
    val skillLevelInfoDialog = mutableStateOf<Skill?>(null)
    val skillDetailDialog = mutableStateOf<Skill?>(null)
    val allyDetailDialog = mutableStateOf<Ally?>(null)
    val allyStarUpDialog = mutableStateOf<Ally?>(null)
    val heroDetailDialog = mutableStateOf<Skill?>(null)
    val detailTalentDialog = mutableStateOf<Talent?>(null)
    val gameWinDialog = mutableStateOf<List<Role>>(emptyList())
    val gameLoseDialog = mutableStateOf<List<Role>>(emptyList())
    val awardDialog = mutableStateOf<Award?>(null)
    val statisticView = mutableStateOf(false)
    val infoDialog = mutableStateOf(false)
    val extraInfoDialog = mutableStateOf(false)
    val buffDetailDialog = mutableStateOf<Pair<Buff, Role>?>(null)
    val snackbar = mutableStateOf("")
    val showPrivacyDialog = mutableStateOf(false)
    val showPermissionDialog = mutableStateOf(false)
    val guideDialog = mutableStateOf("")
    val tcgCardDialog = mutableStateOf<TcgCard?>(null)
    val drawResultDialog = mutableStateOf<Award?>(null)

    val challengeGameDialog = mutableStateOf(false)
    val gameAllyDialog = mutableStateOf(false)
    val battleSkillDialog = mutableStateOf(false)
    val equipDialog = mutableStateOf(false)
    val adventureSkillDialog = mutableStateOf(false)
    val selectAllyToGameDialog = mutableStateOf(false)
    val selectAllyToBattleDialog =
        mutableStateOf<SelectAllyData?>(null)
    val selectOneAllyDialog = mutableStateOf<Pair<(Ally) -> Boolean, (Ally) -> Unit>?>(null)
    val selectOneSkillDialog = mutableStateOf<Pair<(Skill) -> Boolean, (Skill) -> Unit>?>(null)
    val selectOneHeroDialog = mutableStateOf<Pair<(Skill) -> Boolean, (Skill) -> Unit>?>(null)
    val selectSkillToGameDialog = mutableStateOf(false)
    val selectHeroToGameDialog = mutableStateOf(false)
    val itemsDialog = mutableStateOf(false)
    val reputationDialog = mutableStateOf(false)
    val eventPassDialog = mutableStateOf<Event?>(null)
    val eventFailDialog = mutableStateOf<Event?>(null)
    val eventDetailDialog = mutableStateOf<Event?>(null)
    val fatalEnemyDialog = mutableStateOf<Event?>(null)
    val battleAwardDialog = mutableStateOf<Award?>(null)
    val endingDetailDialog = mutableStateOf<Ending?>(null)
    val tcgAwardDetail = mutableStateOf(false)
    val endingDialog = mutableStateOf<Ending?>(null)
    val storyDialog = mutableStateOf<Ending?>(null)
    val storyDetailDialog = mutableStateOf<Story?>(null)
    val skinsDialog = mutableStateOf(false)
    val skinDialog = mutableStateOf<Skin?>(null)

    val moneyTransferDialog = mutableStateOf(false)

    val adPoolDialog = mutableStateOf(false)
    val drawPoolDialog = mutableStateOf(false)
    val tutorDialog = mutableStateOf(false)
    val aiFaDianDialog = mutableStateOf<Unlock?>(null)
    val getKeyDialog = mutableStateOf(false)
    val useSaveDialog = mutableStateOf<GameSave?>(null)
    val shareImageDialog = mutableStateOf<Bitmap?>(null)
    val gameReviewDialog = mutableStateOf(false)

    val alertDialog = mutableStateOf<CommonAlert?>(null) // confirm cancel
    val debugSkillDialog = mutableStateOf<((Skill) -> Unit)?>(null)
    val debugAdvSkillDialog = mutableStateOf<((Skill) -> Unit)?>(null)
}