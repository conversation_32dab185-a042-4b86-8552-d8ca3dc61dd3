package com.moyu.chuanqirensheng.ad

import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.lucky.AdManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.award.VipManager
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.thread.gameDispatcher
import com.moyu.core.AppWrapper
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

const val KEY_RELIVE_PLAY = "relive"
val isWatching = mutableStateOf(false)

object AdHolder : AdInterface {
    var playCount = 0

    override fun playAd(adId: String, callback: suspend () -> Unit) {
        if (isWatching.value) {
            AppWrapper.getString(R.string.greater_than_5_seconds).toast()
            return
        }
        isWatching.value = true
        GameApp.globalScope.launch(gameDispatcher) {
            if (VipManager.isSkipAd()) {
                AwardManager.adNum.value += 1
                AwardManager.adMoney.value += AdManager.getRandomAdMoney()
                callback()
                isWatching.value = false
            } else {
                realShowAd(adId, callback)
                delay(10000)
                isWatching.value = false
            }
        }
    }

    override fun preload(adId: String) {
        TTAdPlayer.preload(adId)
    }

    private fun realShowAd(reportKey: String, callback: suspend () -> Unit) {
        TTAdPlayer.playAd(reportKey, callback)
        playCount += 1
    }
}