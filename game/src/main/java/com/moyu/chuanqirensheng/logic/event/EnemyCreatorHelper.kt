package com.moyu.chuanqirensheng.logic.event

import com.moyu.chuanqirensheng.feature.stage.StageManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.core.logic.enemy.DefaultEnemyCreator
import com.moyu.core.model.event.Event
import com.moyu.core.model.property.Property
import com.moyu.core.model.race.Race
import com.moyu.core.model.role.Role

fun createEnemyRole(race: Race, event: Event): Role {
    val diffProperty = event.getDiffProperty()
    val dungeonProperty = if (repo.gameMode.value.isStage()) StageManager.currentStage.value.toProperty() else Property()
    return DefaultEnemyCreator.create(
        race,
        diffProperty + dungeonProperty,
    )
}