package com.moyu.chuanqirensheng.logic.ally

import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.hero.getCardQualityFrame
import com.moyu.chuanqirensheng.logic.hero.getQualityFrame
import com.moyu.chuanqirensheng.logic.hero.getQualityName
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.model.ally.Ally

fun Ally.getTouchInfo(): String {
    return "$star" + GameApp.instance.getWrapString(R.string.star) + quality.getQualityName().take(2) + name + GameApp.instance.getWrapString(
        R.string.number
    ) + num + "，" + getRaceTips().take(2)
}

fun Ally.getRaceTips(): String {
    return when (type) {
        1 -> GameApp.instance.getWrapString(R.string.race_desc_1)
        2 -> GameApp.instance.getWrapString(R.string.race_desc_2)
        3 -> GameApp.instance.getWrapString(R.string.race_desc_3)
        4 -> GameApp.instance.getWrapString(R.string.race_desc_4)
        else -> GameApp.instance.getWrapString(R.string.race_desc_5)
    }
}

fun Ally.getElementTypeRes(): Int {
    return getImageResourceDrawable("card_race_$type")
}

fun Ally.getCardFrameDrawable(): Int {
    return quality.getCardQualityFrame()
}

fun Ally.getFrameDrawable(): Int {
    return quality.getQualityFrame()
}

fun Ally.getRegionDrawable(): Int {
    return getImageResourceDrawable("battle_race${getRaceType()}")
}