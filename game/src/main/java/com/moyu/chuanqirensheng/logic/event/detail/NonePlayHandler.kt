package com.moyu.chuanqirensheng.logic.event.detail

import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.event.PlayHandler
import com.moyu.core.model.event.Event

class NonePlayHandler(
    override val skipWin: Boolean = true,
    override val dialogJump: <PERSON>olean = true,
    override val playId: Int = 0
): PlayHandler() {

    @Composable
    override fun Layout(event: Event) { }

    override fun onEventSelect(event: Event) {
        EventManager.doEventResult(event, true)
    }
}