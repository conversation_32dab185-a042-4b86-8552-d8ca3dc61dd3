package com.moyu.chuanqirensheng.logic.event

import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.core.model.event.Event


const val INIT_AGE = 0
val battleEventIds = listOf(21, 22, 23, 24, 25, 26)
val fatalEventPlayIds = listOf(21, 23)
val keepFightingIds = listOf(21, 23, 24, 27)
val specialEventPlayIds = listOf(11, 12)
const val EVENT_SIZE = 3

const val INIT_LOCAL = 102 // 初始地理卡
const val INIT_RELIGION = 103 // 国家信仰
const val WORLD_EVENT = 31 // 世界事件


fun Event.forceLose(): Boolean {
    return BattleManager.adventureProps.value.failEventPlayIds.contains(
        play
    ) || BattleManager.adventureProps.value.failEventPlayIds.contains(0)
}

fun Event.forceWin(): Boolean {
    return BattleManager.adventureProps.value.winEventPlayIds.contains(
        play
    ) || (BattleManager.adventureProps.value.winEventPlayIds.contains(0))
}


fun Event.isStartingEvent(): Boolean {
    return this.play == INIT_LOCAL || this.play == INIT_RELIGION
}

fun Event.isStartingEvent2(): Boolean {
    return this.play == INIT_LOCAL
}

fun Event.isStartingEvent3(): Boolean {
    return this.play == INIT_RELIGION
}

fun Event.isWorldEvent(): Boolean {
    return this.play == WORLD_EVENT
}

fun Event.isKeepFight(): Boolean {
    return this.play in keepFightingIds
}


fun Event.isBattle() = play in battleEventIds

fun Event.isRiotGuide() = play in specialEventPlayIds

fun Int.ageToYear(): String {
    return (this - INIT_AGE).let {
        if (it >= 100) it.toString()
        else if (it >= 10) "0$it"
        else "00$it"
    }
}