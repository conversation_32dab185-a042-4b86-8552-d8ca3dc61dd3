package com.moyu.chuanqirensheng.logic.task

import com.moyu.chuanqirensheng.datastore.KEY_GAME_LOGIN_DAY
import com.moyu.chuanqirensheng.datastore.KEY_GAME_TASK_PROGRESS
import com.moyu.chuanqirensheng.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.datastore.increaseIntValueByKey
import com.moyu.chuanqirensheng.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.datastore.setIntValueIfBiggerByKey
import com.moyu.chuanqirensheng.datastore.setIntValueIfSmallerByKey
import com.moyu.chuanqirensheng.logic.talent.TalentManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.core.model.ally.Ally
import com.moyu.core.model.event.Event
import com.moyu.core.model.property.AdventureProps
import com.moyu.core.model.role.Role
import com.moyu.core.model.sell.Award
import com.moyu.core.model.sell.Sell
import com.moyu.core.model.skill.Skill

fun getLoginDays(): Int {
    return getIntFlowByKey(KEY_GAME_LOGIN_DAY)
}

//  1=次数，2=天数
fun onTaskStartGameTime() {
    setIntValueByKey(KEY_GAME_TASK_PROGRESS + TaskEvent.START_GAME.id + "_1", 1)
}

fun onTaskStartGameDay() {
    increaseIntValueByKey(FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.START_GAME.id + "_2")
    increaseIntValueByKey(KEY_GAME_LOGIN_DAY)
}

fun onTaskAge(age: Int) {
    setIntValueIfBiggerByKey(FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.AGE.id + "_1", age)
    setIntValueIfBiggerByKey(KEY_GAME_TASK_PROGRESS + TaskEvent.AGE.id + "_1", age)
}

fun onTaskEnterEvent(event: Event) {
    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + TaskEvent.ENTER_EVENT.id + "_${event.play}")
    increaseIntValueByKey(FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.ENTER_EVENT.id + "_${event.play}")
}

fun onTaskKillEnemy(enemies: List<Role>) {
    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + TaskEvent.KILL.id, enemies.size)
    increaseIntValueByKey(
        FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.KILL.id,
        enemies.size
    )
    enemies.forEach {
        increaseIntValueByKey(
            KEY_GAME_TASK_PROGRESS + TaskEvent.KILL.id + "_${it.getRace().raceType}", 1
        )
        increaseIntValueByKey(
            FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.KILL.id + "_${it.getRace().raceType}", 1
        )
        it.getRace().skillId.forEach {
            increaseIntValueByKey(
                KEY_GAME_TASK_PROGRESS + TaskEvent.KILL.id + "_${it}", 1
            )
            increaseIntValueByKey(
                FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.KILL.id + "_${it}", 1
            )
        }
    }
}

fun onTaskDoneEvent(event: Event) {
//    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + TaskEvent.DONE_EVENT.id + "_${event.play}")
//    increaseIntValueByKey(FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.DONE_EVENT.id + "_${event.play}")
//    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + TaskEvent.DONE_EVENT.id + "_${event.id}")
//    increaseIntValueByKey(FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.DONE_EVENT.id + "_${event.id}")
}

fun onTaskAdvProp(adventureProps: AdventureProps) {
    repeat(4) {
        setIntValueIfBiggerByKey(
            KEY_GAME_TASK_PROGRESS + TaskEvent.ADV_PROP.id + "_${it + 1}",
            adventureProps.getPropertyByTarget(it + 1)
        )
        setIntValueIfBiggerByKey(
            FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.ADV_PROP.id + "_${it + 1}",
            adventureProps.getPropertyByTarget(it + 1)
        )
    }
    // todo 气温低于xx度，写死
    setIntValueIfSmallerByKey(
        KEY_GAME_TASK_PROGRESS + TaskEvent.ADV_PROP_BELOW.id + "_4",
        adventureProps.getPropertyByTarget(4),
        repo.gameCore.getInitAdvProps().last()
    )
    setIntValueIfSmallerByKey(
        FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.ADV_PROP_BELOW.id + "_4",
        adventureProps.getPropertyByTarget(4),
        repo.gameCore.getInitAdvProps().last()
    )
}

fun onTaskGetItem(award: Award) {
    if (award.elements.any { it != 0 }) {
        award.elements.forEachIndexed { index, i ->
            if (i > 0) {
                increaseIntValueByKey(
                    KEY_GAME_TASK_PROGRESS + TaskEvent.GAIN_ITEM.id + "_${index + 1}",
                    i
                )
                increaseIntValueByKey(
                    FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.GAIN_ITEM.id + "_${index + 1}",
                    i
                )
            }
        }
    }
}

fun onLoseAlly(ally: Ally) {
//    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + TaskEvent.LOSE_ITEM.id + "_1", 1)
//    increaseIntValueByKey(FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.LOSE_ITEM.id + "_1", 1)
//
//    increaseIntValueByKey(
//        KEY_GAME_TASK_PROGRESS + TaskEvent.LOSE_ITEM.id + "_1" + "_${ally.type}",
//        1
//    )
//    increaseIntValueByKey(
//        FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.LOSE_ITEM.id + "_1" + "_${ally.type}",
//        1
//    )
}

fun onTaskLoseHero(target: Skill) {
//    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + TaskEvent.LOSE_ITEM.id + "_4", 1)
//    increaseIntValueByKey(FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.LOSE_ITEM.id + "_4", 1)
//
//    increaseIntValueByKey(
//        KEY_GAME_TASK_PROGRESS + TaskEvent.LOSE_ITEM.id + "_4" + "_${target.elementType}",
//        1
//    )
//    increaseIntValueByKey(
//        FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.LOSE_ITEM.id + "_4" + "_${target.elementType}",
//        1
//    )
}

fun onTaskReputation(id: Int) {
    increaseIntValueByKey(
        KEY_GAME_TASK_PROGRESS + TaskEvent.REPUTATION.id + "_${id}"
    )
    increaseIntValueByKey(
        FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.REPUTATION.id + "_${id}"
    )
}

/**
 * 抵御入侵是 1，2
 * 战役是3，4
 * 攻城战是5，6
 */
fun onTaskFatalPlay(type: Int, event: Event) {
//    val realType = if (event.play == FATAL_ENEMY) type else if (event.play == RACE_BATTLE) type + 4 else type + 2
//    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + TaskEvent.FATAL_PLAY.id + "_${realType}", 1)
//    increaseIntValueByKey(
//        FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.FATAL_PLAY.id + "_${realType}",
//        1
//    )
}

fun onTaskCountryLevel(level: Int) {
//    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + TaskEvent.COUNTRY_LEVEL.id + "_$level", 1)
//    increaseIntValueByKey(
//        FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.COUNTRY_LEVEL.id + "_$level",
//        1
//    )
}

fun onTaskBuy(sell: Sell) {
//    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + TaskEvent.BUY.id + "_${sell.type}")
//    increaseIntValueByKey(FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.BUY.id + "_${sell.type}")
    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + TaskEvent.BUY.id)
    increaseIntValueByKey(FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.BUY.id)
}

fun onTaskTalentUp(level: Int) {
    // 1=任意升到x级，2=总等级升到x级
    setIntValueIfBiggerByKey(
        KEY_GAME_TASK_PROGRESS + TaskEvent.TALENT_UP.id + "_1",
        level
    )
    setIntValueIfBiggerByKey(
        FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.TALENT_UP.id + "_1",
        level
    )
    val totalLevel = TalentManager.talents.values.sum()
    setIntValueIfBiggerByKey(KEY_GAME_TASK_PROGRESS + TaskEvent.TALENT_UP.id + "_2", totalLevel)
    setIntValueIfBiggerByKey(
        FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.TALENT_UP.id + "_2",
        totalLevel
    )
}


fun onDrawAllyCard(count: Int) {
    // 1=任意升到x级，2=总等级升到x级
    increaseIntValueByKey(
        KEY_GAME_TASK_PROGRESS + TaskEvent.DRAW.id + "_1",
        count
    )
    increaseIntValueByKey(
        FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.DRAW.id + "_1",
        count
    )
    increaseIntValueByKey(
        KEY_GAME_TASK_PROGRESS + TaskEvent.DRAW.id,
        count
    )
    increaseIntValueByKey(
        FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.DRAW.id,
        count
    )
}

fun onDrawSkillCard(count: Int) {
    // 1=任意升到x级，2=总等级升到x级
    increaseIntValueByKey(
        KEY_GAME_TASK_PROGRESS + TaskEvent.DRAW.id + "_2",
        count
    )
    increaseIntValueByKey(
        FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.DRAW.id + "_2",
        count
    )
    increaseIntValueByKey(
        KEY_GAME_TASK_PROGRESS + TaskEvent.DRAW.id,
        count
    )
    increaseIntValueByKey(
        FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.DRAW.id,
        count
    )
}

fun onDrawHeroCard(count: Int) {
    // 1=任意升到x级，2=总等级升到x级
    increaseIntValueByKey(
        KEY_GAME_TASK_PROGRESS + TaskEvent.DRAW.id + "_3",
        count
    )
    increaseIntValueByKey(
        FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.DRAW.id + "_3",
        count
    )
    increaseIntValueByKey(
        KEY_GAME_TASK_PROGRESS + TaskEvent.DRAW.id,
        count
    )
    increaseIntValueByKey(
        FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.DRAW.id,
        count
    )
}

fun onAdWatch() {
    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + TaskEvent.AD.id)
    increaseIntValueByKey(FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.AD.id)
}