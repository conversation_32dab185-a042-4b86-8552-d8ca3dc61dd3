package com.moyu.chuanqirensheng.logic.event.detail

import androidx.compose.runtime.Composable
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.PlayHandler
import com.moyu.chuanqirensheng.logic.event.createEnemyRole
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.event.BattleLayout
import com.moyu.core.model.event.Event
import com.moyu.core.model.race.getRaceTypeName

/**
 * 种族战斗
 */
const val RACE_BATTLE = 23
class RaceBattlePlayHandler(
    override val skipWin: Boolean = false,
    override val dialogJump: Boolean = false,
    override val playId: Int = RACE_BATTLE
) : PlayHandler() {

    @Composable
    override fun Layout(event: Event) {
        BattleLayout(
            extraTitle = GameApp.instance.getWrapString(R.string.limits, event.playPara2.first().toInt().getRaceTypeName()),
            event = event,
            allyFilter = {
                val raceId = event.playPara2.first().toInt()
                it.getRaceType() == raceId
            })
    }

    override fun onEventSelect(event: Event) {
        val raceId = event.playPara2.first().toInt()
        val pool = repo.gameCore.getPoolById(event.playPara1.first())
        val enemies = pool.pool.map {
            repo.gameCore.getRaceById(it)
        }.map {
            createEnemyRole(it, event)
        }.toMutableList()
        repo.setCurrentEnemies(enemies)

        // deselect 非目标种族
        BattleManager.getBattleAllies().filterNot { it.id == raceId }.forEach {
            BattleManager.selectAllyToBattle(it)
        }
    }
}