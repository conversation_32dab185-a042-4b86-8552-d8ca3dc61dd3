package com.moyu.chuanqirensheng.logic.speed

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.datastore.KEY_SPEED
import com.moyu.chuanqirensheng.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.logic.award.VipManager
import com.moyu.chuanqirensheng.logic.guide.GuideManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch


object GameSpeedManager: GameSpeedController {
    private const val baseAnimDuration = 400
    private const val shortDurationFactor = 3
    private var speed by mutableStateOf(gameSpeeds[0])

    override fun getCurrentSpeed() = speed

    override fun setSpeed(speed: Int) {
        this.speed = gameSpeeds[speed]
    }

    override fun getSpeeds(): List<Speed> {
        return gameSpeeds
    }

    override fun nextSpeed() {
        GameApp.globalScope.launch(Dispatchers.Main) {
            val maxSpeedIndex = if (VipManager.isSpeed10Unlocked()) {
                gameSpeeds.lastIndex
            } else if (VipManager.isSpeed5Unlocked()) {
                gameSpeeds.lastIndex - 1
            } else if (VipManager.isSpeed3Unlocked()) {
                gameSpeeds.lastIndex - 2
            } else {
                gameSpeeds.lastIndex - 3
            }
            speed = if (getCurrentSpeed().index == maxSpeedIndex) {
                gameSpeeds.first()
            } else {
                val index = gameSpeeds.indexOf(speed) + 1
                gameSpeeds[index]
            }
            setIntValueByKey(KEY_SPEED, getCurrentSpeed().index)
        }
    }

    override fun animDuration(): Long {
        return if (DebugManager.dryTest) 0 else (baseAnimDuration * speed.animationDurationFactor).toLong()
    }

    override fun stop(): Boolean {
        return speed.stop
    }

    override fun shortDuration(): Long {
        return if (DebugManager.dryTest) 0 else (baseAnimDuration * speed.animationDurationFactor / shortDurationFactor).toLong()
    }

    override fun longDuration(): Long {
        return if (DebugManager.dryTest) 0 else (baseAnimDuration * speed.animationDurationFactor * 3).toLong()
    }

    override fun isStop(): Boolean {
        return speed.stop || GuideManager.pauseGame()
    }
}