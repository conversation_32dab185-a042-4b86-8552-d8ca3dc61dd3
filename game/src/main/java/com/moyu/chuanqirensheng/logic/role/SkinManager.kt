package com.moyu.chuanqirensheng.logic.role

import com.moyu.core.model.property.AdventureProps
import com.moyu.core.model.skin.Skin


fun Skin.getProperty(): AdventureProps {
    return effectType.zip(effectNum).map {
        AdventureProps.getRoleProperty(it.first, it.second)
    }.reduce { acc, property -> acc + property }
}

object SkinManager {
//    val skins = mutableStateListOf<Skin>()
//    val currentSkin = mutableStateOf(repo.gameCore.getSkinPool().first())

    fun init() {
//        skins.addAll(getListObject<Skin>(KEY_SKINS).map { skin ->
//            repo.gameCore.getSkinPool().first { it.id == skin.id }
//        })
//        currentSkin.value = getObject<Skin>(KEY_SKIN)?.let { skin ->
//            repo.gameCore.getSkinPool().first { it.id == skin.id }
//        } ?: repo.gameCore.getSkinPool().first()
    }

    fun selectSkin(skin: Skin) {
//        currentSkin.value = skin
//        setObject(KEY_SKIN, skin)
//        BattleManager.createYou()
    }

    fun gainSkin(skin: Skin) {
//        skins.removeAll { it.id == skin.id }
//        skins.add(skin)
        save()
    }

    fun save() {
//        setListObject(KEY_SKINS, skins)
    }
}