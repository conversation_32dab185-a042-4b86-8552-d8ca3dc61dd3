package com.moyu.chuanqirensheng.logic.event.detail

import androidx.compose.runtime.Composable
import com.moyu.chuanqirensheng.logic.event.PlayHandler
import com.moyu.core.model.event.Event

const val DEVELOP_PLAY = 1

class DevelopPlayHandler(
    override val skipWin: Boolean = true,
    override val dialogJump: Boolean = true,
    override val playId: Int = DEVELOP_PLAY
) : PlayHandler() {

    @Composable
    override fun Layout(event: Event) {
        BasicEventContentWithBackground(event)
    }

    override fun onEventSelect(event: Event) {
        eventFinished.value = false
        eventResult.value = true
    }
}