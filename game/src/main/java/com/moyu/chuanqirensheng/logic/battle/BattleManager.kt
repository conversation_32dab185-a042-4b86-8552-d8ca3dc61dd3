package com.moyu.chuanqirensheng.logic.battle

import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.unit.Dp
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.datastore.KEY_HERO_AUTO
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.feature.tower.createTowerRole
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.event.adventureSkillTrigger
import com.moyu.chuanqirensheng.logic.event.createPvpPlayerRole
import com.moyu.chuanqirensheng.logic.event.detail.SHOW_BATTLE_PLAY
import com.moyu.chuanqirensheng.logic.record.RecordManager
import com.moyu.chuanqirensheng.logic.skill.ExtraSkillProcessor.doReverseEffect
import com.moyu.chuanqirensheng.logic.skill.GetBadgeInGame
import com.moyu.chuanqirensheng.logic.skill.GetElementInGame
import com.moyu.chuanqirensheng.logic.skill.GetReputationInGame
import com.moyu.chuanqirensheng.logic.skill.PopulationLevelUpEvent
import com.moyu.chuanqirensheng.logic.skill.PopulationLoseEvent
import com.moyu.chuanqirensheng.logic.skill.UseBadgeInGame
import com.moyu.chuanqirensheng.logic.skill.UseElementInGame
import com.moyu.chuanqirensheng.logic.talent.TalentManager
import com.moyu.chuanqirensheng.logic.task.onTaskAdvProp
import com.moyu.chuanqirensheng.logic.task.onTaskCountryLevel
import com.moyu.chuanqirensheng.logic.task.onTaskReputationLevel
import com.moyu.chuanqirensheng.logic.unlock.UnlockManager
import com.moyu.chuanqirensheng.model.award.GuardedB
import com.moyu.chuanqirensheng.model.award.GuardedMemoryList
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.effect.dialogEffectState
import com.moyu.chuanqirensheng.screen.effect.restartEffect
import com.moyu.chuanqirensheng.screen.effect.starUpEffect
import com.moyu.chuanqirensheng.sub.report.ReportManager
import com.moyu.core.GameCore
import com.moyu.core.logic.enemy.DefaultAllyCreator
import com.moyu.core.logic.identifier.Identifier
import com.moyu.core.model.GameItem
import com.moyu.core.model.ally.Ally
import com.moyu.core.model.level.ReputationLevel
import com.moyu.core.model.property.AdventureProps
import com.moyu.core.model.property.EMPTY_ADV_PROPS
import com.moyu.core.model.property.EMPTY_PROPERTY
import com.moyu.core.model.property.Property
import com.moyu.core.model.role.Role
import com.moyu.core.model.role.RoleExtraInfo
import com.moyu.core.model.sell.Award
import com.moyu.core.model.sell.EMPTY_BADGE
import com.moyu.core.model.sell.EMPTY_REPUTATION
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.isAdventure
import com.moyu.core.model.skill.isBattleSkill
import com.moyu.core.model.skill.isHeroSkill
import com.moyu.core.music.SoundEffect
import com.moyu.core.util.RANDOM
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.uuid.UUID
import kotlinx.uuid.generateUUID
import java.lang.Integer.max
import java.util.concurrent.CopyOnWriteArrayList
import kotlin.math.min

private fun randomCountryName(): String {
    val countryNamePrefixList = GameApp.instance.getWrapString(R.string.country_name_prefix).split(",")
    val countryNameInfixList = GameApp.instance.getWrapString(R.string.country_name_infix).split(",")
    val countryNameSuffixList = GameApp.instance.getWrapString(R.string.country_name_suffix).split(",")

    return countryNamePrefixList.shuffled(RANDOM).first() + countryNameInfixList.shuffled(RANDOM)
        .first() + countryNameSuffixList.shuffled(RANDOM).first()
}

object BattleManager {
    val heroManualEffect = GuardedB(KEY_HERO_AUTO)
    val battleRolePositions = mutableMapOf<String, Pair<Dp, Dp>>()
    val heroGameData = mutableStateListOf<Skill>()
    val skillGameData = mutableStateListOf<Skill>()
    val allyGameData = mutableStateListOf<Ally>()

    val elements = GuardedMemoryList(mutableStateListOf())
    val extraElements = GuardedMemoryList(mutableStateListOf())
    val reputations = CopyOnWriteArrayList<Int>()
    val badges = CopyOnWriteArrayList<Int>()

    val badgeNew = mutableStateOf(false)
    val reputationNew = mutableStateOf(false)

    val adventureProps = mutableStateOf(EMPTY_ADV_PROPS)
    val battleProp = mutableStateOf(EMPTY_PROPERTY)
    val battleRaceProps = mutableListOf(
        EMPTY_PROPERTY,
        EMPTY_PROPERTY,
        EMPTY_PROPERTY,
        EMPTY_PROPERTY,
        EMPTY_PROPERTY
    )

    val countryName = mutableStateOf(randomCountryName())
    val countryLevelUpAges = mutableStateListOf(0, 0, 0, 0, 0, 0, 0, 0, 0, 0)

    fun init() {
        battleRolePositions.clear()

        battleRaceProps.clear()
        repeat(5) {
            battleRaceProps.add(EMPTY_PROPERTY)
        }

        adventureProps.value = EMPTY_ADV_PROPS

        battleProp.value = EMPTY_PROPERTY

        allyGameData.clear()
        skillGameData.clear()
        heroGameData.clear()

        elements.clear()
        elements.addAll(repo.gameCore.getInitResources())

        extraElements.clear()
        extraElements.addAll(repo.gameCore.getInitExtraResources())

        reputations.clear()
        reputations.addAll(EMPTY_REPUTATION)

        badges.clear()
        badges.addAll(EMPTY_BADGE)

        skillGameData.addAll(repo.skillManager.data.filter {
            it.selected
        }.map { it.copy(enhancementOneGame = emptyList(), new = false) }
            .map { skill ->
                if (skill.equipAllyUuid.isNotEmpty() && skill.equipAllyUuid !in repo.allyManager.data.filter { it.selected }
                        .map { it.uuid }) {
                    // 如果技能史诗人物的目标不存在
                    skill.copy(equipAllyUuid = "")
                } else skill
            })
        heroGameData.addAll(repo.heroManager.data.filter { equip ->
            equip.selected
        }.map { it.copy(new = false, effected = !heroManualEffect.value).nextYear() })

        allyGameData.addAll(repo.allyManager.data.filter {
            it.selected
        }.map { ally ->
            // 修复在军团卡升星之后 ，可以装四个技能，然后可以带到下一局游戏，技能下了，多出来的技能框就消失了
            val allSkills =
                skillGameData.filter { it.selected }.filter { it.equipAllyUuid == ally.uuid }
            val needEquipSkills = allSkills.take(ally.skillNum)
            val notEquipSkills = allSkills.takeLast(allSkills.size - needEquipSkills.size)
            notEquipSkills.forEach {
                skillGameData.indexOfItemInGame(it.uuid) { index ->
                    skillGameData[index] = skillGameData[index].copy(equipAllyUuid = "")
                }
            }
            ally.copy(equipSkills = needEquipSkills, new = false, inGame = true)
        })
        createYou()
    }

    fun createRandomCountryName() {
        countryName.value = randomCountryName()
    }

    fun createYou() {
        adventureProps.value = AdventureProps.createNew()
        val talents = TalentManager.talents.mapNotNull { talent ->
            repo.gameCore.getSkillPool()
                .firstOrNull { it.mainId == talent.key && it.level == talent.value }
        }
        repo.you.value = Role(
            extraInfo = RoleExtraInfo(allyUuid = UUID.generateUUID().toString()),
            roleIdentifier = Identifier.player()
        ).apply {
            talents.forEach {
                learnSkill(it, roleIdentifier)
            }
        }
    }

    fun selectAllToPvpOrTowerGame() {
        allyGameData.clear()
        allyGameData.addAll(repo.allyManager.data.map {
            it.copyToGame()
        })
        skillGameData.clear()
        skillGameData.addAll(repo.skillManager.data.map {
            it.copyToGame().copy(equipAllyUuid = "")
        })
    }

    fun selectToGame(target: Ally) {
        val index = allyGameData.indexOfFirst { target.id == it.id } // 这里没有重复技能，未进入游戏，没有uuid，用id判定
        if (index != -1) {
            allyGameData.removeAt(index)
            repo.allyManager.selectToGame(target)
        } else {
            if (allyGameData.size >= UnlockManager.getInitAllyNum()) {
                (GameApp.instance.getWrapString(R.string.carry_to_game) + UnlockManager.getInitAllyNum() + GameApp.instance.getWrapString(
                    R.string.ally_to_game_tips
                )).toast()
            } else {
                repo.allyManager.selectToGame(target)
                allyGameData.add(target.copyToGame())
            }
        }
    }

    fun selectToGame(target: Skill) {
        val index = skillGameData.indexOfFirst { target.id == it.id }
        if (index != -1) {
            skillGameData.removeAt(index)
            repo.skillManager.selectToGame(target)
        } else {
            if (skillGameData.size >= UnlockManager.getInitSkillNum()) {
                (GameApp.instance.getWrapString(R.string.carry_to_game) + UnlockManager.getInitSkillNum() + GameApp.instance.getWrapString(
                    R.string.skill_to_game_tips
                )).toast()
            } else {
                repo.skillManager.selectToGame(target)
                skillGameData.add(target.copyToGame())
            }
        }
    }

    fun selectHeroToGame(target: Skill) {
        val index = heroGameData.indexOfFirst { target.id == it.id }
        if (index != -1) {
            heroGameData.removeAt(index)
            repo.heroManager.selectToGame(target)
            createYou()
        } else {
            if (heroGameData.size >= UnlockManager.getInitHeroNum()) {
                (GameApp.instance.getWrapString(R.string.carry_to_game) + UnlockManager.getInitHeroNum() + GameApp.instance.getWrapString(
                    R.string.hero_to_game_tips
                )).toast()
            } else {
                repo.heroManager.selectToGame(target)
                heroGameData.add(target.copyToGame())
            }
        }
    }

    suspend fun gainInGame(target: Ally) {
        allyGameData.add(target.copyToGame())
        RecordManager.gainInGame(target)
    }

    suspend fun gainInGame(target: Skill) {
        val setAgeSkill = if (target.isHeroSkill()) target else {
            target.copy(life = adventureProps.value.age)
        }
        setAgeSkill.copyToGame().let {
            skillGameData.add(it)
            RecordManager.gainInGame(it)
            if (it.isAdventure()) {
                repo.you.value.apply {
                    learnSkill(it, this.roleIdentifier)
                }
            }
        }
    }

    suspend fun gainHeroInGame(target: Skill) {
        target.copyToGame().let {
            heroGameData.add(it)
            RecordManager.gainHeroInGame(it)
            repo.you.value.apply {
                learnSkill(it, this.roleIdentifier)
            }
        }
    }

    suspend fun dropFromGame(target: Ally, needTrigger: Boolean = true) {
        skillGameData.filter { it.equipAllyUuid == target.uuid }.forEach {
            skillGameData.indexOfItemInGame(it.uuid) { index ->
                //bug，遇到交换军团卡的事件，如果不取下军团卡身上的技能，交换军团卡之后军团卡身上技能就没了，这种情况改成自动卸下吧
                skillGameData[index] = skillGameData[index].copy(equipAllyUuid = "")
            }
        }
        allyGameData.removeAll { it.uuid == target.uuid }
        if (needTrigger) {
            RecordManager.dropFromGame(target)
        }
    }

    suspend fun dropHeroFromGame(target: Skill) {
        heroGameData.removeAll { it.uuid == target.uuid }
        RecordManager.dropHeroInGame(target)
        repo.you.value.apply {
            forgetSkill(target)
        }
        repo.you.value.getGraveSkills().firstOrNull { it.id == target.id }?.doReverseEffect()
    }

    suspend fun dropFromGame(target: Skill) {
        skillGameData.removeAll { it.uuid == target.uuid }
        RecordManager.dropInGame(target)
        repo.you.value.apply {
            forgetSkill(target)
        }
        repo.you.value.getGraveSkills().firstOrNull { it.id == target.id }?.doReverseEffect()
    }

    fun getGameAllies(): List<Ally> {
        return allyGameData.sortedByDescending { it.selectedToBattle }
    }

    fun getAliveGameAllies(): List<Ally> {
        return allyGameData.filter {
            !it.isDead()
        }
    }

    fun getBattleAllies(): List<Ally> {
        return allyGameData.filter { it.selectedToBattle }
            .sortedBy { it.selectedToBattleTime }
    }

    fun selectAllyToBattle(ally: Ally) {
        if (isAllyInBattle(ally)) {
            allyGameData.indexOfItemInGame(ally.uuid) {
                allyGameData[it] = allyGameData[it].switchSelectToBattle()
            }
        } else {
            if (3 <= getBattleAllies().filter { !it.isDead() }.size) {
                GameApp.instance.getWrapString(R.string.battle_member_limitation).toast()
            } else {
                allyGameData.indexOfItemInGame(ally.uuid) {
                    allyGameData[it] = allyGameData[it].switchSelectToBattle()
                }
            }
        }
    }

    fun updateAlliesAfterBattle(allies: List<Role>) {
        GameApp.globalScope.launch(Dispatchers.Main) {
            if (EventManager.selectedEvent.value?.play != SHOW_BATTLE_PLAY) {
                allies.forEach {
                    updateAllyInGameById(
                        it,
                        hp = min(it.getOriginProperty().hp, it.getCurrentProperty().hp)
                    )
                }
            }
        }
    }

    suspend fun updateAllyInGameById(role: Role, hp: Int) {
        allyGameData.indexOfItemInGame(role.extraInfo.allyUuid) { index ->
            allyGameData[index] =
                allyGameData[index].copy(gameHp = if (hp >= role.getOriginProperty().hp) 100 else hp * 100 / role.getOriginProperty().hp)
            if (hp == 0) {
                dropFromGame(allyGameData[index], true)
            }
        }
    }

    suspend fun starUp(ally: Ally): Ally? {
        // 需要吞掉的卡数量, 不可以吞掉比自己更高的星级卡片
        val toDelete =
            allyGameData.filter { it.mainId == ally.mainId && it.uuid != ally.uuid }.take(ally.starUpNum)
        if (toDelete.size < ally.starUpNum) {
            GameApp.instance.getWrapString(R.string.card_not_enough).toast()
        } else if (allyGameData.filter { it.mainId == ally.mainId && it.uuid != ally.uuid }.any { it.star > ally.star }) {
            GameApp.instance.getWrapString(R.string.ally_up_tips).toast()
        } else {
            toDelete.forEach {
                dropFromGame(it, needTrigger = false)
            }
            ReportManager.onUpgradeAllyInGame(ally.id, ally.star)
            allyGameData.indexOfItemInGame(ally.uuid) { index ->
                val targetHp = (toDelete.sumOf { it.gameHp } + allyGameData[index].gameHp) / (toDelete.size + 1)
                allyGameData[index] = allyGameData[index].starUp().copy(inGame = true, gameHp = targetHp)
                GameCore.instance.onBattleEffect(SoundEffect.UpgradeItem)
                restartEffect(dialogEffectState, starUpEffect)
                return allyGameData[index]
            }
        }
        return null
    }

    fun reliveAllyInGame(ally: Ally, relive: Boolean = true) {
        allyGameData.indexOfItemInGame(ally.uuid) { index ->
            allyGameData[index] = allyGameData[index].relive()
        }
        if (relive) {
            GameCore.instance.onBattleEffect(SoundEffect.ReliveAlly)
        } else {
            GameCore.instance.onBattleEffect(SoundEffect.HealAlly)
        }
    }

    suspend fun hurtAllyInGame(ally: Ally, percent: Int) {
        allyGameData.indexOfItemInGame(ally.uuid) { index ->
            allyGameData[index] = allyGameData[index].hurt(percent)
            if (allyGameData[index].isDead()) {
                dropFromGame(allyGameData[index])
            }
        }
        GameCore.instance.onBattleEffect(SoundEffect.Damage1)
    }

    fun getGameSkills(): List<Skill> {
        return skillGameData
    }

    fun getAdventureSkills(): List<Skill> {
        return skillGameData.filter { it.isAdventure() }
    }

    fun oneShotUnSelect() {
        getBattleAllies().forEach {
            selectAllyToBattle(it)
        }
    }

    fun oneShotSelect(filter: (Ally) -> Boolean) {
        getBattleAllies().forEach {
            selectAllyToBattle(it)
        }
        repeat(3) {
            getGameAllies()
                .filter { !it.selectedToBattle }
                .filter { filter(it) }
                .sortedByDescending { it.star }
                .sortedByDescending { it.quality }
                .firstOrNull { !it.isDead() }
                ?.let {
                    selectAllyToBattle(it)
                }
        }
    }

    fun getGameHeroes(): List<Skill> {
        return heroGameData
    }

    fun getRoleByAlly(ally: Ally): Role {
        if (repo.inGame.value) {
            val extraProperty = battleProp.value + battleRaceProps[ally.getRaceType() - 1]

            return DefaultAllyCreator.create(
                repo.gameCore.getRaceById(ally.id),
                extraProperty + (ally.exerciseProperty ?: Property()) // 附加属性加上
            ).copy(extraInfo = RoleExtraInfo(ally.uuid, allyQuality = ally.quality))
                .apply {
                    setPropertyToDefault()
                    setCurrentHp(max(1, this.getOriginProperty().hp * ally.gameHp / 100))
                    ally.equipSkills.forEach {
                        learnSkill(it, this.roleIdentifier)
                    }
                }
        } else {
            return DefaultAllyCreator.create(
                repo.gameCore.getRaceById(ally.id), Property()
            ).copy(extraInfo = RoleExtraInfo(ally.uuid, allyQuality = ally.quality))
                .apply {
                    setPropertyToDefault()
                }
        }

    }

    fun isDead(ally: Ally): Boolean {
        allyGameData.indexOfItemInGame(ally.uuid) { index ->
            return allyGameData[index].isDead()
        }
        return false
    }

    fun onNewGame() {
        init() // 还要再init一次，改变了选择，要重新梳理下技能和军团卡的关系
        repo.you.value.apply {
            if (!heroManualEffect.value) {
                heroGameData.forEach {
                    learnSkill(it, this.roleIdentifier)
                }
            }
        }
    }

    fun equipSkillToAlly(skill: Skill, ally: Ally): Ally {
        if (ally.equipSkills.any { it.mainId == skill.mainId }) {
            GameApp.instance.getWrapString(R.string.same_skill_tips).toast()
            return ally
        }
        val newSkill = skill.copy(equipAllyUuid = ally.uuid)
        skillGameData.indexOfItemInGame(skill.uuid) {
            skillGameData[it] = newSkill
            repo.skillManager.data.indexOfFirst { it.uuid == newSkill.uuid }.takeIf { it != -1 }
                ?.let {
                    repo.skillManager.update(repo.skillManager.data[it].copy(equipAllyUuid = newSkill.equipAllyUuid))
                }
        }
        GameCore.instance.onBattleEffect(SoundEffect.EquipAnything)
        allyGameData.indexOfItemInGame(ally.uuid) {
            val newSkills = allyGameData[it].equipSkills.toMutableList().apply {
                add(newSkill)
            }
            allyGameData[it] = allyGameData[it].copy(equipSkills = newSkills)
            repo.allyManager.save()
            return allyGameData[it]
        }
//        error("没找到对应军团卡")
        return ally
    }

    fun unEquipSkillToAlly(skill: Skill): Ally {
        skillGameData.indexOfItemInGame(skill.uuid) {
            skillGameData[it] = skill.copy(equipAllyUuid = "")
            repo.skillManager.data.indexOfFirst { it.uuid == skill.uuid }.takeIf { it != -1 }?.let {
                repo.skillManager.update(repo.skillManager.data[it].copy(equipAllyUuid = ""))
            }
        }
        allyGameData.indexOfItemInGame(skill.equipAllyUuid) {
            val newSkills = allyGameData[it].equipSkills.toMutableList().apply {
                removeAll { it.id == skill.id }
            }
            allyGameData[it] = allyGameData[it].copy(equipSkills = newSkills)
            repo.allyManager.save()
            return allyGameData[it]
        }
        error("军团卡没找到")
    }

    fun getBattleRoles(): List<Role> {
        return getBattleAllies().filterNot { it.isDead() }.map {
            getRoleByAlly(it)
        }
    }

    fun onEventSelect() { }

    fun onBattleEnd() { }

    fun isAllyInBattle(ally: Ally): Boolean {
        return allyGameData.any { it.uuid == ally.uuid && it.selectedToBattle }
    }

    fun onPermanentDiff(target: Role, diff: Property) {
        allyGameData.indexOfItemInGame(target.extraInfo.allyUuid) {
            val newProperty = (allyGameData[it].exerciseProperty ?: Property()) + diff
            allyGameData[it] = allyGameData[it].copy(exerciseProperty = newProperty)
        }
    }

    fun setAllyUnNew() {
        val temp = allyGameData.toList()
        allyGameData.clear()
        allyGameData.addAll(temp.map { it.copy(new = false) })
    }

    fun setBattleSkillUnNew() {
        val temp = skillGameData.filter { it.isBattleSkill() }.toList()
        skillGameData.removeAll { it.isBattleSkill() }
        skillGameData.addAll(temp.map { it.copy(new = false) })
    }

    fun setAdvSkillUnNew() {
        val temp = skillGameData.filter { it.isAdventure() }.toList()
        skillGameData.removeAll { it.isAdventure() }
        skillGameData.addAll(temp.map { it.copy(new = false) })
    }

    fun setHeroUnNew() {
        val temp = heroGameData.toList()
        heroGameData.clear()
        heroGameData.addAll(temp.map { it.copy(new = false) })
    }

    fun setBadgeUnNew() {
        badgeNew.value = false
    }

    fun setReputationUnNew() {
        reputationNew.value = false
    }

    fun switchAllyBattlePosition(from: Int, to: Int) {
        val alliesInBattle = getBattleAllies()
        var fromTime = 0L
        var toTime = 0L
        if (alliesInBattle.isEmpty()) return
        if (alliesInBattle.getOrNull(from) == null) return
        if (alliesInBattle.getOrNull(to) == null) return

        allyGameData.indexOfItemInGame(alliesInBattle[from].uuid) {
            fromTime = allyGameData[it].selectedToBattleTime
        }
        allyGameData.indexOfItemInGame(alliesInBattle[to].uuid) {
            toTime = allyGameData[it].selectedToBattleTime
            allyGameData[it] = allyGameData[it].copy(selectedToBattleTime = fromTime)
        }
        allyGameData.indexOfItemInGame(alliesInBattle[from].uuid) {
            allyGameData[it] = allyGameData[it].copy(selectedToBattleTime = toTime)
        }
    }

    fun getElement(type: Int): Int {
        return elements[type]
    }

    fun getExtraElement(type: Int): Int {
        return extraElements[type]
    }

    fun getReputation(type: Int): Int {
        return reputations[type]
    }

    fun getBadge(type: Int): Int {
        return badges[type]
    }

    suspend fun gainElement(type: Int, num: Int) {
        withContext(Dispatchers.Main) {
            val realNum = if (DebugManager.reputation10 && num > 0) {
                num * 10
            } else {
                num
            }
            if (realNum > 0) {
                elements[type] = getElement(type) + realNum
                adventureSkillTrigger(triggerSkill = GetElementInGame.copy(mainId = type))
            } else if (realNum < 0) {
                elements[type] = 0.coerceAtLeast(getElement(type) + realNum)
                adventureSkillTrigger(triggerSkill = UseElementInGame.copy(mainId = type))
            }
        }
    }

    suspend fun gainExtraElement(type: Int, num: Int) {
        withContext(Dispatchers.Main) {
            val realNum = if (DebugManager.reputation10 && num > 0) {
                num * 10
            } else {
                num
            }
            extraElements[type] = getExtraElement(type) + realNum
        }
    }

    suspend fun gainReputation(type: Int, num: Int) {
        withContext(Dispatchers.Main) {
            val realNum = if (DebugManager.reputation10 && num > 0) {
                num * 10
            } else {
                num
            }
            if (realNum > 0) {
                reputations[type] = getReputation(type) + realNum
                onTaskReputationLevel(toReputationLevels())
                adventureSkillTrigger(triggerSkill = GetReputationInGame.copy(mainId = type))
            } else if (realNum < 0) {
                reputations[type] = 0.coerceAtLeast(getReputation(type) + realNum)
            }
            reputationNew.value = true
        }
    }

    suspend fun gainBadge(type: Int, num: Int) {
        withContext(Dispatchers.Main) {
            val realNum = if (DebugManager.reputation10 && num > 0) {
                num * 10
            } else {
                num
            }
            if (realNum > 0) {
                badges[type] = getBadge(type) + realNum
                adventureSkillTrigger(triggerSkill = GetBadgeInGame.copy(mainId = type))
            } else if (realNum < 0) {
                badges[type] = 0.coerceAtLeast(getBadge(type) + realNum)
                adventureSkillTrigger(triggerSkill = UseBadgeInGame.copy(mainId = type))
            }
            badgeNew.value = true
        }
    }

    suspend fun oneYearPass() {
        AwardManager.gainAward(Award(elements = extraElements))
        heroGameData.map {
            it.nextYear()
        }.apply {
            val die = this.filter { it.life > repo.gameCore.getHeroById(it.id).life }
            heroGameData.clear()
            heroGameData.addAll(this.filter { it.life <= repo.gameCore.getHeroById(it.id).life })
            die.forEach {
                repo.you.value.forgetSkill(it)
                dropHeroFromGame(it)
            }
        }
        skillGameData.map {
            it.nextYear()
        }.let {
            skillGameData.clear()
            skillGameData.addAll(it)
        }
        repo.you.value.oneYearPass()
    }

    suspend fun loseEventResult(property: AdventureProps) {
        withContext(Dispatchers.Main) {
            val winIdList = adventureProps.value.winEventPlayIds
            val loseIdList = adventureProps.value.failEventPlayIds
            val newWinIdList = winIdList.toMutableList().apply {
                property.winEventPlayIds.forEach {
                    remove(it)
                }
            }
            val newLoseIdList = loseIdList.toMutableList().apply {
                property.failEventPlayIds.forEach {
                    remove(it)
                }
            }
            adventureProps.value = adventureProps.value.copy(
                winEventPlayIds = newWinIdList,
                failEventPlayIds = newLoseIdList
            )
        }
    }
    suspend fun gainAdventureProp(property: AdventureProps) {
        withContext(Dispatchers.Main) {
            val oldProps = adventureProps.value
            adventureProps.value =
                adventureProps.value + if (DebugManager.reputation10 && property.perBiggerI(
                        EMPTY_ADV_PROPS
                    )
                ) property * 10 else property
            adventureProps.value = adventureProps.value.ensureNonNegative()
            if (adventureProps.value.age == 0) {
                adventureProps.value = adventureProps.value.ensureStartPopulation()
            }
            onTaskAdvProp(adventureProps.value)
            if (oldProps.getPopulationLevel().level != adventureProps.value.getPopulationLevel().level) {
                countryLevelUpAges[adventureProps.value.getPopulationLevel().level] =
                    adventureProps.value.age
                onTaskCountryLevel(adventureProps.value.getPopulationLevel().level)
                adventureSkillTrigger(triggerSkill = PopulationLevelUpEvent)
            }
            if (oldProps.population != adventureProps.value.population) {
                adventureSkillTrigger(triggerSkill = PopulationLoseEvent.copy(mainId = oldProps.population - adventureProps.value.population))
            }
        }
    }

    fun toReputationLevels(): List<Int> {
        return reputations.map {
            AdventureProps.getReputationLevel(it)
        }
    }

    fun toReputationLevelData(): List<ReputationLevel> {
        return reputations.map {
            AdventureProps.getReputationLevelData(it)
        }
    }

    fun gainPermanentProp(propertyByEnum: Property) {
        battleProp.value += propertyByEnum
    }

    fun gainPermanentRaceProp(race: Int, property: Property) {
        battleRaceProps[race] = battleRaceProps[race] + property
    }

    suspend fun heroEffect(skill: Skill): Skill {
        if (!skill.effected) {
            GameCore.instance.onBattleEffect(SoundEffect.UpgradeItem)
            heroGameData.indexOfItemInGame(skill.uuid) {
                if (!heroGameData[it].effected) {
                    heroGameData[it] = heroGameData[it].copy(effected = true, life = 1).initYear()
                    repo.you.value.learnSkill(heroGameData[it], repo.you.value)
                    adventureSkillTrigger(triggerSkill = null)
                }
                return heroGameData[it]
            }
        }
        return skill
    }

    fun getPvpBattleRoles(): List<Role> {
        return getBattleAllies().map { ally ->
            createPvpPlayerRole(ally.getRace(), TalentManager.talents).apply {
                setPropertyToDefault()
                ally.equipSkills.forEach {
                    learnSkill(it, this.roleIdentifier)
                }
            }
        }
    }

    fun getTowerBattleRoles(): List<Role> {
        return getBattleAllies().map { ally ->
            createTowerRole(ally.getRace(), TalentManager.talents).apply {
                setPropertyToDefault()
                ally.equipSkills.forEach {
                    learnSkill(it, this.roleIdentifier)
                }
            }
        }
    }
}

inline fun <T : GameItem> List<T>.indexOfItemInGame(uuid: String, callback: (Int) -> Unit) {
    indexOfFirst { uuid == it.uuid }.takeIf { it != -1 }?.let { index ->
        callback(index)
    }
}