package com.moyu.chuanqirensheng.logic.judge

import androidx.compose.runtime.MutableState
import com.moyu.chuanqirensheng.logic.story.Ending
import com.moyu.core.model.role.Role

interface GameJudge {
    val inBattle: MutableState<Boolean>
    val gameOver: MutableState<Boolean>
    fun onGameWin()
    fun onGameOver(ending: Ending? = null)
    fun onBattleWin(allies: List<Role>, enemies: List<Role>)
    fun onBattleLose(allies: List<Role>, enemies: List<Role>)
}