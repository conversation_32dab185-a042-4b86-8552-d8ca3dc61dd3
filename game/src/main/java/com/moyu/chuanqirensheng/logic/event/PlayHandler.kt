package com.moyu.chuanqirensheng.logic.event

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.TweenSpec
import androidx.compose.animation.core.animateIntAsState
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.datastore.KEY_GUIDE_INDEX
import com.moyu.chuanqirensheng.datastore.KEY_NON_BATTLE_DONE
import com.moyu.chuanqirensheng.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.logic.guide.GuideManager
import com.moyu.chuanqirensheng.repository.Dialogs.alertDialog
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.dialog.AwardList
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.screen.skill.IconView
import com.moyu.chuanqirensheng.text.playNameMap
import com.moyu.chuanqirensheng.text.playRuleMap
import com.moyu.chuanqirensheng.ui.theme.gapLarge
import com.moyu.chuanqirensheng.ui.theme.imageHuge
import com.moyu.chuanqirensheng.ui.theme.imageMedium
import com.moyu.chuanqirensheng.ui.theme.padding3
import com.moyu.chuanqirensheng.ui.theme.paddingHugeLite
import com.moyu.chuanqirensheng.ui.theme.paddingLarge
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.util.composeDp
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.model.event.Event
import com.moyu.core.model.sell.Award
import com.moyu.core.model.sell.toAward


@Composable
fun PlayRuleRow(event: Event) {
    EffectButton(onClick = {
        alertDialog.value = CommonAlert(
            title = "${
                playNameMap[event.play]?.replace(
                    "\n",
                    ""
                )
            }${GameApp.instance.getWrapString(R.string.rules)}",
            content = playRuleMap[event.play] ?: "",
            onlyConfirm = true
        )
    }) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            Image(
                modifier = Modifier.size(imageMedium),
                painter = painterResource(id = R.drawable.common_information),
                contentDescription = stringResource(id = R.string.explain)
            )
            Spacer(modifier = Modifier.size(paddingSmall))
            Text(
                text = stringResource(R.string.click_to_see) + playNameMap[event.play] + stringResource(
                    R.string.rules
                ),
                style = MaterialTheme.typography.h4,
                color = Color.Black
            )
        }
    }
}

abstract class PlayHandler {
    abstract val playId: Int
    abstract val skipWin: Boolean // 跳过事件，判定为事件成功还是失败
    abstract val dialogJump: Boolean // 播放完对话，是否自动结束事件
    val eventFinished: MutableState<Boolean> = mutableStateOf(false) // 事件是否结束
    val eventResult: MutableState<Boolean> = mutableStateOf(false) // 事件结果

    @Composable
    abstract fun Layout(event: Event)

    fun eventSelect(event: Event) {
        eventFinished.value = false
        eventResult.value = skipWin
        onEventSelect(event)
    }

    abstract fun onEventSelect(event: Event)

    @Composable
    fun BasicEventContentWithBackground(
        event: Event,
        isShowEffect: Boolean = false,
        showEventIcon: Boolean = true,
        content: @Composable BoxScope.() -> Unit = {}
    ) {
        Column {
            Spacer(modifier = Modifier.size(gapLarge))
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(250.composeDp()), contentAlignment = Alignment.Center
            ) {
                Image(
                    painter = painterResource(id = R.drawable.common_big_pad2),
                    contentDescription = null,
                    modifier = Modifier.fillMaxWidth(),
                    contentScale = ContentScale.FillWidth
                )
                Image(
                    painter = painterResource(getImageResourceDrawable("play${playId}_icon")),
                    contentDescription = null,
                    modifier = Modifier
                        .align(Alignment.TopStart)
                        .size(imageHuge)
                        .offset(y = -paddingHugeLite, x = padding3)
                )
                val effectIndex by animateIntAsState(
                    targetValue = if (isShowEffect) 17 else 1,
                    animationSpec = TweenSpec(
                        durationMillis = 1200,
                        easing = LinearEasing
                    ), label = ""
                )
                if (isShowEffect) {
                    Image(
                        painter = painterResource(
                            id = getImageResourceDrawable(
                                "heal_${effectIndex}"
                            )
                        ),
                        contentDescription = null,
                        modifier = Modifier
                            .fillMaxHeight()
                            .padding(vertical = paddingLarge),
                        contentScale = ContentScale.FillHeight
                    )
                }
                if (showEventIcon) {
                    val award = remember {
                        val innerAward = event.winReward.map {
                            repo.gameCore.getPoolById(it)
                        }.map { it.toAward() }.toAward()
                        Award(skills = innerAward.skills, heroes = innerAward.heroes, allies = innerAward.allies)
                    }
                    if (award.isEmpty()) {
                        Image(
                            painter = painterResource(
                                id = R.drawable.effect_big_reward
                            ),
                            contentDescription = null,
                            modifier = Modifier
                                .fillMaxHeight()
                                .padding(vertical = paddingLarge),
                            contentScale = ContentScale.FillHeight
                        )
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            IconView(
                                itemSize = ItemSize.Huge,
                                res = getImageResourceDrawable(event.pic),
                                frame = R.drawable.item_quality_2
                            )
                            Spacer(modifier = Modifier.size(paddingSmall))
                            Text(
                                text = event.name,
                                style = MaterialTheme.typography.h2,
                                color = Color.Black
                            )
                        }
                    } else {
                        AwardList(award = award)
                    }
                }
                content(this)
            }
        }
    }
}
