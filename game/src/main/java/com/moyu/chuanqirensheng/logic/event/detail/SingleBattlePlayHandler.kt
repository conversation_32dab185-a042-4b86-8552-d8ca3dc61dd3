package com.moyu.chuanqirensheng.logic.event.detail

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import com.moyu.chuanqirensheng.logic.event.PlayHandler
import com.moyu.chuanqirensheng.logic.event.createEnemyRole
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.event.BattleLayout
import com.moyu.chuanqirensheng.screen.skill.SingleSkillView
import com.moyu.core.logic.role.positionListEnemy
import com.moyu.core.model.event.Event
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.Skill
import com.moyu.core.util.RANDOM

const val SINGLE_BATTLE_PLAY = 25
class SingleBattlePlayHandler(
    override val skipWin: Boolean = false,
    override val dialogJump: Boolean = false,
    override val playId: Int = SINGLE_BATTLE_PLAY
): PlayHandler() {
    private val halo = mutableStateOf<Skill?>(null)

    @Composable
    override fun Layout(event: Event) {
        Box(modifier = Modifier.fillMaxSize()) {
            BattleLayout(event = event, capacity = 1)
            halo.value?.let {
                Row(
                    Modifier
                        .align(Alignment.CenterEnd), verticalAlignment = Alignment.CenterVertically) {
                    SingleSkillView(skill = it, textColor = Color.White)
                }
            }
        }
    }

    override fun onEventSelect(event: Event) {
        // 决斗，普通战斗,宿敌战斗,诅咒战斗
        val positionMap = mutableMapOf<Int, Role?>()
        val pool = repo.gameCore.getPoolById(event.playPara1.first())
        pool.pool.map {
            repo.gameCore.getRaceById(it)
        }.map {
            createEnemyRole(it, event)
        }.forEachIndexed { _, role ->
            // 固定站到中间
            positionMap[positionListEnemy[1]] = role
        }
        repo.setCurrentEnemies(positionMap)

        event.playPara1.getOrNull(1).takeIf { it != 0 }?.let {
            val haloPool = repo.gameCore.getPoolById(event.playPara1[1])
            haloPool.pool.shuffled(RANDOM).first().takeIf { it != 0 }?.let { haloSkillId ->
                halo.value = repo.gameCore.getSkillById(haloSkillId).apply {
                    positionMap.values.firstNotNullOf { it }.let {
                        it.learnSkill(this, it)
                    }
                }
            }
        }
    }
}