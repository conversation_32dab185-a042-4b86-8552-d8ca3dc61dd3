package com.moyu.chuanqirensheng.logic.skill

import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.core.GameCore
import com.moyu.core.logic.info.addExtraSkillInfo
import com.moyu.core.logic.skill.special
import com.moyu.core.model.property.AdventureProps
import com.moyu.core.model.property.Property
import com.moyu.core.model.race.Race
import com.moyu.core.model.sell.Award
import com.moyu.core.model.sell.toAward
import com.moyu.core.model.sell.toAwards
import com.moyu.core.model.skill.Skill
import com.moyu.core.util.RANDOM
import kotlin.math.roundToInt

object ExtraSkillProcessor {
    suspend fun Skill.doReverseEffect() {
        val range = this.special()
        for (index in range) {
            when (effectType[index].type) {
                102 -> { // 属性永久提高
                    doPermanent(index, effectNum[index])
                    GameCore.instance.addExtraSkillInfo("", this)
                }

                101 -> { // 属性永久降低
                    doPermanent(index, -effectNum[index])
                }

                212 -> {
                    /**
                     * 所有盟友卡，都提高指定的属性
                    1=攻击2=防御3=生命4=暴击5=暴击伤害6=格挡7=吸血8=速度"
                     */
                    BattleManager.gainPermanentProp(
                        Property.getPropertyByEnum(
                            subType[index],
                            effectNum[index]
                        )
                    )
                }

                in 213..223 -> {
                    val race = effectType[index].type - 213
                    BattleManager.gainPermanentRaceProp(
                        race,
                        Property.getPropertyByEnum(subType[index], effectNum[index])
                    )
                }

                in 202..211 -> {
                    val race = effectType[index].type - 202
                    BattleManager.gainPermanentRaceProp(
                        race,
                        Property.getPropertyByEnum(subType[index], -effectNum[index])
                    )
                }
                201 -> {
                    BattleManager.gainPermanentProp(
                        Property.getPropertyByEnum(
                            subType[index],
                            -effectNum[index]
                        )
                    )
                }

                300 -> { // 获取道具
                    // todo num要取技能指定的nums
                    val award = repo.gameCore.getPoolById(subType[index])
                        .toAward(forceNum = effectNum[index].toInt())
                    AwardManager.gainAward(-Award(
                        extraElements = award.extraElements,
                    ))
                }

                400 -> { // 获取道具
                    val award = repo.gameCore.getPoolById(subType[index]).toAwards(
                        forceNum = effectNum[index].toInt(),
                    ).toAward()
                    AwardManager.gainAward(Award(
                        extraElements = award.extraElements,
                    ))
                }

                701 -> {
                    //指定事件必定成功（effectNum填写play）
                    BattleManager.loseEventResult(
                        AdventureProps(
                            winEventPlayIds = listOf(
                                subType[index]
                            )
                        )
                    )
                }

                702 -> {
                    //指定事件必定失败（effectNum填写play）
                    BattleManager.loseEventResult(
                        AdventureProps(
                            failEventPlayIds = listOf(
                                effectNum[index].toInt()
                            )
                        )
                    )
                }

                else -> {
                }
            }
        }
    }
    suspend fun Skill.doExtraEffect() {
        val range = this.special()
        for (index in range) {
            when (effectType[index].type) {
                101 -> { // 属性永久提高
                    doPermanent(index, effectNum[index])
                }

                102 -> { // 属性永久降低
                    doPermanent(index, -effectNum[index])
                }

                201 -> {
                    /**
                     * 所有盟友卡，都提高指定的属性
                    1=攻击2=防御3=生命4=暴击5=暴击伤害6=格挡7=吸血8=速度"
                     */
                    GameCore.instance.addExtraSkillInfo(
                        GameApp.instance.getWrapString(R.string.extra_skill_buff),
                        this
                    )
                    BattleManager.gainPermanentProp(
                        Property.getPropertyByEnum(
                            subType[index],
                            effectNum[index]
                        )
                    )
                }

                in 202..211 -> {
                    GameCore.instance.addExtraSkillInfo(
                        GameApp.instance.getWrapString(R.string.extra_skill_buff),
                        this
                    )
                    val race = effectType[index].type - 202
                    BattleManager.gainPermanentRaceProp(
                        race,
                        Property.getPropertyByEnum(subType[index], effectNum[index])
                    )
                }

                212 -> {
                    /**
                     * 所有盟友卡，都降低指定的属性
                    1=攻击2=防御3=生命4=暴击5=暴击伤害6=格挡7=吸血8=速度"
                     */
                    GameCore.instance.addExtraSkillInfo(
                        GameApp.instance.getWrapString(R.string.extra_skill_debuff),
                        this
                    )
                    BattleManager.gainPermanentProp(
                        Property.getPropertyByEnum(
                            subType[index],
                            -effectNum[index]
                        )
                    )
                }

                in 213..223 -> {
                    GameCore.instance.addExtraSkillInfo(
                        GameApp.instance.getWrapString(R.string.extra_skill_debuff),
                        this
                    )
                    val race = effectType[index].type - 213
                    BattleManager.gainPermanentRaceProp(
                        race,
                        Property.getPropertyByEnum(subType[index], -effectNum[index])
                    )
                }

                300 -> { // 获取道具
                    // todo num要取技能指定的nums
                    GameCore.instance.addExtraSkillInfo("", this)
                    val award = repo.gameCore.getPoolById(subType[index])
                        .toAward(forceNum = effectNum[index].toInt())
                    AwardManager.gainAward(award)
                }

                400 -> { // 获取道具
                    GameCore.instance.addExtraSkillInfo("", this)
                    val award = repo.gameCore.getPoolById(subType[index]).toAwards(
                        forceNum = effectNum[index].toInt(),
                        onLoseAlly = { BattleManager.allyGameData },
                        onLoseSkill = { BattleManager.skillGameData + BattleManager.heroGameData },
                    ).toAward()
                    AwardManager.gainAward(-award)
                }

                602 -> {
                    if (subType[index] != 5) {
                        error("602的subType只支持5")
                    }
                    GameCore.instance.addExtraSkillInfo(
                        GameApp.instance.getWrapString(R.string.mercenary_price_down) + effectNum[index].toInt() + "。",
                        this
                    )
                    BattleManager.gainAdventureProp(
                        AdventureProps(
                            shopPriceMercenary = effectNum[index].toInt()
                        )
                    )
                }

                701 -> {
                    //指定事件必定成功（effectNum填写play）
                    GameCore.instance.addExtraSkillInfo(
                        GameApp.instance.getWrapString(R.string.event_sure_win) + subType[index] + "。",
                        this
                    )
                    BattleManager.gainAdventureProp(
                        AdventureProps(
                            winEventPlayIds = listOf(
                                subType[index]
                            )
                        )
                    )
                }

                702 -> {
                    //指定事件必定失败（effectNum填写play）
                    GameCore.instance.addExtraSkillInfo(
                        GameApp.instance.getWrapString(R.string.event_sure_fail) + effectNum[index].toInt() + "。",
                        this
                    )
                    BattleManager.gainAdventureProp(
                        AdventureProps(
                            failEventPlayIds = listOf(
                                effectNum[index].toInt()
                            )
                        )
                    )
                }

                703 -> {
                    //复活随机1个阵亡的军团卡
                    BattleManager.getGameAllies().filter { it.isDead() }.shuffled(RANDOM)
                        .firstOrNull()?.let {
                            GameCore.instance.addExtraSkillInfo(
                                GameApp.instance.getWrapString(R.string.relive_ally) + it.name + "。",
                                this
                            )
                            BattleManager.reliveAllyInGame(it)
                        } ?: kotlin.run {
                        GameCore.instance.addExtraSkillInfo(
                            GameApp.instance.getWrapString(R.string.no_dead_ally), this
                        )
                    }
                }

                704 -> {
                    //恢复x个军团卡到满血（x=0表示所有军团卡恢复到满血，否则优先选择未满血的军团卡进行恢复）
                    val hurts = BattleManager.getGameAllies().filter { it.isHurt() }
                    val targets = hurts.shuffled(RANDOM).take(1)
                    targets.takeIf { it.isNotEmpty() }?.forEach {
                        GameCore.instance.addExtraSkillInfo(
                            GameApp.instance.getWrapString(R.string.heal_ally) + it.name + "。",
                            this
                        )
                        BattleManager.reliveAllyInGame(it)
                    } ?: kotlin.run {
                        GameCore.instance.addExtraSkillInfo(
                            GameApp.instance.getWrapString(R.string.no_hurt_ally), this
                        )
                    }
                }


                705 -> {
                    //掉血x个军团卡（x=0表示所有军团）
                    val hurts = BattleManager.getGameAllies().filter { !it.isDead() }
                    val percent = effectNum[index].toInt()
                    val race = subType[index]
                    hurts.filter { it.getRaceType() == race || race == 0 }.takeIf { it.isNotEmpty() }?.forEach {
                        GameCore.instance.addExtraSkillInfo(
                            GameApp.instance.getWrapString(R.string.hurt_ally) + it.name + "。",
                            this
                        )
                        BattleManager.hurtAllyInGame(it, percent)
                    } ?: kotlin.run {
                        GameCore.instance.addExtraSkillInfo(
                            GameApp.instance.getWrapString(R.string.no_alive_ally), this
                        )
                    }
                }

                else -> {
                    error("未知的额外效果类型：${effectType[index].type}")
                }
            }
        }
    }

    private suspend fun Skill.doPermanent(index: Int, diff: Double) {
        when (subType[index]) {
            1 -> {
                GameCore.instance.addExtraSkillInfo(
                    "${GameApp.instance.getWrapString(R.string.role_prop1)}${
                        if (diff > 0) GameApp.instance.getWrapString(
                            R.string.boost
                        ) else GameApp.instance.getWrapString(R.string.lower)
                    }${diff.roundToInt()}。",
                    this
                )
                BattleManager.gainAdventureProp(AdventureProps(science = diff.roundToInt()))
            }

            2 -> {
                GameCore.instance.addExtraSkillInfo(
                    "${GameApp.instance.getWrapString(R.string.role_prop2)}${
                        if (diff > 0) GameApp.instance.getWrapString(
                            R.string.boost
                        ) else GameApp.instance.getWrapString(R.string.lower)
                    }${diff.roundToInt()}。",
                    this
                )
                BattleManager.gainAdventureProp(AdventureProps(politics = diff.roundToInt()))
            }

            3 -> {
                GameCore.instance.addExtraSkillInfo(
                    "${GameApp.instance.getWrapString(R.string.role_prop3)}${
                        if (diff > 0) GameApp.instance.getWrapString(
                            R.string.boost
                        ) else GameApp.instance.getWrapString(R.string.lower)
                    }${diff.roundToInt()}。",
                    this
                )
                BattleManager.gainAdventureProp(AdventureProps(military = diff.roundToInt()))
            }

            4 -> {
                GameCore.instance.addExtraSkillInfo(
                    "${GameApp.instance.getWrapString(R.string.role_prop4)}${
                        if (diff > 0) GameApp.instance.getWrapString(
                            R.string.boost
                        ) else GameApp.instance.getWrapString(R.string.lower)
                    }${diff.roundToInt()}。",
                    this
                )
                BattleManager.gainAdventureProp(AdventureProps(religion = diff.roundToInt()))
            }

            5 -> {
                GameCore.instance.addExtraSkillInfo(
                    "${GameApp.instance.getWrapString(R.string.role_prop5)}${
                        if (diff > 0) GameApp.instance.getWrapString(
                            R.string.boost
                        ) else GameApp.instance.getWrapString(R.string.lower)
                    }${diff.roundToInt()}。",
                    this
                )
                BattleManager.gainAdventureProp(AdventureProps(commerce = diff.roundToInt()))
            }

            6 -> {
                GameCore.instance.addExtraSkillInfo(
                    "${GameApp.instance.getWrapString(R.string.role_prop6)}${
                        if (diff > 0) GameApp.instance.getWrapString(
                            R.string.boost
                        ) else GameApp.instance.getWrapString(R.string.lower)
                    }${diff.roundToInt()}。",
                    this
                )
                BattleManager.gainAdventureProp(AdventureProps(art = diff.roundToInt()))
            }

            7 -> {
                GameCore.instance.addExtraSkillInfo(
                    "${GameApp.instance.getWrapString(R.string.role_prop7)}${
                        if (diff > 0) GameApp.instance.getWrapString(
                            R.string.boost
                        ) else GameApp.instance.getWrapString(R.string.lower)
                    }${diff.roundToInt()}。",
                    this
                )
                BattleManager.gainAdventureProp(AdventureProps(population = diff.roundToInt()))
            }

            else -> error("不存在Skill.doPermanent")
        }
    }

    fun Skill.doPvpSkillProperty(race: Race): Property {
        val range = this.special()
        var resultProperty = Property()
        for (index in range) {
            resultProperty += when (effectType[index].type) {
                in 202..211 -> {
                    val raceIndex = effectType[index].type - 202
                    val property = Property.getPropertyByEnum(
                        subType[index],
                        effectNum[index]
                    )
                    if (race.raceType == raceIndex + 1) {
                        property
                    } else Property()
                }

                else -> {
                    // error("未知的额外效果类型：${effectType[index].type}")
                    Property()
                }
            }
        }
        return resultProperty
    }
}