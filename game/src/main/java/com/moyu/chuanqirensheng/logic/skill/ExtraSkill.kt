package com.moyu.chuanqirensheng.logic.skill

import com.moyu.core.logic.skill.SpecialSkillTemplate
import com.moyu.core.model.skill.Skill

fun Skill.isEnterEvent() = id == -10001
fun Skill.isSucceededEvent() = id == -10101
fun Skill.isFailedEvent() = id == -10201
fun Skill.getEventType() = mainId
fun Skill.isDefeatEnemy(raceId: Int) = id == -10501 && (raceId == 0 || raceId == mainId)

fun Skill.isDealFate(type: Int) = id == -10611 && (mainId == type)

fun Skill.isReputationLevelUp(type: Int) = id == -10301 && (type == 0 || mainId == type)
fun Skill.isReputationLevelDown(type: Int) = id == -10302 && (type == 0 || mainId == type)

fun Skill.isReputationUp(type: Int) = id == -10311 && (type == 0 || mainId == type)
fun Skill.isReputationDown(type: Int) = id == -10312 && (type == 0 || mainId == type)

fun Skill.isGetAllyCard(raceType: Int) = id == -10801 && (raceType == 0 || mainId == raceType)
fun Skill.isLoseAllyCard(raceType: Int) = id == -10802 && (raceType == 0 || mainId == raceType)

fun Skill.isGetAdventureCard(type: Int) = id == -10811 && (type == 0 || mainId == type)
fun Skill.isLoseAdventureCard(type: Int) = id == -10812 && (type == 0 || mainId == type)

fun Skill.isGetBattleSkillCard(type: Int) = id == -10821 && (type == 0 || mainId == type)
fun Skill.isLoseBattleSkillCard(type: Int) = id == -10822 && (type == 0 || mainId == type)

fun Skill.isGetHeroCard(type: Int) = id == -10831 && (type == 0 || mainId == type)
fun Skill.isLoseHeroCard(type: Int) = id == -10832 && (type == 0 || mainId == type)

// todo 这里为什么要+1，因为消费资源传入的都是index，而这里配置的是type，所以index要+1
fun Skill.isGetElement(type: Int) = id == -10841 && (type == 0 || mainId + 1 == type)
fun Skill.isLoseElement(type: Int) = id == -10842 && (type == 0 || mainId + 1 == type)

fun Skill.isGetReputation(type: Int) = id == -10861 && (type == 0 || mainId == type)
fun Skill.isLoseReputation(type: Int) = id == -10862 && (type == 0 || mainId == type)

fun Skill.isGetBadge(type: Int) = id == -10871 && (type == 0 || mainId == type)
fun Skill.isLoseBadge(type: Int) = id == -10882 && (type == 0 || mainId == type)


fun Skill.isPopulationLevelUp() = id == -12001
fun Skill.isLosePopulation() = id == -12002// mainId 为失去的人口数量
fun Skill.isLosePopulationMoreThan(target: Int) = isLosePopulation() && mainId >= target// mainId 为失去的人口数量

fun Skill.isDice(value: Int) = id == -11001 && value == mainId
fun Skill.isAgeEvent() = id == -11101
fun Skill.isGetItem(itemId: Int) = id == -11201 && itemId == mainId

val EnterEvent = SpecialSkillTemplate.copy(id = -10001, name = "进入事件")
val SucceededEvent = SpecialSkillTemplate.copy(id = -10101, name = "事件成功")
val FailedEvent = SpecialSkillTemplate.copy(id = -10201, name = "事件失败")
val AgeEvent = SpecialSkillTemplate.copy(id = -11101, name = "年龄+1")

val ReputationUpEvent = SpecialSkillTemplate.copy(id = -10311, name = "声望提升")
val ReputationDownEvent = SpecialSkillTemplate.copy(id = -10312, name = "声望降低")

val ReputationLevelUpEvent = SpecialSkillTemplate.copy(id = -10301, name = "声望等级提升")
val ReputationLevelDownEvent = SpecialSkillTemplate.copy(id = -10302, name = "声望等级降低")

val GainExp = SpecialSkillTemplate.copy(id = -10331, name = "获得经验")
val LevelUpEvent = SpecialSkillTemplate.copy(id = -10321, name = "升级")
val GainMoney = SpecialSkillTemplate.copy(id = -10411, name = "获得金币")
val UseMoney = SpecialSkillTemplate.copy(id = -10401, name = "消费金币")
val BuyGoodEvent = SpecialSkillTemplate.copy(id = -10421, name = "消费金币")
val DefeatEnemy = SpecialSkillTemplate.copy(id = -10501, name = "战胜敌人")
val ObtainFatalEnemy = SpecialSkillTemplate.copy(id = -10621, name = "收服宿敌")
val DealInvasionEnemy = SpecialSkillTemplate.copy(id = -10611, name = "处决宿敌")

val GetAllyCard = SpecialSkillTemplate.copy(id = -10801, name = "获得军团卡")
val LoseAllyFromGame = SpecialSkillTemplate.copy(id = -10802, name = "失去军团卡")

val GetAdventureSkillInGame = SpecialSkillTemplate.copy(id = -10811, name = "获得冒险卡")
val LoseAdventureSkillInGame = SpecialSkillTemplate.copy(id = -10812, name = "失去冒险卡")

val GetBattleSkillInGame = SpecialSkillTemplate.copy(id = -10821, name = "获得战术卡")
val LoseBattleSkillInGame = SpecialSkillTemplate.copy(id = -10822, name = "失去战术卡")

val GetHeroInGame = SpecialSkillTemplate.copy(id = -10831, name = "获得史诗人物")
val LoseHeroInGame = SpecialSkillTemplate.copy(id = -10832, name = "失去史诗人物")

val GetElementInGame = SpecialSkillTemplate.copy(id = -10841, name = "获得资源")
val UseElementInGame = SpecialSkillTemplate.copy(id = -10842, name = "消费资源")

val GetReputationInGame = SpecialSkillTemplate.copy(id = -10861, name = "获得声望")
val LoseReputationInGame = SpecialSkillTemplate.copy(id = -10862, name = "消费声望")

val GetBadgeInGame = SpecialSkillTemplate.copy(id = -10871, name = "获得特产")
val UseBadgeInGame = SpecialSkillTemplate.copy(id = -10872, name = "消费特产")

val GetItem = SpecialSkillTemplate.copy(id = -11201, name = "获得道具")
val DiceEvent =
    SpecialSkillTemplate.copy(id = -11001, name = "试炼掷骰", mainId = -10201, level = 0)

val PopulationLevelUpEvent = SpecialSkillTemplate.copy(id = -12001, name = "人口等级提升")
val PopulationLoseEvent = SpecialSkillTemplate.copy(id = -12002, name = "失去人口") // mainId 为失去的人口数量

