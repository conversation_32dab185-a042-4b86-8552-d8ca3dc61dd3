package com.moyu.chuanqirensheng.logic.record

import com.moyu.chuanqirensheng.logic.event.adventureSkillTrigger
import com.moyu.chuanqirensheng.logic.skill.*
import com.moyu.chuanqirensheng.logic.task.onLoseAlly
import com.moyu.chuanqirensheng.logic.task.onTaskLoseHero
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.core.model.ally.Ally
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.isAdventure
import com.moyu.core.model.skill.isBattle
import kotlinx.serialization.Serializable

@Serializable
data class RecordData(
    val defeatEnemyRecord: MutableList<Int> = mutableListOf(), // raceType

    val specialDecisionRecord: MutableList<Int> = mutableListOf(), // 1-6

    val lostAllyRecord: MutableList<Int> = mutableListOf(), // raceType
    val getAllyRecord: MutableList<Int> = mutableListOf(), // raceType

    val getAdventureCardRecord: MutableList<Int> = mutableListOf(), // elementTyp
    val dropAdventureCardRecord: MutableList<Int> = mutableListOf(), // elementTyp

    val getBattleSkillCardRecord: MutableList<Int> = mutableListOf(), // elementTyp
    val dropBattleSkillCardRecord: MutableList<Int> = mutableListOf(), // elementTyp

    val getHeroCardRecord: MutableList<Int> = mutableListOf(), // part
    val loseHeroCardRecord: MutableList<Int> = mutableListOf(), // part
)
object RecordManager {
    var recordData = RecordData()

    fun onNewGame() {
        recordData = RecordData()
    }

    suspend fun addDefeatEnemies(enemies: List<Role>) {
        enemies.forEach {
            recordData.defeatEnemyRecord.add(it.getRace().raceType)
            adventureSkillTrigger(DefeatEnemy.copy(mainId = it.getRace().raceType))
        }
    }

    suspend fun specialDecision(type: Int) {
        recordData.specialDecisionRecord.add(type)
        adventureSkillTrigger(DealInvasionEnemy.copy(mainId = type))
    }

    suspend fun gainHeroInGame(target: Skill) {
        recordData.getHeroCardRecord.add(target.elementType)
        adventureSkillTrigger(GetHeroInGame.copy(mainId = target.elementType))
    }

    suspend fun dropHeroInGame(target: Skill) {
        recordData.loseHeroCardRecord.add(target.elementType)
        adventureSkillTrigger(LoseHeroInGame.copy(mainId = target.elementType))
        onTaskLoseHero(target)
    }

    suspend fun dropInGame(target: Skill) {
        if (target.isAdventure()) {
            recordData.dropAdventureCardRecord.add(target.elementType)
            adventureSkillTrigger(LoseAdventureSkillInGame.copy(mainId = target.elementType))
        } else if (target.isBattle()) {
            recordData.dropBattleSkillCardRecord.add(target.elementType)
            adventureSkillTrigger(LoseBattleSkillInGame.copy(mainId = target.elementType))
        }
    }

    suspend fun gainInGame(target: Skill) {
        if (target.isAdventure()) {
            recordData.getAdventureCardRecord.add(target.elementType)
            adventureSkillTrigger(GetAdventureSkillInGame.copy(mainId = target.elementType))
        } else if (target.isBattle()) {
            recordData.getBattleSkillCardRecord.add(target.elementType)
            adventureSkillTrigger(GetBattleSkillInGame.copy(mainId = target.elementType))
        }
    }

    suspend fun gainInGame(target: Ally) {
        recordData.getAllyRecord.add(target.getRaceType())
        val race = repo.gameCore.getRaceById(target.id)
        adventureSkillTrigger(GetAllyCard.copy(mainId = race.raceType))
    }

    suspend fun dropFromGame(target: Ally) {
        recordData.lostAllyRecord.add(target.getRaceType())
        val race = repo.gameCore.getRaceById(target.id)
        adventureSkillTrigger(LoseAllyFromGame.copy(mainId = race.raceType))
        onLoseAlly(target)
    }
}