package com.moyu.chuanqirensheng.logic.talent

import androidx.compose.runtime.mutableStateMapOf
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.cloud.AntiCheatManager.checkTalent
import com.moyu.chuanqirensheng.datastore.KEY_TALENT_LEVEL
import com.moyu.chuanqirensheng.datastore.getMapObject
import com.moyu.chuanqirensheng.datastore.setMapObject
import com.moyu.chuanqirensheng.feature.pvp.pvpBlockMainIds
import com.moyu.chuanqirensheng.feature.pvp.pvpLevelBlockMainIds
import com.moyu.chuanqirensheng.feature.pvp.pvpTalentMainIds
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.story.toReputationName
import com.moyu.chuanqirensheng.logic.task.onTaskTalentUp
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.report.ReportManager
import com.moyu.core.GameCore
import com.moyu.core.model.talent.Talent
import com.moyu.core.music.SoundEffect
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.serialization.builtins.serializer
import kotlin.math.min


object TalentManager {
    val talents = mutableStateMapOf<Int, Int>()

    fun init() {
        talents.clear()
        talents.putAll(getMapObject(KEY_TALENT_LEVEL, Int.serializer(), Int.serializer()))

        // 将 value 大于 10 的条目修改为 10
        talents.entries.forEach { entry ->
            val talent = repo.gameCore.getTalentPool().first { it.mainId == entry.key }
            if (entry.value > talent.levelLimit) {
                talents[entry.key] = talent.levelLimit
            }
        }
    }

    suspend fun upgradeTalent(talent: Talent, diamond: Int): Talent {
        if (talent.level < talent.levelLimit) {
            checkTalent(talent = talent)
            val talentLevel = talents[talent.mainId] ?: 0
            GameCore.instance.onBattleEffect(SoundEffect.UpgradeTalent)
            AwardManager.gainDiamond(-diamond)
            talents[talent.mainId] = talentLevel + 1
            setMapObject(KEY_TALENT_LEVEL, talents, Int.serializer(), Int.serializer())
            GameApp.globalScope.launch(Dispatchers.Main) {
                onTaskTalentUp(talentLevel + 1)
            }
            ReportManager.onTalentUp(talent.id, talent.level, diamond)
            return repo.gameCore.getTalentPool()
                .first { it.mainId == talent.mainId && it.level == talentLevel + 1 }
        } else {
            return talent
        }
    }

    fun getLockInfoByTalent(scroll: Talent): Pair<Boolean, String> {
        return when (scroll.conditionType) {
            0 -> Pair(false, "")
            100 -> {
                val target = scroll.conditionNum
                val targetTalent = repo.gameCore.getTalentPool().first {
                    it.type == scroll.type && it.position.first() == scroll.position.first() - 1 && it.position[1] == scroll.position[1]
                }
                Pair(
                    (talents[targetTalent.mainId] ?: 0) < target,
                    GameApp.instance.getWrapString(R.string.talent_condition1) + target + GameApp.instance.getWrapString(
                        R.string.talent_condition2
                    )
                )
            }

            else -> {
                val reputationType = scroll.conditionType
                val target = scroll.conditionNum
                Pair(
                    BattleManager.toReputationLevels()[reputationType - 1] < target,
                    reputationType.toReputationName().toString() + GameApp.instance.getWrapString(R.string.talent_condition3) + target + GameApp.instance.getWrapString(
                        R.string.talent_condition2
                    )
                )
            }
        }
    }

    fun getPvpTalents(): Map<Int, Int> {
        return talents.filter { it.value > 0 }.filter { it.key !in pvpBlockMainIds }.filter { it.key in pvpTalentMainIds }.let { map ->
            val mutableMap = mutableMapOf<Int, Int>()
            map.keys.forEach {
                mutableMap[it] = if (it in pvpLevelBlockMainIds) {
                    min(map[it]?: 0, 5)
                } else map[it]?: 0
            }
            mutableMap
        }
    }
}