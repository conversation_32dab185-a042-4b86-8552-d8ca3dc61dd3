package com.moyu.chuanqirensheng.logic.sell

import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.core.model.sell.Sell

fun Sell.getPriceTextWithUnit(): String {
    return if (isAifadian()) {
        if (!GameApp.instance.resources.getBoolean(R.bool.has_billing)) {
            GameApp.instance.getWrapString(R.string.go_and_get)
        } else if (!GameApp.instance.resources.getBoolean(R.bool.has_google_service)) {
            price.toString() + GameApp.instance.getWrapString(R.string.real_money_unit)
        } else {
            priceDollar.toString() + GameApp.instance.getWrapString(R.string.real_money_dollar)
        }
    } else price.toString()
}