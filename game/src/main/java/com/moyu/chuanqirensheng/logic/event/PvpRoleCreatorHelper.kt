package com.moyu.chuanqirensheng.logic.event

import com.moyu.chuanqirensheng.feature.pvp.pvpTalentMainIds
import com.moyu.chuanqirensheng.logic.skill.ExtraSkillProcessor.doPvpSkillProperty
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.core.logic.enemy.DefaultAllyCreator
import com.moyu.core.logic.enemy.PvpRoleCreator
import com.moyu.core.model.property.Property
import com.moyu.core.model.race.Race
import com.moyu.core.model.role.Role

fun createPvpEnemyRole(race: Race, skillIds: List<Int>, talents: Map<Int, Int>): Role {
    var resultProperty = Property()
    // todo 帝国2
    talents.filterKeys { it in pvpTalentMainIds }.keys.forEach { mainId ->
        val talentSkill = repo.gameCore.getSkillPool().firstOrNull { it.mainId == mainId && it.level == talents[mainId] }
        talentSkill?.let {
            resultProperty += talentSkill.doPvpSkillProperty(race)
        }
    }
    return PvpRoleCreator.create(
        race,
        resultProperty,
        skillIds
    )
}


fun createPvpPlayerRole(race: Race, talents: Map<Int, Int>): Role {
    var resultProperty = Property()
    talents.filterKeys { it in pvpTalentMainIds }.keys.forEach { mainId ->
        val talentSkill = repo.gameCore.getSkillPool().firstOrNull { it.mainId == mainId && it.level == talents[mainId] }
        talentSkill?.let {
            resultProperty += talentSkill.doPvpSkillProperty(race)
        }
    }
    return DefaultAllyCreator.create(
        race,
        resultProperty,
        emptyList(),
    )
}