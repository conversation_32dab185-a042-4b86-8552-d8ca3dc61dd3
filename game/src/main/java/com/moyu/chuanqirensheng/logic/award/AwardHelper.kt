package com.moyu.chuanqirensheng.logic.award

import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.detail.MERCENARY_PLAY
import com.moyu.chuanqirensheng.logic.story.Ending
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.AppWrapper
import com.moyu.core.GameCore
import com.moyu.core.model.event.Event
import com.moyu.core.model.pool.Pool
import com.moyu.core.model.sell.Award
import com.moyu.core.model.sell.toAward
import com.moyu.core.model.sell.toAwards
import kotlin.math.abs
import kotlin.math.max

val elementNames = listOf(
    GameApp.instance.getWrapString(R.string.resource_name_1),
    GameApp.instance.getWrapString(R.string.resource_name_2),
    GameApp.instance.getWrapString(R.string.resource_name_3),
    GameApp.instance.getWrapString(R.string.resource_name_4),
    GameApp.instance.getWrapString(R.string.resource_name_5)
)

val elementToast = listOf(
    GameApp.instance.getWrapString(R.string.resource_desc_1),
    GameApp.instance.getWrapString(R.string.resource_desc_2),
    GameApp.instance.getWrapString(R.string.resource_desc_3),
    GameApp.instance.getWrapString(R.string.resource_desc_4),
    GameApp.instance.getWrapString(R.string.resource_desc_5)
)

fun Int.toBadgeTip(): String {
    return repo.gameCore.getBadgePool().first { it.id == this }.tips
}

fun Int.toElementIcon(): Int {
    return getImageResourceDrawable("element_${this + 1}")
}

fun Int.toExtraElementIcon(): Int {
    return getImageResourceDrawable("extra_element${this + 1}")
}

fun Int.toElementName(): String {
    return elementNames[this]
}

fun Int.toElementToast(): String {
    return elementToast[this]
}

fun Int.toExtraElementName(): String {
    return elementNames[this] + AppWrapper.getString(R.string.production_capacity)
}

fun Ending.toAward(): Award {
    val poolId = repo.gameCore.getEndingPoolIdByAge(age)
    val award = if (poolId != -1) GameCore.instance.getPoolById(poolId).toAward() else Award()
    return Award(diamond = repo.gameCore.getEndingDiamondByAge(age)) + award
}

fun Event.toConditionAward(): Award {
    return GameCore.instance.getPoolById(condition).toAward().let {
        if (play == MERCENARY_PLAY) {
            it.copy(elements = it.elements.toMutableList().apply {
                set(0, max(0, this[0] - BattleManager.adventureProps.value.shopPriceMercenary))
            })
        } else {
            it
        }
    }
}

fun Event.toAward(win: Boolean): Award {
    return toAwards(win).toAward()
}

fun Event.toAwards(win: Boolean): List<Award> {
    return try {
        if (win) winReward.map { GameCore.instance.getPoolById(it).toAward() }
        else loseReward.map {
            // 这里要小心处理
            val pool = GameCore.instance.getPoolById(it)
            // 这里是局内event可能要丢弃卡牌，我先分割pool为单个pool，一个一个算
            val pools = pool.split()
            val awards = pools.map { singlePool ->
                if (singlePool.singlePoolNeedLoseCard()) {
                    // 如果是需要丢弃卡牌，把数值改正
                    val award = singlePool.copy(num = listOf(abs(singlePool.num.first()))).toAwards(
                        onLoseAlly = { BattleManager.allyGameData },
                        onLoseSkill = { BattleManager.skillGameData + BattleManager.heroGameData },
                    ).toAward()
                    // 转负
                    -award
                } else {
                    singlePool.toAward()
                }
            }
            awards.toAward()
        }
    } catch (e: Exception) {
        emptyList()
    }
}

// todo 太麻烦了，这里是局内可能失去的卡牌枚举
fun Int.isInGameCard(): Boolean {
    return this == 1 || this == 2 || this == 3 || this == 4 || this == 11
}

fun Pool.singlePoolNeedLoseCard(): Boolean {
    return type.first().isInGameCard() && this.num.first() < 0
}
