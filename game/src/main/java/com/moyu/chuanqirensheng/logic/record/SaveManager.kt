package com.moyu.chuanqirensheng.logic.record

import com.moyu.chuanqirensheng.datastore.KEY_RECORD
import com.moyu.chuanqirensheng.datastore.KEY_RECORD_EVENT
import com.moyu.chuanqirensheng.datastore.getObject
import com.moyu.chuanqirensheng.datastore.setObject
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.music.MusicManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.util.EVENT_SELECT_SCREEN
import com.moyu.chuanqirensheng.util.goto
import com.moyu.core.model.ally.Ally
import com.moyu.core.model.event.Event
import com.moyu.core.model.property.AdventureProps
import com.moyu.core.model.property.EMPTY_ADV_PROPS
import com.moyu.core.model.property.EMPTY_PROPERTY
import com.moyu.core.model.property.Property
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.Skill
import kotlinx.serialization.Serializable

@Serializable
data class SaveData(
    val you: Role = Role(),
    val usedEvents: List<Event> = emptyList(),
    val winEvents: List<Event> = emptyList(),
    val loseEvents: List<Event> = emptyList(),
    val selectionEvents: List<Event> = emptyList(),
    val records: RecordData = RecordData(),
    val heroGameData: List<Skill> = emptyList(),
    val skillGameData: List<Skill> = emptyList(),
    val allyGameData: List<Ally> = emptyList(),
    val isChallenge: Boolean = false,
    val reputations: List<Int> = emptyList(),
    val elements: List<Int> = emptyList(),
    val extraElements: List<Int> = emptyList(),
    val badges: List<Int> = emptyList(),
    val adventureProps: AdventureProps = EMPTY_ADV_PROPS,
    val battleProp: Property = EMPTY_PROPERTY,
    val battleRaceProps: List<Property> = mutableListOf(
        EMPTY_PROPERTY,
        EMPTY_PROPERTY,
        EMPTY_PROPERTY,
        EMPTY_PROPERTY,
        EMPTY_PROPERTY
    ),
    val countryName: String = "",
    val worldEventTime: List<Int> = emptyList()
)

object SaveManager {
    fun haveSaver(): Boolean {
        return getObject<SaveData>(KEY_RECORD) != null
    }

    fun recreateGame() {
        getObject<SaveData>(KEY_RECORD)?.let { recordData ->
            repo.you.value = recordData.you

            EventManager.resetEventRecorder(
                recordData.usedEvents.map { it.create() },
                recordData.winEvents.map { it.create() },
                recordData.loseEvents.map { it.create() }
            )

            EventManager.selectionEvents.clear()
            EventManager.selectionEvents.addAll(recordData.selectionEvents.map {
                it.create().createUUID()
            })

            RecordManager.recordData = recordData.records

            BattleManager.heroGameData.clear()
            BattleManager.heroGameData.addAll(recordData.heroGameData.mapNotNull {
                it.create()?.copy(life = it.life, extraInfo = it.extraInfo)
            })

            BattleManager.skillGameData.clear()
            BattleManager.skillGameData.addAll(recordData.skillGameData.mapNotNull {
                it.create()
                    ?.copy(life = it.life, extraInfo = it.extraInfo)
            })

            BattleManager.allyGameData.clear()
            BattleManager.allyGameData.addAll(recordData.allyGameData.map { it.create() })

            BattleManager.elements.clear()
            BattleManager.elements.addAll(recordData.elements)

            BattleManager.extraElements.clear()
            BattleManager.extraElements.addAll(recordData.extraElements)

            BattleManager.reputations.clear()
            BattleManager.reputations.addAll(recordData.reputations)
            while (BattleManager.reputations.size < repo.gameCore.getStoryPool().size) {
                BattleManager.reputations.add(0)
            }

            BattleManager.badges.clear()
            BattleManager.badges.addAll(recordData.badges)
            while (BattleManager.badges.size < repo.gameCore.getBadgePool().size) {
                BattleManager.badges.add(0)
            }

            BattleManager.adventureProps.value = recordData.adventureProps
            BattleManager.battleProp.value = recordData.battleProp

            BattleManager.battleRaceProps.clear()
            BattleManager.battleRaceProps.addAll(recordData.battleRaceProps)

            BattleManager.countryName.value = recordData.countryName
            EventManager.currentBgMusic.value = MusicManager.getRandomDungeonMusic()

            EventManager.worldEventTime.clear()
            EventManager.worldEventTime.addAll(recordData.worldEventTime)

            repo.isChallenge.value = recordData.isChallenge

            getObject<Event>(KEY_RECORD_EVENT)?.createOrNull()?.let { event ->
                goto(EVENT_SELECT_SCREEN)
                EventManager.selectEvent(event)
                EventManager.doEventResult(
                    event, EventManager.getOrCreateHandler(event).skipWin
                )
            } ?: kotlin.run {
                goto(EVENT_SELECT_SCREEN)
            }
        }
    }

    fun selectEvent(event: Event) {
        setObject(KEY_RECORD_EVENT, event)
    }

    fun clearSave() {
        setObject(KEY_RECORD_EVENT, Event(-1))
        setObject(KEY_RECORD, null)
    }

    fun onSelections(events: List<Event>) {
        setObject(KEY_RECORD_EVENT, Event(-1))
        setObject(
            KEY_RECORD, SaveData(
                you = repo.you.value,
                usedEvents = EventManager.getUsedEvents(),
                winEvents = EventManager.getSucceededEvents(),
                loseEvents = EventManager.getFailedEvents(),
                selectionEvents = events,
                records = RecordManager.recordData,
                heroGameData = BattleManager.heroGameData,
                skillGameData = BattleManager.skillGameData,
                allyGameData = BattleManager.allyGameData,
                elements = BattleManager.elements,
                extraElements = BattleManager.extraElements,
                reputations = BattleManager.reputations,
                badges = BattleManager.badges,
                isChallenge = repo.isChallenge.value,
                adventureProps = BattleManager.adventureProps.value,
                battleProp = BattleManager.battleProp.value,
                battleRaceProps = BattleManager.battleRaceProps,
                countryName = BattleManager.countryName.value,
                worldEventTime = EventManager.worldEventTime
            )
        )
    }
}