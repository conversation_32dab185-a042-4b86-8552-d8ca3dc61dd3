package com.moyu.chuanqirensheng.logic.award

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateMapOf
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.cloud.AntiCheatManager.checkVip
import com.moyu.chuanqirensheng.datastore.KEY_VIP_GAINED
import com.moyu.chuanqirensheng.model.award.GuardedB
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.core.AppWrapper
import com.moyu.core.model.Vip
import com.moyu.core.model.sell.toAward

const val DAILY_QUEST_NUM_INC_TYPE = 31
const val REFRESH_SHOP_NUM_INC_TYPE = 32
const val SPEED_UP_TYPE = 33
const val REFRESH_SHOP_FREE_TYPE = 34
const val ENDING_DOUBLE_AWARD_TYPE = 35
const val DAILY_QUEST_DOUBLE_AWARD_TYPE = 37
const val AD_AVOID_TYPE = 39
// 通过unlock逻辑实现，不需要
const val UNLOCK_ALLY_TYPE = 41
const val UNLOCK_BATTLE_TYPE = 42
const val UNLOCK_HERO_TYPE = 43
const val PVP_NUM_TYPE = 44

object VipManager {

    private val gainedMap = mutableStateMapOf<Int, MutableState<Boolean>>()

    fun init() {
        repo.gameCore.getVipPool().forEach {
            gainedMap[it.id] = GuardedB(KEY_VIP_GAINED + it.id)
        }
    }

    fun getVipLevel(): Int {
        return repo.gameCore.getVipPool()
            .lastOrNull { it.num <= AwardManager.electric.value }?.level ?: 0
    }

    fun getVipLevelData(): Vip {
        return repo.gameCore.getVipPool().lastOrNull { it.num <= AwardManager.electric.value }?:repo.gameCore.getVipPool().first()
    }

    fun isSpeed3Unlocked(): Boolean {
        return repo.gameCore.getVipPool().first { it.effectType == SPEED_UP_TYPE && it.effectNum == 3 }.level <= getVipLevel()
    }

    fun isSpeed5Unlocked(): Boolean {
        return repo.gameCore.getVipPool().first { it.effectType == SPEED_UP_TYPE && it.effectNum == 5 }.level <= getVipLevel()
    }

    fun isSpeed10Unlocked(): Boolean {
        return repo.gameCore.getVipPool().first { it.effectType == SPEED_UP_TYPE && it.effectNum == 10 }.level <= getVipLevel()
    }

    fun getExtraDailyQuest(): Int {
        return repo.gameCore.getVipPool().filter { it.level <= getVipLevel() }
            .filter { it.effectType == DAILY_QUEST_NUM_INC_TYPE }.sumOf { it.effectNum }
    }

    fun isSkipAd(): Boolean {
        return repo.gameCore.getVipPool().first { it.effectType == AD_AVOID_TYPE }.level <= getVipLevel()
    }

    fun getRealShopRefreshLimit(): Int {
        return repo.gameCore.getDailyShopRefreshCount() + repo.gameCore.getVipPool()
            .filter { it.level <= getVipLevel() }
            .filter { it.effectType == REFRESH_SHOP_NUM_INC_TYPE }.sumOf { it.effectNum }
    }

    fun getShopRefreshCost(): Int {
        return if (getVipLevel() >= repo.gameCore.getVipPool()
                .first { it.effectType == REFRESH_SHOP_FREE_TYPE }.level
        ) 0 else repo.gameCore.getRefreshShopCost()
    }

    fun isDoubleQuestAward(): Boolean {
        return getVipLevel() >= (repo.gameCore.getVipPool()
            .firstOrNull { it.effectType == DAILY_QUEST_DOUBLE_AWARD_TYPE }?.level ?: 999)
    }

    fun isThisLevelGained(cheat: Vip): Boolean {
        return gainedMap[cheat.id]!!.value
    }

    suspend fun gain(vip: Vip) {
        val gained = gainedMap[vip.id]!!
        if (gained.value) {
            AppWrapper.getString(R.string.already_got).toast()
            return
        }
        checkVip(vip)
        gained.value = true
        val award = vip.toAward()
        if (!award.isEmpty()) {
            Dialogs.awardDialog.value = award
            AwardManager.gainAward(award)
        } else {
            AppWrapper.getString(R.string.unlock_success).toast()
        }
    }

    fun getExtraPvpNum(): Int {
        return repo.gameCore.getVipPool().filter { it.level <= getVipLevel() }
            .sumOf {
                if (it.effectType == PVP_NUM_TYPE) it.effectNum else 0
            }
    }

    fun isDoubleEndingAward(): Boolean {
        return getVipLevel() >= repo.gameCore.getVipPool().first { it.effectType == ENDING_DOUBLE_AWARD_TYPE }.level
    }

    fun isCheat(): Boolean {
        return (AwardManager.electric.value < 300 && repo.allyManager.data.any { it.isXuanJiaJun() })
                || (AwardManager.electric.value < 1000 && repo.heroManager.data.any { it.isLiBai() })
                || (AwardManager.electric.value < 2000 && repo.skillManager.data.any { it.isJinChanTuoQiao() })
                || (AwardManager.electric.value < 3000 && repo.allyManager.data.any { it.isDaTianShi() })
                || (AwardManager.electric.value < 10000 && repo.heroManager.data.any { it.isSuoLuoMen() })
    }
}