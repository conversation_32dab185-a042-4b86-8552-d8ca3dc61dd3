package com.moyu.chuanqirensheng.logic.story

import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import kotlinx.serialization.Serializable

@Serializable
data class Ending(
    val uuid: String,
    val dieReason: String = GameApp.instance.getWrapString(R.string.die_good),
    val ending: String = GameApp.instance.getWrapString(R.string.none),
    val skinId: Int = 10000,
    val age: Int,
    val kill: Int,
    val level: Int,
    val death: Int,
    val maxReputationType: Int,
    val maxReputationValue: Int,
    val justice: Int,
    val endingText: String,
    val countryLevelUpAges: List<Int> = emptyList(),
    val countryName: String = "",
    val location: String = "",
    val religion: String = "",
    val tech: String = "",
    val rank: Double = 0.0,
)
