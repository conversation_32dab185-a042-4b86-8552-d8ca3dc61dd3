package com.moyu.chuanqirensheng.logic.rank

import com.moyu.chuanqirensheng.datastore.KEY_LIKE
import com.moyu.chuanqirensheng.datastore.KEY_LIKE_TIME
import com.moyu.chuanqirensheng.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.datastore.setLongValueByKey
import com.moyu.chuanqirensheng.model.award.Guarded
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.isSameDay

object RankManager {
    val famousLiked = Guarded(KEY_LIKE)
    fun init() {
        if (!isNetTimeValid()) {
            return
        }
        if (!isSameDay(getLongFlowByKey(KEY_LIKE_TIME), getCurrentTime())) {
            setLongValueByKey(KEY_LIKE_TIME, getCurrentTime())
            famousLiked.value = 0
        }
    }
}