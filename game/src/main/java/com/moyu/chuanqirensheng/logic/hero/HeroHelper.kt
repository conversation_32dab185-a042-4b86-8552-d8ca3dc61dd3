package com.moyu.chuanqirensheng.logic.hero

import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp


fun Int.getQualityName(): String {
    return when (this) {
        1 -> GameApp.instance.getWrapString(R.string.blue_quality)
        2 -> GameApp.instance.getWrapString(R.string.purple_quality)
        else -> GameApp.instance.getWrapString(R.string.orange_quality)
    }
}

fun Int.getQualityFrame(): Int {
    return when (this) {
        1 -> R.drawable.item_quality_1
        2 -> R.drawable.item_quality_2
        3 -> R.drawable.item_quality_3
        else -> R.drawable.item_quality_0
    }
}

fun Int.getQualityTransFrame(): Int {
    return when (this) {
        1 -> R.drawable.item_quality_1_trans
        2 -> R.drawable.item_quality_2_trans
        3 -> R.drawable.item_quality_3_trans
        else -> R.drawable.item_quality_0_trans
    }
}

fun Int.getCardTopQualityFrame(): Int {
    return when (this) {
        1 -> R.drawable.card_top_frame_1
        2 -> R.drawable.card_top_frame_2
        else -> R.drawable.card_top_frame_3
    }
}

fun Int.getCardBottomQualityFrame(): Int {
    return when (this) {
        1 -> R.drawable.card_bottom_frame_1
        2 -> R.drawable.card_bottom_frame_2
        else -> R.drawable.card_bottom_frame_3
    }
}

fun Int.getCardQualityFrame(): Int {
    return when (this) {
        1 -> R.drawable.card_frame_green
        2 -> R.drawable.card_frame_blue
        else -> R.drawable.card_frame_orange
    }
}

fun Int.getAllyQualityCardBack(): Int {
    return when (this) {
        1 -> R.drawable.card_back_ally1
        2 -> R.drawable.card_back_ally2
        else -> R.drawable.card_back_ally3
    }
}

fun Int.getSkillQualityCardBack(): Int {
    return when (this) {
        1 -> R.drawable.card_back_skill1
        2 -> R.drawable.card_back_skill2
        else -> R.drawable.card_back_skill3
    }
}

fun Int.getHeroQualityCardBack(): Int {
    return when (this) {
        1 -> R.drawable.card_back_hero1
        2 -> R.drawable.card_back_hero2
        else -> R.drawable.card_back_hero3
    }
}
