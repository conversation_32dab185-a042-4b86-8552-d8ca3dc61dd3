package com.moyu.chuanqirensheng.logic.event.detail

import androidx.compose.runtime.Composable
import com.moyu.chuanqirensheng.logic.event.PlayHandler
import com.moyu.core.model.event.Event

const val MERCENARY_PLAY  = 5
class MercenaryPlayHandler(
    override val skipWin: Boolean = true,
    override val dialogJump: Boolean = true,
    override val playId: Int = MERCENARY_PLAY
) : PlayHandler() {

    @Composable
    override fun Layout(event: Event) {
        BasicEventContentWithBackground(event)
    }

    override fun onEventSelect(event: Event) {
        eventFinished.value = false
        eventResult.value = true
    }
}