package com.moyu.chuanqirensheng.logic.guide

import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.datastore.KEY_GUIDE_INDEX
import com.moyu.chuanqirensheng.datastore.setIntValueByKey
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

const val FIRST_GUIDE_END = 13
const val BATTLE_GUIDE_START = 15

object GuideManager {
    val guideIndex = mutableStateOf(0)
    val showGuide = mutableStateOf(false)
    val battleDone = mutableStateOf(false)
    val nonBattleDone = mutableStateOf(false)

    fun canBack(): Boolean {
        return !showGuide.value
    }

    fun pauseGame(): Boolean {
        return showGuide.value
    }

    fun showFirstGuide() {
        if (guideIndex.value == 0) {
            showGuide.value = true
        }
    }

    fun onEnterGame() {
        if (guideIndex.value == 0) {
            GameApp.globalScope.launch {
                delay(300)
                guideIndex.value = 2
            }
        } else if (guideIndex.value == 2) {
            GameApp.globalScope.launch {
                delay(300)
                guideIndex.value = 8
                showGuide.value = false
            }
        }
    }

    fun onQuitFirstGuide() {
        if (guideIndex.value in listOf(6, 8)) {
            showGuide.value = false
        }
        when (guideIndex.value) {
            1, 3, 4, 5, 7, 9 -> { // 8不能直接到9，8引导完成后，需要等玩家完成前3个初始事件
                guideIndex.value += 1
                setIntValueByKey(KEY_GUIDE_INDEX, guideIndex.value)
            }
        }
    }

    fun toNextGuide() {
        guideIndex.value += 1
        setIntValueByKey(KEY_GUIDE_INDEX, guideIndex.value)
    }
}