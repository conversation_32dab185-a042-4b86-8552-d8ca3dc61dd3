package com.moyu.chuanqirensheng.logic.judge

import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.datastore.KEY_DIED_IN_GAME
import com.moyu.chuanqirensheng.datastore.increaseIntValueByKey
import com.moyu.chuanqirensheng.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.logic.battle.BattleManager.updateAlliesAfterBattle
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.event.fatalEventPlayIds
import com.moyu.chuanqirensheng.logic.record.SaveManager
import com.moyu.chuanqirensheng.logic.story.Ending
import com.moyu.chuanqirensheng.logic.task.onTaskKillEnemy
import com.moyu.chuanqirensheng.music.playerMusicByScreen
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.effect.dialogEffectState
import com.moyu.chuanqirensheng.screen.effect.loseBattleEffect
import com.moyu.chuanqirensheng.screen.effect.restartEffect
import com.moyu.chuanqirensheng.screen.effect.winBattleEffect
import com.moyu.chuanqirensheng.sub.report.ReportManager
import com.moyu.chuanqirensheng.thread.gameDispatcher
import com.moyu.chuanqirensheng.util.LOGIN_SCREEN
import com.moyu.chuanqirensheng.util.goto
import com.moyu.core.GameCore
import com.moyu.core.model.role.Role
import com.moyu.core.music.SoundEffect
import kotlinx.coroutines.launch

class GameJudgeManager : GameJudge {
    override val inBattle = mutableStateOf(false)
    override val gameOver = mutableStateOf(false)

    override fun onGameWin() {
        if (!repo.gameMode.value.isPvp() && !repo.gameMode.value.isTower()) {
            setIntValueByKey(KEY_DIED_IN_GAME, 0)
        }
        gameOver.value = true
        inBattle.value = false
        GameApp.instance.getWrapString(R.string.congratulations).toast()
        goto(LOGIN_SCREEN)
        playerMusicByScreen() // 音乐
    }

    override fun onGameOver(ending: Ending?) {
        if (!repo.gameMode.value.isPvp() && !repo.gameMode.value.isTower()) {
            setIntValueByKey(KEY_DIED_IN_GAME, 0)
        }
        gameOver.value = true
        inBattle.value = false
        SaveManager.clearSave()
        goto(LOGIN_SCREEN)
        playerMusicByScreen() // 音乐
    }

    override fun onBattleWin(allies: List<Role>, enemies: List<Role>) {
        ReportManager.onGameEnd(1)
        updateAlliesAfterBattle(allies)
        repo.inBattle.value = false
        GameCore.instance.onBattleEffect(SoundEffect.BattleWin)
        GameApp.globalScope.launch(gameDispatcher) {
            onTaskKillEnemy(enemies)
            if (EventManager.selectedEvent.value?.play in fatalEventPlayIds) {
                // 宿敌胜利弹窗不一样
                Dialogs.fatalEnemyDialog.value = EventManager.selectedEvent.value
            } else {
                Dialogs.gameWinDialog.value = allies + enemies
            }
            restartEffect(dialogEffectState, winBattleEffect)
        }
    }

    override fun onBattleLose(allies: List<Role>, enemies: List<Role>) {
        ReportManager.onGameEnd(0)
        repo.inBattle.value = false
        GameCore.instance.onBattleEffect(SoundEffect.BattleFailed)
        Dialogs.gameLoseDialog.value = allies + enemies
        restartEffect(dialogEffectState, loseBattleEffect)
        if (!repo.gameMode.value.isPvp() && !repo.gameMode.value.isTower()) {
            increaseIntValueByKey(KEY_DIED_IN_GAME)
        }
    }
}