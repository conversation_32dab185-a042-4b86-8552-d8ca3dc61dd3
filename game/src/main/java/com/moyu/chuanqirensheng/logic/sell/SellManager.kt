package com.moyu.chuanqirensheng.logic.sell

import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp.Companion.instance
import com.moyu.chuanqirensheng.cloud.AntiCheatManager.checkPoolCheck
import com.moyu.chuanqirensheng.cloud.AntiCheatManager.checkSell
import com.moyu.chuanqirensheng.cloud.MAX_AGE
import com.moyu.chuanqirensheng.datastore.KEY_GAME_TASK_PROGRESS
import com.moyu.chuanqirensheng.datastore.KEY_GIFT_AWARDED
import com.moyu.chuanqirensheng.datastore.KEY_SELL_ITEM1_REFRESHED
import com.moyu.chuanqirensheng.datastore.KEY_SELL_ITEM2_REFRESHED
import com.moyu.chuanqirensheng.datastore.KEY_SELL_ITEM3_REFRESHED
import com.moyu.chuanqirensheng.datastore.KEY_SELL_ITEMS
import com.moyu.chuanqirensheng.datastore.KEY_SELL_ITEMS_UPDATE_TIME_IN_MILLIS
import com.moyu.chuanqirensheng.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.datastore.getListObject
import com.moyu.chuanqirensheng.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.datastore.setListObject
import com.moyu.chuanqirensheng.datastore.setListObjectSync
import com.moyu.chuanqirensheng.datastore.setLongValueByKey
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.feature.monthcard.MonthCardManager
import com.moyu.chuanqirensheng.feature.newTask.SevenDayManager
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.award.VipManager
import com.moyu.chuanqirensheng.logic.story.StoryManager
import com.moyu.chuanqirensheng.logic.task.FOREVER
import com.moyu.chuanqirensheng.logic.task.TaskEvent
import com.moyu.chuanqirensheng.logic.task.onTaskBuyAlly
import com.moyu.chuanqirensheng.logic.task.onTaskBuyFree
import com.moyu.chuanqirensheng.logic.task.onTaskBuyHero
import com.moyu.chuanqirensheng.logic.task.onTaskBuySkill
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.sub.report.ReportManager
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.isSameDay
import com.moyu.core.AppWrapper
import com.moyu.core.GameCore
import com.moyu.core.model.sell.Award
import com.moyu.core.model.sell.Sell
import com.moyu.core.model.sell.toAward
import com.moyu.core.music.SoundEffect
import com.moyu.core.util.RANDOM

const val SELL_FOREVER = "s_)"
const val SELL_TYPE_PVP = 8
const val INFINITE_STORAGE = 6000
const val VIP_INDEX = 3
const val STORY_INDEX = 2

val shopTitles = listOf(
    Triple(instance.getWrapString(R.string.daily_gift), 1, repo.gameCore.getFreeShopItemNum()),
    Triple(instance.getWrapString(R.string.ally_card), 2, repo.gameCore.getAllyShopItemNum()),
    Triple(instance.getWrapString(R.string.skill_card), 3, repo.gameCore.getSkillShopItemNum()),
    Triple(instance.getWrapString(R.string.equip_card), 4, repo.gameCore.getEquipShopItemNum()),
    Triple(instance.getWrapString(R.string.special_ally), 5, 5),
    Triple(instance.getWrapString(R.string.special_skill), 6, 5),
    Triple(instance.getWrapString(R.string.special_hero), 7, 5),
)

object SellManager {
    val items = mutableStateListOf<Sell>()
    val refresh1Count = mutableStateOf(0)
    val refresh2Count = mutableStateOf(0)
    val refresh3Count = mutableStateOf(0)
    var isDrawing = false

    fun init() {
        if (!isNetTimeValid()) {
            return
        }
        if (isSameDay(
                getLongFlowByKey(KEY_SELL_ITEMS_UPDATE_TIME_IN_MILLIS), getCurrentTime()
            )
        ) {
            // 商店宝箱：仅同一天内更新过，才加载，否则不加载，自动会在Repo刷新
            if (items.isEmpty()) {
                getListObject<Sell>(KEY_SELL_ITEMS).let {
                    items.addAll(it.mapNotNull { sell ->
                        repo.gameCore.getSellPool().firstOrNull { it.id == sell.id }
                            ?.copy(opened = sell.opened, num = sell.num, storage = sell.storage)
                    })
                }
            }
            refresh1Count.value = getIntFlowByKey(KEY_SELL_ITEM1_REFRESHED)
            refresh2Count.value = getIntFlowByKey(KEY_SELL_ITEM2_REFRESHED)
            refresh3Count.value = getIntFlowByKey(KEY_SELL_ITEM3_REFRESHED)
        } else {
            items.clear()
            refresh1Count.value = 0
            refresh2Count.value = 0
            refresh3Count.value = 0
            setIntValueByKey(KEY_SELL_ITEM1_REFRESHED, 0)
            setIntValueByKey(KEY_SELL_ITEM2_REFRESHED, 0)
            setIntValueByKey(KEY_SELL_ITEM3_REFRESHED, 0)
        }
        if (items.isEmpty() || items.none { it.isMonthCard() } || items.none { it.isExpGift() }) {
            refreshSellItemByType(0)
            setLongValueByKey(KEY_SELL_ITEMS_UPDATE_TIME_IN_MILLIS, getCurrentTime())
        } else if (items.count { it.isTriggerGift() } < repo.gameCore.getSellPool().filter { it.isTriggerGift() }.size) {
            // todo 覆盖升级，gift只有首冲，其他都没有，需要添加
            items.removeAll { it.isTriggerGift() }
            items.addAll(repo.gameCore.getSellPool().filter { it.isTriggerGift() })
        } else if (items.filter { it.type == SELL_TYPE_PVP }.isEmpty()) {
            // todo 保护 单独处理非全局商店页的商品，也就是pvp商品
            val maxAge = Integer.min(
                MAX_AGE, getIntFlowByKey(
                    FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.AGE.id + "_1",
                    1
                )
            )
            val pvpItems = repo.gameCore.getSellPool().filter { it.type == SELL_TYPE_PVP }.filter { it.preCondition() }
                .filter { it.unlock == 0 || it.unlock in StoryManager.getUnlockedStoryIds() }.filter {
                    maxAge >= it.condition.first() && maxAge <= it.condition[1]
                }
            items.removeAll { it.type == SELL_TYPE_PVP }
            items.addAll(pvpItems.filter { it.regularType == 1 })
            items.addAll(pvpItems.filter { it.regularType == 2 }.shuffled(RANDOM).take(5))
            setListObject(KEY_SELL_ITEMS, items)
        }
    }

    fun refreshSellItemByType(type: Int) {
        if (!isNetTimeValid()) {
            // 防止作弊
            return
        }
        if (type != 0) {
            when (type) {
                2 -> {
                    refresh1Count.value += 1
                    (instance.getWrapString(R.string.ally_refreshed) + refresh1Count.value + instance.getWrapString(
                        R.string.times
                    )).toast()
                    setIntValueByKey(KEY_SELL_ITEM1_REFRESHED, refresh1Count.value)
                }

                3 -> {
                    refresh2Count.value += 1
                    (instance.getWrapString(R.string.skill_refreshed) + refresh2Count.value + instance.getWrapString(
                        R.string.times
                    )).toast()
                    setIntValueByKey(KEY_SELL_ITEM2_REFRESHED, refresh2Count.value)
                }

                else -> {
                    refresh3Count.value += 1
                    (instance.getWrapString(R.string.quip_refreshed) + refresh3Count.value + instance.getWrapString(
                        R.string.times
                    )).toast()
                    setIntValueByKey(KEY_SELL_ITEM3_REFRESHED, refresh3Count.value)
                }
            }
        }
        val maxAge = Integer.min(
            MAX_AGE, getIntFlowByKey(
                FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.AGE.id + "_1",
                1
            )
        )
        val filteredChests = repo.gameCore.getSellPool().filter {
            maxAge >= it.condition.first() && maxAge <= it.condition[1]
        }
        val targetItems = mutableListOf<Sell>()
        shopTitles.filter { if (type == 0) true else it.second == type }.forEach { triple ->
            // 如果是付费商品，可以不管precondition，先刷出来，显示的地方会屏蔽。保证购买首冲后能立刻显示
            val raw = filteredChests.filter { it.type == triple.second }.filter { it.preCondition() || it.isAifadian() }
                .filter { it.unlock == 0 || it.unlock in StoryManager.getUnlockedStoryIds() }
            val solid = raw.filter { it.regularType == 1 }
            val items =
                raw.filter { it.regularType == 2 }.shuffled(RANDOM).take(triple.third - solid.size)
                    .map { it.copy() }
            val final = solid + items
            targetItems.addAll(final)
        }

        if (type == 0) {
            // 全部刷新
            items.clear()
        } else {
            items.removeAll { it.type == type }
        }
        items.addAll(targetItems)
        if (items.none { it.isMonthCard() }) {
            items.addAll(repo.gameCore.getSellPool().filter { it.isMonthCard() })
        }
        if (items.none { it.isExpGift() }) {
            items.addAll(repo.gameCore.getSellPool().filter { it.isExpGift() })
        }
        if (type == 0 || items.filter { it.type == SELL_TYPE_PVP }.isEmpty()) {
            // 单独处理非全局商店页的商品，也就是pvp商品
            val pvpItems = repo.gameCore.getSellPool().filter { it.type == SELL_TYPE_PVP }.filter { it.preCondition() }
                .filter { it.unlock == 0 || it.unlock in StoryManager.getUnlockedStoryIds() }.filter {
                    maxAge >= it.condition.first() && maxAge <= it.condition[1]
                }
            items.removeAll { it.type == SELL_TYPE_PVP }
            items.addAll(pvpItems.filter { it.regularType == 1 })
            items.addAll(pvpItems.filter { it.regularType == 2 }.shuffled(RANDOM).take(5))
        }
        setListObject(KEY_SELL_ITEMS, items)
    }

    suspend fun openSellChest(sell: Sell) {
        if (!isNetTimeValid()) {
            // 防止作弊
            instance.getWrapString(R.string.time_error_tips).toast()
            return
        }
        checkSell(sell)
        checkPoolCheck()
        if (sell.isDiamondMoney() && AwardManager.diamond.value < sell.price) {
            GiftManager.onDiamondNotEnough()
            instance.getWrapString(R.string.diamond_not_enough).toast()
        } else if (sell.isKeyMoney() && AwardManager.key.value < sell.price) {
            GiftManager.onKeyNotEnough()
            instance.getWrapString(R.string.key_not_enough).toast()
        } else if (sell.isPvpMoney() && AwardManager.pvpDiamond.value < sell.price) {
            instance.getString(R.string.pvp_diamond_not_enough).toast()
        } else {
            GameCore.instance.onBattleEffect(SoundEffect.BuyGood)
            items.indexOfFirst { it.id == sell.id }.takeIf { it >= 0 }?.let {
                items[it] = items[it].copy(storage = items[it].storage - 1)
                setListObjectSync(KEY_SELL_ITEMS, items)
                when (sell.priceType) {
                    1 -> {
                        AwardManager.gainDiamond(-sell.price)
                    }

                    2 -> {
                        AwardManager.gainKey(-sell.price)
                    }

                    4 -> {
                        AwardManager.gainPvpDiamond(-sell.price)
                    }
                }
                ReportManager.onShopPurchase(
                    sellId = sell.id,
                    price = sell.price,
                    priceType = sell.priceType
                )
                sell.toAward().apply {
                    AwardManager.gainAward(this)
                    if (this.outAllies.isNotEmpty()) {
                        onTaskBuyAlly()
                    } else if (this.outSkills.isNotEmpty()) {
                        onTaskBuySkill()
                    } else if (this.outHeroes.isNotEmpty()) {
                        onTaskBuyHero()
                    }
                    if (sell.isFreeGift()) {
                        onTaskBuyFree()
                    }
                    Dialogs.awardDialog.value = this
                }
            } ?: AppWrapper.getString(R.string.error_tips).toast()
        }
    }

    fun getReds(): List<Boolean> {
        return listOf(
            items.filter { it.type == 1 }.any { it.storage > 0 },
            false,
            false,
            repo.gameCore.getVipPool().any {
                VipManager.getVipLevel() >= it.level && !VipManager.isThisLevelGained(it)
            })
    }

    fun getRedFree(type: Int): Boolean {
        return items.filter { it.type == type }.filter { it.isFreeGift() }.any { it.storage > 0 }
    }

    suspend fun dealAfterPay(it: Sell, realAward: Award) {
        if (it.isTriggerGift()) {
            // todo 一定要先更新KEY_GIFT_AWARDED，不然首页的礼包icon不会正常消失，
            setBooleanValueByKey(KEY_GIFT_AWARDED + it.id, true)
            setBooleanValueByKey(SELL_FOREVER + it.id, true)
            AwardManager.gainAward(realAward)
            if (repo.inGame.value && !repo.gameMode.value.isPvp()) {
                // todo 礼包奖励，如果在局内，直接局内也要获得盟友卡
                AwardManager.gainAward(Award(allies = realAward.outAllies, skills = realAward.outSkills))
            }
            if (it.isSevenDay()) {
                SevenDayManager.markGoogleSellItem(it)
            }
            Dialogs.awardDialog.value = realAward
        } else if (it.isMonthCard()) {
            MonthCardManager.openPackage(it)
        } else if (it.isTower()) {
            TowerManager.openPackage(it)
        } else {
            openGoogleBillSell(it)
            AwardManager.gainAward(realAward)
            Dialogs.awardDialog.value = realAward
            if (it.isSevenDay()) {
                SevenDayManager.markGoogleSellItem(it)
            }
        }
    }

    suspend fun openGoogleBillSell(sell: Sell) {
        GameCore.instance.onBattleEffect(SoundEffect.BuyGood)
        items.indexOfFirst { it.id == sell.id }.takeIf { it >= 0 }?.let {
            if (!sell.isMonthCard()) { // 月卡的storage是用来表示购后可以领取的天数，不用-1
                items[it] = items[it].copy(storage = items[it].storage - 1)
            }
            setListObject(KEY_SELL_ITEMS, items)
        }
        setBooleanValueByKey(SELL_FOREVER + sell.id, true)
    }
}

fun Sell.preCondition(): Boolean {
    return if (showCondition == 0) true else {
        if (instance.resources.getBoolean(R.bool.has_billing)) {
            getBooleanFlowByKey(SELL_FOREVER + showCondition)
        } else {
            // taptap包，没有了触发礼包，只能都true
            true
        }
    }
}