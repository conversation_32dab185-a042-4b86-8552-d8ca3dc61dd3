package com.moyu.chuanqirensheng.logic.event.detail

import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.logic.event.PlayHandler
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.core.model.event.Event
import com.moyu.core.model.sell.Award
import com.moyu.core.model.sell.toAward

class DiplomacyPlayHandler(
    override val skipWin: <PERSON>olean = true,
    override val dialogJump: Boolean = true,
    override val playId: Int = 8
) : PlayHandler() {

    @Composable
    override fun Layout(event: Event) {
        BasicEventContentWithBackground(event)
    }

    override fun onEventSelect(event: Event) {
        eventFinished.value = false
        eventResult.value = true
    }
}