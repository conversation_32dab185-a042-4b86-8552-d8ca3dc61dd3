package com.moyu.chuanqirensheng.logic.award

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateListOf
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.api.RetrofitModel
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.cloud.AntiCheatManager.checkAlly
import com.moyu.chuanqirensheng.cloud.AntiCheatManager.checkHero
import com.moyu.chuanqirensheng.datastore.KEY_AD_MONEY
import com.moyu.chuanqirensheng.datastore.KEY_AD_NUM
import com.moyu.chuanqirensheng.datastore.KEY_DIAMOND
import com.moyu.chuanqirensheng.datastore.KEY_ELECTRIC
import com.moyu.chuanqirensheng.datastore.KEY_FIRST_CHARGE_DONE
import com.moyu.chuanqirensheng.datastore.KEY_HOLIDAY_MONEY
import com.moyu.chuanqirensheng.datastore.KEY_KEY
import com.moyu.chuanqirensheng.datastore.KEY_KEY_COST
import com.moyu.chuanqirensheng.datastore.KEY_LOTTERY_MONEY
import com.moyu.chuanqirensheng.datastore.KEY_PVP_DIAMOND
import com.moyu.chuanqirensheng.datastore.KEY_TCG_CARD_REWARD
import com.moyu.chuanqirensheng.datastore.KEY_UNLOCK_EVIDENCE
import com.moyu.chuanqirensheng.datastore.KEY_WAR_PASS
import com.moyu.chuanqirensheng.datastore.KEY_WAR_PASS2
import com.moyu.chuanqirensheng.datastore.getListObject
import com.moyu.chuanqirensheng.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.datastore.setListObjectSync
import com.moyu.chuanqirensheng.datastore.setLongValueByKey
import com.moyu.chuanqirensheng.feature.battlepass.KEY_WAR_PASS2_UNLOCK_EVIDENCE
import com.moyu.chuanqirensheng.feature.battlepass.KEY_WAR_PASS_NUM
import com.moyu.chuanqirensheng.feature.battlepass.KEY_WAR_PASS_UNLOCK_EVIDENCE
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.feature.holiday.HolidayManager
import com.moyu.chuanqirensheng.feature.monthcard.MonthCardManager
import com.moyu.chuanqirensheng.feature.newTask.SevenDayManager
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.adventureSkillTrigger
import com.moyu.chuanqirensheng.logic.skill.ReputationDownEvent
import com.moyu.chuanqirensheng.logic.skill.ReputationLevelDownEvent
import com.moyu.chuanqirensheng.logic.skill.ReputationLevelUpEvent
import com.moyu.chuanqirensheng.logic.skill.ReputationUpEvent
import com.moyu.chuanqirensheng.logic.task.onTaskGetItem
import com.moyu.chuanqirensheng.logic.tcg.TcgManager
import com.moyu.chuanqirensheng.logic.unlock.UnlockManager
import com.moyu.chuanqirensheng.model.award.Guarded
import com.moyu.chuanqirensheng.model.award.GuardedB
import com.moyu.chuanqirensheng.model.award.awardList
import com.moyu.chuanqirensheng.music.MusicManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.util.AdUtil.decodeText
import com.moyu.chuanqirensheng.util.AdUtil.encodeText
import com.moyu.chuanqirensheng.util.getVersionCode
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.core.GameCore
import com.moyu.core.model.info.BattleInfoType
import com.moyu.core.model.sell.Award
import com.moyu.core.model.tcg.TcgAward
import com.moyu.core.music.SoundEffect
import com.moyu.core.util.perBiggerI
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

object AwardManager {
    val tcgCardRewardRecords = mutableStateListOf<Int>()

    val battlePassBought = mutableMapOf<Int, MutableState<Boolean>>()
    val battlePass2Bought = mutableMapOf<Int, MutableState<Boolean>>()
    val diamond = Guarded(KEY_DIAMOND)
    val key = Guarded(KEY_KEY)
    val keyCost = Guarded(KEY_KEY_COST)
    val pvpDiamond = Guarded(KEY_PVP_DIAMOND)
    val electric = Guarded(KEY_ELECTRIC)
    val warPass = Guarded(KEY_WAR_PASS)
    val warPass2 = Guarded(KEY_WAR_PASS2)
    val adNum = Guarded(KEY_AD_NUM)
    val lotteryMoney = Guarded(KEY_LOTTERY_MONEY)
    val adMoney = Guarded(KEY_AD_MONEY)
    val holidayMoney = Guarded(KEY_HOLIDAY_MONEY)

    fun init() {
        (KEY_WAR_PASS_UNLOCK_EVIDENCE..KEY_WAR_PASS_UNLOCK_EVIDENCE + KEY_WAR_PASS_NUM).forEachIndexed { index, i ->
            battlePassBought[index + 1] =
                GuardedB(KEY_UNLOCK_EVIDENCE + i)
        }
        (KEY_WAR_PASS2_UNLOCK_EVIDENCE..KEY_WAR_PASS2_UNLOCK_EVIDENCE + KEY_WAR_PASS_NUM).forEachIndexed { index, i ->
            battlePass2Bought[index + 1] =
                GuardedB(KEY_UNLOCK_EVIDENCE + i)
        }
        tcgCardRewardRecords.addAll(
            getListObject(
                KEY_TCG_CARD_REWARD
            )
        )
    }

    suspend fun gainAward(award: Award) {
        withContext(Dispatchers.Main) {
            onTaskGetItem(award)
            MusicManager.playSound(SoundEffect.GainAward)
            award.elements.forEachIndexed { index, i ->
                if (i != 0) {
                    BattleManager.gainElement(index, i)
                }
            }
            award.extraElements.forEachIndexed { index, i ->
                if (i != 0) {
                    BattleManager.gainExtraElement(index, i)
                }
            }
            if (award.reputations.any { it != 0 }) {
                val oldReputations = BattleManager.toReputationLevels()
                award.reputations.forEachIndexed { index, i ->
                    if (i != 0) {
                        if (i > 0) {
                            adventureSkillTrigger(triggerSkill = ReputationUpEvent.copy(mainId = index))
                        } else {
                            adventureSkillTrigger(triggerSkill = ReputationDownEvent.copy(mainId = index))
                        }
                        BattleManager.gainReputation(index, i)
                    }
                    // 是否声望升级
                    if (BattleManager.toReputationLevels()[index] > oldReputations[index]) {
                        adventureSkillTrigger(triggerSkill = ReputationLevelUpEvent.copy(mainId = index))
                    } else if (BattleManager.toReputationLevels()[index] < oldReputations[index]) {
                        adventureSkillTrigger(triggerSkill = ReputationLevelDownEvent.copy(mainId = index))
                    }
                }
            }
            award.badges.forEachIndexed { index, i ->
                if (i != 0) {
                    BattleManager.gainBadge(index, i)
                }
            }
            award.lotteryMoney.takeIf { it != 0 }?.let {
                lotteryMoney.value += it
            }
            award.holidayMoney.takeIf { it != 0 }?.let {
                holidayMoney.value += it
            }
            award.diamond.takeIf { it != 0 }?.let {
                gainDiamond(it)
            }
            award.pvpDiamond.takeIf { it != 0 }?.let {
                gainPvpDiamond(it)
            }
            award.pvpScore.takeIf { it != 0 }?.let {
                PvpManager.pvpScore.value += it
            }
            award.adMoney.takeIf { it > 0 }?.let {
                adMoney.value += it
            }
            award.key.takeIf { it != 0 }?.let {
                gainKey(it)
            }
            award.electric.takeIf { it > 0 }?.let {
                gainElectric(it)
            }
            award.warPass.takeIf { it > 0 }?.let {
                gainWarPass(it)
            }
            award.warPass2.takeIf { it != 0 }?.let {
                gainWarPass2(it)
            }
            award.allies.forEach { ally ->
                BattleManager.gainInGame(ally)
                repo.onBattleInfo(
                    GameApp.instance.getWrapString(R.string.gain_ally) + ally.name,
                    BattleInfoType.Battle
                )
            }
            award.loseAllies.forEach { ally ->
                BattleManager.dropFromGame(ally)
                repo.onBattleInfo(
                    GameApp.instance.getWrapString(R.string.lose_ally) + ally.name,
                    BattleInfoType.Battle
                )
            }
            award.outAllies.forEach { ally ->
                checkAlly(ally)
                repo.allyManager.gain(ally.copy(new = true))
            }
            award.heroes.forEach { hero ->
                BattleManager.gainHeroInGame(hero)
                repo.onBattleInfo(
                    GameApp.instance.getWrapString(R.string.gain_equip) + hero.name,
                    BattleInfoType.Battle
                )
            }
            award.loseHeroes.forEach { equip ->
                BattleManager.dropHeroFromGame(equip)
                repo.onBattleInfo(
                    GameApp.instance.getWrapString(R.string.lose_equip) + equip.name,
                    BattleInfoType.Battle
                )
            }
            award.outHeroes.forEach { hero ->
                checkHero(hero)
                repo.heroManager.gain(hero.copy(new = true).initYear())
            }
            award.skills.forEach { skill ->
                BattleManager.gainInGame(skill)
                repo.onBattleInfo(
                    GameApp.instance.getWrapString(R.string.gain_skill_card) + skill.name,
                    BattleInfoType.Battle
                )
            }
            award.loseSkills.forEach { skill ->
                BattleManager.dropFromGame(skill)
                repo.onBattleInfo(
                    GameApp.instance.getWrapString(R.string.lose_skill_card) + skill.name,
                    BattleInfoType.Battle
                )
            }
            award.outSkills.forEach { skill ->
                repo.skillManager.gain(skill.copy(new = true))
            }
            award.property.takeIf { it.isNotEmpty() }?.let { property ->
                BattleManager.gainAdventureProp(property)
            }
            award.tcgs.forEach { tcgCard ->
                TcgManager.gainCard(tcgCard)
                repo.onBattleInfo(
                    GameApp.instance.getWrapString(R.string.gain_tcg_comma) + tcgCard.name,
                    BattleInfoType.Battle
                )
            }
            award.unlockList.forEach {
                UnlockManager.unlockCode(it)
            }
        }
    }

    fun doNetAward(text: String) {
        GameApp.globalScope.launch {
            // 确认是否是网络兑换码
            val netCode = decodeText(text)
            if (netCode?.startsWith("u_") == true) {
                val flow = getLongFlowByKey(text)
                if (flow != 0L) {
                    GameApp.instance.getWrapString(R.string.code_already_used_by_you).toast()
                } else {
                    GameApp.instance.getObjectId()?.let {
                        encodeText(it)?.let { data ->
                            val result =
                                RetrofitModel.getCodeAwards(it, netCode, data, getVersionCode())
                            if (!result.valid) {
                                GameApp.instance.getWrapString(R.string.code_error_tips2).toast()
                            } else {
                                // 标记已领取
                                setLongValueByKey(text, 1)
                                if (result.unlockList.isNotEmpty()) {
                                    GameApp.instance.getWrapString(R.string.unlocked_tips).toast()
                                } else if (result.message.isNotEmpty()) {
                                    result.message.toast()
                                }
                                if (!result.isEmpty()) {
                                    gainAward(result)
                                    if (result.isMonthCard()) {
                                        repo.gameCore.getSellPool().firstOrNull { it.id == result.sellId }?.let {
                                            MonthCardManager.openPackage(it)
                                        }
                                    } else {
                                        gainAward(result)
                                        // 显示
                                        Dialogs.awardDialog.value = result
                                    }
                                    val sell = repo.gameCore.getSellPool().firstOrNull { it.id == result.sellId }
                                    if (sell?.isSevenDay() == true) {
                                        SevenDayManager.markGoogleSellItem(sell)
                                    }
                                    GiftManager.onGiftBought(result.sellId)
                                }
                            }
                        } ?: GameApp.instance.getWrapString(R.string.code_error).toast()
                    } ?: GameApp.instance.getWrapString(R.string.confirm_login).toast()
                }
            } else {
                awardList.find { text == it.code && (isNetTimeValid()) }
                    ?.let {
                        val flow = getLongFlowByKey(it.code)
                        if (flow == 0L) {
                            val award = it
                            // 标记已领取
                            setLongValueByKey(it.code, 1)
                            // 领取奖励
                            gainAward(award)
                            // 显示
                            Dialogs.awardDialog.value = award
                        } else {
                            GameApp.instance.getWrapString(R.string.code_used).toast()
                        }
                    } ?: GameApp.instance.getWrapString(R.string.code_not_found).toast()
            }
        }
    }

    fun gainDiamond(gain: Int) {
        diamond.value += gain
        if (gain > 0) {
            repo.onBattleInfo(
                GameApp.instance.getWrapString(R.string.gain_diamond) + gain,
                BattleInfoType.ExtraSkill
            )
        } else {
            GameCore.instance.onBattleEffect(SoundEffect.UserMoney)
        }
    }

    fun gainPvpDiamond(gain: Int) {
        pvpDiamond.value += gain
        if (gain > 0) {
            repo.onBattleInfo(
                GameApp.instance.getWrapString(R.string.gain_pvp_diamond) + gain,
                BattleInfoType.ExtraSkill
            )
        } else {
            GameCore.instance.onBattleEffect(SoundEffect.UserMoney)
        }
    }

    fun gainKey(gain: Int) {
        key.value += gain
        if (gain > 0) {
            repo.onBattleInfo(
                GameApp.instance.getWrapString(R.string.gain_keys) + gain, BattleInfoType.ExtraSkill
            )
        } else {
            keyCost.value += -gain
            GameCore.instance.onBattleEffect(SoundEffect.UserMoney)
        }
    }

    fun gainHolidayMoney(gain: Int) {
        holidayMoney.value += gain
    }

    suspend fun gainElectric(gain: Int) {
        withContext(Dispatchers.Main) {
            electric.value += gain
            SevenDayManager.gainElectric(gain)
            HolidayManager.gainElectric(gain)
            setBooleanValueByKey(KEY_FIRST_CHARGE_DONE, true)
            GameApp.globalScope.launch(Dispatchers.IO) {
                delay(2000)
                GameApp.instance.uploadCurrentSave()
            }

        }
    }

    suspend fun gainWarPass(gain: Int) {
        withContext(Dispatchers.Main) {
            warPass.value += gain
        }
    }

    suspend fun gainWarPass2(gain: Int) {
        withContext(Dispatchers.Main) {
            warPass2.value += gain
        }
    }

    suspend fun gainTcgCardReward(tcgAward: TcgAward, award: Award) {
        if (tcgCardRewardRecords.contains(tcgAward.id)) {
            GameApp.instance.getWrapString(R.string.already_got).toast()
        } else {
            tcgCardRewardRecords.add(tcgAward.id)
            setListObjectSync(
                KEY_TCG_CARD_REWARD, tcgCardRewardRecords
            )
            GameApp.globalScope.launch {
                gainAward(award)
                Dialogs.awardDialog.value = award
            }
        }
    }

    fun isAffordable(award: Award, consume: Boolean = false): Boolean {
        if (award.elements.any { it != 0 } && !BattleManager.elements.perBiggerI(award.elements)) {
            return false
        }
        if (award.extraElements.any { it != 0 } && !BattleManager.extraElements.perBiggerI(award.extraElements)) {
            return false
        }
        if (award.reputations.any { it != 0 } && !BattleManager.reputations.perBiggerI(award.reputations)) {
            return false
        }
        if (award.badges.any { it != 0 } && !BattleManager.badges.perBiggerI(award.badges)) {
            return false
        }
        if (!BattleManager.adventureProps.value.perBiggerI(award.property)) {
            return false
        }
        if (award.skills.isNotEmpty()) {
            award.skills.forEach { target->
                if (BattleManager.skillGameData.none {
                    it.id == target.id
                }) {
                    return false
                }
            }
        }
        if (consume) {
            GameApp.globalScope.launch(Dispatchers.Main) {
                award.elements.forEachIndexed { index, i ->
                    BattleManager.gainElement(index, -i)
                }
                award.badges.forEachIndexed { index, i ->
                    BattleManager.gainBadge(index, -i)
                }
            }
        }
        return true
    }
}