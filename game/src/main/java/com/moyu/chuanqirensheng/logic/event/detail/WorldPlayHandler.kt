package com.moyu.chuanqirensheng.logic.event.detail

import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.logic.event.PlayHandler
import com.moyu.chuanqirensheng.screen.common.CardSize
import com.moyu.chuanqirensheng.screen.event.CommonCard
import com.moyu.chuanqirensheng.ui.theme.gapLarge
import com.moyu.core.model.event.Event

class WorldPlayHandler(
    override val skipWin: Boolean = true,
    override val dialogJump: Boolean = true,
    override val playId: Int = 31
) : PlayHandler() {

    @Composable
    override fun Layout(event: Event) {
        Spacer(modifier = Modifier.size(gapLarge))
        CommonCard(cardSize = CardSize.Large, title = event.name, icon = event.pic, id = event.id)
    }

    override fun onEventSelect(event: Event) {
        eventFinished.value = false
        eventResult.value = true
    }
}