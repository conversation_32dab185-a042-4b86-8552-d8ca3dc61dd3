package com.moyu.chuanqirensheng.logic.ally

import androidx.compose.runtime.mutableStateListOf
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.datastore.KEY_ALLIES
import com.moyu.chuanqirensheng.datastore.KEY_DIAMOND_NOT_ENOUGH
import com.moyu.chuanqirensheng.datastore.increaseIntValueByKey
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.basic.BasicItemHolder
import com.moyu.chuanqirensheng.logic.basic.ItemHolder
import com.moyu.chuanqirensheng.logic.task.TaskManager
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.effect.dialogEffectState
import com.moyu.chuanqirensheng.screen.effect.restartEffect
import com.moyu.chuanqirensheng.screen.effect.starUpEffect
import com.moyu.chuanqirensheng.sub.report.ReportManager
import com.moyu.core.GameCore
import com.moyu.core.model.ally.Ally
import com.moyu.core.music.SoundEffect
import kotlin.math.max

class AllyManager(
    private val holder: ItemHolder<Ally> = BasicItemHolder(saveKey = KEY_ALLIES,
        elementSerializer = Ally.serializer(),
        data = mutableStateListOf(),
        sameGroup = { item1, item2 -> item1.mainId == item2.mainId },
        increase = { current, add ->
            current.copy(
                num = current.num + add.num,
                new = add.new,
                id = if (add.star > current.star) add.id else current.id,
                starUpNum = if (add.star > current.star) add.starUpNum else current.starUpNum,
                starUpRes = if (add.star > current.star) add.starUpRes else current.starUpRes,
                star = max(current.star, add.star)
            )
        })
) : ItemHolder<Ally> by holder {

    // data修改都需要确保主线程
    fun selectToGame(target: Ally) {
        GameCore.instance.onBattleEffect(SoundEffect.EquipItem)
        data.indexOfFirst { it.id == target.id }.takeIf { it != -1 }?.let {
            data[it] = data[it].switchSelect()
            save()
        }
    }

    // data修改都需要确保主线程
    // 升星逻辑改了，有基底概念，所以要-1
    fun upgrade(ally: Ally): Ally? {
        if (AwardManager.diamond.value < ally.starUpRes) {
            GiftManager.onDiamondNotEnough()
            GameApp.instance.getWrapString(R.string.diamond_not_enough).toast()
        } else if (ally.starUpNum == 0) {
            GameApp.instance.getWrapString(R.string.already_max_star_tips).toast()
        } else if (ally.starUpNum > ally.num - 1) {
            GameApp.instance.getWrapString(R.string.card_not_enough).toast()
        } else if (ally.star >= ally.starLimit) {
            GameApp.instance.getWrapString(R.string.already_max_star_tips).toast()
        } else {
            ReportManager.onUpgradeAllyOutGame(ally.id, ally.star)
            GameCore.instance.onBattleEffect(SoundEffect.UpgradeItem)
            restartEffect(dialogEffectState, starUpEffect)
            AwardManager.gainDiamond(-ally.starUpRes)
            data.indexOfFirst { it.id == ally.id }.takeIf { it != -1 }?.let {
                data[it] = data[it].copy(num = data[it].num - ally.starUpNum).starUp()
                save()
                return data[it]
            }
        }
        return null
    }
}
