package com.moyu.chuanqirensheng.logic.skill

import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.event.INIT_AGE
import com.moyu.chuanqirensheng.logic.record.RecordManager
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.core.GameCore
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.isAdventure
import com.moyu.core.util.chance
import kotlin.math.abs

object ExtraSkillTrigger {
    fun trigger(
        skill: Skill,
        skillOwner: Role,
        triggeringSkill: Skill?
    ): Boolean {
        skill.apply {
            val conditionTrigger = mutableListOf<Boolean>()
            // 概率可以被技能影响，根据类型先获得最终的实际概率
            val realRate = skillOwner.getChance(this, triggerType, rate)

            // 如果没有roll中，则返回
            if (!realRate.chance() && !GameCore.instance.getDebugConfig().easySkill) return false

            // roll中了，还要看条件，如果有的话
            activeCondition.forEachIndexed { index, condition ->
                val conditionResult = when (abs(condition)) {
                    10000 -> {
                        // 进入事件时（参数位置填写eventType，0是任意事件）
                        triggeringSkill?.isEnterEvent() == true && (activeConditionNum[index] == 0
                                || activeConditionNum[index] == triggeringSkill.getEventType())
                    }

                    10100 -> {
                        // 事件成功时（参数位置填写eventType，0是任意事件）
                        triggeringSkill?.isSucceededEvent() == true && (activeConditionNum[index] == 0
                                || activeConditionNum[index] == triggeringSkill.getEventType())
                    }

                    10200 -> {
                        // 事件失败时（参数位置填写eventType，0是任意事件）
                        triggeringSkill?.isFailedEvent() == true && (activeConditionNum[index] == 0
                                || activeConditionNum[index] == triggeringSkill.getEventType())
                    }

                    in 10300..10399 -> {
                        // 事件累计完成x次（参数位置填写eventType，0是任意事件）
                        val x = abs(condition) - 10300
                        triggeringSkill?.isSucceededEvent() == true && (activeConditionNum[index] == 0
                                || activeConditionNum[index] == triggeringSkill.getEventType())
                                && EventManager.getSucceededEvents()
                            .filter {
                                it.play == activeConditionNum[index] || activeConditionNum[index] == 0
                            }.size >= x
                    }

                    in 10400..10499 -> {
                        // 事件累计失败x次（参数位置填写eventType，0是任意事件）
                        val x = abs(condition) - 10400
                        triggeringSkill?.isFailedEvent() == true && (activeConditionNum[index] == 0
                                || activeConditionNum[index] == triggeringSkill.getEventType())
                                && EventManager.getFailedEvents()
                            .filter { it.play == activeConditionNum[index] || activeConditionNum[index] == 0 }.size >= x
                    }

                    in 10500..10599 -> {
                        // 事件累计完成x次（参数位置填写eventType，0是任意事件）
                        val x = abs(condition) - 10500
                        (triggeringSkill?.isSucceededEvent() == true || triggeringSkill?.isFailedEvent() == true)
                                && (activeConditionNum[index] == 0
                                || activeConditionNum[index] == triggeringSkill.getEventType())
                                && EventManager.getUsedEvents()
                            .filter { it.play == activeConditionNum[index] || activeConditionNum[index] == 0 }.size >= x
                    }

                    10600 -> {
                        (triggeringSkill?.isSucceededEvent() == true || triggeringSkill?.isFailedEvent() == true)
                                && triggeringSkill.id == activeConditionNum[index]
                    }

                    10700 -> {
                        // 所有商店购买物品时
                        false
                    }

                    11001 -> {
                        // x岁时（参数位置填写岁数）
                        triggeringSkill?.isAgeEvent() == true && life != 0 && life == activeConditionNum[index]
                    }

                    11002 -> {
                        // 年龄每增长x岁时
                        triggeringSkill?.isAgeEvent() == true && life != 0 && (life - INIT_AGE) % activeConditionNum[index] == 0
                    }

                    11003 -> {
                        // 小于x岁时（负号表示大于等于）
                        if (activeConditionNum[index] > 0) {
                            triggeringSkill?.isAgeEvent() == true && life < activeConditionNum[index]
                        } else {
                            triggeringSkill?.isAgeEvent() == true && life >= -activeConditionNum[index]
                        }
                    }

                    12000 -> {
                        // 持有至少1个指定ID的物品时
                        BattleManager.skillGameData.any { it.mainId == activeConditionNum[index] }
                                || BattleManager.heroGameData.any { it.mainId == activeConditionNum[index] }
                                || BattleManager.allyGameData.any { it.mainId == activeConditionNum[index] }
                    }

                    12001 -> {
                        // 没有指定ID的物品时
                        BattleManager.skillGameData.none { it.mainId == activeConditionNum[index] }
                    }

                    in 12100..12199 -> {
                        // 持有poolType1【局内军团卡】物品的数量大于等于一定数量时，xx为物品1的子类，无类则填00
                        // 子类1=步兵2=骑兵3=弓兵4=器械5=神职
                        val allyType = abs(condition) - 12100
                        BattleManager.allyGameData.filter { it.type == allyType || allyType == 0 }.size >= activeConditionNum[index]
                    }

                    in 12200..12299 -> {
                        // 持有poolType2【局内冒险技能卡】物品的数量大于等于一定数量时，xx为物品2的子类
                        // 子类1=科技2=政策3=宗教4=建筑5=地理6=天灾7=危机
                        /**
                        11=科技卡
                        12=政策卡
                        13=宗教卡
                        14=建筑卡
                        15=地理卡
                        16=天灾卡
                        17=危机卡
                         */
                        val allyType = abs(condition) - 12200
                        BattleManager.skillGameData.filter { it.isAdventure() }
                            .filter { it.elementType == allyType || allyType == 0 }.size >= activeConditionNum[index]
                    }

                    in 12300..12399 -> {
                        // 持有poolType3【局内战术卡】物品的数量大于等于一定数量时，xx为物品3的子类
                        // 子类1=近战2=远程3=攻城4=神圣5=混乱
                        val allyType = abs(condition) - 12300
                        BattleManager.skillGameData.filter { !it.isAdventure() }
                            .filter { it.elementType == allyType || allyType == 0 }.size >= activeConditionNum[index]
                    }

                    in 12400..12499 -> {
                        // 持有poolType4【局内史诗人物】物品的数量大于等于一定数量时，xx为物品4的子类
                        // 子类1=科学2=政治3=军事4=宗教5=商业6=艺术
                        /**
                        21=科学史诗人物
                        22=政治史诗人物
                        23=军事史诗人物
                        24=宗教史诗人物
                        25=商业史诗人物
                        26=艺术史诗人物
                         */
                        val allyType = abs(condition) - 12400
                        BattleManager.heroGameData.filter { !it.isAdventure() }
                            .filter { it.elementType == allyType || allyType == 0 }.size >= activeConditionNum[index]
                    }

                    in 12500..12599 -> {
                        // 持有poolType5【基础资源】物品的数量大于等于一定数量时，xx为物品5的子类
                        // 1=金币2=木材3=石头4=铁矿5=粮食
                        val allyType = abs(condition) - 12501 // index从0开始
                        BattleManager.elements[allyType] >= activeConditionNum[index]
                    }

                    in 12600..12699 -> {
                        // 持有poolType6【外交声望】物品的数量大于等于一定数量时，xx为物品6的子类
                        // 1=秦帝国2=罗马帝国3=古埃及帝国4=亚瑟王朝5=西班牙帝国
                        val allyType = abs(condition) - 12601 // index从0开始
                        BattleManager.reputations[allyType] >= activeConditionNum[index]
                    }

                    in 12700..12799 -> {
                        // 持有poolType7【特产】物品的数量大于等于一定数量时，xx为物品7的子类
                        val allyType = abs(condition) - 12701 // index从0开始
                        BattleManager.badges[allyType] >= activeConditionNum[index]
                    }

                    in 14100..14199 -> {
                        // 每获得指定数量的poolType1物品时，xx为物品1的子类（可反复触发）
                        val targetRace = abs(condition) - 14100
                        triggeringSkill?.isGetAllyCard(targetRace) == true
                                && RecordManager.recordData.getAllyRecord.size > 0
                                && RecordManager.recordData.getAllyRecord.filter {
                            it == targetRace || targetRace == 0
                        }.size % activeConditionNum[index] == 0
                    }

                    in 14200..14299 -> {
                        // 每获得指定数量的poolType2物品时，xx为物品2的子类（可反复触发）
                        val targetRace = abs(condition) - 14200
                        triggeringSkill?.isGetAdventureCard(targetRace) == true
                                && RecordManager.recordData.getAdventureCardRecord.size > 0
                                && RecordManager.recordData.getAdventureCardRecord.filter {
                            it == targetRace || targetRace == 0
                        }.size % activeConditionNum[index] == 0
                    }

                    in 14300..14399 -> {
                        // 每获得指定数量的poolType3物品时，xx为物品2的子类（可反复触发）
                        val targetRace = abs(condition) - 14300
                        triggeringSkill?.isGetBattleSkillCard(targetRace) == true
                                && RecordManager.recordData.getBattleSkillCardRecord.size > 0
                                && RecordManager.recordData.getBattleSkillCardRecord.filter {
                            it == targetRace || targetRace == 0
                        }.size % activeConditionNum[index] == 0
                    }

                    in 14400..14499 -> {
                        // 每获得指定数量的poolType4物品时，xx为物品2的子类（可反复触发）
                        val targetRace = abs(condition) - 14400
                        triggeringSkill?.isGetHeroCard(targetRace) == true
                                && RecordManager.recordData.getHeroCardRecord.size > 0
                                && RecordManager.recordData.getHeroCardRecord.filter {
                            it == targetRace || targetRace == 0
                        }.size % activeConditionNum[index] == 0
                    }

                    in 14500..14599 -> {
                        // 每获得指定数量的poolType5物品时，xx为物品2的子类（可反复触发）
                        val targetRace = abs(condition) - 14500
                        triggeringSkill?.isGetElement(targetRace) == true
                                && RecordManager.recordData.getAllyRecord.size > 0
                                && RecordManager.recordData.getAllyRecord.filter {
                            it == targetRace || targetRace == 0
                        }.size % activeConditionNum[index] == 0
                    }

                    in 14600..14699 -> {
                        // 每获得指定数量的poolType6物品时，xx为物品2的子类（可反复触发）
                        val targetRace = abs(condition) - 14600
                        triggeringSkill?.isGetReputation(targetRace) == true
                    }

                    in 14700..14799 -> {
                        // 每获得指定数量的poolType7物品时，xx为物品2的子类（可反复触发）
                        val targetRace = abs(condition) - 14700
                        triggeringSkill?.isGetBadge(targetRace) == true
                    }

                    in 16100..16199 -> {
                        // 每获得指定数量的poolType1物品时，xx为物品1的子类（只触发一次）
                        val targetRace = abs(condition) - 16100
                        triggeringSkill?.isGetAllyCard(targetRace) == true
                                && RecordManager.recordData.getAllyRecord.size > 0
                                && RecordManager.recordData.getAllyRecord.filter {
                            it == targetRace || targetRace == 0
                        }.size >= activeConditionNum[index]
                    }

                    in 16200..16299 -> {
                        // 每获得指定数量的poolType2物品时，xx为物品2的子类（只触发一次）
                        val targetRace = abs(condition) - 16200
                        triggeringSkill?.isGetAdventureCard(targetRace) == true
                                && RecordManager.recordData.getAdventureCardRecord.size > 0
                                && RecordManager.recordData.getAdventureCardRecord.filter {
                            it == targetRace || targetRace == 0
                        }.size >= activeConditionNum[index]
                    }

                    in 16300..16399 -> {
                        // 每获得指定数量的poolType3物品时，xx为物品2的子类（只触发一次）
                        val targetRace = abs(condition) - 16300
                        triggeringSkill?.isGetBattleSkillCard(targetRace) == true
                                && RecordManager.recordData.getBattleSkillCardRecord.size > 0
                                && RecordManager.recordData.getBattleSkillCardRecord.filter {
                            it == targetRace || targetRace == 0
                        }.size >= activeConditionNum[index]
                    }

                    in 16400..16499 -> {
                        // 每获得指定数量的poolType4物品时，xx为物品2的子类（只触发一次）
                        val targetRace = abs(condition) - 16400
                        triggeringSkill?.isGetHeroCard(targetRace) == true
                                && RecordManager.recordData.getHeroCardRecord.size > 0
                                && RecordManager.recordData.getHeroCardRecord.filter {
                            it == targetRace || targetRace == 0
                        }.size >= activeConditionNum[index]
                    }

                    in 16500..16599 -> {
                        // 每获得指定数量的poolType5物品时，xx为物品2的子类（只触发一次）
                        val targetRace = abs(condition) - 16500
                        triggeringSkill?.isGetElement(targetRace) == true
                    }

                    in 16600..16699 -> {
                        // 每获得指定数量的poolType6物品时，xx为物品2的子类（只触发一次）
                        val targetRace = abs(condition) - 16600
                        triggeringSkill?.isGetReputation(targetRace) == true
                    }

                    in 16700..16799 -> {
                        // 每获得指定数量的poolType7物品时，xx为物品2的子类（只触发一次）
                        val targetRace = abs(condition) - 16700
                        triggeringSkill?.isGetBadge(targetRace) == true
                    }


                    in 18100..18199 -> {
                        // 每消费指定数量的poolType1物品时，xx为物品1的子类（可反复触发）
                        val targetRace = abs(condition) - 18100
                        triggeringSkill?.isLoseAllyCard(targetRace) == true
                                && RecordManager.recordData.lostAllyRecord.size > 0
                                && RecordManager.recordData.lostAllyRecord.filter {
                            it == targetRace || targetRace == 0
                        }.size % activeConditionNum[index] == 0
                    }

                    in 18200..18299 -> {
                        // 每消费指定数量的poolType2物品时，xx为物品2的子类（可反复触发）
                        val targetRace = abs(condition) - 18200
                        triggeringSkill?.isLoseAdventureCard(targetRace) == true
                                && RecordManager.recordData.dropAdventureCardRecord.size > 0
                                && RecordManager.recordData.dropAdventureCardRecord.filter {
                            it == targetRace || targetRace == 0
                        }.size % activeConditionNum[index] == 0
                    }

                    in 18300..18399 -> {
                        // 每消费指定数量的poolType3物品时，xx为物品2的子类（可反复触发）
                        val targetRace = abs(condition) - 18300
                        triggeringSkill?.isLoseBattleSkillCard(targetRace) == true
                                && RecordManager.recordData.dropBattleSkillCardRecord.size > 0
                                && RecordManager.recordData.dropBattleSkillCardRecord.filter {
                            it == targetRace || targetRace == 0
                        }.size % activeConditionNum[index] == 0
                    }

                    in 18400..18499 -> {
                        // 每消费指定数量的poolType4物品时，xx为物品2的子类（可反复触发）
                        val targetRace = abs(condition) - 18400
                        triggeringSkill?.isLoseHeroCard(targetRace) == true
                                && RecordManager.recordData.loseHeroCardRecord.size > 0
                                && RecordManager.recordData.loseHeroCardRecord.filter {
                            it == targetRace || targetRace == 0
                        }.size % activeConditionNum[index] == 0
                    }

                    in 18500..18599 -> {
                        // 每消费指定数量的poolType5物品时，xx为物品2的子类（可反复触发）
                        val targetRace = abs(condition) - 18500
                        triggeringSkill?.isLoseElement(targetRace) == true
                    }

                    in 18600..18699 -> {
                        // 每消费指定数量的poolType6物品时，xx为物品2的子类（可反复触发）
                        val targetRace = abs(condition) - 18600
                        triggeringSkill?.isLoseReputation(targetRace) == true
                    }

                    in 18700..18799 -> {
                        // 每消费指定数量的poolType7物品时，xx为物品2的子类（可反复触发）
                        val targetRace = abs(condition) - 18700
                        triggeringSkill?.isLoseBadge(targetRace) == true
                    }

                    in 18900..18999 -> {
                        // 每消费指定数量的poolType9物品时，xx为物品2的子类（可反复触发） 这里是国家属性，7是人口，只用到了7
                        val targetRace = abs(condition) - 18900
                        if (targetRace == 7) {
                            triggeringSkill?.isLosePopulationMoreThan(activeConditionNum[index]) == true
                        } else false
                    }

                    in 20100..20199 -> {
                        // 每获得指定数量的poolType1物品时，xx为物品1的子类（只触发一次）
                        val targetRace = abs(condition) - 20100
                        triggeringSkill?.isLoseAllyCard(targetRace) == true
                                && RecordManager.recordData.lostAllyRecord.size > 0
                                && RecordManager.recordData.lostAllyRecord.filter {
                            it == targetRace || targetRace == 0
                        }.size >= activeConditionNum[index]
                    }

                    in 20200..20299 -> {
                        // 每获得指定数量的poolType2物品时，xx为物品2的子类（只触发一次）
                        val targetRace = abs(condition) - 20200
                        triggeringSkill?.isLoseAdventureCard(targetRace) == true
                                && RecordManager.recordData.dropAdventureCardRecord.size > 0
                                && RecordManager.recordData.dropAdventureCardRecord.filter {
                            it == targetRace
                        }.size >= activeConditionNum[index]
                    }

                    in 20300..20399 -> {
                        // 每获得指定数量的poolType3物品时，xx为物品2的子类（只触发一次）
                        val targetRace = abs(condition) - 20300
                        triggeringSkill?.isLoseBattleSkillCard(targetRace) == true
                                && RecordManager.recordData.dropBattleSkillCardRecord.size > 0
                                && RecordManager.recordData.dropBattleSkillCardRecord.filter {
                            it == targetRace || targetRace == 0
                        }.size >= activeConditionNum[index]
                    }

                    in 20400..20499 -> {
                        // 每获得指定数量的poolType4物品时，xx为物品2的子类（只触发一次）
                        val targetRace = abs(condition) - 20400
                        triggeringSkill?.isLoseHeroCard(targetRace) == true
                                && RecordManager.recordData.loseHeroCardRecord.size > 0
                                && RecordManager.recordData.loseHeroCardRecord.filter {
                            it == targetRace || targetRace == 0
                        }.size >= activeConditionNum[index]
                    }

                    in 20500..20599 -> {
                        // 每获得指定数量的poolType5物品时，xx为物品2的子类（只触发一次）
                        val targetRace = abs(condition) - 20500
                        triggeringSkill?.isLoseElement(targetRace) == true
                    }

                    in 20600..20699 -> {
                        // 每获得指定数量的poolType6物品时，xx为物品2的子类（只触发一次）
                        val targetRace = abs(condition) - 20600
                        triggeringSkill?.isLoseReputation(targetRace) == true
                    }

                    in 20700..20799 -> {
                        // 每获得指定数量的poolType7物品时，xx为物品2的子类（只触发一次）
                        val targetRace = abs(condition) - 20700
                        triggeringSkill?.isLoseBadge(targetRace) == true
                    }

                    in 20900..20999 -> {
                        // 每获得指定数量的poolType9物品时，xx为物品2的子类（只触发一次）这里是国家属性，7是人口，只用到了7 累计失去人口
                        val targetRace = abs(condition) - 20900
                        if (targetRace == 7) {
                            triggeringSkill?.isLosePopulationMoreThan(activeConditionNum[index]) == true
                        } else false
                    }

                    23001 -> {
                        // 科学大于等于x点时（参数位置填写值，负数表示小于）
                        if (activeConditionNum[index] < 0) {
                            BattleManager.adventureProps.value.science < abs(activeConditionNum[index])
                        } else {
                            BattleManager.adventureProps.value.science >= activeConditionNum[index]
                        }
                    }

                    23002 -> {
                        // 政治大于等于x点时（参数位置填写值，负数表示小于）
                        if (activeConditionNum[index] < 0) {
                            BattleManager.adventureProps.value.politics < abs(activeConditionNum[index])
                        } else {
                            BattleManager.adventureProps.value.politics >= activeConditionNum[index]
                        }
                    }

                    23003 -> {
                        // 军事大于等于x点时（参数位置填写值，负数表示小于）
                        if (activeConditionNum[index] < 0) {
                            BattleManager.adventureProps.value.military < abs(activeConditionNum[index])
                        } else {
                            BattleManager.adventureProps.value.military >= activeConditionNum[index]
                        }
                    }

                    23004 -> {
                        // 宗教大于等于x点时（参数位置填写值，负数表示小于）
                        if (activeConditionNum[index] < 0) {
                            BattleManager.adventureProps.value.religion < abs(activeConditionNum[index])
                        } else {
                            BattleManager.adventureProps.value.religion >= activeConditionNum[index]
                        }
                    }

                    23005 -> {
                        // 经济大于等于x点时（参数位置填写值，负数表示小于）
                        if (activeConditionNum[index] < 0) {
                            BattleManager.adventureProps.value.commerce < abs(activeConditionNum[index])
                        } else {
                            BattleManager.adventureProps.value.commerce >= activeConditionNum[index]
                        }
                    }

                    23006 -> {
                        // 民心大于等于x点时（参数位置填写值，负数表示小于）
                        if (activeConditionNum[index] < 0) {
                            BattleManager.adventureProps.value.art < abs(activeConditionNum[index])
                        } else {
                            BattleManager.adventureProps.value.art >= activeConditionNum[index]
                        }
                    }

                    23007 -> {
                        // 人口大于等于x点时（参数位置填写值，负数表示小于）
                        if (activeConditionNum[index] < 0) {
                            BattleManager.adventureProps.value.population < abs(activeConditionNum[index])
                        } else {
                            BattleManager.adventureProps.value.population >= activeConditionNum[index]
                        }
                    }

                    in 24000..24099 -> {
                        // 指定的声望达到x级时（参数位置填写声望类型枚举，0表示任意声望）
                        val targetLevel = abs(condition) - 24000
                        (if (activeConditionNum[index] == 0) {
                            BattleManager.toReputationLevels().any { it >= targetLevel }
                        } else {
                            val whichReputation = activeConditionNum[index]
                            BattleManager.toReputationLevels()[whichReputation - 1] >= targetLevel
                        })
                    }

                    24100 -> {
                        // 指定声望的等级提高时
                        triggeringSkill?.isReputationLevelUp(activeConditionNum[index]) == true
                    }

                    24200 -> {
                        // 指定声望的等级降低时
                        triggeringSkill?.isReputationLevelDown(activeConditionNum[index]) == true
                    }

                    24300 -> {
                        // 获得指定声望的声望值时（无论+还是-）
                        triggeringSkill?.isReputationUp(activeConditionNum[index]) == true
                                || triggeringSkill?.isReputationDown(activeConditionNum[index]) == true
                    }

                    25000 -> {
                        // 每击败x个敌人时（可反复触发）
                        triggeringSkill?.isDefeatEnemy(0) == true
                                && RecordManager.recordData.defeatEnemyRecord.size > 0
                                && RecordManager.recordData.defeatEnemyRecord.size % activeConditionNum[index] == 0
                    }

                    in 25001..25005 -> {
                        // 每击败x个人类/恶魔/亡灵/野兽敌人时（可反复触发）
                        val targetRace = abs(condition) - 25000
                        triggeringSkill?.isDefeatEnemy(targetRace) == true
                                && RecordManager.recordData.defeatEnemyRecord.size > 0
                                && RecordManager.recordData.defeatEnemyRecord.filter {
                            it == targetRace
                        }.size % activeConditionNum[index] == 0
                    }

                    25100 -> {
                        // 累计击败x个敌人时（只触发一次）
                        triggeringSkill?.isDefeatEnemy(0) == true
                                && RecordManager.recordData.defeatEnemyRecord.size > 0
                                && RecordManager.recordData.defeatEnemyRecord.size >= activeConditionNum[index]
                    }

                    in 25101..25105 -> {
                        // 累计击败x个人类/恶魔/亡灵/野兽敌人时（只触发一次）
                        val targetRace = abs(condition) - 25100
                        triggeringSkill?.isDefeatEnemy(targetRace) == true
                                && RecordManager.recordData.defeatEnemyRecord.size > 0
                                && RecordManager.recordData.defeatEnemyRecord.filter {
                            it == targetRace
                        }.size >= activeConditionNum[index]
                    }

                    26000 -> {
                        // 每失去x个盟友卡时（可反复触发）
                        triggeringSkill?.isLoseAllyCard(0) == true
                                && RecordManager.recordData.lostAllyRecord.size > 0
                                && RecordManager.recordData.lostAllyRecord.size % activeConditionNum[index] == 0
                    }

                    in 26001..26005 -> {
                        // 每失去x个人类/恶魔/亡灵/野兽盟友卡时（可反复触发）
                        val targetRace = abs(condition) - 26000
                        triggeringSkill?.isLoseAllyCard(targetRace) == true
                                && RecordManager.recordData.lostAllyRecord.size > 0
                                && RecordManager.recordData.lostAllyRecord.filter {
                            it == targetRace
                        }.size % activeConditionNum[index] == 0
                    }

                    26100 -> {
                        // 累计失去x个盟友卡时（只触发一次）
                        triggeringSkill?.isLoseAllyCard(0) == true
                                && RecordManager.recordData.lostAllyRecord.size > 0
                                && RecordManager.recordData.lostAllyRecord.size >= activeConditionNum[index]
                    }

                    in 26101..26105 -> {
                        // 累计失去x个人类/恶魔/亡灵/野兽盟友卡时（只触发一次）
                        val targetRace = abs(condition) - 26100
                        triggeringSkill?.isLoseAllyCard(targetRace) == true
                                && RecordManager.recordData.lostAllyRecord.size > 0
                                && RecordManager.recordData.lostAllyRecord.filter {
                            it == targetRace
                        }.size >= activeConditionNum[index]
                    }

                    in 27000..27006 -> {
                        val type = abs(condition) - 27000
                        triggeringSkill?.isDealFate(type) == true
                                && RecordManager.recordData.specialDecisionRecord.any { it == type }
                                && RecordManager.recordData.specialDecisionRecord.filter { it == type }.size % activeConditionNum[index] == 0
                    }

                    in 27100..27106 -> {
                        val targetRace = abs(condition) - 27100
                        triggeringSkill?.isDealFate(targetRace) == true
                                && RecordManager.recordData.specialDecisionRecord.size > 0
                                && RecordManager.recordData.specialDecisionRecord.size >= activeConditionNum[index]
                    }

                    in 30200..30299 -> {
                        // 若玩家的国家头衔处于1-5时
                        // 部落-城邦-王国-联邦-帝国
                        val targetRace = abs(condition) - 30200
                        BattleManager.adventureProps.value.getPopulationLevel().level == targetRace
                    }

                    else -> {
                        if (condition != 0) {
                            "请确认是否配置错误，技能triggerType不存在${condition}".toast()
                        }
                        true
                    }
                }
                // 支持配置condition为负数，负数则表示取反
                conditionTrigger.add(if (condition >= 0) conditionResult else !conditionResult)
            }
            // 这里是支持条件逻辑，与或者非
            return if (conditionLogic == 1) conditionTrigger.all { it }
            else conditionTrigger.any { it }
        }
    }
}