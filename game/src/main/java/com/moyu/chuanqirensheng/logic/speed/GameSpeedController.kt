package com.moyu.chuanqirensheng.logic.speed

import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.core.model.speed.GameSpeed

/**
 * 当前内置的游戏速度
 */
val gameSpeeds = listOf(
    Speed(0, 1f, GameApp.instance.getWrapString(R.string.pause), "battle_icon_pause", true),
    Speed(1, 1f, GameApp.instance.getWrapString(R.string.normal_speed), "battle_icon_continue"),
    Speed(2, 0.5f, GameApp.instance.getWrapString(R.string.double_speed), "battle_icon_x2"),
    Speed(3, 0.35f, GameApp.instance.getWrapString(R.string.triple_speed), "battle_icon_x3"),
    Speed(4, 0.2f, GameApp.instance.getWrapString(R.string.x5_speed), "battle_icon_x5"),
    Speed(5, 0.1f, GameApp.instance.getWrapString(R.string.x10_speed), "battle_icon_x10"),
)

/**
 * 游戏速度控制接口，任意动画都会从这里获取参数
 */
interface GameSpeedController: GameSpeed {
    fun getCurrentSpeed(): Speed
    fun nextSpeed()
    fun setSpeed(speed: Int)
    fun getSpeeds(): List<Speed>
    fun isStop(): Boolean
}