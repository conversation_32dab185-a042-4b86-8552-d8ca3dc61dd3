package com.moyu.chuanqirensheng.logic.skill

import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.datastore.KEY_TEXT_FIXED_COLOR
import com.moyu.chuanqirensheng.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.logic.hero.getCardQualityFrame
import com.moyu.chuanqirensheng.logic.hero.getQualityFrame
import com.moyu.chuanqirensheng.logic.hero.getQualityName
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.text.getHeroElementName
import com.moyu.chuanqirensheng.text.getSkillElementName
import com.moyu.chuanqirensheng.ui.theme.DarkGreen
import com.moyu.chuanqirensheng.ui.theme.getTextColor
import com.moyu.core.logic.info.getElementTypeName
import com.moyu.core.logic.skill.getRealDesc
import com.moyu.core.model.damage.DamageType
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.isAdventure
import com.moyu.core.model.skill.isHeroSkill
import com.moyu.core.model.skill.quality

const val SKILL_TYPE = 8
const val SKILL_ELEMENT = 6
const val SKILL_LABEL = 8

fun Skill.getTouchInfo(): String {
    return if (isAdventure()) elementType.getSkillElementName() + name else "$level" + GameApp.instance.getWrapString(R.string.star) + quality().getQualityName().take(2) + name + "，" + elementType.getElementTypeName() + "，" + GameApp.instance.getWrapString(
        R.string.number
    ) + num
}

fun Skill.getHeroTouchInfo(): String {
    return "$level" + GameApp.instance.getWrapString(R.string.star) + quality().getQualityName().take(2) + name + "，" + elementType.getHeroElementName() + "，" + GameApp.instance.getWrapString(
        R.string.number
    ) + num
}

fun Skill.canStarUp(): Boolean {
    return !peek && !repo.inGame.value && repo.gameCore.getSkillPool()
        .filter { it.mainId == mainId }.size > 1
}

fun Skill.getNextLevelSkill(): Skill? {
    val skillPool = repo.gameCore.getSkillPool()
    return skillPool.firstOrNull { it.mainId == this.mainId && it.level == this.level + 1 }
}

fun Skill.getCardFrameDrawable(): Int {
    return quality().getCardQualityFrame()
}

fun Skill.getFrameDrawable(): Int {
    return if (isHeroSkill()) {
        repo.gameCore.getHeroPool().firstOrNull { it.id == id }?.quality?.getQualityFrame()
            ?: (2.getQualityFrame())
    } else {
        repo.gameCore.getScrollPool().firstOrNull { it.id == id }?.quality?.getQualityFrame()
            ?: (2.getQualityFrame())
    }
}


fun Skill.empire(): String {
    return if (isHeroSkill()) {
        repo.gameCore.getHeroPool().firstOrNull { it.id == id }?.empire ?: "0"
    } else {
        repo.gameCore.getScrollPool().firstOrNull { it.id == id }?.empire ?: "0"
    }
}

fun Skill.story(): String {
    return if (isHeroSkill()) {
        repo.gameCore.getHeroPool().firstOrNull { it.id == id }?.story ?: "0"
    } else {
        repo.gameCore.getScrollPool().firstOrNull { it.id == id }?.story ?: "0"
    }
}

private val damageDisplayList = DamageType.values().map { it.display }

// 正则表达式 匹配中括号、【】括号 以及 damageTypeDisplayList
private val regexStr =
    """(?<=\[)(.+?)(?=])|(?<=【)(.+?)(?=】)|""" + damageDisplayList.joinToString("|")

@Composable
fun Skill.getRealDescColorful(
    spanStyle: SpanStyle = MaterialTheme.typography.h4.toSpanStyle(),
): AnnotatedString {
    return getRealDesc().toSkillAnnotatedString(spanStyle = spanStyle)
}


@Composable
fun String.toSkillAnnotatedString(
    spanStyle: SpanStyle = MaterialTheme.typography.h4.toSpanStyle(),
): AnnotatedString {
    if (getBooleanFlowByKey(KEY_TEXT_FIXED_COLOR)) {
        return buildAnnotatedString { append(this@toSkillAnnotatedString) }
    }
    return buildAnnotatedString {
        append(this@toSkillAnnotatedString)
        Regex(regexStr).findAll(this@toSkillAnnotatedString).forEach { matchResult: MatchResult ->
            if (damageDisplayList.any { damageDisplay -> damageDisplay == matchResult.value }) {
                addStyle(
                    style = spanStyle.copy(
                        color = DamageType.fromDisplayValue(matchResult.value)?.getTextColor()
                            ?: Color.White
                    ),
                    start = matchResult.range.first,
                    end = matchResult.range.last + 1
                )
            } else {
                addStyle(
                    style = spanStyle.copy(DarkGreen),
                    start = matchResult.range.first,
                    end = matchResult.range.last + 1
                )
            }
        }
    }
}