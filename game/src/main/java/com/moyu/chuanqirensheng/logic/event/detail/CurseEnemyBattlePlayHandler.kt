package com.moyu.chuanqirensheng.logic.event.detail

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.logic.event.PlayHandler
import com.moyu.chuanqirensheng.logic.event.createEnemyRole
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.event.BattleLayout
import com.moyu.chuanqirensheng.screen.skill.SingleSkillView
import com.moyu.chuanqirensheng.ui.theme.gapSmallPlus
import com.moyu.core.model.event.Event
import com.moyu.core.model.skill.Skill
import com.moyu.core.util.RANDOM

/**
 * 诅咒战斗
 */
class CurseEnemyBattlePlayHandler(
    override val skipWin: Boolean = false,
    override val dialogJump: Boolean = false,
    override val playId: Int = 24
) : PlayHandler() {

    private val halo = mutableStateOf<Skill?>(null)

    @Composable
    override fun Layout(event: Event) {
        Box(modifier = Modifier.fillMaxSize()) {
            BattleLayout(event = event)
            halo.value?.let {
                Row(
                    Modifier
                        .align(Alignment.Center)
                        .graphicsLayer {
                            translationY = gapSmallPlus.toPx()
                        }, verticalAlignment = Alignment.CenterVertically) {
                    Text(
                        text = stringResource(R.string.curse_scene) + "：",
                        style = MaterialTheme.typography.h3
                    )
                    SingleSkillView(skill = it, textColor = Color.White)
                }
            }
        }
    }

    override fun onEventSelect(event: Event) {
        // 决斗，普通战斗,宿敌战斗,诅咒战斗
        val pool = repo.gameCore.getPoolById(event.playPara1.first())
        val enemies = pool.pool.map {
            repo.gameCore.getRaceById(it)
        }.map {
            createEnemyRole(it, event)
        }.toMutableList()
        repo.setCurrentEnemies(enemies)

        // 天气
        val haloPool = repo.gameCore.getPoolById(event.playPara2.first().toInt())
        halo.value = repo.gameCore.getSkillById(haloPool.pool.shuffled(RANDOM).first()).apply {
            enemies[0].learnSkill(this, enemies[0])
        }
    }
}