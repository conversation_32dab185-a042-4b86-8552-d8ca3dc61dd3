package com.moyu.chuanqirensheng.logic.event

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.award.toConditionAward
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.dialog.AwardList
import com.moyu.chuanqirensheng.screen.dialog.defaultParam
import com.moyu.core.model.event.Event
import com.moyu.core.model.sell.Award

@Composable
fun EventConditionLayoutAward(
    modifier: Modifier = Modifier,
    award: Award,
    itemSize: ItemSize,
    textColor: Color = Color.White
) {
    AwardList(
        modifier = modifier,
        award = award,
        param = defaultParam.copy(
            showName = false,
            itemSize = itemSize,
            checkAffordable = true,
            showColumn = false,
            frameDrawable = null,
            showReputationLevel = true,
            maxLine = 1,
            minLine = 1,
            numInFrame = false,
            textColor = textColor,
        ),
    )
}

@Composable
fun EventConditionLayout(
    modifier: Modifier = Modifier,
    event: Event,
    itemSize: ItemSize,
    textColor: Color = Color.White
) {
    AwardList(
        modifier = modifier,
        award = event.toConditionAward(),
        param = defaultParam.copy(
            showName = false,
            itemSize = itemSize,
            checkAffordable = true,
            showColumn = false,
            frameDrawable = null,
            maxLine = 1,
            minLine = 1,
            numInFrame = false,
            showReputationLevel = true,
            textColor = textColor,
        ),
    )
}

fun triggerEvent(event: Event, consume: Boolean = true): Boolean {
    if (event.condition == 0 || DebugManager.easyEvent) return true
    val award = event.toConditionAward()
    return AwardManager.isAffordable(award, consume)
}