package com.moyu.chuanqirensheng.logic.story

import androidx.compose.runtime.mutableStateListOf
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.cloud.MAX_AGE
import com.moyu.chuanqirensheng.datastore.KEY_ENDINGS
import com.moyu.chuanqirensheng.datastore.KEY_STORY_SELECTION
import com.moyu.chuanqirensheng.datastore.getListObject
import com.moyu.chuanqirensheng.datastore.setListObject
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.ageToYear
import com.moyu.chuanqirensheng.logic.event.isStartingEvent
import com.moyu.chuanqirensheng.logic.event.isStartingEvent1
import com.moyu.chuanqirensheng.logic.event.isStartingEvent2
import com.moyu.chuanqirensheng.logic.event.isStartingEvent3
import com.moyu.chuanqirensheng.logic.event.isWorldEvent
import com.moyu.chuanqirensheng.logic.record.RecordManager
import com.moyu.chuanqirensheng.logic.record.SaveManager
import com.moyu.chuanqirensheng.logic.unlock.UnlockManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.sub.report.ReportManager
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.chuanqirensheng.util.scoreRanks
import com.moyu.core.model.event.Event
import com.moyu.core.model.property.AdventureProps
import com.moyu.core.model.role.Role
import com.moyu.core.model.story.Story
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

fun Int.toReputationName(): String? {
    return repo.gameCore.getStoryPool().firstOrNull { it.id == this }?.name
}

const val COMMON_STORY = 1000

object StoryManager {
    val stories = mutableStateListOf<Story>()
    val endings = mutableStateListOf<Ending>()

    fun init() {
        // 初始化
        getListObject<Story>(KEY_STORY_SELECTION).let { taskList ->
            val tasks = taskList.map { task ->
                repo.gameCore.getStoryPool().firstOrNull { it.id == task.id }
                    ?.copy(selected = task.selected)
            }.filterNotNull()
            stories.addAll(tasks)
        }
        // 首次直接全部加入
        if (stories.isEmpty()) {
            stories.addAll(repo.gameCore.getStoryPool())
        }
        // 精确检测是否有缺失的故事包
        repo.gameCore.getStoryPool().forEach { story->
            if (stories.none { it.id == story.id }) {
                stories.add(story)
            }
        }
        // 如果没有选中任何故事包，选中前3个
        if (stories.none { it.selected }) {
            stories[0] = stories[0].switchSelection()
            stories[1] = stories[1].switchSelection()
            stories[2] = stories[2].switchSelection()
        }
        endings.addAll(
            getListObject(KEY_ENDINGS)
        )
    }

    fun saveStory(
        role: Role,
        usedEvents: List<Event>,
        succeededEvents: List<Event>,
    ): Ending? {
        if (usedEvents.none { !it.isStartingEvent() }) {
            return null
        }
        endings.removeAll { it.uuid == role.extraInfo.allyUuid }
        val maxReputationPair = BattleManager.reputations.mapIndexed { index, i ->
            Pair(
                index, i
            )
        }.maxByOrNull { it.second }!!
        val lastEvent = usedEvents.last()
        val startingEvent1 = succeededEvents.first { it.isStartingEvent1() }
        val startingEvent2 = succeededEvents.first { it.isStartingEvent2() }
        val startingEvent3 = succeededEvents.first { it.isStartingEvent3() }
        val endingRank = scoreRanks(
            BattleManager.adventureProps.value.age,
            GameApp.instance.loginData.value.top50Ages.take(10)
        )
        val scoreText = BattleManager.countryName.value + "，" +
                GameApp.instance.getWrapString(R.string.lasted) + BattleManager.adventureProps.value.age + GameApp.instance.getWrapString(
            R.string.age
        ) + "，" + GameApp.instance.getWrapString(R.string.score_rank) + endingRank + "%" + GameApp.instance.getWrapString(
            R.string.score_rank2
        )
        val startingText =
            GameApp.instance.getWrapString(R.string.year_zero) + "，${GameApp.instance.getUserName()}" + GameApp.instance.getWrapString(
                R.string.build
            ) + "【${BattleManager.countryName.value}】，${startingEvent1.storyDesc1}，${startingEvent2.storyDesc1}，${startingEvent3.storyDesc1}。\n\n"
        val realEvents = usedEvents.filter { !it.isStartingEvent() && !it.isWorldEvent() }
        return Ending(uuid = role.extraInfo.allyUuid,
            ending = lastEvent.endTitle,
            countryName = BattleManager.countryName.value,
            location = startingEvent2.name,
            religion = startingEvent3.name,
            skinId = 0,//SkinManager.currentSkin.value.id,
            dieReason = if (BattleManager.adventureProps.value.age >= MAX_AGE) GameApp.instance.getWrapString(R.string.all_win) else GameApp.instance.getWrapString(R.string.die_good),
            rank = endingRank,
            age = BattleManager.adventureProps.value.age,
            kill = RecordManager.recordData.defeatEnemyRecord.size,
            level = role.getLevel(),
            countryLevelUpAges = BattleManager.countryLevelUpAges,
            death = RecordManager.recordData.lostAllyRecord.size,
            maxReputationType = maxReputationPair.first,
            maxReputationValue = maxReputationPair.second,
            justice = BattleManager.adventureProps.value.art,
            endingText = scoreText + "\n\n" + startingText + usedEvents.filter { !it.isStartingEvent() }
                .map { event ->
                    val age = if (event.isWorldEvent()) 0 else realEvents.indexOf(event) + 1
                    val time =
                        GameApp.instance.getWrapString(R.string.the) + age.ageToYear() + GameApp.instance.getWrapString(
                            com.moyu.core.R.string.year
                        ) + "，"
                    val countryLevel = BattleManager.countryLevelUpAges.indexOfFirst { it == age }
                    val countryLevelUpText = if (countryLevel > 0) {
                        "\n" + GameApp.instance.getWrapString(R.string.your_country_became) + "【${
                            AdventureProps.getPopulationLevelData(
                                countryLevel
                            ).name
                        }】"
                    } else {
                        ""
                    }
                    val eventText = if (succeededEvents.any { it.id == event.id }) {
                        event.storyDesc1.takeIf { it != "0" } ?: ""
                    } else {
                        event.storyDesc2.takeIf { it != "0" } ?: ""
                    }

                    (if (event.isWorldEvent()) "" else time) + eventText + countryLevelUpText
                }.reduce { acc, s -> acc + "\n" + s }).apply {
            if (endings.size >= 199) {
                GameApp.instance.getWrapString(R.string.story_records_max_tips).toast()
            } else {
                endings.add(this)
                setListObject(KEY_ENDINGS, endings)
            }
        }
    }

    fun delete(ending: Ending) {
        endings.removeAll { it.uuid == ending.uuid }
        setListObject(KEY_ENDINGS, endings)
    }

    fun switchSelection(story: Story) {
        GameApp.globalScope.launch(Dispatchers.Main) {
            if (SaveManager.haveSaver()) {
                GameApp.instance.getWrapString(R.string.finish_game_tips).toast()
            } else {
                if (story.selected) {
                    if (stories.filter { it.selected }.size <= 3) {
                        GameApp.instance.getWrapString(R.string.story_at_least_one).toast()
                        return@launch
                    }
                    GameApp.instance.getWrapString(R.string.story_blocked).toast()
                    ReportManager.onStoryPack(story.id, 0)
                } else {
                    GameApp.instance.getWrapString(R.string.story_added).toast()
                    ReportManager.onStoryPack(story.id, 1)
                }
                stories.indexOfFirst { it.id == story.id }.takeIf { it != -1 }?.let { index ->
                    stories[index] = stories[index].switchSelection()
                    setListObject(KEY_STORY_SELECTION, stories)
                }
            }
        }
    }

    fun isCurrentDifficultMode(): Boolean {
        return false
    }

    fun eventInStoryBag(event: Event): Boolean {
        return event.storyBag == COMMON_STORY || event.storyBag in getEnabledStories().map { story -> story.unlockId }
    }

    fun getImageByIndex(index: Int): Int {
        return getImageResourceDrawable(stories[index].pic)
    }

    fun getEnabledStories(): List<Story> {
        return stories.filter { it.selected }
    }

    fun getUnlockedStoryIds(): List<Int> {
        return stories.filter {
            val lock = repo.gameCore.getUnlockById(it.unlockId)
            UnlockManager.getUnlockedFlow(lock)
        }.map { it.unlockId }
    }
}