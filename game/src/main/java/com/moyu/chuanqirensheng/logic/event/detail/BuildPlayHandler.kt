package com.moyu.chuanqirensheng.logic.event.detail

import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.logic.event.PlayHandler
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.core.model.event.Event
import com.moyu.core.model.sell.Award
import com.moyu.core.model.sell.toAward

const val BUILD_PLAY = 3
class BuildPlayHandler(
    override val skipWin: Boolean = true,
    override val dialogJump: Boolean = true,
    override val playId: Int = BUILD_PLAY
) : PlayHandler() {

    @Composable
    override fun Layout(event: Event) {
        BasicEventContentWithBackground(event)
    }

    override fun onEventSelect(event: Event) {
        eventFinished.value = false
        eventResult.value = true
    }
}