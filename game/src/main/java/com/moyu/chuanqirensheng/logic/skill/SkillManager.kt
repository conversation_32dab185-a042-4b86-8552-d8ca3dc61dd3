package com.moyu.chuanqirensheng.logic.skill

import androidx.compose.runtime.mutableStateListOf
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.datastore.KEY_SCROLL_SKILLS
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.basic.BasicItemHolder
import com.moyu.chuanqirensheng.logic.basic.ItemHolder
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.effect.dialogEffectState
import com.moyu.chuanqirensheng.screen.effect.restartEffect
import com.moyu.chuanqirensheng.screen.effect.starUpEffect
import com.moyu.core.GameCore
import com.moyu.core.model.skill.Skill
import com.moyu.core.music.SoundEffect
import kotlin.math.max

class SkillManager(
    private val holder: ItemHolder<Skill> = BasicItemHolder(
        elementSerializer = Skill.serializer(),
        saveKey = KEY_SCROLL_SKILLS,
        data = mutableStateListOf(),
        sameGroup = { item1, item2 -> item1.mainId == item2.mainId },
        increase = { current, add ->
            current.copy(num = current.num + add.num, new = add.new).copy(level = max(current.level, add.level), id = if (add.level > current.level) add.id else current.id)
        },
    )
) : ItemHolder<Skill> by holder {

    fun selectToGame(target: Skill) {
        GameCore.instance.onBattleEffect(SoundEffect.EquipItem)
        data.indexOfFirst { it.id == target.id }.takeIf { it != -1 }?.let {
            data[it] = data[it].switchSelect()
            save()
        }
    }

    fun update(target: Skill) {
        data.indexOfFirst { it.uuid == target.uuid }.takeIf { it != -1 }?.let {
            data[it] = target
            save()
        }
    }

    // 升星逻辑改了，有基底概念，所以要-1
    fun upgrade(skill: Skill): Skill {
        val scroll = repo.gameCore.getScrollById(skill.id)
        if (AwardManager.diamond.value < scroll.starUpResourceNum) {
            GiftManager.onDiamondNotEnough()
            GameApp.instance.getWrapString(R.string.diamond_not_enough).toast()
        } else if (scroll.starUpNum == 0) {
            GameApp.instance.getWrapString(R.string.already_max_star_tips).toast()
        } else if (scroll.starUpNum > (skill.num - 1)) {
            GameApp.instance.getWrapString(R.string.card_not_enough).toast()
        } else if (scroll.star >= scroll.starLimit) {
            GameApp.instance.getWrapString(R.string.already_max_star_tips).toast()
        } else {
            GameCore.instance.onBattleEffect(SoundEffect.UpgradeItem)
            restartEffect(dialogEffectState, starUpEffect)
            AwardManager.gainDiamond(-scroll.starUpResourceNum)
            data.indexOfFirst { it.id == skill.id }.takeIf { it != -1 }?.let {
                data[it] = data[it].copy(num = data[it].num - scroll.starUpNum).starUp()
                save()
                return data[it]
            }
        }
        return skill
    }
}