package com.moyu.chuanqirensheng.logic.event

import androidx.compose.runtime.mutableStateListOf
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.feature.stage.StageManager
import com.moyu.chuanqirensheng.logic.story.StoryManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.core.model.event.Event
import com.moyu.core.util.RANDOM

class StartEventHelper {

    //    private val starting1Events = mutableStateListOf<Event>()
    private val starting2Events = mutableStateListOf<Event>()
    private val starting3Events = mutableStateListOf<Event>()

    fun getEventTitle(): String? {
        return if (starting2Events.isNotEmpty()) GameApp.instance.getWrapString(R.string.choose_personality)
        else if (starting3Events.isNotEmpty()) GameApp.instance.getWrapString(R.string.choose_destiny)
        else null
//        if (starting1Events.isNotEmpty()) GameApp.instance.getWrapString(R.string.choose_life) else

    }

    fun clear() {
//        starting1Events.clear()
        starting2Events.clear()
        starting3Events.clear()
    }

    fun init() {
        if (DebugManager.allEvent) {
//            starting1Events.addAll(repo.gameCore.getEventPool().filter { it.isStartingEvent1() })
            starting2Events.addAll(repo.gameCore.getEventPool().filter { it.isStartingEvent2() })
            starting3Events.addAll(repo.gameCore.getEventPool().filter { it.isStartingEvent3() })
        } else {
//            starting1Events.addAll(getStartingEvents { it.isStartingEvent1() })
            starting2Events.addAll(getStartingEvents { it.isStartingEvent2() })
            starting3Events.addAll(getStartingEvents { it.isStartingEvent3() })
        }
    }

    fun getNextEvents(): List<Event>? {
        return if (starting2Events.isNotEmpty()) {
            return starting2Events
        } else if (starting3Events.isNotEmpty()) {
            return starting3Events
        } else {
            null
        }
//        if (starting1Events.isNotEmpty()) {
//            return starting1Events
//        } else

    }

    fun doSelectEvent(event: Event): Boolean {
//        if (event.isStartingEvent1()) {
//            if (starting1Events.isEmpty()) return true
//            starting1Events.clear()
//        } else
        if (event.isStartingEvent2()) {
            if (starting2Events.isEmpty()) return true
            starting2Events.clear()
        } else if (event.isStartingEvent3()) {
            if (starting3Events.isEmpty()) return true
            starting3Events.clear()
        }
        return false
    }

    fun isStartEnded(): Boolean {
//        starting1Events.isEmpty() &&
        return starting2Events.isEmpty() && starting3Events.isEmpty()
    }

    fun getStartingEvents(predicate: (Event) -> Boolean): Collection<Event> {
        return repo.gameCore.getEventPool().filter(predicate)
            .filter {
                it.storyBag == 0 || it.storyBag == 1000 || StoryManager.eventInStoryBag(it)
            }
            .filter { if (repo.gameMode.value.isStage()) it.levelId == StageManager.currentStage.value.id else it.levelId == 0 }
            .shuffled(RANDOM).take(EVENT_SIZE)
    }
}