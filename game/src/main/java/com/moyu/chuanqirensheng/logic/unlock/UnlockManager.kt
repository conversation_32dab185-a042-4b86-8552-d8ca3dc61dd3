package com.moyu.chuanqirensheng.logic.unlock

import com.moyu.chuanqirensheng.datastore.KEY_DIAMOND_NOT_ENOUGH
import com.moyu.chuanqirensheng.datastore.KEY_DIED_IN_GAME
import com.moyu.chuanqirensheng.datastore.KEY_DIED_IN_PVP
import com.moyu.chuanqirensheng.datastore.KEY_GAME_TASK_PROGRESS
import com.moyu.chuanqirensheng.datastore.KEY_GIFT_AWARDED
import com.moyu.chuanqirensheng.datastore.KEY_KEY_NOT_ENOUGH
import com.moyu.chuanqirensheng.datastore.KEY_UNLOCK_EVIDENCE
import com.moyu.chuanqirensheng.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.datastore.setBooleanValueByKeySync
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.feature.battlepass.KEY_WAR_PASS2_UNLOCK_EVIDENCE
import com.moyu.chuanqirensheng.feature.battlepass.KEY_WAR_PASS_NUM
import com.moyu.chuanqirensheng.feature.battlepass.KEY_WAR_PASS_UNLOCK_EVIDENCE
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.award.VipManager
import com.moyu.chuanqirensheng.logic.talent.TalentManager
import com.moyu.chuanqirensheng.logic.task.FOREVER
import com.moyu.chuanqirensheng.logic.task.TaskEvent
import com.moyu.chuanqirensheng.logic.task.getLoginDays
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.core.model.unlock.Unlock

const val JUDGE_UNLOCK_ID = 100001

const val INIT_ALLY_UNLOCK_ID = 101
const val INIT_SKILL_UNLOCK_ID = 201
const val INIT_HERO_UNLOCK_ID = 301
const val UNLOCK_SEVEN_DAY = 17

const val MENU_ALLY = 2
const val MENU_HISTORY = 3
const val MENU_TASK = 5
const val MENU_SELL = 6
const val MENU_MORE = 7

const val UNLOCK_SIGN = 8
const val UNLOCK_WAR_PASS1 = 9
const val UNLOCK_WAR_PASS2 = 16
const val UNLOCK_LOTTERY = 18

const val UNLOCK_PVP = 13
const val UNLOCK_DRAW = 19
const val UNLOCK_HOLIDAY = 20
const val UNLOCK_TOWER = 22

/**
1=天赋
2=登录天数
3=局内玩家至少达到多少岁
4=兑换码解锁
5=天赋解锁
6=商店解锁
 */
object UnlockManager {
    fun init() {
        // null
    }

    fun getUnlockedFlow(unlock: Unlock): Boolean {
        if (DebugManager.unlockAll || unlock.initialLock == 0) return true
        val result = unlock.conditionType.mapIndexed { index, conditionType ->
            when (conditionType) {
                UnlockId.Age.id ->
                    getIntFlowByKey(FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.AGE.id + "_1") >= unlock.conditionNum[index]
                UnlockId.LoginDay.id -> {
                    getLoginDays() >= unlock.conditionNum[index]
                }
                UnlockId.Talent.id -> {
                    val talentLevels = TalentManager.talents.values.sum()
                    talentLevels >= unlock.conditionNum[index]
                }

                UnlockId.Code.id ->
                    // 根据本地记录的是否已解锁来判定
                    getBooleanFlowByKey(
                        KEY_UNLOCK_EVIDENCE + unlock.conditionNum[index]
                    )

                UnlockId.VipLevel.id ->
                    // 根据本地记录的是否已解锁来判定
                    VipManager.getVipLevel() >= unlock.conditionNum[index]
                UnlockId.GiftId.id -> {
                    getBooleanFlowByKey(KEY_GIFT_AWARDED + unlock.conditionNum[index])
                }
                UnlockId.Charge.id -> {
                    // 玩家是否充值过（参数1=是，0=否）
                    if (unlock.conditionNum[index] == 0) {
                        AwardManager.electric.value <= 0
                    } else {
                        AwardManager.electric.value > 0
                    }
                }
                UnlockId.Die.id -> {
                    getIntFlowByKey(KEY_DIED_IN_GAME) >= unlock.conditionNum[index]
                }
                UnlockId.Ad.id -> {
                    AwardManager.adNum.value > 0
                }
                UnlockId.NoMoney.id -> {
                    //  11=使用货币提示不足（参数1=非付费货币 2=付费货币）
                    if (unlock.conditionNum[index] == 2) {
                        getIntFlowByKey(KEY_KEY_NOT_ENOUGH) > 0
                    } else {
                        getIntFlowByKey(KEY_DIAMOND_NOT_ENOUGH) > 0
                    }
                }
                UnlockId.PvpDie.id -> {
                    //  12=玩家竞技场战斗失败（参数N=连续失败N次）
                    getIntFlowByKey(KEY_DIED_IN_PVP) >= unlock.conditionNum[index]
                }
                UnlockId.ElectricMoreOrEqual.id -> {
                    AwardManager.electric.value >= unlock.conditionNum[index]
                }
                UnlockId.ElectricLess.id -> {
                    AwardManager.electric.value < unlock.conditionNum[index]
                }
                UnlockId.Tower.id -> {
                    TowerManager.maxLevel.value >= unlock.conditionNum[index]
                }
                else -> false
            }
        }
        return result.all { it }
    }

    suspend fun unlockCode(id: Int) {
        if (id in KEY_WAR_PASS_UNLOCK_EVIDENCE..KEY_WAR_PASS_UNLOCK_EVIDENCE + KEY_WAR_PASS_NUM) {
            // season 从1开始
            AwardManager.battlePassBought[id - KEY_WAR_PASS_UNLOCK_EVIDENCE + 1]!!.value = true
        }
        if (id in KEY_WAR_PASS2_UNLOCK_EVIDENCE..KEY_WAR_PASS2_UNLOCK_EVIDENCE + KEY_WAR_PASS_NUM) {
            // season 从1开始
            AwardManager.battlePass2Bought[id - KEY_WAR_PASS2_UNLOCK_EVIDENCE + 1]!!.value = true
        }
        setBooleanValueByKeySync(KEY_UNLOCK_EVIDENCE + id, true)
        Dialogs.warPassUnlockDialog.value = false
        Dialogs.warPass2UnlockDialog.value = false
    }

    fun getInitAllyUnlockByIndex(index: Int): Unlock {
        return repo.gameCore.getUnlockById(INIT_ALLY_UNLOCK_ID + index)
    }

    fun getInitSkillUnlockByIndex(index: Int): Unlock {
        return repo.gameCore.getUnlockById(INIT_SKILL_UNLOCK_ID + index)
    }

    fun getInitHeroUnlockByIndex(index: Int): Unlock {
        return repo.gameCore.getUnlockById(INIT_HERO_UNLOCK_ID + index)
    }

    fun getInitAllyNum(): Int {
        return (0..9).count {
            getUnlockedFlow(getInitAllyUnlockByIndex(9 - it))
        }
    }

    fun getInitSkillNum(): Int {
        return (0..9).count {
            getUnlockedFlow(getInitSkillUnlockByIndex(9 - it))
        }
    }

    fun getInitHeroNum(): Int {
        return (0..9).count {
            getUnlockedFlow(getInitHeroUnlockByIndex(9 - it))
        }
    }
}