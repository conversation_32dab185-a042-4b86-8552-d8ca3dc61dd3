package com.moyu.chuanqirensheng.logic.task

import androidx.compose.runtime.mutableStateListOf
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.cloud.AntiCheatManager.checkQuest
import com.moyu.chuanqirensheng.cloud.MAX_AGE
import com.moyu.chuanqirensheng.datastore.KEY_CHARGE_TASK
import com.moyu.chuanqirensheng.datastore.KEY_COLLECT_TASK
import com.moyu.chuanqirensheng.datastore.KEY_COST_TASK
import com.moyu.chuanqirensheng.datastore.KEY_ENDING_DONE
import com.moyu.chuanqirensheng.datastore.KEY_GAME_DRAW_TASK
import com.moyu.chuanqirensheng.datastore.KEY_GAME_LOGIN_DAY
import com.moyu.chuanqirensheng.datastore.KEY_GAME_NEW_TASK_UPDATE_TIME_IN_MILLIS
import com.moyu.chuanqirensheng.datastore.KEY_GAME_PVP_TASK
import com.moyu.chuanqirensheng.datastore.KEY_GAME_PVP_TASK_UPDATE_TIME_IN_MILLIS
import com.moyu.chuanqirensheng.datastore.KEY_GAME_TASK
import com.moyu.chuanqirensheng.datastore.KEY_GAME_TASK_PROGRESS
import com.moyu.chuanqirensheng.datastore.KEY_GAME_TASK_UPDATE_TIME_IN_MILLIS
import com.moyu.chuanqirensheng.datastore.KEY_GAME_WARPASS2_TASK_UPDATE_TIME_IN_MILLIS
import com.moyu.chuanqirensheng.datastore.KEY_GAME_WARPASS_TASK_UPDATE_TIME_IN_MILLIS
import com.moyu.chuanqirensheng.datastore.KEY_HOLIDAY_GAME_TASK
import com.moyu.chuanqirensheng.datastore.KEY_NEW_TASK
import com.moyu.chuanqirensheng.datastore.KEY_ONE_TIME_GAME_TASK
import com.moyu.chuanqirensheng.datastore.KEY_WAR_PASS2_TASK
import com.moyu.chuanqirensheng.datastore.KEY_WAR_PASS_TASK
import com.moyu.chuanqirensheng.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.datastore.getListObject
import com.moyu.chuanqirensheng.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.datastore.mapData
import com.moyu.chuanqirensheng.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.datastore.setListObject
import com.moyu.chuanqirensheng.datastore.setListObjectSync
import com.moyu.chuanqirensheng.datastore.setLongValueByKey
import com.moyu.chuanqirensheng.feature.draw.DrawManager
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.feature.holiday.HolidayLotteryManager
import com.moyu.chuanqirensheng.feature.holiday.HolidayManager
import com.moyu.chuanqirensheng.feature.holiday.ui.holidaySolidRanks
import com.moyu.chuanqirensheng.feature.newTask.SevenDayManager
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.feature.pvp.ui.lastPvpRanks
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.award.VipManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.isSameDay
import com.moyu.core.model.sell.Award
import com.moyu.core.model.task.GameTask
import com.moyu.core.util.RANDOM
import timber.log.Timber
import java.lang.Integer.min

const val FOREVER = "k_)"

object TaskManager {
    val dailyTasks = mutableStateListOf<GameTask>()
    val warPassTasks = mutableStateListOf<GameTask>()
    val warPass2Tasks = mutableStateListOf<GameTask>()
    val newTasks = mutableStateListOf<GameTask>()
    val oneTimeTasks = mutableStateListOf<GameTask>()
    val pvpTasks = mutableStateListOf<GameTask>()

    suspend fun init() {
        if (dailyTasks.isEmpty()) {
            createTasks()
        }
        createPvpTasks()
        createWarPassTasks()
        createWarPass2Tasks()
        if (newTasks.isEmpty()) {
            createNewTasks()
        }
        createOneTimeTasks()
    }

    fun createOneTimeTasks() {
        if (oneTimeTasks.isEmpty()) {
            getListObject<GameTask>(KEY_ONE_TIME_GAME_TASK).let { taskList ->
                val tasks = taskList.mapNotNull { task ->
                    repo.gameCore.getGameTaskPool().firstOrNull { it.id == task.id }
                        ?.copy(done = task.done, opened = task.opened)
                }
                oneTimeTasks.addAll(tasks)
            }
        }
        val filteredTasks = repo.gameCore.getGameTaskPool().filter { it.isOneTimeTask() }
            .filterNot { pool -> oneTimeTasks.any { it.id == pool.id } }
        oneTimeTasks.addAll(filteredTasks)
        setListObject(KEY_ONE_TIME_GAME_TASK, oneTimeTasks)
    }



    fun createWarPassTasks() {
        if (!isNetTimeValid()) {
            // 防止作弊
            return
        }
        if (warPassTasks.isEmpty()) {
            getListObject<GameTask>(KEY_WAR_PASS_TASK).let { taskList ->
                val tasks = taskList.mapNotNull { task ->
                    repo.gameCore.getGameTaskPool().firstOrNull { it.id == task.id }
                        ?.copy(
                            done = task.done,
                            opened = task.opened,
                            needRemoveCount = task.needRemoveCount
                        )
                }
                warPassTasks.addAll(tasks)
            }
        }
        if (!isSameDay(
                getLongFlowByKey(KEY_GAME_WARPASS_TASK_UPDATE_TIME_IN_MILLIS),
                getCurrentTime()
            )
        ) {
            // 通行证任务，每天+4个
            val filteredTasks = repo.gameCore.getGameTaskPool()
            val showedMaxId = warPassTasks.maxOfOrNull { it.id } ?: 0
            warPassTasks.addAll(filteredTasks.filter { it.taskType == 3 }.filter {
                it.id > showedMaxId
            }.take(repo.gameCore.getWarPassQuestCount()).map { task ->
                // 要用永久计数，但是又要移除之前的计数
                val needRemove = if (!task.isInstant()) {
                    val postFix = task.subType.map {
                        if (it == 0) "" else "_$it"
                    }.reduce { acc, s -> acc + s }
                    getIntFlowByKey(FOREVER + KEY_GAME_TASK_PROGRESS + task.type + postFix, 0)
                } else 0
                task.copy(needRemoveCount = needRemove)
            })
            setLongValueByKey(KEY_GAME_WARPASS_TASK_UPDATE_TIME_IN_MILLIS, getCurrentTime())
        }
        setListObject(KEY_WAR_PASS_TASK, warPassTasks)
    }

    fun createWarPass2Tasks() {
        if (!isNetTimeValid()) {
            // 防止作弊
            return
        }
        if (warPass2Tasks.isEmpty()) {
            getListObject<GameTask>(KEY_WAR_PASS2_TASK).let { taskList ->
                val tasks = taskList.mapNotNull { task ->
                    repo.gameCore.getGameTaskPool().firstOrNull { it.id == task.id }
                        ?.copy(
                            done = task.done,
                            opened = task.opened,
                            needRemoveCount = task.needRemoveCount
                        )
                }
                warPass2Tasks.addAll(tasks)
            }
        }
        if (warPass2Tasks.isEmpty()) {
            val filteredTasks = repo.gameCore.getGameTaskPool()
            warPass2Tasks.clear()
            warPass2Tasks.addAll(filteredTasks.filter { it.isWarPass2Task() })
            setLongValueByKey(KEY_GAME_WARPASS2_TASK_UPDATE_TIME_IN_MILLIS, getCurrentTime())
            setListObject(KEY_WAR_PASS2_TASK, warPass2Tasks)
        }
    }

    fun createNewTasks() {
        if (!isNetTimeValid()) {
            // 防止作弊
            return
        }
        if (newTasks.isEmpty()) {
            getListObject<GameTask>(KEY_NEW_TASK).let { taskList ->
                val tasks = taskList.mapNotNull { task ->
                    repo.gameCore.getGameTaskPool().firstOrNull { it.id == task.id }
                        ?.copy(done = task.done, opened = task.opened)
                }
                newTasks.addAll(tasks)
            }
        }
        if (!isSameDay(
                getLongFlowByKey(KEY_GAME_NEW_TASK_UPDATE_TIME_IN_MILLIS), getCurrentTime()
            )
        ) {
            // 新手任务，每天+4个
            val filteredTasks = repo.gameCore.getGameTaskPool()
            val showedMaxId = newTasks.maxOfOrNull { it.id } ?: 0
            newTasks.addAll(filteredTasks.filter { it.isNewTask() }.filter {
                it.id > showedMaxId
            }.take(repo.gameCore.getNewQuestCount()).map { task ->
                // 要用永久计数，但是又要移除之前的计数
                val needRemove = if (!task.isInstant()) {
                    val postFix = task.subType.map {
                        if (it == 0) "" else "_$it"
                    }.reduce { acc, s -> acc + s }
                    getIntFlowByKey(FOREVER + KEY_GAME_TASK_PROGRESS + task.type + postFix, 0)
                } else 0
                task.copy(needRemoveCount = needRemove)
            })
        }
        setLongValueByKey(KEY_GAME_NEW_TASK_UPDATE_TIME_IN_MILLIS, getCurrentTime())
        setListObject(KEY_NEW_TASK, newTasks)
    }

    suspend fun createTasks() {
        if (!isNetTimeValid()) {
            // 防止作弊
            return
        }
        // 任务中,启动游戏可以直接标记已完成
        onTaskStartGameTime()
        if (isSameDay(
                getLongFlowByKey(KEY_GAME_TASK_UPDATE_TIME_IN_MILLIS), getCurrentTime()
            )
        ) {
            // 任务：仅同一天内更新过，才加载，否则不加载，自动会在Repo刷新
            if (dailyTasks.isEmpty()) {
                try {
                    getListObject<GameTask>(KEY_GAME_TASK).let {
                        dailyTasks.addAll(it.mapNotNull { task ->
                            repo.gameCore.getGameTaskPool().firstOrNull { it.id == task.id }
                                ?.copy(done = task.done, opened = task.opened)
                        })
                    }
                } catch (e: Exception) {
                    Timber.e(e)
                    // 修改了task格式
                    clearDayTasks()
                }
            }
        } else {
            // 任务：如果已经过了一天，清理任务记录
            clearDayTasks()
            onTaskStartGameDay()
        }

        // 强制设置最少1天签到
        if (getIntFlowByKey(KEY_GAME_LOGIN_DAY) == 0) {
            setIntValueByKey(KEY_GAME_LOGIN_DAY, 1)
        }
        val filteredTasks = repo.gameCore.getGameTaskPool()
        if (dailyTasks.isEmpty()) {
            val maxAge = min(
                MAX_AGE, getIntFlowByKey(
                    FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.AGE.id + "_1",
                    1
                )
            )
            dailyTasks.addAll(filteredTasks.filter { it.isDailyTask() }.filter {
                maxAge >= it.talent.first() && maxAge <= it.talent[1]
            }.shuffled(RANDOM)
                .take(VipManager.getExtraDailyQuest() + repo.gameCore.getDailyQuestCount())
            )

            setLongValueByKey(KEY_GAME_TASK_UPDATE_TIME_IN_MILLIS, getCurrentTime())
            setListObject(KEY_GAME_TASK, dailyTasks)
        }
    }

    fun createPvpTasks() {
        if (!isNetTimeValid()) {
            // 防止作弊
            return
        }
        if (isSameDay(
                getLongFlowByKey(KEY_GAME_PVP_TASK_UPDATE_TIME_IN_MILLIS), getCurrentTime()
            )
        ) {
            // 任务：仅同一天内更新过，才加载，否则不加载，自动会在Repo刷新
            if (pvpTasks.isEmpty()) {
                try {
                    getListObject(KEY_GAME_PVP_TASK, GameTask.serializer()).let {
                        pvpTasks.addAll(it.map { task ->
                            repo.gameCore.getGameTaskById(task.id)
                                .copy(done = task.done, opened = task.opened)
                        })
                    }
                } catch (e: Exception) {
                    Timber.e(e)
                    // 修改了task格式
                    clearDayTasks()
                }
            }
        } else {
            pvpTasks.clear()
        }

        val filteredTasks = repo.gameCore.getGameTaskPool()
        if (pvpTasks.isEmpty()) {
            val maxGameProgress = getIntFlowByKey(
                FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.AGE.id + "_1",
                1
            )
            pvpTasks.addAll(filteredTasks.filter { it.isPvpTask() }.filter {
                maxGameProgress >= it.talent.first() && maxGameProgress <= it.talent[1]
            }.shuffled(RANDOM))

            setLongValueByKey(KEY_GAME_PVP_TASK_UPDATE_TIME_IN_MILLIS, getCurrentTime())
            setListObject(KEY_GAME_PVP_TASK, pvpTasks)
        }
    }

    fun getTaskDoneFlow(task: GameTask): Boolean {
        when (task.type) {
            TaskEvent.ENDING.id -> {
                return getBooleanFlowByKey(KEY_ENDING_DONE + "_${task.subType}")
            }

            TaskEvent.HAVE_TYPE_ITEM.id -> {
                return if (task.subType.first() == 1) {
                    repo.allyManager.data.size >= task.num
                } else if (task.subType.first() == 3) {
                    repo.skillManager.data.size >= task.num
                } else if (task.subType.first() == 4) {
                    repo.heroManager.data.size >= task.num
                } else {
                    false
                }
            }

            TaskEvent.HAVE_STAR_ITEM.id -> {
                return if (task.subType.first() in 1..5) {
                    val star = task.subType.first()
                    repo.allyManager.data.filter { it.star >= star }.size >= task.num
                } else if (task.subType.first() in 6..10) {
                    val star = task.subType.first() - 5
                    repo.skillManager.data.filter { it.level >= star }.size >= task.num
                } else if (task.subType.first() in 11..15) {
                    val star = task.subType.first() - 10
                    repo.heroManager.data.filter { it.level >= star }.size >= task.num
                } else {
                    false
                }
            }

            TaskEvent.COST.id -> {
                return AwardManager.keyCost.value >= task.num
            }

            TaskEvent.CHARGE.id -> {
                return SevenDayManager.isChargeTaskDone(task.num)
            }

            TaskEvent.HOLIDAY_LOTTERY.id -> {
                return HolidayLotteryManager.isLotteryTaskDone(task.num)
            }

            TaskEvent.HOLIDAY_CHARGE.id -> {
                return HolidayManager.isChargeTaskDone(task.num)
            }

            TaskEvent.PVP_BATTLE.id -> {
                return if (task.subType.first() == 0) {
                    PvpManager.pkWinToday.value + PvpManager.pkLoseToday.value >= task.num
                } else {
                    PvpManager.pkWinToday.value >= task.num
                }
            }

            TaskEvent.PVP_RANK.id -> {
                val realRank = lastPvpRanks.value
                return when (task.subType.first()) {
                    1 -> {
                        realRank.getOrNull(0)?.userId == GameApp.instance.getObjectId()
                    }

                    2 -> {
                        realRank.getOrNull(1)?.userId == GameApp.instance.getObjectId()
                    }

                    3 -> {
                        realRank.getOrNull(2)?.userId == GameApp.instance.getObjectId()
                    }

                    4 -> {
                        realRank.getOrNull(3)?.userId == GameApp.instance.getObjectId()
                    }

                    5 -> {
                        realRank.getOrNull(4)?.userId == GameApp.instance.getObjectId()
                    }

                    6 -> {
                        // 6-10名
                        val userIds = if (realRank.size > 5) {
                            realRank.subList(5, min(realRank.size, 10)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(GameApp.instance.getObjectId())
                    }

                    7 -> {
                        // 11-20名
                        val userIds = if (realRank.size > 10) {
                            realRank.subList(10, min(realRank.size, 20)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(GameApp.instance.getObjectId())
                    }

                    8 -> {
                        // 21-50名
                        val userIds = if (realRank.size > 20) {
                            realRank.subList(20, min(realRank.size, 50)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(GameApp.instance.getObjectId())
                    }

                    9 -> {
                        // 51-100名
                        val userIds = if (realRank.size > 50) {
                            realRank.subList(50, min(realRank.size, 100)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(GameApp.instance.getObjectId())
                    }

                    else -> {
                        false
                    }
                }
            }

            /**
             *  24：1=第1名；2=第2名；3=第3名；4=4-5名；5=6-10名；6=11-50名；7=51-100名
             */
            TaskEvent.HOLIDAY_RANK.id -> {
                val realRank = holidaySolidRanks.value
                return when (task.subType.first()) {
                    1 -> {
                        realRank.getOrNull(0)?.userId == GameApp.instance.getObjectId()
                                && HolidayLotteryManager.holidaySpinTotal.value >= (realRank.take(10).lastOrNull()?.holidayNum ?: 9999)
                                && realRank.size >= 3
                    }

                    2 -> {
                        realRank.getOrNull(1)?.userId == GameApp.instance.getObjectId()
                                && HolidayLotteryManager.holidaySpinTotal.value >= (realRank.take(10).lastOrNull()?.holidayNum ?: 9999)
                                && realRank.size >= 3
                    }

                    3 -> {
                        realRank.getOrNull(2)?.userId == GameApp.instance.getObjectId()
                                && HolidayLotteryManager.holidaySpinTotal.value >= (realRank.take(10).lastOrNull()?.holidayNum ?: 9999)
                                && realRank.size >= 3
                    }

                    4 -> {
                        (realRank.getOrNull(3)?.userId == GameApp.instance.getObjectId()
                                || realRank.getOrNull(4)?.userId == GameApp.instance.getObjectId())
                                && HolidayLotteryManager.holidaySpinTotal.value >= (realRank.take(10).lastOrNull()?.holidayNum ?: 9999)
                    }

                    5 -> {
                        // 6-10名
                        val userIds = if (realRank.size > 5) {
                            realRank.subList(5, min(realRank.size, 10)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(GameApp.instance.getObjectId())
                                && HolidayLotteryManager.holidaySpinTotal.value >= (realRank.take(10).lastOrNull()?.holidayNum ?: 9999)
                    }

                    6 -> {
                        // 11-50名
                        val userIds = if (realRank.size > 10) {
                            realRank.subList(10, min(realRank.size, 50)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(GameApp.instance.getObjectId())
                                && HolidayLotteryManager.holidaySpinTotal.value >= (realRank.lastOrNull()?.holidayNum ?: 9999)
                    }

                    7 -> {
                        // 51-100名
                        val userIds = if (realRank.size > 50) {
                            realRank.subList(50, min(realRank.size, 100)).map { it.userId }
                        } else {
                            emptyList()
                        }
                        userIds.contains(GameApp.instance.getObjectId())
                                && HolidayLotteryManager.holidaySpinTotal.value >= (realRank.lastOrNull()?.holidayNum ?: 9999)
                    }

                    else -> {
                        false
                    }
                }
            }


            else -> {
                val postFix = task.subType.map {
                    if (it == 0) "" else "_$it"
                }.reduce { acc, s -> acc + s }
                val preFix = if (task.taskType != 1) FOREVER else ""
                return getIntFlowByKey(preFix + KEY_GAME_TASK_PROGRESS + task.type + postFix) - task.needRemoveCount >= task.num
            }
        }
    }

    fun getTaskProgressFlow(task: GameTask): String {
        when (task.type) {
            TaskEvent.ENDING.id -> {
                return getBooleanFlowByKey(KEY_ENDING_DONE + "_${task.subType}").let {
                    if (it) {
                        GameApp.instance.getWrapString(R.string.completed)
                    } else {
                        GameApp.instance.getWrapString(R.string.not_complete_yet)
                    }
                }
            }

            TaskEvent.HAVE_TYPE_ITEM.id -> {
                return if (task.subType.first() == 1) {
                    "" + repo.allyManager.data.size + "/" + task.num
                } else if (task.subType.first() == 3) {
                    "" + repo.skillManager.data.size + "/" + task.num
                } else if (task.subType.first() == 4) {
                    "" + repo.heroManager.data.size + "/" + task.num
                } else {
                    "0/" + task.num
                }
            }

            TaskEvent.HAVE_STAR_ITEM.id -> {
                return if (task.subType.first() in 1..5) {
                    val star = task.subType.first()
                    "" + repo.allyManager.data.filter { it.star >= star }.size + "/" + task.num
                } else if (task.subType.first() in 6..10) {
                    val star = task.subType.first() - 5
                    "" + repo.skillManager.data.filter { it.level >= star }.size + "/" + task.num
                } else if (task.subType.first() in 11..15) {
                    val star = task.subType.first() - 10
                    "" + repo.heroManager.data.filter { it.level >= star }.size + "/" + task.num
                } else {
                    "0/" + task.num
                }
            }

            TaskEvent.COST.id -> {
                return AwardManager.keyCost.value.toString() + "/" + task.num
            }

            TaskEvent.CHARGE.id -> {
                return SevenDayManager.isChargeTaskDoneString(task.num)
            }

            TaskEvent.HOLIDAY_CHARGE.id -> {
                return HolidayManager.isChargeTaskDoneString(task.num)
            }

            TaskEvent.HOLIDAY_LOTTERY.id -> {
                return HolidayLotteryManager.isChargeTaskDoneString(task.num)
            }

            TaskEvent.PVP_BATTLE.id -> {
                return if (task.subType.first() == 0) {
                    (PvpManager.pkWinToday.value + PvpManager.pkLoseToday.value).toString() + "/" + task.num
                } else {
                    (PvpManager.pkWinToday.value).toString() + "/" + task.num
                }
            }

            TaskEvent.PVP_RANK.id -> {
                return ""
            }

            TaskEvent.HOLIDAY_RANK.id -> {
                return ""
            }

            else -> {
                val postFix = task.subType.map {
                    if (it == 0) "" else "_$it"
                }.reduce { acc, s -> acc + s }
                val preFix = if (task.taskType != 1) FOREVER else ""
                return getIntFlowByKey(preFix + KEY_GAME_TASK_PROGRESS + task.type + postFix).let {
                    if (task.type == TaskEvent.REPUTATION_LEVEL.id) {
                        ""
                    } else {
                        "${it - task.needRemoveCount}/${task.num}"
                    }
                }
            }
        }
    }

    suspend fun questReward(quest: GameTask, award: Award) {
        checkQuest(quest)
        if (quest.isDailyTask()) {
            val index = dailyTasks.indexOfFirst { it.id == quest.id }
            if (!dailyTasks[index].opened) {
                dailyTasks[index] = dailyTasks[index].copy(opened = true)
                setListObjectSync(KEY_GAME_TASK, dailyTasks)
                val realAward = if (VipManager.isDoubleQuestAward()) {
                    award + award
                } else award
                AwardManager.gainAward(realAward)
                Dialogs.awardDialog.value = realAward
            }
        } else if (quest.isPvpTask()) {
            val index = pvpTasks.indexOfFirst { it.id == quest.id }
            if (!pvpTasks[index].opened) {
                pvpTasks[index] = pvpTasks[index].copy(opened = true)
                setListObjectSync(KEY_GAME_PVP_TASK, pvpTasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else if (quest.isNewTask()) {
            val index = newTasks.indexOfFirst { it.id == quest.id }
            if (!newTasks[index].opened) {
                newTasks[index] = newTasks[index].copy(opened = true)
                setListObjectSync(KEY_NEW_TASK, newTasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else if (quest.isCollectTask()) {
            val index = SevenDayManager.collectTasks.indexOfFirst { it.id == quest.id }
            if (!SevenDayManager.collectTasks[index].opened) {
                SevenDayManager.collectTasks[index] = SevenDayManager.collectTasks[index].copy(opened = true)
                setListObjectSync(KEY_COLLECT_TASK, SevenDayManager.collectTasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else if (quest.isCostTask()) {
            val index = SevenDayManager.costTasks.indexOfFirst { it.id == quest.id }
            if (!SevenDayManager.costTasks[index].opened) {
                SevenDayManager.costTasks[index] = SevenDayManager.costTasks[index].copy(opened = true)
                setListObjectSync(KEY_COST_TASK, SevenDayManager.costTasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else if (quest.isChargeTask()) {
            val index = SevenDayManager.chargeTasks.indexOfFirst { it.id == quest.id }
            if (!SevenDayManager.chargeTasks[index].opened) {
                SevenDayManager.chargeTasks[index] = SevenDayManager.chargeTasks[index].copy(opened = true)
                setListObjectSync(KEY_CHARGE_TASK, SevenDayManager.chargeTasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else if (quest.isWarPassTask()) {
            val index = warPassTasks.indexOfFirst { it.id == quest.id }
            if (!warPassTasks[index].opened) {
                warPassTasks[index] = warPassTasks[index].copy(opened = true)
                setListObjectSync(KEY_WAR_PASS_TASK, warPassTasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else if (quest.isDrawTask()) {
            val index = DrawManager.drawTasks.indexOfFirst { it.id == quest.id }
            if (!DrawManager.drawTasks[index].opened) {
                DrawManager.drawTasks[index] = DrawManager.drawTasks[index].copy(opened = true)
                setListObjectSync(KEY_GAME_DRAW_TASK, DrawManager.drawTasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else if (quest.isHolidayTask()) {
            val index = HolidayManager.holidayTasks.indexOfFirst { it.id == quest.id }
            if (!HolidayManager.holidayTasks[index].opened) {
                HolidayManager.holidayTasks[index] = HolidayManager.holidayTasks[index].copy(opened = true)
                setListObjectSync(KEY_HOLIDAY_GAME_TASK, HolidayManager.holidayTasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else if (quest.isWarPass2Task()) {
            val index = warPass2Tasks.indexOfFirst { it.id == quest.id }
            if (!warPass2Tasks[index].opened) {
                warPass2Tasks[index] = warPass2Tasks[index].copy(opened = true)
                setListObjectSync(KEY_WAR_PASS2_TASK, warPass2Tasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else if (quest.isOneTimeTask()) {
            val index = oneTimeTasks.indexOfFirst { it.id == quest.id }
            if (!oneTimeTasks[index].opened) {
                oneTimeTasks[index] = oneTimeTasks[index].copy(opened = true)
                setListObjectSync(KEY_ONE_TIME_GAME_TASK, oneTimeTasks)
                AwardManager.gainAward(award)
                Dialogs.awardDialog.value = award
            }
        } else {
            error("未知任务类型${quest.taskType}")
        }
    }

    fun clearDayTasks() {
        dailyTasks.clear()
        mapData.keys.filter {
            it.startsWith(
                KEY_GAME_TASK_PROGRESS
            )
        }.forEach {
            setIntValueByKey(it, 0)
        }
        // todo 每日清理数据放这里
        GiftManager.onRefreshNumTheNextDay()
    }
}