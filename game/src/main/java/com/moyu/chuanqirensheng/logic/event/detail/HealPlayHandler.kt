package com.moyu.chuanqirensheng.logic.event.detail

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.event.PlayHandler
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.screen.ally.SingleAllyView
import com.moyu.chuanqirensheng.screen.arena.beingHealEffect
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.effect.newTurnEffectState
import com.moyu.chuanqirensheng.screen.effect.restartEffect
import com.moyu.chuanqirensheng.screen.skill.EmptyIconView
import com.moyu.chuanqirensheng.ui.theme.buttonHeight
import com.moyu.chuanqirensheng.ui.theme.paddingHuge
import com.moyu.core.model.ally.Ally
import com.moyu.core.model.event.Event

class HealPlayHandler(
    override val skipWin: Boolean = true,
    override val dialogJump: Boolean = false,
    override val playId: Int = 4
) : PlayHandler() {
    private val ally1 = mutableStateOf<Ally?>(null)
    private val ally2 = mutableStateOf<Ally?>(null)
    private val showSelect1 = {
        Dialogs.selectOneAllyDialog.value = Pair({
            it != ally1.value  && it != ally2.value && !BattleManager.isDead(it)
        }, {
            ally1.value = it
        })
    }
    private val showSelect2 = {
        Dialogs.selectOneAllyDialog.value = Pair({
            it != ally1.value  && it != ally2.value && !BattleManager.isDead(it)
        }, {
            ally2.value = it
        })
    }

    @Composable
    override fun Layout(event: Event) {
        val effectStart = remember { mutableStateOf(false) }
        BasicEventContentWithBackground(event, isShowEffect = effectStart.value, showEventIcon = false) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(vertical = paddingHuge),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = stringResource(R.string.heal) + "100%",
                    color = Color.Black,
                    style = MaterialTheme.typography.h2
                )

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    ally1.value?.let {
                        SingleAllyView(ally = it, itemSize = ItemSize.LargePlus) {
                            showSelect1()
                        }
                    } ?: EmptyIconView(itemSize = ItemSize.LargePlus) {
                        showSelect1()
                    }

                    ally2.value?.let {
                        SingleAllyView(ally = it, itemSize = ItemSize.LargePlus) {
                            showSelect2()
                        }
                    } ?: EmptyIconView(itemSize = ItemSize.LargePlus) {
                        showSelect2()
                    }
                }

                if (ally1.value != null || ally2.value != null) {
                    GameButton(
                        text = stringResource(id = R.string.made_my_choice),
                        buttonStyle = ButtonStyle.Orange,
                        onClick = {
                            if (ally1.value != null || ally2.value != null) {
                                if (!eventFinished.value) {
                                    effectStart.value = true
                                    eventFinished.value = true
                                    restartEffect(newTurnEffectState, beingHealEffect)
                                    // 和复活效果一样
                                    ally1.value?.let {
                                        BattleManager.reliveAllyInGame(it, false)
                                    }
                                    ally2.value?.let {
                                        BattleManager.reliveAllyInGame(it, false)
                                    }
                                }
                            }
                            EventManager.doEventResult(event, true)
                        })
                } else {
                    Spacer(modifier = Modifier.size(buttonHeight))
                }
            }
        }
        Spacer(modifier = Modifier.size(paddingHuge))
        if (ally1.value == null && ally2.value == null) {
            GameButton(
                text = stringResource(id = R.string.left),
                buttonStyle = ButtonStyle.Orange,
                buttonSize = ButtonSize.Medium
            ) {
                EventManager.doEventResult(
                    event, eventResult.value
                )
            }
        }
    }

    override fun onEventSelect(event: Event) {
        ally1.value = null
        ally2.value = null
    }
}
