package com.moyu.chuanqirensheng.ui.theme

import androidx.compose.material.Typography
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import com.moyu.chuanqirensheng.sub.language.LanguageManager
import com.moyu.chuanqirensheng.util.composeSp

// Set of Material typography styles to start with
fun createTypography(fontFamily: FontFamily?, fontScale: Float): Typography {
    return if (LanguageManager.isSelectedChinese()) {
        Typography(
            h1 = TextStyle(
                fontFamily = fontFamily,
                fontWeight = FontWeight.Bold,
                fontSize = 26.composeSp() / fontScale,
                color = Color.White,
                lineHeight = 38.composeSp() / fontScale
            ),
            h2 = TextStyle(
                fontFamily = fontFamily,
                fontWeight = FontWeight.Bold,
                fontSize = 20.composeSp() / fontScale,
                color = Color.White,
                lineHeight = 23.composeSp() / fontScale
            ),
            h3 = TextStyle(
                fontFamily = fontFamily,
                fontWeight = FontWeight.Bold,
                fontSize = 16.composeSp() / fontScale,
                color = Color.White,
                lineHeight = 18.composeSp() / fontScale
            ),
            h4 = TextStyle(
                fontFamily = fontFamily,
                fontWeight = FontWeight.Bold,
                fontSize = 14.composeSp() / fontScale,
                color = Color.White,
                lineHeight = 16.composeSp() / fontScale,
            ),
            h5 = TextStyle(
                fontFamily = fontFamily,
                fontWeight = FontWeight.Bold,
                fontSize = 13.composeSp() / fontScale,
                color = Color.White,
                lineHeight = 14.composeSp() / fontScale
            ),
            h6 = TextStyle(
                fontFamily = fontFamily,
                fontWeight = FontWeight.Bold,
                fontSize = 12.composeSp() / fontScale,
                color = Color.White,
                lineHeight = 13.composeSp() / fontScale
            ),
            body1 = TextStyle(
                fontFamily = fontFamily,
                fontWeight = FontWeight.Bold,
                fontSize = 10.composeSp() / fontScale,
                color = Color.White,
                lineHeight = 11.composeSp() / fontScale
            ),
            body2 = TextStyle(
                fontFamily = fontFamily,
                fontWeight = FontWeight.Bold,
                fontSize = 6.composeSp() / fontScale,
                color = Color.White,
                lineHeight = 7.composeSp() / fontScale
            ),
        )
    } else {
        Typography(
            h1 = TextStyle(
                fontFamily = fontFamily,
                fontWeight = FontWeight.Bold,
                fontSize = 26.composeSp() / fontScale,
                color = Color.White,
                lineHeight = 36.composeSp() / fontScale
            ),
            h2 = TextStyle(
                fontFamily = fontFamily,
                fontWeight = FontWeight.Bold,
                fontSize = 20.composeSp() / fontScale,
                color = Color.White,
                lineHeight = 22.composeSp() / fontScale
            ),
            h3 = TextStyle(
                fontFamily = fontFamily,
                fontWeight = FontWeight.Bold,
                fontSize = 16.composeSp() / fontScale,
                color = Color.White,
                lineHeight = 16.composeSp() / fontScale
            ),
            h4 = TextStyle(
                fontFamily = fontFamily,
                fontWeight = FontWeight.Bold,
                fontSize = 14.composeSp() / fontScale,
                color = Color.White,
                lineHeight = 14.composeSp() / fontScale,
            ),
            h5 = TextStyle(
                fontFamily = fontFamily,
                fontWeight = FontWeight.Bold,
                fontSize = 12.composeSp() / fontScale,
                color = Color.White,
                lineHeight = 12.composeSp() / fontScale
            ),
            h6 = TextStyle(
                fontFamily = fontFamily,
                fontWeight = FontWeight.Bold,
                fontSize = 11.composeSp() / fontScale,
                color = Color.White,
                lineHeight = 11.composeSp() / fontScale
            ),
            body1 = TextStyle(
                fontFamily = fontFamily,
                fontWeight = FontWeight.Bold,
                fontSize = 10.composeSp() / fontScale,
                color = Color.White,
                lineHeight = 10.composeSp() / fontScale
            ),
            body2 = TextStyle(
                fontFamily = fontFamily,
                fontWeight = FontWeight.Bold,
                fontSize = 6.composeSp() / fontScale,
                color = Color.White,
                lineHeight = 6.composeSp() / fontScale
            ),
        )
    }
}
