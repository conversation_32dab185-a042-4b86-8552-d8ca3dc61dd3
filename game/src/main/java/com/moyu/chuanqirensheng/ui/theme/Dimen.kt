package com.moyu.chuanqirensheng.ui.theme

import com.moyu.chuanqirensheng.util.composeDp

val enemyDetailHeight = 784.composeDp()
val logoHeight = 180.composeDp()

val imageTiny = 4.composeDp()
val imageTinyPlus = 16.composeDp()
val imageSmall = 20.composeDp()
val imageSmallPlus = 28.composeDp()
val imageSmallPlusFrame = 30.composeDp()
val imageMediumMinus = 34.composeDp()
val imageMedium = 36.composeDp()
val imageMediumFrame = 40.composeDp()
val imageLarge = 44.composeDp()
val imageLargeFrame = 52.composeDp()
val imageLargePlus = 58.composeDp()
val imageLargePlusFrame = 60.composeDp()
val imageHugeLite = 68.composeDp()
val imageHugeLiteFrame = 72.composeDp()
val imageHuge = 80.composeDp()
val imageHugeFrame = 94.composeDp()
val imageHugeFramePlus = 114.composeDp()

val moneyWidth = 110.composeDp()
val moneyHeight = 28.composeDp()
val singleRoleWidth = 92.composeDp()
val singleRoleHeight = 110.composeDp()

val gapSmall = 36.composeDp()
val gapSmallPlus = 46.composeDp()
val gapMedium = 60.composeDp()
val gapLarge = 80.composeDp()
val gapHugeMinus = 98.composeDp()
val gapHuge = 110.composeDp()
val gapHugePlus = 130.composeDp()
val gapHugePlusPlus = 230.composeDp()


val padding0 = 0.composeDp()
val padding1 = 1.composeDp()
val padding2 = 2.composeDp()
val padding3 = 3.composeDp()
val padding4 = 4.composeDp()
val padding5 = 5.composeDp()
val padding6 = 6.composeDp()
val padding7 = 7.composeDp()
val padding8 = 8.composeDp()
val padding10 = 10.composeDp()
val padding12 = 12.composeDp()
val padding14 = 14.composeDp()
val padding16 = 16.composeDp()
val padding19 = 19.composeDp()
val padding22 = 22.composeDp()
val padding26 = 26.composeDp()
val padding28 = 28.composeDp()
val padding30 = 30.composeDp()
val padding36 = 36.composeDp()
val padding40 = 40.composeDp()
val padding42 = 42.composeDp()
val padding45 = 45.composeDp()
val padding48 = 48.composeDp()
val padding51 = 51.composeDp()
val padding60 = 60.composeDp()
val padding64 = 64.composeDp()
val padding66 = 66.composeDp()
val padding69 = 69.composeDp()
val padding72 = 72.composeDp()
val padding80 = 80.composeDp()
val padding84 = 84.composeDp()
val padding90 = 90.composeDp()
val padding93 = 93.composeDp()
val padding96 = 96.composeDp()
val padding100 = 100.composeDp()
val padding110 = 110.composeDp()
val padding120 = 120.composeDp()
val padding130 = 130.composeDp()
val padding150 = 150.composeDp()
val padding165 = 165.composeDp()
val padding170 = 170.composeDp()
val padding180 = 180.composeDp()
val padding200 = 200.composeDp()
val padding226 = 226.composeDp()
val padding260 = 260.composeDp()
val padding280 = 280.composeDp()
val padding300 = 300.composeDp()
val padding330 = 330.composeDp()
val padding360 = 360.composeDp()
val padding380 = 380.composeDp()
val padding420 = 420.composeDp()
val padding460 = 460.composeDp()
val padding600 = 600.composeDp()
val padding620 = 620.composeDp()
val padding780 = 780.composeDp()

val paddingTiny = 2.composeDp()
val paddingSmall = 4.composeDp()
val paddingSmallPlus = 6.composeDp()
val paddingMediumMinis = 8.composeDp()
val paddingMedium = 10.composeDp()
val paddingMediumPlus = 12.composeDp()
val paddingLargeMinus = 14.composeDp()
val paddingLarge = 16.composeDp()
val paddingLargePlus = 19.composeDp()
val paddingHugeLite = 22.composeDp()
val paddingHuge = 26.composeDp()
val paddingHugePlus = 30.composeDp()
val paddingHugePlusPlus = 36.composeDp()

val gameAlertDialogTitleHeight = 70.composeDp()

val hugeButtonWidth = 190.composeDp()
val hugeButtonHeight = 84.composeDp()
val bigButtonWidth = 168.composeDp()
val bigButtonHeight = 54.composeDp()
val buttonMinusWidth = 82.composeDp()
val buttonMinusHeight = 30.composeDp()
val buttonWidth = 108.composeDp()
val buttonHeight = 38.composeDp()
val smallButtonWidth = 54.composeDp()
val smallButtonHeight = 26.composeDp()
val taskNameHeight = 36.composeDp()
val tutorTitleWidth = 88.composeDp()
val tutorTitleHeight = 36.composeDp()

val tabButtonHeight = 42.composeDp()

val animateTiny = 8.composeDp()
val animateSmall = 12.composeDp()
val animateLarge = 24.composeDp()

val alertWidth = 500.composeDp()
val alertHeight = 352.composeDp()

val challengeDialogHeight = 420.composeDp()
val detailBigHeight = 580.composeDp()
val expBarHeight = 20.composeDp()
val roleInfoHeight = 88.composeDp()

// 战斗相关
val buffSize = 16.composeDp()

val cardWidth = 90.composeDp()
val cardHeight = 106.composeDp()
val shakeDp = 6.composeDp()

val noticeHeight = 100.composeDp()

val backIconHeight = 42.composeDp()
val titleHeight = 48.composeDp()

val roleEffectWidth = 220.composeDp()
val roleEffectHeight = 300.composeDp()
val upgradeEffectWidth = 400.composeDp()


val codeInputWidth = 210.composeDp()
val codeInputHeight = 40.composeDp()

val chartWidth = 220.composeDp()
val chartHeight = 140.composeDp()

val textFieldHeight = 40.composeDp()

val cardStarSize = 13.composeDp()
val cardStarBigSize = 20.composeDp()

val hpWidth = 68.composeDp()
val hpHeight = 12.composeDp()

val filterWidth = 88.composeDp()
val filterHeight = 30.composeDp()

val propertyBigWidth = 78.composeDp()
val propertyBigHeight = 22.composeDp()

val userHeadWidth = 198.composeDp()
val userHeadHeight = 62.composeDp()

val turnEffectWidth = 120.composeDp()

val userHeadImageSize = 46.composeDp()

val propertyBigImageSize = 20.composeDp()

val eventCardWidth = 136.composeDp()
val eventCardHeight = 222.composeDp()
val eventCardBigWidth = 236.composeDp()
val eventCardBigHeight = 350.composeDp()



val oneRoleWidth = 136.composeDp()

val shopItemWidth = 118.composeDp()
val shopItemHeight = 128.composeDp()

val slideWidth = 180.composeDp()
val slideHeight = 36.composeDp()

val rankIndexOneDigitWidth = 12.composeDp()
val rankIndexOneDigitHeight = 20.composeDp()
val recordFrameHeight80 = 60.composeDp()
val pvpRecordFrameHeight = 84.composeDp()

val dialogFrameHeight = 180.composeDp()

val answerWidth = 400.composeDp()
val answerHeight = 60.composeDp()

val countryLevelWidth = 110.composeDp()
val countryLevelHeight = 80.composeDp()
val eventTopLayoutHeight = 200.composeDp()

val elementWidth = 116.composeDp()
val elementHeight = 22.composeDp()

val tagWidth = 112.composeDp()

val labelWidth = 248.composeDp()
val labelHeight = 68.composeDp()

val settingSize = 42.composeDp()
val settingBgHeight = 54.composeDp()

val tcgItemWidth = 128.composeDp()
val tcgItemHeight = 200.composeDp()



val cheatBarWidth = 164.composeDp()
val cheatBarHeight = 26.composeDp()

val gatherFrameHeight = 58.composeDp()
val cheatFrameHeight = 122.composeDp()
val cheatDecHeight = 322.composeDp()

val reputationItemWidth = 105.composeDp()
val reputationItemHeight = 168.composeDp()

val dialogWidth = 400.composeDp()

val smallDialogHeight = 280.composeDp()


val createCountryLabelHeight = 160.composeDp()
val createCountryPanelHeight = 180.composeDp()


val reputationDialogLabelBarWidth = 98.composeDp()
val reputationDialogLabelBarHeight = 48.composeDp()