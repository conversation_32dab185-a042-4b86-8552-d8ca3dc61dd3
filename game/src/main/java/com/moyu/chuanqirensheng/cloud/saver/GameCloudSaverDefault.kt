package com.moyu.chuanqirensheng.cloud.saver

import byte2HexWithBlank
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.api.CommonResult
import com.moyu.chuanqirensheng.api.RetrofitModel
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.config.compressZip4j
import com.moyu.chuanqirensheng.config.openText
import com.moyu.chuanqirensheng.config.saveText
import com.moyu.chuanqirensheng.config.uncompressZip4j
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.util.getVersions
import com.moyu.chuanqirensheng.util.triggerRebirth
import com.moyu.core.DS_NAME
import hex2Bytes
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import kotlinx.uuid.UUID
import kotlinx.uuid.generateUUID
import timber.log.Timber
import java.io.File


val savePath =
    GameApp.instance.filesDir.absolutePath + File.separator + "datastore" + File.separator + DS_NAME +".preferences_pb"

class GameCloudSaverDefault : GameCloudSaver {
    override suspend fun uploadCurrentSave(): CommonResult {
        GameApp.instance.getObjectId()?.let { objectId->
            getCurrentSave()?.let {
                val gameSave = GameSave(objectId, System.currentTimeMillis(), it)
                try {
                    return RetrofitModel.postGameSave(gameSave)
                } catch (e: Exception) {
                    Timber.e(e)
                    return CommonResult(false, GameApp.instance.getWrapString(R.string.sync_error))
                }
            }?: GameApp.instance.getWrapString(R.string.pack_error).toast()
        }
        return CommonResult(false, GameApp.instance.getWrapString(R.string.error_sync2))
    }

    override suspend fun getCloudSave(): GameSave? {
        return try {
            RetrofitModel.getGameSave(GameApp.instance.getObjectId()!!)
        } catch (e: Exception) {
            Timber.e(e)
            GameApp.instance.getWrapString(R.string.no_save).toast()
            null
        }
    }

    override suspend fun useThisCloudSave(save: GameSave) {
        withContext(Dispatchers.IO) {
            try {
                if (!RetrofitModel.tryUseGameSave(GameApp.instance.getObjectId()!!)) {
                    GameApp.instance.getWrapString(R.string.too_many_use_save_tips).toast()
                } else {
                    val targetFile =
                        File(GameApp.instance.cacheDir.absolutePath + File.separator + "tmp.save")
                    saveText(targetFile.absolutePath, hex2Bytes(save.save))
                    val uncompressTargetFile =
                        GameApp.instance.cacheDir.absolutePath + File.separator + "uncompressed.save"
                    GameApp.instance.getObjectId()?.let {
                        uncompressZip4j(
                            targetFile.absolutePath,
                            uncompressTargetFile,
                            it + getVersions()
                        )
                        File(uncompressTargetFile + File.separator + DS_NAME + ".preferences_pb").renameTo(
                            File(savePath)
                        )
                    } ?: GameApp.instance.getWrapString(R.string.id_error).toast()
                    GameApp.instance.getWrapString(R.string.sync_tips).toast()
                    delay(2000)
                    triggerRebirth()
                }
            } catch (e: Exception) {
                Timber.e(e)
                (GameApp.instance.getWrapString(R.string.error_operate) + e.message).toast()
            }
        }
    }
}

suspend fun getCurrentSave(): String? {
    return withContext(Dispatchers.IO) {
        try {
            val zipFilePath = GameApp.instance.cacheDir.absolutePath + File.separator + "compressed.zip"
            GameApp.instance.getObjectId()?.let { userId ->
                compressZip4j(zipFilePath, savePath, userId + getVersions())
            } ?: GameApp.instance.getWrapString(R.string.upload_exception).toast()
            byte2HexWithBlank(openText(zipFilePath))
        } catch (e: Exception) {
            val zipFilePath = GameApp.instance.cacheDir.absolutePath + File.separator + UUID.generateUUID().toString().take(16) + ".zip"
            GameApp.instance.getObjectId()?.let { userId ->
                compressZip4j(zipFilePath, savePath, userId + getVersions())
            } ?: GameApp.instance.getWrapString(R.string.upload_exception).toast()
            byte2HexWithBlank(openText(zipFilePath))
        }
    }
}