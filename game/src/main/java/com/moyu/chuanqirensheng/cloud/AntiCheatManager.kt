package com.moyu.chuanqirensheng.cloud

import com.example.codes.ally1Codes
import com.example.codes.ally2Codes
import com.example.codes.ally3Codes
import com.example.codes.ally3_a_Codes
import com.example.codes.ally4Codes
import com.example.codes.hero1Codes
import com.example.codes.hero2Codes
import com.example.codes.hero3Codes
import com.example.codes.hero4Codes
import com.example.codes.skill1Codes
import com.example.codes.skill2Codes
import com.example.codes.skill3Codes
import com.moyu.chuanqirensheng.BuildConfig
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.datastore.KEY_CHEATING
import com.moyu.chuanqirensheng.datastore.dataStore
import com.moyu.chuanqirensheng.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.logic.award.VipManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.core.config.ALLY_FILE_NAME
import com.moyu.core.config.DAY_REWARD_FILE_NAME
import com.moyu.core.config.GIFT_FILE_NAME
import com.moyu.core.config.HERO_FILE_NAME
import com.moyu.core.config.HERO_SKILL_FILE_NAME
import com.moyu.core.config.SCROLL_FILE_NAME
import com.moyu.core.config.SELL_FILE_NAME
import com.moyu.core.config.SKILL_FILE_NAME
import com.moyu.core.config.TALENT_FILE_NAME
import com.moyu.core.config.TALENT_SKILL_FILE_NAME
import com.moyu.core.config.VIP_FILE_NAME
import com.moyu.core.config.WAR_PASS2_FILE_NAME
import com.moyu.core.config.WAR_PASS_FILE_NAME
import com.moyu.core.model.BattlePass
import com.moyu.core.model.Vip
import com.moyu.core.model.ally.Ally
import com.moyu.core.model.sell.Sell
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.talent.Talent
import com.moyu.core.model.task.GameTask
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.first

val mapping = mapOf(
    'A' to 'X',
    'B' to 'H',
    'C' to 'Q',
    'D' to 'N',
    'E' to 'Y',
    'F' to 'U',
    'G' to 'Z',
    'H' to 'O',
    'I' to 'E',
    'J' to 'P',
    'K' to 'A',
    'L' to 'T',
    'M' to 'F',
    'N' to 'W',
    'O' to 'V',
    'P' to 'G',
    'Q' to 'K',
    'R' to 'C',
    'S' to 'M',
    'T' to 'I',
    'U' to 'J',
    'V' to 'R',
    'W' to 'L',
    'X' to 'S',
    'Y' to 'D',
    'Z' to 'B',
    '0' to '4',
    '1' to '7',
    '2' to '2',
    '3' to '8',
    '4' to '6',
    '5' to '0',
    '6' to '5',
    '7' to '9',
    '8' to '3',
    '9' to '1'
)

fun reverseMapString(mappedString: String, reverseMappingTable: Map<Char, Char>): String {
    val originalStringBuilder = StringBuilder()
    for (char in mappedString) {
        val originalChar = reverseMappingTable[char] ?: char
        originalStringBuilder.append(originalChar)
    }
    return originalStringBuilder.toString()
}

fun isInWhiteList(): Boolean {
    return GameApp.instance.getUserName()?.let {
        it == "傻了吧唧的妗画"
                || it == "深渊恶魔的凝视"
                || it == "纪明"
                || it  == "手机用户84556060"
    }?: false
}

fun starMap(star: Int): Int {
    return when (star) {
        0 -> 1
        1 -> 1
        2 -> 2
        3 -> 5
        4 -> 9
        else -> 15
    }
}
object AntiCheatManager {

    fun init() {
        // null
    }

    suspend fun isCheating(): Boolean {
        if (isInWhiteList()) return false

        if (VipManager.isCheat()) {
            if (BuildConfig.FLAVOR.contains("Lite")) {
                "电力作弊".toast()
                delay(2000)
            } else {
                setBooleanValueByKey(KEY_CHEATING, true)
                return true
            }
        }

//        val count = GameApp.instance.dataStore.data.first()
//            .asMap().keys.filter { it.name.length == 32 }.size
//        val needFromAiFaDianAlly =
//            repo.allyManager.data.filter { it.isAiFaDianAlly() }.map { it.star }.map {
//                starMap(it)
//            }.sum()
//        val needFromAiFaDianSkill =
//            repo.skillManager.data.filter { it.isAiFaDianSkill() }.map { it.level }.map {
//                starMap(it)
//            }.sum()
//        val needFromAiFaDianHero =
//            repo.heroManager.data.filter { it.isAiFaDianHero() }.map { it.level }.map {
//                starMap(it)
//            }.sum()
//        val keySumAlly = repo.allyManager.data.filter { it.isOrange() }.map { it.star }.map {
//            starMap(it)
//        }.sum() * 600
//        val keySumSkill = repo.skillManager.data.filter { it.isOrange() }.map { it.level }.map {
//            starMap(it)
//        }.sum() * 600
//        val keySumEquip = repo.heroManager.data.filter { it.isOrange() }.map { it.level }.map {
//            starMap(it)
//        }.sum() * 600
//        val needFromOrange =
//            Integer.max(0, (keySumAlly + keySumSkill + keySumEquip) / 16400 - 5) // todo 随着时间推移要改
//        val result =
//            count < needFromAiFaDianAlly + needFromAiFaDianHero + needFromAiFaDianSkill + needFromOrange
//        if (result) {
//            "游戏数据可能存在异常,后续可能会做进一步处理".toast()
////            setBooleanValueByKey(KEY_CHEATING, true)
//        }
//        if (BuildConfig.FLAVOR.contains("Lite")) {
//            "兑换次数=$count, 特典军团$needFromAiFaDianAlly,特典技能$needFromAiFaDianSkill,特典史诗人物$needFromAiFaDianHero,橙卡$needFromOrange".toast()
//            setBooleanValueByKey(KEY_CHEATING, false)
//            return false
//        }
//        if (getBooleanFlowByKey(KEY_CHEATING, false)) {
//            return true
//        }
        return false
    }

    suspend fun isPostCheating(): Boolean {
        if (isInWhiteList()) return false
        val keys = GameApp.instance.dataStore.data.first()
            .asMap().keys.filter { it.name.length == 32 }.map {
                reverseMapString(it.name.take(27), mapping)
            }

        val needFromAiFaDianAlly1 =
            repo.allyManager.data.filter { it.isAiFaDianAlly1() }.map { it.star }.map {
                starMap(it)
            }.sum()
        if (needFromAiFaDianAlly1 > 0) {
            val aiFaDianAlly1Codes = keys.count { it in ally1Codes }
            if (aiFaDianAlly1Codes < needFromAiFaDianAlly1) {
                if (BuildConfig.FLAVOR.contains("Lite")) {
                    "龙骑兵需要${needFromAiFaDianAlly1}次兑换，实际${aiFaDianAlly1Codes}次，判定作弊".toast()
                    delay(2000)
                } else {
                    "游戏数据可能存在异常,无法登录".toast()
                    setBooleanValueByKey(KEY_CHEATING, true)
                    return true
                }
            }
        }

        val needFromAiFaDianAlly2 =
            repo.allyManager.data.filter { it.isAiFaDianAlly2() }.map { it.star }.map {
                starMap(it)
            }.sum()
        if (needFromAiFaDianAlly2 > 0) {
            val aiFaDianAlly2Codes = keys.count { it in ally2Codes }
            if (aiFaDianAlly2Codes < needFromAiFaDianAlly2) {
                if (BuildConfig.FLAVOR.contains("Lite")) {
                    "无敌舰队需要${needFromAiFaDianAlly2}次兑换，实际${aiFaDianAlly2Codes}次，判定作弊".toast()
                    delay(2000)
                } else {
                    "游戏数据可能存在异常,无法登录".toast()
                    setBooleanValueByKey(KEY_CHEATING, true)
                    return true
                }
            }
        }

        val needFromAiFaDianAlly3 =
            repo.allyManager.data.filter { it.isAiFaDianAlly3() }.map { it.star }.map {
                starMap(it)
            }.sum()
        if (needFromAiFaDianAlly3 > 0) {
            val aiFaDianAlly3Codes = keys.count { it in ally3Codes + ally3_a_Codes }
            if (aiFaDianAlly3Codes < needFromAiFaDianAlly3) {
                if (BuildConfig.FLAVOR.contains("Lite")) {
                    "虎豹骑需要${needFromAiFaDianAlly3}次兑换，实际${aiFaDianAlly3Codes}次，判定作弊".toast()
                    delay(2000)
                } else {
                    "游戏数据可能存在异常,无法登录".toast()
                    setBooleanValueByKey(KEY_CHEATING, true)
                    return true
                }
            }
        }

        val needFromAiFaDianAlly4 =
            repo.allyManager.data.filter { it.isAiFaDianAlly4() }.map { it.star }.map {
                starMap(it)
            }.sum()
        if (needFromAiFaDianAlly4 > 0) {
            val aiFaDianAlly4Codes = keys.count { it in ally4Codes }
            if (aiFaDianAlly4Codes < needFromAiFaDianAlly4) {
                if (BuildConfig.FLAVOR.contains("Lite")) {
                    "女武神需要${needFromAiFaDianAlly4}次兑换，实际${aiFaDianAlly4Codes}次，判定作弊".toast()
                    delay(2000)
                } else {
                    "游戏数据可能存在异常,无法登录".toast()
                    setBooleanValueByKey(KEY_CHEATING, true)
                    return true
                }
            }
        }

        val needFromAiFaDianSkill1 =
            repo.skillManager.data.filter { it.isAiFaDianSkill1() }.map { it.level }.map {
                starMap(it)
            }.sum()
        if (needFromAiFaDianSkill1 > 0) {
            val aiFaDianSkill1Codes = keys.count { it in skill1Codes }
            if (aiFaDianSkill1Codes < needFromAiFaDianSkill1) {
                if (BuildConfig.FLAVOR.contains("Lite")) {
                    "荣冠战争需要${needFromAiFaDianSkill1}次兑换，实际${aiFaDianSkill1Codes}次，判定作弊".toast()
                    delay(2000)
                } else {
                    "游戏数据可能存在异常,无法登录".toast()
                    setBooleanValueByKey(KEY_CHEATING, true)
                    return true
                }
            }
        }

        val needFromAiFaDianSkill2 =
            repo.skillManager.data.filter { it.isAiFaDianSkill2() }.map { it.level }.map {
                starMap(it)
            }.sum()
        if (needFromAiFaDianSkill2 > 0) {
            val aiFaDianSkill2Codes = keys.count { it in skill2Codes }
            if (aiFaDianSkill2Codes < needFromAiFaDianSkill2) {
                if (BuildConfig.FLAVOR.contains("Lite")) {
                    "追魂箭需要${needFromAiFaDianSkill2}次兑换，实际${aiFaDianSkill2Codes}次，判定作弊".toast()
                    delay(2000)
                } else {
                    "游戏数据可能存在异常,无法登录".toast()
                    setBooleanValueByKey(KEY_CHEATING, true)
                    return true
                }
            }
        }

        val needFromAiFaDianSkill3 =
            repo.skillManager.data.filter { it.isAiFaDianSkill3() }.map { it.level }.map {
                starMap(it)
            }.sum()
        if (needFromAiFaDianSkill3 > 0) {
            val aiFaDianSkill3Codes = keys.count { it in skill3Codes }
            if (aiFaDianSkill3Codes < needFromAiFaDianSkill3) {
                if (BuildConfig.FLAVOR.contains("Lite")) {
                    "金刚夜叉箴言需要${needFromAiFaDianSkill3}次兑换，实际${aiFaDianSkill3Codes}次，判定作弊".toast()
                    delay(2000)
                } else {
                    "游戏数据可能存在异常,无法登录".toast()
                    setBooleanValueByKey(KEY_CHEATING, true)
                    return true
                }
            }
        }

        val needFromAiFaDianHero1 =
            repo.heroManager.data.filter { it.isAiFaDianHero1() }.map { it.level }.map {
                starMap(it)
            }.sum()
        if (needFromAiFaDianHero1 > 0) {
            val aiFaDianHero1Codes = keys.count { it in hero1Codes }
            if (aiFaDianHero1Codes < needFromAiFaDianHero1) {
                if (BuildConfig.FLAVOR.contains("Lite")) {
                    "达芬奇需要${needFromAiFaDianHero1}次兑换，实际${aiFaDianHero1Codes}次，判定作弊".toast()
                    delay(2000)
                } else {
                    "游戏数据可能存在异常,无法登录".toast()
                    setBooleanValueByKey(KEY_CHEATING, true)
                    return true
                }
            }
        }

        val needFromAiFaDianHero2 =
            repo.heroManager.data.filter { it.isAiFaDianHero2() }.map { it.level }.map {
                starMap(it)
            }.sum()
        if (needFromAiFaDianHero2 > 0) {
            val aiFaDianHero2Codes = keys.count { it in hero2Codes }
            if (aiFaDianHero2Codes < needFromAiFaDianHero2) {
                if (BuildConfig.FLAVOR.contains("Lite")) {
                    "赫拉克勒斯需要${needFromAiFaDianHero2}次兑换，实际${aiFaDianHero2Codes}次，判定作弊".toast()
                    delay(2000)
                } else {
                    "游戏数据可能存在异常,无法登录".toast()
                    setBooleanValueByKey(KEY_CHEATING, true)
                    return true
                }
            }
        }

        val needFromAiFaDianHero3 =
            repo.heroManager.data.filter { it.isAiFaDianHero3() }.map { it.level }.map {
                starMap(it)
            }.sum()
        if (needFromAiFaDianHero3 > 0) {
            val aiFaDianHero3Codes = keys.count { it in hero3Codes }
            if (aiFaDianHero3Codes < needFromAiFaDianHero3) {
                if (BuildConfig.FLAVOR.contains("Lite")) {
                    "曹操需要${needFromAiFaDianHero3}次兑换，实际${aiFaDianHero3Codes}次，判定作弊".toast()
                    delay(2000)
                } else {
                    "游戏数据可能存在异常,无法登录".toast()
                    setBooleanValueByKey(KEY_CHEATING, true)
                    return true
                }
            }
        }

        val needFromAiFaDianHero4 =
            repo.heroManager.data.filter { it.isAiFaDianHero4() }.map { it.level }.map {
                starMap(it)
            }.sum()
        if (needFromAiFaDianHero4 > 0) {
            val aiFaDianHero4Codes = keys.count { it in hero4Codes }
            if (aiFaDianHero4Codes < needFromAiFaDianHero4) {
                if (BuildConfig.FLAVOR.contains("Lite")) {
                    "姬发需要${needFromAiFaDianHero4}次兑换，实际${aiFaDianHero4Codes}次，判定作弊".toast()
                    delay(2000)
                } else {
                    "游戏数据可能存在异常,无法登录".toast()
                    setBooleanValueByKey(KEY_CHEATING, true)
                    return true
                }
            }
        }

        return false
    }

    val antiConfigs = listOf(
        ALLY_FILE_NAME,
        SCROLL_FILE_NAME,
        SKILL_FILE_NAME,
        VIP_FILE_NAME,
        GIFT_FILE_NAME,
        TALENT_FILE_NAME,
        TALENT_SKILL_FILE_NAME,
        SELL_FILE_NAME,
        HERO_FILE_NAME,
        DAY_REWARD_FILE_NAME,
        HERO_SKILL_FILE_NAME,
        WAR_PASS_FILE_NAME,
        WAR_PASS2_FILE_NAME
    )

    suspend fun checkPoolCheck() {
//        ConfigManager.configLoaders.filter { it.getKey() in antiConfigs }.forEach {
//            val list2 = it.reLoadConfig()
//            val list1 = repo.gameCore.getPoolByKeyAny(it.getKey())
//            list1.forEachIndexed { index, configData ->
//                if (configData != list2[index]) {
//                    setBooleanValueByKey(KEY_CHEATING, true)
//                    "疑似配置修改作弊，如为误杀请反馈".toast()
//                    delay(3000)
//                    error("配置修改作弊:${GameApp.instance.getObjectId()}")
//                }
//            }
//        }
    }

    suspend fun checkSell(sell: Sell) {
//        ConfigManager.configLoaders.first { it.getKey() == SELL_FILE_NAME }.let {
//            it.reLoadConfig().first { (it as Sell).id == sell.id }.let {
//                if (it != sell.copy(storage = (it as Sell).storage)) { // 对比的是最终要买的sell，storage可能变了
//                    setBooleanValueByKey(KEY_CHEATING, true)
//                    "疑似配置修改作弊，如为误杀请反馈".toast()
//                    delay(3000)
//                    error("配置修改作弊:${GameApp.instance.getObjectId()}")
//                }
//            }
//        }
    }

    suspend fun checkTalent(talent: Talent) {
//        ConfigManager.configLoaders.first { it.getKey() == TALENT_FILE_NAME }.let {
//            it.reLoadConfig().first { (it as Talent).id == talent.id }.let {
//                if (it != talent) { // 对比的是最终要买的sell，storage可能变了
//                    setBooleanValueByKey(KEY_CHEATING, true)
//                    "疑似配置修改作弊，如为误杀请反馈".toast()
//                    delay(3000)
//                    error("配置修改作弊:${GameApp.instance.getObjectId()}")
//                }
//            }
//        }
    }

    suspend fun checkQuest(task: GameTask) {
//        ConfigManager.configLoaders.first { it.getKey() == TASK_FILE_NAME }.let {
//            it.reLoadConfig().first { (it as GameTask).id == task.id }.let {
//                if (it != task.copy(opened = false, done = false, needRemoveCount = 0)) {
//                    setBooleanValueByKey(KEY_CHEATING, true)
//                    "疑似配置修改作弊，如为误杀请反馈".toast()
//                    delay(3000)
//                    error("配置修改作弊:${GameApp.instance.getObjectId()}")
//                }
//            }
//        }
    }

    suspend fun checkAlly(ally: Ally) {
//        ConfigManager.configLoaders.first { it.getKey() == ALLY_FILE_NAME }.let {
//            it.reLoadConfig().first { (it as Ally).id == ally.id }.let {
//                val configAlly = it as Ally
//                if (configAlly.star != ally.star || configAlly.starUpNum != ally.starUpNum) { // 对比的是最终要买的sell，storage可能变了
//                    setBooleanValueByKey(KEY_CHEATING, true)
//                    "疑似配置修改作弊，如为误杀请反馈".toast()
//                    delay(3000)
//                    error("配置修改作弊:${GameApp.instance.getObjectId()}")
//                }
//            }
//        }
    }

    suspend fun checkHero(ally: Skill) {
//        ConfigManager.configLoaders.first { it.getKey() == HERO_SKILL_FILE_NAME }.let {
//            it.reLoadConfig().first { (it as Skill).id == ally.id }.let {
//                val configAlly = it as Skill
//                if (configAlly.level != ally.level) { // 对比的是最终要买的sell，storage可能变了
//                    setBooleanValueByKey(KEY_CHEATING, true)
//                    "疑似配置修改作弊，如为误杀请反馈".toast()
//                    delay(3000)
//                    error("配置修改作弊:${GameApp.instance.getObjectId()}")
//                }
//            }
//        }
    }

    suspend fun checkVip(vip: Vip) {
//        ConfigManager.configLoaders.first { it.getKey() == VIP_FILE_NAME }.let {
//            it.reLoadConfig().first { (it as Vip).id == vip.id }.let {
//                val configAlly = it as Vip
//                if (configAlly.level != vip.level) { // 对比的是最终要买的sell，storage可能变了
//                    setBooleanValueByKey(KEY_CHEATING, true)
//                    "疑似配置修改作弊，如为误杀请反馈".toast()
//                    delay(3000)
//                    error("配置修改作弊:${GameApp.instance.getObjectId()}")
//                }
//            }
//        }
    }

    suspend fun checkWarPass(battlePass: BattlePass) {
//        ConfigManager.configLoaders.first { it.getKey() == WAR_PASS_FILE_NAME }.let {
//            it.reLoadConfig().first { (it as BattlePass).id == battlePass.id }.let {
//                val configAlly = it as BattlePass
//                if (configAlly.level != battlePass.level) { // 对比的是最终要买的sell，storage可能变了
//                    setBooleanValueByKey(KEY_CHEATING, true)
//                    "疑似配置修改作弊，如为误杀请反馈".toast()
//                    delay(3000)
//                    error("配置修改作弊:${GameApp.instance.getObjectId()}")
//                }
//            }
//        }
    }

    suspend fun checkWarPass2(battlePass: BattlePass) {
//        ConfigManager.configLoaders.first { it.getKey() == WAR_PASS2_FILE_NAME }.let {
//            it.reLoadConfig().first { (it as BattlePass).id == battlePass.id }.let {
//                val configAlly = it as BattlePass
//                if (configAlly.level != battlePass.level) { // 对比的是最终要买的sell，storage可能变了
//                    setBooleanValueByKey(KEY_CHEATING, true)
//                    "疑似配置修改作弊，如为误杀请反馈".toast()
//                    delay(3000)
//                    error("配置修改作弊:${GameApp.instance.getObjectId()}")
//                }
//            }
//        }
    }
}