package com.moyu.chuanqirensheng.cloud

import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.toast
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

object CloudManager {
    suspend fun init() {
        if (GameApp.instance.canDoNetwork()) {
            if (!GameApp.newUser) {
                GameApp.globalScope.launch(Dispatchers.IO) {
                    async {
                        delay(25 * 60 * 1000)
                        GameApp.instance.uploadCurrentSave().apply {
                            GameApp.instance.getWrapString(R.string.auto_sync_tips).toast()
                        }
                        delay(5 * 60 * 1000)
                    }
                }
            }
        }

        GameApp.globalScope.launch(Dispatchers.IO) {
            async {
                while (true) {
                    delay(30 * 1000)
                    AntiCheatManager.checkPoolCheck()
                }
            }
        }
    }

    fun checkIfNewUserHaveCloudSave() {
        if (GameApp.instance.canDoNetwork()) {
            GameApp.globalScope.launch(Dispatchers.IO) {
                delay(3000)
                GameApp.instance.getCloudSave()?.let {
                    Dialogs.useSaveDialog.value = it
                }
            }
        }
    }
}