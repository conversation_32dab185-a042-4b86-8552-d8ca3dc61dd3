package com.moyu.chuanqirensheng.cloud.privacy

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.datastore.KEY_NEED_SHOW_PERMISSION
import com.moyu.chuanqirensheng.datastore.KEY_NEED_SHOW_PRIVACY
import com.moyu.chuanqirensheng.datastore.KEY_VERIFIED
import com.moyu.chuanqirensheng.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.repository.Dialogs

object PrivacyManager {
    var privacyNeedShow by mutableStateOf(false)
    var permissionNeedShow by mutableStateOf(false)
    var antiAddictVerified by mutableStateOf(false)

    fun init() {
        antiAddictVerified = getBooleanFlowByKey(KEY_VERIFIED, !GameApp.instance.resources.getBoolean(
            R.bool.need_anti_addict_check))
        privacyNeedShow = getBooleanFlowByKey(KEY_NEED_SHOW_PRIVACY, GameApp.instance.resources.getBoolean(
            R.bool.need_privacy_check))
        Dialogs.showPrivacyDialog.value = privacyNeedShow
        permissionNeedShow = getBooleanFlowByKey(KEY_NEED_SHOW_PERMISSION, GameApp.instance.resources.getBoolean(
            R.bool.need_permission_check))
        if (!privacyNeedShow && permissionNeedShow) {
            Dialogs.showPermissionDialog.value = true
        }
    }
}