package com.moyu.chuanqirensheng.cloud.bill

import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.repository.repo

const val KEY_FIRST_CHARGE = "110670"
const val KEY_150 = "3001"
const val KEY_750 = "3002"
const val KEY_2450 = "3003"
const val KEY_4950 = "3004"
const val KEY_8200 = "3005"
const val ALLY_1 = "1001"
const val ALLY_2 = "1002"
const val ALLY_3 = "1003"
const val ALLY_4 = "1004"
const val ALLY_5 = "1013"
const val HERO_1 = "1005"
const val HERO_2 = "1006"
const val HERO_3 = "1007"
const val HERO_4 = "1008"
const val HERO_5 = "1015"
const val SKILL_1 = "1009"
const val SKILL_2 = "1010"
const val SKILL_3 = "1011"
const val SKILL_4 = "1012"
const val SKILL_5 = "1014"
const val BATTLE_PASS_1 = "10001"
const val BATTLE_PASS_2 = "10002"
const val BATTLE_PASS2_1 = "10011"
const val STORY_1 = "2002"
const val STORY_2 = "2004"
const val STORY_3 = "2005"
const val STORY_4 = "2006"

data class KeyItems(
    val text: String,
    val url: String,
    val discount: String = "",
    val dollar: String = ""
)

val keyOfAifadian = listOf(
    KeyItems("25", repo.gameCore.getGetKeyUrls()[0], ""),
    KeyItems("150", repo.gameCore.getGetKeyUrls()[1], ""),
    KeyItems("750+85", repo.gameCore.getGetKeyUrls()[2], GameApp.instance.getWrapString(R.string.extra_gain) + "\n85"),
    KeyItems("2450+650", repo.gameCore.getGetKeyUrls()[3], GameApp.instance.getWrapString(R.string.extra_gain) + "\n650"),
    KeyItems(
        "4950+1850",
        repo.gameCore.getGetKeyUrls()[4],
        GameApp.instance.getWrapString(R.string.extra_gain) + "\n1850"
    ),
    KeyItems(
        "8200+8200",
        repo.gameCore.getGetKeyUrls()[5],
        GameApp.instance.getWrapString(R.string.extra_gain) + "\n8200"
    ),
)

val keyOfGoogleBill = listOf(
    KeyItems("150", KEY_150, "", "0.99$"),
    KeyItems("750+85", KEY_750, GameApp.instance.getWrapString(R.string.extra_gain) + "\n85", "4.99$"),
    KeyItems("2450+650", KEY_2450, GameApp.instance.getWrapString(R.string.extra_gain) + "\n650", "14.99$"),
    KeyItems("4950+1850", KEY_4950, GameApp.instance.getWrapString(R.string.extra_gain) + "\n1850", "29.99$"),
    KeyItems("8200+8200", KEY_8200, GameApp.instance.getWrapString(R.string.extra_gain) + "\n8200", "49.99$"),
)

val keyOfWechatPay = listOf(
    KeyItems("150", KEY_150, "", "6元"),
    KeyItems("750+85", KEY_750, GameApp.instance.getWrapString(R.string.extra_gain) + "\n85", "30元"),
    KeyItems("2450+650", KEY_2450, GameApp.instance.getWrapString(R.string.extra_gain) + "\n650", "98元"),
    KeyItems("4950+1850", KEY_4950, GameApp.instance.getWrapString(R.string.extra_gain) + "\n1850", "198元"),
    KeyItems("8200+8200", KEY_8200, GameApp.instance.getWrapString(R.string.extra_gain) + "\n8200", "328元"),
)

