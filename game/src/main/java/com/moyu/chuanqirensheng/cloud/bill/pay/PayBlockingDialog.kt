package com.moyu.chuanqirensheng.cloud.bill.pay

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.ui.theme.alertWidth
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding460
import com.moyu.chuanqirensheng.ui.theme.paddingHuge
import com.moyu.chuanqirensheng.ui.theme.paddingLarge
import com.moyu.chuanqirensheng.ui.theme.paddingLargePlus
import kotlinx.coroutines.delay

@Composable
fun PayBlockDialog(
    switch: MutableState<String?>
) {
    if (switch.value != null) {
        val canCancel = remember {
            mutableStateOf(false)
        }
        LaunchedEffect(Unit) {
            delay(30000)
            canCancel.value = true
        }
        Dialog(
            onDismissRequest = { switch.value = null },
            properties = DialogProperties(
                dismissOnBackPress = false,
                dismissOnClickOutside = false,
                usePlatformDefaultWidth = false
            )
        ) {
            Box(
                modifier = Modifier
                    .size(alertWidth, padding460)
                    .padding(paddingLarge)
            ) {
                Image(
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.FillBounds,
                    painter = painterResource(R.drawable.common_window),
                    contentDescription = null
                )
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = paddingHuge)
                ) {
                    Spacer(modifier = Modifier.size(padding19))
                    Text(
                        text = stringResource(R.string.order_confirming),
                        style = MaterialTheme.typography.h2,
                        color = Color.Black
                    )
                    Spacer(modifier = Modifier.size(paddingLargePlus))
                    Text(
                        text = switch.value?:"",
                        style = MaterialTheme.typography.h2,
                        color = Color.Black
                    )
                    Spacer(modifier = Modifier.weight(1f))
                    if (canCancel.value) {
                        GameButton(text = stringResource(id = R.string.cancel)) {
                            switch.value = null
                        }
                    }
                    Spacer(modifier = Modifier.size(paddingLarge))
                }
            }
        }
    }
}
