package com.moyu.chuanqirensheng.cloud.loginsdk

import androidx.compose.runtime.MutableState
import androidx.fragment.app.FragmentActivity

/**
 * 隔离游戏sdk能力，后续可能会替换成非tap sdk版本
 */
interface GameSdkProcessor {
    fun initGameSdk()
    fun login(activity: FragmentActivity)
    fun initSDK(activity: FragmentActivity)
    fun antiAddictPassed(): MutableState<Boolean>
    fun hasLogin(): Boolean
    fun getAvatarUrl(): String?
    fun getUserName(): String?
    fun getObjectId(): String?
    fun getAntiAddictionContent(): String
    fun checkAntiAddiction(activity: FragmentActivity)
    fun dealAfterLogin(name: String, id: String, avatarUrl: String, activity: FragmentActivity)
    fun isAgeUnder8(): Boolean
    fun isAgeIn8To16(): Boolean
}