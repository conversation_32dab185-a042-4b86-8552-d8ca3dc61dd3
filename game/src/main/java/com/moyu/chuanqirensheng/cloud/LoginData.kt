package com.moyu.chuanqirensheng.cloud

import kotlinx.serialization.Serializable

const val MAX_AGE = 999

@Serializable
data class LoginData(
    val time: Long,
    val verified: Boolean,
    val show: Boolean = false,
    val dialogText: String = "",
    val buttonText: String = "",
    val buttonLink: String = "",
    val messageId: Int = 1,
    val randomInt: Int = 1,
    val canShowAifadian: Boolean = false,
    val top50Ages: List<Int> = emptyList(),
    val maxAge: Int = MAX_AGE,
    val jumpKey: String = "O-U79sKBxoKNN5oktaN9RfxBe3DvzyaY",
    val needPostAntiCheat: Boolean = false,
    val customClient: String = "QQ3159671927、微信K19940987297"
)
