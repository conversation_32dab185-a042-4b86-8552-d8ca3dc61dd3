package com.moyu.chuanqirensheng.music

import android.content.Context
import android.media.AudioAttributes
import android.media.AudioManager
import android.media.MediaPlayer
import android.media.SoundPool
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.datastore.KEY_MUTE_MUSIC
import com.moyu.chuanqirensheng.datastore.KEY_MUTE_SOUND
import com.moyu.chuanqirensheng.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.event.detail.FATAL_ENEMY
import com.moyu.chuanqirensheng.logic.speed.GameSpeedManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.util.EVENT_DETAIL_SCREEN
import com.moyu.chuanqirensheng.util.EVENT_SELECT_SCREEN
import com.moyu.chuanqirensheng.util.LOGIN_SCREEN
import com.moyu.chuanqirensheng.util.isCurrentRoute
import com.moyu.core.music.SoundEffect
import com.moyu.core.util.RANDOM
import kotlinx.coroutines.Job
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.concurrent.Executors

// 给一个固定的线程给music
val musicDispatcher = Executors.newSingleThreadExecutor().asCoroutineDispatcher()

fun playerMusicByScreen() {
    if (Dialogs.endingDialog.value != null) {
        // 死亡弹窗，不播放音乐
        return
    }
    if (isCurrentRoute(LOGIN_SCREEN)) {
        MusicManager.playBackground()
    } else if (isCurrentRoute(EVENT_SELECT_SCREEN)) {
        MusicManager.playSelectEvent(EventManager.currentBgMusic.value)
    } else if (isCurrentRoute(EVENT_DETAIL_SCREEN)) {
        if (repo.inBattle.value) {
            MusicManager.playBattle(EventManager.selectedEvent.value?.play == FATAL_ENEMY)
        } else {
            MusicManager.playSelectEvent(EventManager.currentBgMusic.value)
        }
    } else {
        MusicManager.resumeMusic()
    }
}

object MusicManager {
    private val soundPool = SoundPool.Builder().setMaxStreams(4).setAudioAttributes(
        AudioAttributes.Builder().setLegacyStreamType(AudioManager.STREAM_MUSIC).build()
    ).build()
    private var mediaPlayer: MediaPlayer? = null
    private val soundHashMap = HashMap<SoundEffect, Int>()
    var muteSound by mutableStateOf(false)
    var muteMusic by mutableStateOf(false)
    private var muteByAd = false

    private var muteByBackground = false

    var lastMusicWithStop = 0
    var lastMusic = 0

    // 初始化声音池的方法
    fun initSoundPool() {
        soundHashMap[SoundEffect.Click] = soundPool.load(
            GameApp.instance.assets.openFd("effect_1.mp3"), 1
        )
        soundHashMap[SoundEffect.StartGame] = soundPool.load(
            GameApp.instance.assets.openFd("effect_2.mp3"), 1
        )
        soundHashMap[SoundEffect.SelectEvent] = soundPool.load(
            GameApp.instance.assets.openFd("effect_3.mp3"), 1
        )
        soundHashMap[SoundEffect.EventWin] = soundPool.load(
            GameApp.instance.assets.openFd("effect_4.mp3"), 1
        )
        soundHashMap[SoundEffect.EventFail] = soundPool.load(
            GameApp.instance.assets.openFd("effect_4.mp3"), 1
        )
        soundHashMap[SoundEffect.BuyGood] = soundPool.load(
            GameApp.instance.assets.openFd("effect_5.mp3"), 1
        )
        soundHashMap[SoundEffect.UpgradeTalent] = soundPool.load(
            GameApp.instance.assets.openFd("effect_6.mp3"), 1
        )
        soundHashMap[SoundEffect.TriggerSkill] = soundPool.load(
            GameApp.instance.assets.openFd("effect_7.mp3"), 1
        )
        soundHashMap[SoundEffect.EquipItem] = soundPool.load(
            GameApp.instance.assets.openFd("effect_8.mp3"), 1
        )
        soundHashMap[SoundEffect.UpgradeItem] = soundPool.load(
            GameApp.instance.assets.openFd("effect_9.mp3"), 1
        )
        soundHashMap[SoundEffect.DuringDialog] = soundPool.load(
            GameApp.instance.assets.openFd("effect_10.mp3"), 1
        )
        soundHashMap[SoundEffect.SelectEventSpecial] = soundPool.load(
            GameApp.instance.assets.openFd("effect_11.mp3"), 1
        )
        soundHashMap[SoundEffect.GameOver] = soundPool.load(
            GameApp.instance.assets.openFd("effect_12.mp3"), 1
        )
        soundHashMap[SoundEffect.GainAward] = soundPool.load(
            GameApp.instance.assets.openFd("effect_13.mp3"), 1
        )
        soundHashMap[SoundEffect.Damage1] = soundPool.load(
            GameApp.instance.assets.openFd("battleeffect_1.mp3"), 1
        )
        soundHashMap[SoundEffect.Damage2] = soundPool.load(
            GameApp.instance.assets.openFd("battleeffect_2.mp3"), 1
        )
        soundHashMap[SoundEffect.Damage3] = soundPool.load(
            GameApp.instance.assets.openFd("battleeffect_3.mp3"), 1
        )
        soundHashMap[SoundEffect.Damage4] = soundPool.load(
            GameApp.instance.assets.openFd("battleeffect_4.mp3"), 1
        )
        soundHashMap[SoundEffect.Damage5] = soundPool.load(
            GameApp.instance.assets.openFd("battleeffect_5.mp3"), 1
        )
        soundHashMap[SoundEffect.Dispel] = soundPool.load(
            GameApp.instance.assets.openFd("battleeffect_6.mp3"), 1
        )
        soundHashMap[SoundEffect.Control] = soundPool.load(
            GameApp.instance.assets.openFd("battleeffect_7.mp3"), 1
        )
        soundHashMap[SoundEffect.Heal] = soundPool.load(
            GameApp.instance.assets.openFd("battleeffect_8.mp3"), 1
        )
        soundHashMap[SoundEffect.AvoidDeath] = soundPool.load(
            GameApp.instance.assets.openFd("battleeffect_9.mp3"), 1
        )
        soundHashMap[SoundEffect.RealDie] = soundPool.load(
            GameApp.instance.assets.openFd("battleeffect_10.mp3"), 1
        )
        soundHashMap[SoundEffect.GetBuff] = soundPool.load(
            GameApp.instance.assets.openFd("battleeffect_11.mp3"), 1
        )
        soundHashMap[SoundEffect.GetDebuff] = soundPool.load(
            GameApp.instance.assets.openFd("battleeffect_12.mp3"), 1
        )
        soundHashMap[SoundEffect.BattleWin] = soundPool.load(
            GameApp.instance.assets.openFd("battleeffect_13.mp3"), 1
        )
        soundHashMap[SoundEffect.Shield] = soundPool.load(
            GameApp.instance.assets.openFd("battleeffect_14.mp3"), 1
        )
        soundHashMap[SoundEffect.Summon] = soundPool.load(
            GameApp.instance.assets.openFd("battleeffect_15.mp3"), 1
        )
    }

    fun stopAll() {
        GameApp.globalScope.launch(musicDispatcher) {
            try {
                lastMusicWithStop = 0
                mediaPlayer?.stop()
                mediaPlayer?.release()
                mediaPlayer = null
            } catch (e: Exception) {
                Timber.e(e.message)
            }
        }
    }

    fun playBackground() {
        playMusic(R.raw.audio_1)
    }

    fun playSelectEvent(music: Int) {
        playMusic(music)
    }

    fun getRandomDungeonMusic() : Int {
        return listOf(R.raw.audio_2, R.raw.audio_3, R.raw.audio_4).shuffled(RANDOM).first()
    }

    fun playBattle(isBoss: Boolean) {
        if (isBoss) {
            playMusic(R.raw.audio_6)
        } else {
            playMusic(R.raw.audio_5)
        }
    }

    var musicJob: Job? = null
    private fun playMusic(music: Int) {
        musicJob?.cancel()
        musicJob = GameApp.globalScope.launch(musicDispatcher) {
            delay(500)
            try {
                if (GameApp.instance.isForeground) {
                    if (lastMusicWithStop != music) {
                        if (mediaPlayer != null) {
                            mediaPlayer?.stop()
                            mediaPlayer?.release()
                            mediaPlayer = null
                        }
                        lastMusicWithStop = music
                        lastMusic = music
                        mediaPlayer = MediaPlayer.create(GameApp.instance, music)
                        mediaPlayer?.setVolume(getMusicVolume(), getMusicVolume())
                        mediaPlayer?.isLooping = true
                        mediaPlayer?.start()
                    }
                }
            } catch (e: Exception) {
                Timber.e(e.message)
            }
        }
    }

    // 播放声音的方法
    fun playSound(sound: SoundEffect, loop: Boolean = false) { // 获取AudioManager引用
        GameApp.globalScope.launch(musicDispatcher) {
            try {
                if (muteSound) return@launch
                val audioManager =
                    GameApp.instance.getSystemService(Context.AUDIO_SERVICE) as AudioManager
                // 获取当前音量
                val streamVolumeCurrent =
                    audioManager.getStreamVolume(AudioManager.STREAM_MUSIC).toFloat()
                // 获取系统最大音量
                val streamVolumeMax =
                    audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC).toFloat()
                // 计算得到播放音量
                val volume = streamVolumeCurrent / streamVolumeMax
                // 调用SoundPool的play方法来播放声音文件
                soundHashMap[sound]?.let {
                    soundPool.play(
                        it,
                        volume,
                        volume,
                        1,
                        if (loop) -1 else 0,
                        1 / GameSpeedManager.getCurrentSpeed().animationDurationFactor
                    )
                }
            } catch (e: Exception) {
                Timber.e(e.message)
            }
        }
    }

    fun muteByBackGround(value: Boolean) {
        muteByBackground = value
        doMuteState()
    }

    fun muteByAd(value: Boolean) {
        muteByAd = value
        doMuteState()
    }

    fun switchMuteSound() {
        muteSound = !muteSound
        doMuteState()
        setBooleanValueByKey(KEY_MUTE_SOUND, muteSound)
    }

    fun switchMuteMusic() {
        muteMusic = !muteMusic
        doMuteState()
        setBooleanValueByKey(KEY_MUTE_MUSIC, muteMusic)
    }

    fun doMuteState() {
        GameApp.globalScope.launch(musicDispatcher) {
            try {
                mediaPlayer?.setVolume(getMusicVolume(), getMusicVolume())
            } catch (e: Exception) {
                Timber.e(e.message)
            }
        }
    }

    private fun getMusicVolume(): Float {
        return if (muteMusic || muteByBackground || muteByAd) 0f else 1f
    }

    fun resumeMusic() {
        GameApp.globalScope.launch(musicDispatcher) {
            playMusic(lastMusic)
        }
    }
}