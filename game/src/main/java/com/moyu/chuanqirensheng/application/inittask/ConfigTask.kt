package com.moyu.chuanqirensheng.application.inittask

import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.config.ConfigManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.job.JobContent
import kotlinx.coroutines.runBlocking

class ConfigTask : JobContent<GameApp> {
    override suspend fun execute(context: GameApp) {
        runBlocking {
            ConfigManager.loadConfigs(repo.gameCore)
        }
    }
}