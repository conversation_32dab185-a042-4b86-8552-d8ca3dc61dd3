package com.moyu.chuanqirensheng.application.inittask

import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.cloud.privacy.PrivacyManager
import com.moyu.chuanqirensheng.datastore.KEY_BATTLE_DONE
import com.moyu.chuanqirensheng.datastore.KEY_GUIDE_INDEX
import com.moyu.chuanqirensheng.datastore.KEY_MUTE_MUSIC
import com.moyu.chuanqirensheng.datastore.KEY_MUTE_SOUND
import com.moyu.chuanqirensheng.datastore.KEY_NEWEST_VERSION
import com.moyu.chuanqirensheng.datastore.KEY_NEW_USER
import com.moyu.chuanqirensheng.datastore.KEY_NON_BATTLE_DONE
import com.moyu.chuanqirensheng.datastore.KEY_SPEED
import com.moyu.chuanqirensheng.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.datastore.getStringFlowByKey
import com.moyu.chuanqirensheng.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.logic.guide.GuideManager
import com.moyu.chuanqirensheng.logic.speed.GameSpeedManager
import com.moyu.chuanqirensheng.music.MusicManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.movie.VideoPlayerManager
import com.moyu.chuanqirensheng.util.getVersionCode
import com.moyu.job.JobContent
import kotlinx.coroutines.delay
import kotlinx.uuid.UUID
import kotlinx.uuid.generateUUID


class DataStoreTask : JobContent<GameApp> {
    override suspend fun execute(context: GameApp) {
        VideoPlayerManager.init()
        GameApp.newUser = getStringFlowByKey(KEY_NEW_USER, "") == ""

        val newestVersion = getIntFlowByKey(KEY_NEWEST_VERSION, 0)
        if (newestVersion > getVersionCode()) {
            context.getString(R.string.backward_version).toast()
            delay(2000)
            error("数据兼容错误,请不要退版本")
        }
        setIntValueByKey(KEY_NEWEST_VERSION, getVersionCode())

        // 引导
        GuideManager.guideIndex.value = getIntFlowByKey(KEY_GUIDE_INDEX)
        GuideManager.battleDone.value = getBooleanFlowByKey(KEY_BATTLE_DONE, false)
        GuideManager.nonBattleDone.value = getBooleanFlowByKey(KEY_NON_BATTLE_DONE, false)

        GameApp.instance.initGameSdk()
        PrivacyManager.init()

        // 游戏速度和声音设置
        getIntFlowByKey(KEY_SPEED, 1).let { speed ->
            GameSpeedManager.setSpeed(speed)
        }

        MusicManager.muteMusic = getBooleanFlowByKey(KEY_MUTE_MUSIC)
        MusicManager.muteSound = getBooleanFlowByKey(KEY_MUTE_SOUND)
        MusicManager.doMuteState()

        // 游戏的初始化
        repo.doInit()

        if (GameApp.newUser) {
            repo.gameCore.getFirstSkillIds().forEach {
                val skill = repo.gameCore.getSkillById(it)
                repo.skillManager.gain(
                    skill.copy(
                        selected = true,
                        uuid = UUID.generateUUID().toString(),
                    )
                )
                repo.skillManager.save()
            }


            repo.gameCore.getFirstAllyIds().forEach { allyId ->
                val ally = repo.gameCore.getAllyPool().first { it.id == allyId }
                repo.allyManager.gain(
                    ally.copy(
                        selected = true,
                        uuid = UUID.generateUUID().toString(),
                    )
                )
                repo.allyManager.save()
            }
        }
    }
}