package com.moyu.chuanqirensheng.application

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.screen.common.GAME_BG_PIC
import com.moyu.chuanqirensheng.ui.theme.B35
import com.moyu.chuanqirensheng.ui.theme.B65
import com.moyu.chuanqirensheng.ui.theme.padding100
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding380
import com.moyu.chuanqirensheng.ui.theme.padding420
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding72
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import kotlinx.coroutines.delay


@Composable
fun HeathTipsLayout(heathTipsVisible: MutableState<Boolean>) {
    val contentVisible = remember {
        mutableStateOf(false)
    }
    LaunchedEffect(Unit) {
        delay(200)
        contentVisible.value = true
        delay(2000)
        contentVisible.value = false
        delay(200)
        heathTipsVisible.value = false
    }
    Box(
        Modifier
            .fillMaxSize()
            .clickable { }, contentAlignment = Alignment.Center
    ) {
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.Crop,
            painter = painterResource(id = getImageResourceDrawable(GAME_BG_PIC)),
            contentDescription = null
        )
        AnimatedVisibility(
            visible = contentVisible.value,
            enter = fadeIn(),
            exit = fadeOut(),
        ) {
            Box(modifier = Modifier
                .fillMaxSize()
                .background(B35), contentAlignment = Alignment.Center) {
                Box(modifier = Modifier.size(padding380, padding420)) {
                    Image(
                        modifier = Modifier.fillMaxSize(),
                        painter = painterResource(id = R.drawable.attribute_frame2),
                        contentDescription = null
                    )
                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(padding19),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Column(
                            Modifier.weight(1f),
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.Center
                        ) {
                            Text(
                                text = stringResource(R.string.health_tip_title),
                                style = MaterialTheme.typography.h1
                            )
                            Spacer(modifier = Modifier.size(padding26))
                            Text(
                                text = stringResource(R.string.health_tips),
                                style = MaterialTheme.typography.h2
                            )
                        }
                    }
                }
                Column(
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .fillMaxWidth()
                        .height(padding100)
                        .background(B65)
                        .padding(top = padding6, start = padding6)
                        .padding(end = padding72),
                    verticalArrangement = Arrangement.SpaceEvenly
                ) {
                    Text(
                        text = stringResource(R.string.legal_info1),
                        style = MaterialTheme.typography.h6
                    )
                    Text(
                        text = stringResource(R.string.legal_info2),
                        style = MaterialTheme.typography.h6
                    )
                }
                Image(
                    modifier = Modifier
                        .height(padding100)
                        .padding(padding6)
                        .align(Alignment.BottomEnd),
                    contentScale = ContentScale.FillHeight,
                    painter = painterResource(id = R.drawable.age_warning),
                    contentDescription = null
                )
            }
        }
    }
}