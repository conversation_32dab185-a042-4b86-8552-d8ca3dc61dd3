package com.moyu.chuanqirensheng.application.inittask

import android.app.Activity
import android.app.Application
import android.os.Bundle
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.music.MusicManager
import com.moyu.chuanqirensheng.music.playerMusicByScreen
import com.moyu.job.JobContent
import kotlinx.coroutines.launch

/**
 * 另外音乐会根据前后台处理
 */
class LifecycleTask : JobContent<GameApp> {

    private var activityAccount = 0
    override suspend fun execute(context: GameApp) {
        context.registerActivityLifecycleCallbacks(object : Application.ActivityLifecycleCallbacks {
            override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
            }

            override fun onActivityStarted(activity: Activity) {
                if (activityAccount == 0) {
                    MusicManager.muteByBackGround(false)
                }
                GameApp.instance.isForeground = true
                activityAccount++
            }

            override fun onActivityResumed(activity: Activity) {
                playerMusicByScreen()
            }

            override fun onActivityPaused(activity: Activity) {
            }

            override fun onActivityStopped(activity: Activity) {
                activityAccount--
                if (activityAccount == 0) {
                    GameApp.instance.isForeground = false
                    GameApp.globalScope.launch {
                        MusicManager.muteByBackGround(true)
                        MusicManager.stopAll()
                    }
                }
            }

            override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
            }

            override fun onActivityDestroyed(activity: Activity) {
            }
        })
    }
}
