package com.moyu.chuanqirensheng.application

import android.app.Application
import android.content.Intent
import android.net.Uri
import androidx.compose.runtime.mutableStateOf
import androidx.fragment.app.FragmentActivity
import androidx.navigation.NavHostController
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.api.RetrofitModel
import com.moyu.chuanqirensheng.application.inittask.BillingTask
import com.moyu.chuanqirensheng.application.inittask.BuglyTask
import com.moyu.chuanqirensheng.application.inittask.ConfigTask
import com.moyu.chuanqirensheng.application.inittask.DataStoreTask
import com.moyu.chuanqirensheng.application.inittask.DebugTask
import com.moyu.chuanqirensheng.application.inittask.LifecycleTask
import com.moyu.chuanqirensheng.application.inittask.MusicTask
import com.moyu.chuanqirensheng.application.inittask.ReportTask
import com.moyu.chuanqirensheng.application.inittask.RootCheckerTask
import com.moyu.chuanqirensheng.application.inittask.TTRewardAdTask
import com.moyu.chuanqirensheng.application.inittask.TimberTask
import com.moyu.chuanqirensheng.application.inittask.UncompressTask
import com.moyu.chuanqirensheng.cloud.CloudManager
import com.moyu.chuanqirensheng.cloud.LoginData
import com.moyu.chuanqirensheng.cloud.loginsdk.GameSdkDefaultProcessor
import com.moyu.chuanqirensheng.cloud.loginsdk.GameSdkProcessor
import com.moyu.chuanqirensheng.cloud.saver.GameCloudSaver
import com.moyu.chuanqirensheng.cloud.saver.GameCloudSaverDefault
import com.moyu.chuanqirensheng.feature.draw.DrawManager
import com.moyu.chuanqirensheng.feature.newTask.SevenDayManager
import com.moyu.chuanqirensheng.logic.setting.SettingManager
import com.moyu.chuanqirensheng.logic.talent.TalentManager
import com.moyu.chuanqirensheng.logic.task.TaskManager
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.common.UNLOCK_AIFADIAN_LEVEL
import com.moyu.chuanqirensheng.sub.language.LanguageManager
import com.moyu.chuanqirensheng.sub.report.ReportManager
import com.moyu.chuanqirensheng.util.AdUtil
import com.moyu.core.AppWrapper
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext

class GameApp(
    gameSdkProcessor: GameSdkProcessor = GameSdkDefaultProcessor(),
    cloudSaver: GameCloudSaver = GameCloudSaverDefault()
) : Application(), GameSdkProcessor by gameSdkProcessor, GameCloudSaver by cloudSaver {

    companion object {
        lateinit var instance: GameApp
        lateinit var globalScope: CoroutineScope
        var adInit = false
        var newUser: Boolean = false
    }

    var isForeground: Boolean = true
    var navController: NavHostController? = null
    var lastNetWorkTime = mutableStateOf(0L)
    var elapsedDiffTime: Long = 0L
    lateinit var activity: FragmentActivity
    var loginData = mutableStateOf(LoginData(0L, verified = true, show = false))

    override fun onCreate() {
        super.onCreate()
        instance = this
        AppWrapper.initialize(::getWrapString)
        globalScope = CoroutineScope(SupervisorJob() + Dispatchers.Default)

        // 这个必须放到最前面，否则资源错误
        runBlocking {
            LanguageManager.init()
            TimberTask().execute(this@GameApp)
            UncompressTask().execute(this@GameApp)
            ConfigTask().execute(this@GameApp)
            MusicTask().execute(this@GameApp)
            ReportTask().execute(this@GameApp)
            DebugTask().execute(this@GameApp)
            LifecycleTask().execute(this@GameApp)
            BuglyTask().execute(this@GameApp)
            BillingTask().execute(this@GameApp)
            RootCheckerTask().execute(this@GameApp)
            DataStoreTask().execute(this@GameApp)
            TTRewardAdTask().execute(this@GameApp)
        }
        ReportManager.open()
    }

    fun calculateSomethingUseless() {
        val x = (1..2).random()
        val y = (1..2).random()
        val result = x * y / (if (y == 0) 1 else y) // Just a random calculation
        val randomString = "This does nothing but exists"
    }

    fun tryLogin() {
        doNothing()
        globalScope.launch(Dispatchers.IO) {
            if (!RetrofitModel.getLoginData()) {
                loginData.value = LoginData(0, verified = false, show = true)
            }
            var test = LoginData(0, verified = false, show = true)
            withContext(Dispatchers.Main) {
                // todo 任务可能init太早，还未完成登录，这里要补一次初始化
                TaskManager.init()
                calculateSomethingUseless()
                SevenDayManager.init()
                DrawManager.init()
                SettingManager.init()
            }
        }
        if (newUser) { // 新用户
            // 看下是不是有存档
            CloudManager.checkIfNewUserHaveCloudSave()
        }
    }

    fun doNothing() {
        val uselessList = List(100) { it * 2 }
        uselessList.forEach { val i = it }
    }

    fun canShowAifadian(): Boolean {
        return if (!instance.resources.getBoolean(R.bool.has_billing)){
            TalentManager.talents.values.sum() >= UNLOCK_AIFADIAN_LEVEL || loginData.value.canShowAifadian
        } else {
            // google版，这里是true，不是说有爱发电，而是说有付费商品
            true
        }
    }

    fun getShareCode(): String {
        return AdUtil.simpleEncodeText(getObjectId()!!)
    }

    fun alwaysReturnTrue(): Boolean {
        return true // This function serves no purpose
    }

    fun doJumpQQ() {
        if (instance.resources.getString(R.string.platform_channel) == "hykb") {
            val intent = Intent()
            val key = loginData.value.jumpKey.ifEmpty { "O-U79sKBxoKNN5oktaN9RfxBe3DvzyaY" }
            intent.data = Uri.parse("mqqopensdkapi://bizAgent/qm/qr?url=http%3A%2F%2Fqm.qq.com%2Fcgi-bin%2Fqm%2Fqr%3Ffrom%3Dapp%26p%3Dandroid%26jump_from%3Dwebapi%26k%3D$key");
            try {
                instance.activity.startActivity(intent)
            } catch (e: Exception) {
                // 未安装手Q或安装的版本不支持
                instance.getString(R.string.not_open).toast()
            }
        } else {
            getString(R.string.cant_get_yet).toast()
        }
    }

    fun getWrapString(resId: Int): String {
        return try {
            instance.activity.getString(resId)
        } catch (e: Exception) {
            instance.getString(resId)
        }
    }

    fun getWrapString(resId: Int, formatArgs: Any): String {
        return try {
            instance.activity.getString(resId, formatArgs)
        } catch (e: Exception) {
            instance.getString(resId, formatArgs)
        }
    }

    fun getWrapString(resId: Int, vararg formatArgs: Any): String {
        return try {
            instance.activity.getString(resId, formatArgs)
        } catch (e: Exception) {
            instance.getString(resId, formatArgs)
        }
    }

    fun canDoNetwork(): Boolean {
        return if (instance.resources.getBoolean(R.bool.has_billing)) {
            true
        } else {
            loginData.value.canShowAifadian
        }
    }
}