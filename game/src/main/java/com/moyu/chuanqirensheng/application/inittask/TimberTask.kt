package com.moyu.chuanqirensheng.application.inittask

import android.util.Log
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.remember
import com.moyu.chuanqirensheng.BuildConfig
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.job.JobContent
import timber.log.Timber

/**
 * Timber
 * 如果是跑dryTest，不打日志
 * 如果是release，仅打error日志
 */
class TimberTask : JobContent<GameApp> {
    override suspend fun execute(context: GameApp) {
        Timber.plant(object : Timber.DebugTree() {
            override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
                if (DebugManager.dryTest) return
                if (BuildConfig.DEBUG) {
                    super.log(priority, tag, message, t)
                } else {
                    if (priority == Log.ERROR) super.log(priority, tag, message, t)
                }
            }
        })
    }
}


class Ref(var value: Int)

// 注意，此处的 inline 会使下列函数实际上直接内联到调用处
// 以确保 logging 仅在原始调用位置被调用
@Composable
fun LogCompositions(tag: String, msg: String) {
    if (BuildConfig.DEBUG) {
        val ref = remember { Ref(0) }
        SideEffect { ref.value++ }
        Timber.e("${tag}_Compositions: $msg ${ref.value}")
    }
}