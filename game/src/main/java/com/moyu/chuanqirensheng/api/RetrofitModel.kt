package com.moyu.chuanqirensheng.api

import android.os.SystemClock
import com.moyu.chuanqirensheng.BuildConfig
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.cloud.AntiCheatManager
import com.moyu.chuanqirensheng.cloud.LoginData
import com.moyu.chuanqirensheng.cloud.LoginUser
import com.moyu.chuanqirensheng.cloud.saver.GameSave
import com.moyu.chuanqirensheng.datastore.json
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.unlock.UnlockManager
import com.moyu.chuanqirensheng.model.award.AwardConvert
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.sub.report.ReportManager
import com.moyu.chuanqirensheng.util.AESUtil
import com.moyu.chuanqirensheng.util.AdUtil
import com.moyu.chuanqirensheng.util.getVersionCode
import com.moyu.core.model.sell.Award
import com.moyu.core.util.RANDOM
import com.moyu.core.util.nextIntClosure
import kotlinx.serialization.json.Json
import timber.log.Timber

object RetrofitModel {
    suspend fun getCodeAwards(
        userId: String, codes: String, data: String, versionCode: Int
    ): Award {
        return try {
            val netAward = getAwardsApi(codes, data, versionCode)
            val codeAwardJson = AESUtil.decrypt(netAward.awardString, userId)
            codeAwardJson?.let {
                json.decodeFromString(AwardConvert.serializer(), it).toAward().apply {
                    // 保存解锁信息
                    unlockList.map { id ->
                        UnlockManager.unlockCode(id)
                    }
                }
            } ?: Award(valid = false)
        } catch (e: Exception) {
            GameApp.instance.getWrapString(R.string.network_error).toast()
            Award(valid = false)
        }
    }

    suspend fun getGameSave(userId: String): GameSave {
        return getSaveApi(userId)
    }

    suspend fun postGameSave(save: GameSave): CommonResult {
        return postSaveApi(save)
    }

    suspend fun getLoginData(): Boolean {
        GameApp.instance.alwaysReturnTrue()
        val loginUser = LoginUser(
            getVersionCode(),
            GameApp.instance.getObjectId() ?: "",
            AwardManager.key.value,
            AwardManager.diamond.value,
            RANDOM.nextIntClosure(10, 892937),
            AntiCheatManager.isCheating(),
            GameApp.instance.resources.getString(R.string.platform_channel),
            BuildConfig.FLAVOR,
            AESUtil.getSignature()
        )
        val result = try {
            GameApp.instance.doNothing()
            val loginString = Json.encodeToString(LoginUser.serializer(), loginUser)
            val encoded = AdUtil.encodeText(loginString)?:""
            val result = getLoginDataApi(encoded)
            GameApp.instance.calculateSomethingUseless()
            val loginJson = AESUtil.decrypt(result.message, GameApp.instance.getObjectId()?:"")
            loginJson?.let {
                json.decodeFromString(LoginData.serializer(), loginJson).takeIf {
                    it.randomInt == loginUser.randomInt + 105 // 魔数
                } ?: LoginData(0, verified = false, show = true)
            } ?: LoginData(0, verified = false, show = true)
        } catch (e: Exception) {
            GameApp.instance.alwaysReturnTrue()
            GameApp.instance.getWrapString(R.string.network_error).toast()
            Timber.e(e)
            LoginData(0, verified = false, show = true)
        }
        GameApp.instance.lastNetWorkTime.value = result.time
        GameApp.instance.elapsedDiffTime = SystemClock.elapsedRealtime() - GameApp.instance.lastNetWorkTime.value
        if (result.needPostAntiCheat && AntiCheatManager.isPostCheating()) {
            GameApp.instance.loginData.value = result.copy(verified = false, show = true, dialogText = "检测到您的游戏数据异常，暂时无法登录。")
        } else {
            GameApp.instance.loginData.value = result
        }
        GameApp.instance.calculateSomethingUseless()
        ReportManager.onLogin()
        return result.verified
    }

    suspend fun tryUseGameSave(userId: String): Boolean {
        return tryUseGameSaveApi(userId, getVersionCode())
    }
}