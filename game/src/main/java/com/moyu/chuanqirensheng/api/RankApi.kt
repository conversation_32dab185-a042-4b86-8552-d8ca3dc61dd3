package com.moyu.chuanqirensheng.api

import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.api.RetrofitManager.httpClient
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.cloud.NetAward
import com.moyu.chuanqirensheng.cloud.saver.GameSave
import com.moyu.chuanqirensheng.model.record.AllRankData
import io.ktor.client.call.body
import io.ktor.client.request.get
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.http.ContentType
import io.ktor.http.contentType

val serverUrl = GameApp.instance.resources.getString(R.string.serverUrl)

suspend fun getLoginDataApi(versionCode: String): CommonResult {
    return httpClient.get(serverUrl + "login_user") {
        url {
            parameters.append("data", versionCode)
        }
    }.body()
}

suspend fun getRanksApi(platform: String, type: Int): CommonResult {
    return httpClient.get(serverUrl + "ranks_v2") {
        url {
            parameters.append("data", platform)
            parameters.append("type", type.toString())
        }
    }.body()
}

suspend fun postRankDataApi(allRankData: AllRankData) {
    return httpClient.post(serverUrl + "rank_data_v2") {
        contentType(ContentType.Application.Json)
        setBody(allRankData)
    }.body()
}

suspend fun getPvpByScore(platform: String, pvpScore: String): CommonResult {
    return httpClient.get(serverUrl + "rank_data_pvp_v2") {
        url {
            parameters.append("platform", platform)
            parameters.append("pvpScore", pvpScore)
        }
    }.body()
}

suspend fun getTodayRanks(platform: String): CommonResult {
    return httpClient.get(serverUrl + "pvp_today_rank") {
        url {
            parameters.append("platform", platform)
        }
    }.body()
}

suspend fun getLastDayRanks(platform: String): CommonResult {
    return httpClient.get(serverUrl + "pvp_last_day_rank") {
        url {
            parameters.append("platform", platform)
        }
    }.body()
}

suspend fun postPvpRankData(allRankData: AllRankData) {
    return httpClient.post(serverUrl + "rank_pvp_data") {
        contentType(ContentType.Application.Json)
        setBody(allRankData)
    }.body()
}

suspend fun getSaveApi(userId: String): GameSave {
    return httpClient.get(serverUrl + "save_v5") {
        url {
            parameters.append("userId", userId)
        }
    }.body()
}

suspend fun postSaveApi(save: GameSave): CommonResult {
    return httpClient.post(serverUrl + "save_v5") {
        contentType(ContentType.Application.Json)
        setBody(save)
    }.body()
}


suspend fun getAwardsApi(codes: String, data: String, versionCode: Int): NetAward {
    return httpClient.get(serverUrl + "awards") {
        url {
            parameters.append("codes", codes)
            parameters.append("data", data)
            parameters.append("versionCode", versionCode.toString())
        }
    }.body()
}

suspend fun tryUseGameSaveApi(
    userId: String, versionCode: Int
): Boolean {
    return httpClient.get(serverUrl + "try_use_save") {
        url {
            parameters.append("userId", userId)
            parameters.append("versionCode", versionCode.toString())
        }
    }.body()
}

suspend fun getPrepayApi(
    data: String, versionCode: Int
): CommonResult {
    return httpClient.get(serverUrl + "try_use_rank") {
        url {
            parameters.append("data", data)
            parameters.append("versionCode", versionCode.toString())
        }
    }.body()
}

suspend fun checkPayOkApi(orderEncrypted: String, versionCode: Int): CommonResult {
    return httpClient.get(serverUrl + "try_check_p") {
        url {
            parameters.append("data", orderEncrypted)
            parameters.append("versionCode", versionCode.toString())
        }
    }.body()
}


suspend fun postHolidayRankData(allRankData: AllRankData) {
    return httpClient.post(serverUrl + "rank_holiday_data") {
        contentType(ContentType.Application.Json)
        setBody(allRankData)
    }.body()
}

suspend fun getHolidayRanks(platform: String): CommonResult {
    return httpClient.get(serverUrl + "holiday_rank") {
        url {
            parameters.append("platform", platform)
        }
    }.body()
}

suspend fun getHolidaySolidRanks(platform: String): CommonResult {
    return httpClient.get(serverUrl + "holiday_solid_rank") {
        url {
            parameters.append("platform", platform)
        }
    }.body()
}


suspend fun postTowerRankData(rankData: AllRankData) {
    return httpClient.post(serverUrl + "rank_tower_data_v3") {
        contentType(ContentType.Application.Json)
        setBody(rankData)
    }.body()
}
