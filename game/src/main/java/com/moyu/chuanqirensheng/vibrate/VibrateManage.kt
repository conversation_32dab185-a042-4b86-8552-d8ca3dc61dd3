package com.moyu.chuanqirensheng.vibrate

import android.content.Context.VIBRATOR_SERVICE
import android.os.Vibrator
import com.moyu.chuanqirensheng.application.GameApp

object VibrateManage {
//    val vibrator: Vibrator = GameApp.instance.getSystemService(VIBRATOR_SERVICE) as Vibrator

    /**
     * 启动振动一次
     *
     * @param time 持续振动时间 毫秒
     */
    fun startVibrator(time: Long = 250L) {
//        vibrator.vibrate(time)
    }
}