<resources>																			
  <string name="quit_battle_content">Quitting will result in battle failure (Legions in battle will be killed). Are you sure you want to quit the battle?</string>																			
  <string name="app_name">Rebirth of Empire</string>																			
  <string name="network_error">Network error</string>																			
  <string name="quit_event_content">Partial event exit will cause this event to fail.</string>																			
  <string name="quit_event_title">Confirm exit?</string>																			
  <string name="quit_life_content">Do you want to exit or save? You can continue from where you left off next time</string>																			
  <string name="quit_life_title">Exit Game</string>																			
  <string name="temp_save">Save</string>																			
  <string name="quit_battle_title">Quit Battle</string>																			
  <string name="guide_block_quit">Please complete the guide first</string>																			
  <string name="quit_game_toast">Exit game</string>																			
  <string name="locked">Locked</string>																			
  <string name="unlocked">Unlocked</string>																			
  <string name="talent_pre_unlock_toast">Please learn prerequisite talents first</string>																			
  <string name="learn">Learn</string>																			
  <string name="star_up">Star Up</string>																			
  <string name="star_up_in_game">Star Up In-Game</string>																			
  <string name="star_max">Max</string>																			
  <string name="same_card">Duplicate Card</string>																			
  <string name="next_level">Next Level</string>																			
  <string name="setting">Settings</string>																			
  <string name="turn_off_music">Turn off music</string>																			
  <string name="turn_off_sound">Turn off sound</string>																			
  <string name="turn_off_dialog">Skip dialogues</string>																			
  <string name="game_selected">Selected</string>																			
  <string name="game_not_selected">Not selected</string>																			
  <string name="reach_max_refresh">You can\'t refresh any more today</string>																			
  <string name="reach_max_refresh2">Please try again tomorrow.</string>																			
  <string name="refresh_shop">Refresh shop</string>																			
  <string name="refresh_cost">Refreshing will cost</string>																			
  <string name="refresh_cost2">Diamonds, do you want to refresh?</string>																			
  <string name="refresh">Refresh</string>																			
  <string name="diamond_not_enough">Not enough honor</string>																			
  <string name="check_award_pool">View Prize Pool</string>																			
  <string name="sold_out">Sold out</string>																			
  <string name="watch_ad">Watch ads</string>																			
  <string name="key_not_enough">Not enough Diamonds</string>																			
  <string name="challenge_money_not_enough">Not enough Challenge Keys</string>																			
  <string name="electric_not_enough">Insufficient privilege value</string>																			
  <string name="challenge_money">Challenge Keys</string>																			
  <string name="already_max_gained">You have already reached a full level</string>																			
  <string name="no_need_buy">, and don\'t need to buy it any more</string>																			
  <string name="confirm_buy">Confirm purchase?</string>																			
  <string name="confirm_buy2">Confirm to buy:</string>																			
  <string name="go_and_get">Go Get</string>																			
  <string name="net_error_retry">Data error, please try again later</string>																			
  <string name="collection_progress">Collection progress:</string>																			
  <string name="talent_level">Talent level:</string>																			
  <string name="rank_title">Rankings</string>																			
  <string name="famous_title">Celebrities</string>																			
  <string name="life_rank_title">History</string>																			
  <string name="ending_rank_title">Conquests</string>																			
  <string name="talent_rank_title">Talent</string>																			
  <string name="kill_rank_title">Battle</string>																			
  <string name="collect_rank_title">Collection</string>																			
  <string name="age_content">Maximum Age:</string>																			
  <string name="ending_content">Conquests:</string>																			
  <string name="kill_content">Defeated egions:</string>																			
  <string name="die_ally_content">Conquests:</string>																			
  <string name="award_got_toast">Reward collected</string>																			
  <string name="quest_not_done_toast">Mission not completed, unable to collect rewards</string>																			
  <string name="already_got">Collected</string>																			
  <string name="double_gain">Double reward</string>																			
  <string name="gain">Obtained</string>																			
  <string name="routine_quest">Daily</string>																			
  <string name="new_quest">Novice</string>																			
  <string name="challenge_quest">Challenge</string>																			
  <string name="one_time_quest">Achievement</string>																			
  <string name="warpass_quest">Battlepass Quests</string>																			
  <string name="role_prop6">Art</string>																			
  <string name="role_prop7">Pop</string>																			
  <string name="star_level">Stars</string>																			
  <string name="cloud_record">Cloud Save</string>																			
  <string name="tutor">Tutorials</string>																			
  <string name="more_game">More Games</string>																			
  <string name="challenge_rank">Challenge List</string>																			
  <string name="about">About</string>																			
  <string name="new_tutor">Novice Guide</string>																			
  <string name="goto_comment">Go to reviews</string>																			
  <string name="go">Go</string>																			
  <string name="not_go">No</string>																			
  <string name="more">More</string>																			
  <string name="exchange">Exchange</string>																			
  <string name="common_codes">Reward code (click to copy):</string>																			
  <string name="copied">Copied</string>																			
  <string name="electric_content">Purchasing items grants privilege value . Privilege value accumulates permanently.</string>																			
  <string name="gain_award">Claim</string>																			
  <string name="get_electric">Getting Privilege Value</string>																			
  <string name="no_electric">There is temporarily no way to get privilege value, thanks for your support!</string>																			
  <string name="not_login">Not logged in</string>																			
  <string name="ready_to_go">Establish a nation</string>																			
  <string name="start_life">Start Game</string>																			
  <string name="start_challenge">Daily Challenges</string>																			
  <string name="fortune">Legion</string>																			
  <string name="business">Trading Post</string>																			
  <string name="quest">Task</string>																			
  <string name="events_comma">Events:</string>																			
  <string name="events">Events</string>																			
  <string name="allies_comma">Legion:</string>																			
  <string name="skills_comma">Tactics:</string>																			
  <string name="records">Highest record:</string>																			
  <string name="age">Yr</string>																			
  <string name="no_record">No Record</string>																			
  <string name="shop">Shop</string>																			
  <string name="story_shop">Packs</string>																			
  <string name="war_pass">Battlepass</string>																			
  <string name="war_pass2">Rise of Empire</string>																			
  <string name="cheating_title">Privilege Card</string>																			
  <string name="identity">Identity</string>																			
  <string name="stories">Empire Card Packs</string>																			
  <string name="record">View History</string>																			
  <string name="achievement">Loot</string>																			
  <string name="guide_ally_tips">Click the Legion button</string>																			
  <string name="guide1">Click Start Game to prepare to establish your country</string>																			
  <string name="guide2">You can bring some cards into the game as an initial boost to your country\'s development. This time, I will help you prepare some Legions and tactical cards</string>																			
  <string name="guide3">The preparations for establishing the country are complete. Now you can click Establish Country to start an adventure</string>																			
  <string name="guide4">The above shows your 6 national attributes and population. Please keep the population growing because when the population drops to zero, the country will perish</string>																			
  <string name="guide5">These shows the current reserves and annual production of all resources in the country. Resources increase automatically each year and used for building and developing technology</string>																			
  <string name="guide6">Menu bar below shows available cards and items. Check tutorial for more questions</string>																			
  <string name="guide7">Choose initial technology, location, and beliefs for your country. For the first time, please feel free to choose, and you can gradually form a selection strategy in the subsequent games</string>																			
  <string name="guide8">Game starts from the year 001 AD. Each year, you need to choose an event to ensure the development of the country. Your goal is to make the country survive as long as possible</string>																			
  <string name="guide9">Events have different conditions and rewards. You can only choose the corresponding event when the conditions are met. Now please choose a technology event</string>																			
  <string name="guide10">There\'s one more thing, insert tactical cards into your Legions, so that they can exert their strength in the battle</string>																			
  <string name="guide11">Click on the first legion</string>																			
  <string name="guide12">You can insert the tactical card on this empty slot. Each legion can insert up to 2 tactical cards. In addition, the legion also has its own tactics and ability</string>																			
  <string name="guide13">Select this Tactic to complete the operation</string>																			
  <string name="guide14">Some events have special rules. Please click the exclamation mark icon to view the instructions</string>																			
  <string name="guide15">Prepare to start the first war, click the empty slot to deploy your legion</string>																			
<string name="guide16">Use the Quick Deploy button to deploy your troops instantly</string>																			
<string name="guide17">Once deployed, head straight into battle!</string>																			
  <string name="talent">Talents</string>																			
  <string name="ally_card">Legion</string>																			
  <string name="event_card">Event</string>																			
  <string name="skill_card">Tactics</string>																			
  <string name="equip_card">Heroes</string>																			
  <string name="none">None</string>																			
  <string name="name">Name</string>																			
  <string name="quality">Quality</string>																			
  <string name="number">Quantity</string>																			
  <string name="talent_gift">Talent Gift</string>																			
  <string name="all">All</string>																			
  <string name="quality_max">Orange</string>																			
  <string name="quality_high">Blue</string>																			
  <string name="quality_normal">Green</string>																			
  <string name="star">Star</string>																			
  <string name="skill_type1">Melee</string>																			
  <string name="skill_type2">Ranged</string>																			
  <string name="skill_type3">Siege</string>																			
  <string name="skill_type4">Sacred</string>																			
  <string name="skill_type5">Chaos</string>																			
  <string name="skill_tag1">Damage</string>																			
  <string name="skill_tag2">Heal</string>																			
  <string name="skill_tag3">Buff</string>																			
  <string name="skill_tag4">Debuff</string>																			
  <string name="skill_tag5">Defense</string>																			
  <string name="skill_tag6">Control</string>																			
  <string name="skill_tag7">Negative</string>																			
  <string name="skill_tag8">Special</string>																			
  <string name="equip_part1">Science</string>																			
  <string name="equip_part2">Politics</string>																			
  <string name="equip_part3">Military</string>																			
  <string name="equip_part4">Religion</string>																			
  <string name="equip_part5">Business</string>																			
  <string name="equip_part6">Art</string>																			
  <string name="quest_event">! Events Mission:</string>																			
  <string name="quest_kill">! Combat Mission:</string>																			
  <string name="enemy_quest">! Defeat missions:</string>																			
  <string name="no_available_event">No available events</string>																			
  <string name="ending_life">Skip</string>																			
  <string name="continue_text">Continue</string>																			
  <string name="no_role_to_battle_tips">No Legion will result in a lost battle, are you sure to play without Legion?</string>																			
  <string name="start_battle">Start\nBattle</string>																			
  <string name="role_prop1">Sci</string>																			
  <string name="prop1_tips">Represents scientific level, a prerequisite for many events and policies</string>																			
  <string name="role_prop2">Pol</string>																			
  <string name="prop2_tips">Represents political ability, a prerequisite for many events and policies</string>																			
  <string name="role_prop3">Mil</string>																			
  <string name="prop3_tips">Represents military strength, a prerequisite for many events and policies</string>																			
  <string name="role_prop4">Rel</string>																			
  <string name="prop4_tips">Represents religious influence, a prerequisite for many events and policies</string>																			
  <string name="role_prop5">Biz</string>																			
  <string name="prop5_tips">Represents business development level, a prerequisite for many events and policies</string>																			
  <string name="prop6_tips">Represents artistic achievement level, a prerequisite for many events and policies</string>																			
  <string name="prop7_tips">Population is the foundation of a nation. When it reaches zero, the nation will perish.</string>																			
  <string name="attack">ATK</string>																			
  <string name="attack_tips">Determines the strength of various damage effects</string>																			
  <string name="defense">DEF</string>																			
  <string name="defense_tips">Reduces damage taken</string>																			
  <string name="hp">HP</string>																			
  <string name="hp_tips">When the number of troops reaches to zero, the Legion will die.</string>																			
  <string name="fatal_rate">CRI</string>																			
  <string name="fatal_rate_tips">Determines the chance of a successful critical hit, and deals more damage when it succeeds.</string>																			
  <string name="fatal_damage">CRD</string>																			
  <string name="fatal_damage_tips">Determines how much extra damage will be dealt in a critical hit.</string>																			
  <string name="dodge">BLO</string>																			
  <string name="dodge_tips">Avoids this damage completely when successfully blocked.</string>																			
  <string name="suck_blood">LEE</string>																			
  <string name="suck_blood_tips">Percentage of damage dealt and replenishes your troop strength based on it.</string>																			
  <string name="speed">SPE</string>																			
  <string name="speed_tips">Determines attack priority.</string>																			
  <string name="hero">Heroes</string>																			
  <string name="destroy">Destroy</string>																			
  <string name="replace">Replace</string>																			
  <string name="next_level_property">Next level attributes:</string>																			
  <string name="gold">Gold</string>																			
  <string name="item">Spec</string>																			
  <string name="achievement_award">Achievement Rewards</string>																			
  <string name="collect">Collect</string>																			
  <string name="piece">Piece</string>																			
  <string name="card">Loot</string>																			
  <string name="no_collected">Collect to receive</string>																			
  <string name="life_record">Imperial History</string>																			
  <string name="return_text">Back</string>																			
  <string name="share">Share</string>																			
  <string name="go_share">Go to Share</string>																			
  <string name="ready_to_share">Successfully copied Empire History. Would you like to share it in the comments section?</string>																			
  <string name="statistics">Statistics</string>																			
  <string name="total_damage">Total damage:</string>																			
  <string name="gain_damages">Total damage received:</string>																			
  <string name="best_dps_skill">Best Tactic</string>																			
  <string name="best_dps_skill_enemy">Best enemy attacker</string>																			
  <string name="best_heal">Best healing</string>																			
  <string name="best_heal_enemy">Best enemy healing</string>																			
  <string name="best_release">Most casts</string>																			
  <string name="best_release_enemy">Most enemy casts</string>																			
  <string name="ally_damage">Legion damage</string>																			
  <string name="ally_heal">Legion healing</string>																			
  <string name="enemy_damage">Enemy damage</string>																			
  <string name="enemy_heal">Enemy healing</string>																			
  <string name="battle_skill_card">TacticS</string>																			
  <string name="selection_done">Selected</string>																			
  <string name="do_select">Select</string>																			
  <string name="not_gain_yet">Not obtained</string>																			
  <string name="go_shopping">Please go to shop to purchase</string>																			
  <string name="go_cheat_screen_unlock">Please go to Privilege Card to unlock</string>																			
  <string name="daily_challenge">Daily Challenges</string>																			
  <string name="select_init_battle_skill">Select Initial Tactic</string>																			
  <string name="cancel">Cancel</string>																			
  <string name="reputation_status">Diplom</string>																			
  <string name="reputation">Diplom</string>																			
  <string name="quit_tips">Click outside to close</string>																			
  <string name="diamond_exchange">Honor Exchange</string>																			
  <string name="key_title">Diamonds</string>																			
  <string name="diamond_title">Honor</string>																			
  <string name="select_exchange_num_tips">Select amount to redeem</string>																			
  <string name="exchanged">Redeemed</string>																			
  <string name="adventure_skill_card">Event Cards</string>																			
  <string name="unlock_feature">Unlock Functions</string>																			
  <string name="go_unlock">Go to Unlock</string>																			
  <string name="no_link_target">No Target</string>																			
  <string name="star_up_preview">Star Up Preview:</string>																			
  <string name="stars">Stars</string>																			
  <string name="punish">Punish:</string>																			
  <string name="awards">Reward</string>																			
  <string name="confirm">Confirm</string>																			
  <string name="electric_title">Privilege Value</string>																			
  <string name="exp">EXP</string>																			
  <string name="random_hero_card">Random Heroes</string>																			
  <string name="random_battle_skill">Random Tactic</string>																			
  <string name="random_ally_card">Random Legion</string>																			
  <string name="random_tcg_card">Empire Loot</string>																			
  <string name="battle_awards">Reward</string>																			
  <string name="used">Used</string>																			
  <string name="kill_comma">Location:</string>																			
  <string name="die_comma">Beliefs</string>																			
  <string name="max_reputation">Legion Annihilated :</string>																			
  <string name="evil_status">Technology:</string>																			
  <string name="delete">Delete</string>																			
  <string name="life_ends">End of Life</string>																			
  <string name="see_you_next_life">See you next life</string>																			
  <string name="next_turn_award">Reward:</string>																			
  <string name="start_event_question">Start event?</string>																			
  <string name="click_event_guide">Click [exclamation mark] to complete guide</string>																			
  <string name="changed_mind">Changed mind</string>																			
  <string name="event_fail">Event Failed</string>																			
  <string name="fail_tips">You failed the event</string>																			
  <string name="see_you_again">Got it</string>																			
  <string name="event_done">Event Completed</string>																			
  <string name="event_done_content">You completed the event.</string>																			
  <string name="how_to_deal_fatal_enemy">Post-war processing.</string>																			
  <string name="take_enemy">Recruit</string>																			
  <string name="enter_your_team">Choose Legion</string>																			
  <string name="kill_fatal_enemy">Peace Talks</string>																			
  <string name="property_gain">Choose one hero</string>																			
  <string name="battle_statistics">Statistical</string>																			
  <string name="battle_report">View Report</string>																			
  <string name="please_confirm">Confirm</string>																			
  <string name="associated_boot">Associated boot: game platform for anti-addiction</string>																			
  <string name="read_write_permission">read write: Ad SDK will apply for this</string>																			
  <string name="location_permission">Location: Ad SDK will apply for this</string>																			
  <string name="app_list_permission">Get installed app list: Sharing feature will request this</string>																			
  <string name="permission_tips">Some potential permissions are listed below:</string>																			
  <string name="permission_tips_google">The game itself does not require any permissions, but Google-related payment capabilities may require some permissions.</string>																			
  <string name="permission_content">License Instruction</string>																			
  <string name="agree">Agree</string>																			
  <string name="quit">Exit</string>																			
  <string name="agreements">Game License User Agreement</string>																			
  <string name="privacy_agreements">Privacy Policy</string>																			
  <string name="agreements_tips">Thanks for choosing our game! Please read the "Privacy Policy" and "Game License User Agreement" before using this game. Click "Agree" to start. If you refuse, you will not be able to enter the game.</string>																			
  <string name="agreements_content">Privacy Policy</string>																			
  <string name="died">Killed</string>																			
  <string name="hurt">Wounded</string>																			
  <string name="in_battle">In battle</string>																			
  <string name="damage_details">Injury Details</string>																			
  <string name="normal_shield_blocks">Each shield type block:</string>																			
  <string name="all_shield_blocks">All shield types block:</string>																			
  <string name="real_damage">Actual Damage:</string>																			
  <string name="battle_failed">Battle failed</string>																			
  <string name="die_in_battle">[%1$s] are annihilated!</string>																			
  <string name="failed_to">You lost to [%1$s]!</string>																			
  <string name="battle_win">Victory</string>																			
  <string name="win_over">You defeated[%1$s]!</string>																			
  <string name="get_key">Get Diamonds</string>																			
  <string name="extra_gain">Extra reward</string>																			
  <string name="battle_info">Battle Info</string>																			
  <string name="life_info">Career Info</string>																			
  <string name="items_in_bag">Spec</string>																			
  <string name="login_result">Login Result</string>																			
  <string name="please_shut_down">Please close the game and try again</string>																			
  <string name="login_fail_tips">Login verification failed, please close the game and try again.</string>																			
  <string name="diamond_not_enough_tips">Honor is not enough, do you want to exchange for more?</string>																			
  <string name="versions">Version:</string>																			
  <string name="allies">Legions</string>																			
  <string name="adventure_skill">Events</string>																			
  <string name="battle_skill">Tactics</string>																			
  <string name="quit_guide_tips">Please click on the blank space to close the guide</string>																			
  <string name="music_paused">Music paused</string>																			
  <string name="music_on">Music on</string>																			
  <string name="sound_effect_paused">Sound off</string>																			
  <string name="sound_effect_on">Sound on</string>																			
  <string name="game_paused">Game paused</string>																			
  <string name="speed_normal">1x speed</string>																			
  <string name="speed_double">2x speed</string>																			
  <string name="quit_page">Close Page</string>																			
  <string name="no_charge_tips">There is currently no way to obtain the privilege value, thanks for your support!</string>																			
  <string name="go_other_get_keys">Please complete more tasks for diamonds</string>																			
  <string name="free">Free</string>																			
  <string name="buff_name">Category:</string>																			
  <string name="buff_source">Source:</string>																			
  <string name="buff_effects">Effect:</string>																			
  <string name="buff_layer">Stacks:</string>																			
  <string name="buff_time">Time:</string>																			
  <string name="turn">Round</string>																			
  <string name="limits">(%1$s only)</string>																			
  <string name="turn_me">Round</string>																			
  <string name="shield">Shield</string>																			
  <string name="continue_or_not">Continue?</string>																			
  <string name="continue_contents">Game in progress, continue?</string>																			
  <string name="continue_game">Continue</string>																			
  <string name="new_game">New Game</string>																			
  <string name="ally_tips_contents">You can still bring more Legions. Ready to start?</string>																			
  <string name="ally_tips">Please select the legion you want before entering the game.</string>																			
  <string name="completed">Completed</string>																			
  <string name="not_complete_yet">Undone</string>																			
  <string name="talent_diamond_not_enough">Not enough honor to learn</string>																			
  <string name="story_added">Card pack added to the game</string>																			
  <string name="story_blocked">Card pack blocked</string>																			
  <string name="finish_game_tips">Game in progress. Please complete the game before switching card packs.</string>																			
  <string name="story_records_max_tips">History book quantity limit reached. Please delete some bools.</string>																			
  <string name="die_good">Nation Collapse</string>																			
  <string name="all_win">Passed</string>																			
  <string name="ending_events">End event</string>																			
  <string name="pause">Pause</string>																			
  <string name="normal_speed">1x speed</string>																			
  <string name="double_speed">2x speed</string>																			
  <string name="triple_speed">3x speed</string>																			
  <string name="x5_speed">5x speed</string>																			
  <string name="x10_speed">10x speed</string>																			
  <string name="when_years_old">Year,</string>																			
  <string name="already_max_star_tips">Already max stars, cannot be upgraded</string>																			
  <string name="card_not_enough">There are not enough cards to upgrade</string>																			
  <string name="ally_up_tips">There is a same-name card with higher star class, please use it to upgrade.</string>																			
  <string name="event_sure_win">Specified event must succeed</string>																			
  <string name="event_sure_fail">Specified event must fail</string>																			
  <string name="relive_ally">Resurrected Legion</string>																			
  <string name="relive">Resurrect</string>																			
  <string name="force_win_title">Resurrect</string>																			
  <string name="force_win_tips">Watch the advertisement to resurrect all legions and complete event up to 3 times per day.</string>																			
  <string name="no_dead_ally">but there is no dead legion</string>																			
  <string name="heal_ally">Heal Legion</string>																			
  <string name="hurt_ally">Legion wounded</string>																			
  <string name="no_hurt_ally">but there is no wounded Legion</string>																			
  <string name="no_alive_ally">but there is no surviving Legion</string>																			
  <string name="gain_diamond">Receive honor</string>																			
  <string name="gain_challenge_money">Gain Challenge Keys</string>																			
  <string name="boost">Boost</string>																			
  <string name="lower">Reduce</string>																			
  <string name="daily_gift">Trade Post Gift</string>																			
  <string name="challenge_shop">Challenge Store</string>																			
  <string name="ad_gift">Ad benefit</string>																			
  <string name="time_error_tips">Time data error, please check your network and try again</string>																			
  <string name="quip_refreshed">Heroes refreshed</string>																			
  <string name="times">Times</string>																			
  <string name="skill_refreshed">Tactic refreshed</string>																			
  <string name="ally_refreshed">Legion refreshed</string>																			
  <string name="congratulations">Congratulations! You have passed the level</string>																			
  <string name="click_to_see">Click to view</string>																			
  <string name="rules">Rules</string>																			
  <string name="adventure_skill_cast">You\'ve used an event card</string>																			
  <string name="choose_life">Select Tech</string>																			
  <string name="choose_personality">Select Location</string>																			
  <string name="choose_destiny">Select Religion</string>																			
  <string name="the">A.D.</string>																			
  <string name="choose_event">Yr</string>																			
  <string name="event_not_ready">Event conditions are not met, please select another</string>																			
  <string name="force_win">The forced event success effect is triggered. The event succeeds</string>																			
  <string name="force_lose">The forced event failure effect is triggered. The event fails</string>																			
  <string name="win">Success</string>																			
  <string name="lose">Failed</string>																			
  <string name="life_background">Initial Technology</string>																			
  <string name="personality">Location</string>																			
  <string name="destiny">National beliefs</string>																			
  <string name="left">Leave</string>																			
  <string name="go_get">Get</string>																			
  <string name="buy">Buy</string>																			
  <string name="bought">Bought</string>																			
  <string name="curse_scene">Siege Weather</string>																			
  <string name="hire">Recruit</string>																			
  <string name="blue_quality">Green</string>																			
  <string name="purple_quality">Blue</string>																			
  <string name="orange_quality">Orange</string>																			
  <string name="carry_to_game">Currently Available for</string>																			
  <string name="ally_to_game_tips">Legions</string>																			
  <string name="skill_to_game_tips">TacticS</string>																			
  <string name="hero_to_game_tips">Heroes</string>																			
  <string name="battle_member_limitation">The number of legions has reached the maximum. New legion cannot be added.</string>																			
  <string name="same_skill_tips">Cannot equip multiple Tactical Cards with the same name</string>																			
  <string name="gain_electric">Gain Privileges Value</string>																			
  <string name="gain_keys">Get Diamonds</string>																			
  <string name="code_used">This code has already been used</string>																			
  <string name="code_not_found">Code not found</string>																			
  <string name="confirm_login">Please make sure you are logged in</string>																			
  <string name="code_error">Please contact the programmer for redemption exceptions</string>																			
  <string name="unlocked_tips">Function unlocked. Thanks for your support!</string>																			
  <string name="code_error_tips2">The redemption code is abnormal, please contact to us for support!</string>																			
  <string name="code_already_used_by_you">You have already used this code</string>																			
  <string name="gain_tcg_comma">Get Empire Loot:</string>																			
  <string name="gain_out_skill">Get a Permanent Tactical Card</string>																			
  <string name="gain_skill_card">Obtained</string>																			
  <string name="lose_skill_card">Lose</string>																			
  <string name="gain_out_equip">Get a permanent hero</string>																			
  <string name="gain_equip">Get Heroes</string>																			
  <string name="lose_equip">Lose Heroes</string>																			
  <string name="gain_out_ally">Get a permanent legion</string>																			
  <string name="gain_ally">Get Legion</string>																			
  <string name="lose_ally">Lose Legion</string>																			
  <string name="play_text31">1. The world situation will appear randomly. Your scouts will bring you information from other empires, whose rise or fall will impact you.</string>																			
  <string name="play_text26">1. Put at least 1 legion on the battlefield to start. In military exercises, no real casualties occur. Legions are restored after the battle. \n 2. Find the tutorial for battle details. \n 3. Battle failure does not end the game. You can surrender to avoid casualties, which will cause event failure and penalties.</string>																			
  <string name="play_text_25">1. Put at least 1 legion in battle to start. In raid, each side can only have 1 legion for a 1v1 battle. \n 2. Find the tutorial for battle details. \n 3. Battle failure does not end the game. Surrender to avoid casualties, which causes event failure and penalties. \n 4. Participating legions do not automatically replenish troops after battle.</string>																			
  <string name="play_text24">1. Put at least 1 legion in battle to start. The weather in siege battles would impact the battle. Learn about the weather before starting (siege battle weather is random). \n 2. Find the tutorial for battle details. \n 3. Battle failure does not end the game. Surrender to avoid casualties, which will cause event failure and penalties. \n 4. Participating legions do not automatically replenish troops after battle. \n 5. Geography cards obtained after certain sieges are important for conquering other empires.</string>																			
  <string name="play_text23">1. Put at least 1 legion in battle to start. Campaigns have strict soldier-type restrictions. Only specified types can be put into battle. \n 2. Find the tutorial for battle details. \n 3. Battle failure does not end the game. Surrender to avoid casualties, which will cause event failure and penalties. \n 4. Participating legions do not automatically replenish troops after battle.</string>																			
  <string name="play_text22">1. Put at least 1 legion in battle to start. The encounter has no special rules. It is a standard 3v3 battle. \n 2. Find the tutorial for battle details. \n 3. Battle failure does not end the game. Surrender to avoid casualties, which will cause event failure and penalties. \n 4. Participating legions do not automatically replenish troops after battle.</string>																			
  <string name="play_text21">1. Put at least 1 legion on the battlefield to start. Enemies Resisting invasion are strong. Try to win by avoiding huge population losses. \n 2. Find the tutorial for battle details. \n 3. Battle failure does not end the game. Surrender to avoid casualties, which will cause event failure and penalties. \n 4. Participating Legions do not automatically replenish troops after battle.</string>																			
  <string name="play_text12">1. In a riot-type event, you need to send 1 Legion to suppress the riot, the success rate of suppression depends on the attributes of your nation. If the riot is successfully suppressed, then you will lose nothing. If the suppression fails or you give up suppressing, your nation will suffer a loss. \n2. Regardless of the success or failure of this suppression, the legions that doing the suppression will not be able to return to your nation.</string>																			
  <string name="play_text11">1. In the Negotiation events, you need to send 1 hero to negotiation. Its success depends on the attributes of your nation. If the negotiation is successful, then you will lose nothing; if the negotiation fails or is abandoned, your nation will suffer losses. \n2. Regardless of the success or failure of the negotiation, the hero who negotiated cannot return to your nation.</string>																			
  <string name="play_text10">1. In Crisis-type events, you must choose to endure 1 type of national crisis, and you will receive a Crisis Card. The Crisis Card will not take effect immediately but will trigger penalties by certain conditions, but you can avoid losses by using skill and luck.</string>																			
  <string name="play_text9">1. In natural disaster-type events, you must choose to endure 1 kind of natural disaster effect. Then you will be affected by the natural disaster: one-time loss of population, resources and other contents, which is unavoidable. \n2.A random natural disaster event will occur every 8th year of the 10 years, so please make sure to survive the catastrophe.</string>																			
  <string name="play_text8">1. During the Diplomatic Event, your nation will engage in diplomacy with other empires, which is a good opportunity to gain specialities and prestige. However, there is also a possibility of lowering prestige due to diplomatic conflicts. \n2.Conquering other empires is an important diplomatic event, you need to occupy their capital first before your conquest. \n3. Aligning with another empire requires the highest level of Diplomatic Prestige. Aligning will make this empire never invade you again.</string>																			
  <string name="play_text7">1. In recruitment events, you can recruit a hero to strengthen your legion. Of course, gold are needed. Note: the hero is not permanent, but only for this game.</string>																			
  <string name="play_text6">1. In Military Academy events, you can select a new tactic to strengthen the legion. Of course, gold are needed. Note: Tactic is not permanent, but only for this game.</string>																			
  <string name="play_text5">1. In Mercenary Camp events, you can hire a legion to fight for you. Costs gold. Note: Of course, gold coins are needed. Note: legion is not permanent, but only for this game.</string>																			
  <string name="play_text4">1. In the Conscription event, you can have legions with heavy casualties replenished to full strength by gold coins.</string>																			
  <string name="play_text3">1. In the Construction event, you need to select different types of buildings to build for your nation. After the completion of construction, you will receive a Building Card, which causes an increase in the population (as well as many other effects) and is key to the survival of your country. \n2. The 5th year of every 10 years is a good time for the nation for construction, so make sure you don\'t miss it. \n3. When your country has grown to over 200 years, there is a chance to build wonders. Wonders can increase the population a lot with powerful effects. But they can be quite expensive. \n4. Buildings require lots of resources. Completing more battles and events will allow you to acquire resources and increase reproduction.</string>																			
  <string name="play_text2">1. In the Policy events, you need to establish a policy for your nation, and you will obtain the corresponding policy card. It can be used to improve the attributes of the Legion (as well as some other effects), which is the key to resisting invasions and winning battles. \n2.The 3rd year of every 10 years is a good time for your nation to establish policies, so make sure you don\'t miss it. \n3. Establishing policies requires national attributes. Conducting more technological research, development and recruiting heroes can help you improve your national attributes.</string>																			
  <string name="play_text1">1. In the Development event, you need to select a technology for your nation. You will get the corresponding technology card to improve your nation\'s attributes to cope with challenges later. \n2. The 1st of every 10 years is a good time to develop technology, so make sure you don\'t miss it. \n3. Some rare Religious Events (The Religion depends on your nation\'s beliefs) may appear in the Development Events. It tends to have complex and powerful linkage effects. \n4.Developing technology often requires resources. Completing more battles and events will allow you to acquire resources and increase production.</string>																			
  <string name="play_name1">Develop</string>																			
  <string name="play_name2">Policy</string>																			
  <string name="play_name3">Build</string>																			
  <string name="play_name4">Recruitment</string>																			
  <string name="play_name5">Mercenary</string>																			
  <string name="play_name6">Academy</string>																			
  <string name="play_name7">Conscription</string>																			
  <string name="play_name8">Diplomacy</string>																			
  <string name="play_name9">Disaster</string>																			
  <string name="play_name10">Crisis</string>																			
  <string name="play_name11">Negotiation</string>																			
  <string name="play_name12">Riots</string>																			
  <string name="play_name31">World</string>																			
  <string name="play_name21">Invasion</string>																			
  <string name="play_name22">Encounters</string>																			
  <string name="play_name23">Campaign</string>																			
  <string name="play_name24">Siege</string>																			
  <string name="play_name25">Raid</string>																			
  <string name="play_name26">Exercises</string>																			
  <string name="play_name101">Technology</string>																			
  <string name="play_name102">Location</string>																			
  <string name="play_name103">Beliefs</string>																			
  <string name="tutor_title1">National</string>																			
  <string name="tutor_title2">Attributes</string>																			
  <string name="tutor_title3">Combat Basics</string>																			
  <string name="tutor_title4">Advancement</string>																			
  <string name="tutor_title5">Events</string>																			
  <string name="tutor_title6">Combat</string>																			
  <string name="tutor_title7">Legions</string>																			
  <string name="tutor_title8">TacticS</string>																			
  <string name="tutor_title9">Event Cards</string>																			
  <string name="tutor_title10">Heroes</string>																			
  <string name="tutor_title11">Empire Loot</string>																			
  <string name="tutor_title12">Reward</string>																			
  <string name="tutor1">1. Your country has 7 attributes. Science: represents scientific development level. Politics: represents political ability. Military: represents military strength. Religion: represents religious influence. Business: represents business development. Art: represents artistic achievement. Population: the foundation of your nation and the symbol of prosperity. When it decreases to zero, your nation collapses. \n 2. Role of attributes: Important events and policies require national attributes. Without them, you can\'t get policy cards or complete events. \n 3. Improve attributes: Meet events for technology cards, get Heroes, and learn talents for permanent boosts. Some policy and building cards can also improve attributes.</string>																			
  <string name="tutor2">Legion has 8 battle attributes: -Attack: damage strength -Defense: reduces the damage the legion will be taken -Strength: when it decreases to zero, the Legion dies -Crit: critical hit chance -Crit Damage: extra damage from critical hits -Block: avoids damage -Leech: replenishes troops based on damage dealt -Speed: act faster.</string>																			
  <string name="tutor3">-Each turn, legions and enemies release active tactics not in cooldown; you can trigger a tactic when its conditions are met; Release passive tactics; Perform a normal attack each turn unless a special effect changes it. \n -Each turn, actions alternate from high to low speed. The battle is limited to 30 turns. If enemies are not defeated after 30 turns, you will lose. \n -Five damage types: [Melee] [Ranged] [Siege] [Sacred] [Chaos]. Each type has different effects on armor types. \n -Attack target: The default attacking order is the enemy in front. Taunt skills will change enemies\' target to a taunting legion.</string>																			
  <string name="tutor4">\n -Destruction: Passive buffs/debuffs from tactics cannot be destroyed. If from active/triggered tactics, cannot be destroyed if labeled [indestructible]. \n -Effect duration rounds: The effect\'s duration is counted from the time it is created. \n -Cooldown: Tactics cannot be used during cooldown round. \n -Control: - [Chaos]: Can\'t release active tactics. [Isolation]: Can\'t replenish troops. [Rampage]: Releases single active tactic to random target. [Shake]: Can\'t release triggered tactics. [Disarm]: Can\'t perform normal attacks.</string>																			
  <string name="tutor5">-Non-combat events have different rules and functions. Some are keys to national development, others are troubles. \n -Development events: Select technology for your nation. Get a technology card to improve attributes. The first year of every 10 years is suitable for technology. There are rare religious events which have powerful effects on development events. Developing technology requires resources. So please complete battles and events to get resources. \n -Policy events: Select a certain policy for your nation. Get a policy card to improve your legion attributes. The 3rd year of every 10 years is suitable for policy development. Policies require national attributes. Improve them through research and Heroes. \n -Construction events: Select building cards for the country. Get building cards to increase population and resources, which is the key to survival. The fifth year of every 10 years is good for construction. After 200 years, you can build wonders with powerful effects spending high cost. Building requires resources. please complete battles and events to get resources. \n -Recruitment events: Replenish troops of legion with heavy casualties. Costs golden coins. \n -Mercenary Camp events: Hire legions to fight with golden coins. Legions you hired are not permanent. \n -Military Academy events: Learn new tactics to strengthen legions with golden coins. The tactics are not permanent. \n -Recruitment events: Choose heroes to strengthen legion attributes with golden coins. The hero you recruit is not permanent. \n -Diplomatic events: Conduct diplomacy with empires. Get specialities and reputation. Conflicts may reduce your reputation. If you want to conquer empires, you need to occupy their capital first. Alliances require the highest reputation, and your Allies won\'t invade your nation anymore. \n -Natural Disaster events: Cause a natural disaster to your nation, leading to one-time losses. Unavoidable. Every 10 years, a random natural disaster will occur. \n -Crisis events: Cause national crisis. You may suffer from trigger penalties under certain conditions but you can avoid losses with skills and luck. \n -Negotiation events: Send your hero to negotiate with other empires! Success depends on your national attributes. Failure or abandonment may cause losses. The hero for negotiation cannot return regardless of success. \n -Riot events: You need to send legions to suppress the riot. Success depends on your national attributes. Failure or abandonment may cause losses. Legions suppressing the riots cannot return regardless of success.</string>																			
  <string name="tutor6">-Battle events require at least 1 Legion. Winning grants rewards, while failure or surrender does not end the game but causes losses. \n -After the battle, participating legions do not automatically replenish troops. \n -Resist invasion events: Fight against strong enemies, and win to avoid huge population losses. \n -Encounter events: Standard 3v3 battles. \n -Campaign events: Battles with certain soldier-type restrictions. Only specified types can be deployed. \n -Siege events: there will be weather impacting the battle. Learn about the weather before starting. Geography cards from sieges can be important for conquering other empires. \n -raid events: 1v1 battles. \n -Exercise events: No real casualties, Legions restored after battle.</string>																			
  <string name="tutor7">-Legions are war tools. Without them, you will fail combat events. \n -Carry a limited number of legions to protect your nation. You can obtain those cards through novice tasks, trade shops, and special events. The initial slot increases as your nation grows up. \n -You can upgrade Legions with the name. They can reach a maximum of 5 stars, which would greatly improve attributes and tactics. \n -Get temporary legions in events. Some events reward permanent Legions.</string>																			
  <string name="tutor8">-TacticS are effective only inserted in Legion slots. It can increase combat effectiveness. \n -Carry a limited number of TacticS to strengthen your nation. You can obtain them through novice tasks, trade shops, and special events. The initial slot increases as your nation grows up. \n -You can upgrade TacticS with the same name cards. They can reach a maximum of 5 stars, which would improve their level and effects. \n -You can get temporary TacticS in events. Some events may award permanent TacticS.</string>																			
  <string name="tutor9">-Event cards are key props. There are 7 types: Technology cards, the key for development, can improve attributes and resources. Policy cards, the key for combat, can improve legion attributes. Building cards, the key for survival, can increase population and resources. Religious cards have powerful effects. Natural disaster cards may cause losses. Crisis cards may trigger penalties depending on their effects. Event cards are cleared at the end of each game.</string>																			
  <string name="tutor10">-Heroes can improve country and legion attributes, but some of they have lifespan limits. You can choose to use them manually or automatically. A hero\'s lifespan is consumed after entering battle, which cannot be dismounted after entering. \n -You can take a limited number of Heroes in a game and you can obtain them through novice tasks, trade shops, and special events. The initial slot increases as your nation grows up. \n -You can upgrade Heroes with the same name max to 5 stars, which will increase their lifespan. \n -You can get temporary Heroes in events. Some events reward permanent Heroes.</string>																			
  <string name="tutor11">There are currently two main types of Imperial Loot to collect, including 5 generic types of Imperial Loot (Swords, Books, Scrolls, Gems, Keys) and exclusive Loot for each Empire. \n- You can complete various events to gain Imperial Loot Acquisition, and the event may contain random Imperial Loot. You can only collect 1 piece of each type of empire loot with the same name. \n-Collecting repeated Imperial Loot can be exchanged for rare Legion/Tactic/Epic Characters.</string>																			
  <string name="tutor12">Permanent Rewards: Legions, Tactical Cards, and heroes obtained within the game are divided into temporary and permanent ones. Temporary cards are limited to use in the current game only, while permanent cards can be taken out of this game and used in each game. \n -Honor: At the end of the game, you will acquire honour. The amount of honour is determined by how long your nation has survived. Only nations that have survived for at least 30 years are eligible for the honour, which can be used to add talents. \n -Diamonds: Diamonds can be earned by completing daily, achievement quests, and guides, and can be used to exchange for permanent Legions, TacticS, and heroes at the Trading Shop.</string>																			
  <string name="skill_label1">Damage</string>																			
  <string name="skill_label2">Replenish</string>																			
  <string name="skill_label3">Strengthen</string>																			
  <string name="skill_label4">Weaken</string>																			
  <string name="skill_label5">Defense</string>																			
  <string name="skill_label6">control</string>																			
  <string name="skill_label7">Negative</string>																			
  <string name="skill_label8">Special</string>																			
  <string name="not_open">Not open yet</string>																			
  <string name="life_time">Nation survival time:</string>																			
  <string name="normal_day_rank">Daily ranking</string>																			
  <string name="difficult_day_rank">Difficulty Daily Ranking</string>																			
  <string name="normal_yesterday_rank">yesterday\'s ranking</string>																			
  <string name="difficult_yesterday_rank">Difficult Yesterday\'s ranking</string>																			
  <string name="check_rank_tips">Please go to Challenge Ranking to confirm your ranking yesterday.</string>																			
  <string name="hot_new_game">Hot New Games</string>																			
  <string name="delete_all_records">Delete all history books?</string>																			
  <string name="cant_reverse_tips">Do you want to delete all the history books? This action is irreversible!</string>																			
  <string name="user_cloud_save_title">There is cloud save data. It is automatically loading!</string>																			
  <string name="save_upload_time">Upload time:</string>																			
  <string name="have_save_tips">You have a cloud archive, please confirm whether to use it? \nIf not, your cloud archive may be overwritten by the new archive.\nPlease choose carefully!</string>																			
  <string name="use">Use</string>																			
  <string name="save_use_tips">Each account can only perform the cloud save operation once a day. Using cloud saves will lose all current progress! Including paid content! Please operate carefully! Confirm whether to use it or not?</string>																			
  <string name="save_dump_tips">Are you sure to start a new archive? The old cloud archive will be overwritten due to the automatic/manual upload and cannot be retrieved, please confirm again!</string>																			
  <string name="event_start_condition">Condition:</string>																			
  <string name="event_start_condition_consume">Consumption:</string>																			
  <string name="challenge_task">Challenge Mission</string>																			
  <string name="today_challenge">Today\'s challenges:</string>																			
  <string name="challenge_barrier">Obstacles:</string>																			
  <string name="enemy_enhance">Enemy Enhancement:</string>																			
  <string name="change_story_bag">Change Empire Pack</string>																			
  <string name="unload">Unload.</string>																			
  <string name="unlock_tips">Unlock after upgrading Legion</string>																			
  <string name="equip_tips">Please Insert TacticS in the game</string>																			
  <string name="produce">Produced by: Moyu Game Studio</string>																			
  <string name="contact1">Contact Us</string>																			
  <string name="about_us">We are MoYu Game Studio, different from other studios. We make unique games.</string>																			
  <string name="about_game">Hello, we are Moyu Game Studio, a small team making creative roguelike games. We\'ve made "I Turned the Heroes into Meat Pigeons," "Endless Elementalist," "Elemental Invaders," "Moyu Battle," and "Demon Hunting Guardian." High freedom in skill construction, massive skill library, complex choices, and card effects are common features of our games. Our new game is a unique roguelike adventure, a bit like a business simulation, derived from "I Turned the Heroes into Meat Pigeons." You start by selecting the country\'s location (Gold Coast!), initial technology (animal husbandry!), and national belief (Taoism!). Face national developments and crises every year. Choose one of three actions. In the first year, I chose Chemistry, boosting technological attributes. Second year, my legion clashed with Rome, and I won. Third year, I implemented the Imperial Examination System, boosting strength. Eighth year, a flood caused population losses. Tenth year, Qin Empire invaded, and I repelled them. The next ten years are another cycle of development, evolution, tribulation, and rebirth. My goal: survive 500 years, grow population to 4 million, ally with Egypt, capture Xianyang, conquer Qin Empire. Simple operation, deep strategy. Over 300 combat skills, 700 event cards, 1100 national events. More empire packs coming: Qin Empire, Rome, Egypt, Arthur Dynasty, Spain, Wei Empire, Macedonia, Atlantis, Persia, Greece. Your love motivates us to update.</string>																			
  <string name="choose_event1">Year, you have chosen</string>																			
  <string name="upload_exception">Abnormal account ID, archive upload failed.</string>																			
  <string name="too_many_use_save_tips">One cloud save operation per day. Try again tomorrow.</string>																			
  <string name="id_error">Abnormal account ID, archive download failed.</string>																			
  <string name="sync_tips">The archive synchronization is successful, restart automatically after 2 seconds</string>																			
  <string name="error_operate">Operation failed:</string>																			
  <string name="sync_error">Failed to synchronize archive, please check your network</string>																			
  <string name="pack_error">Local archive packaging error, please report it</string>																			
  <string name="error_sync2">Archive sync error, please report it</string>																			
  <string name="no_save">No cloud archive</string>																			
  <string name="auto_sync_tips">Automatic archive sync successful</string>																			
  <string name="root_tips">Rooted devices do not support the game, sorry!</string>																			
  <string name="backward_version">Data compatibility error, please do not revert your version</string>																			
  <string name="datastore_error">Data corrupted, cannot enter the game.</string>																			
  <string name="story_at_least_one">Select at least three Empire card packs</string>																			
  <string name="element_not_enough">Resources insufficient</string>																			
  <string name="extra_element_not_enough">Resource production insufficient</string>																			
  <string name="reputation_not_enough">Reputation insufficient</string>																			
  <string name="badge_not_enough">Specialty products insufficient</string>																			
  <string name="adventure_prop_not_enough">Country attributes insufficient</string>																			
  <string name="resource_name_1">Gold</string>																			
  <string name="resource_name_2">Wood</string>																			
  <string name="resource_name_3">Stone</string>																			
  <string name="resource_name_4">Iron Ore</string>																			
  <string name="resource_name_5">Food</string>																			
  <string name="battle_pass_content">Complete the battle pass tasks to gain experience. You can get rewards when the experience accumulates enough to upgrade to a total of 30 levels.</string>																			
  <string name="battle_pass2_content">Complete Rise of Empires missions to earn Empire stamps. You can get rewards when the stamps are accumulated enough to upgrade to a total of 60 levels.</string>																			
  <string name="war_pass_exp">battle pass Experience</string>																			
  <string name="war_pass2_exp">Empire Seal</string>																			
  <string name="world_event_cast">World situation</string>																			
  <string name="greater_than_5_seconds">The interval between two ad clicks needs to be more than 5 seconds</string>																			
  <string name="please_feedback">Suspected cheating. If it is a false positive, please report it to us.</string>																			
  <string name="upload_time">Upload time:</string>																			
  <string name="no_cloud_record">No cloud archive</string>																			
  <string name="auto_upload_tips">Archives will be auto-uploaded regularly. Each account can only overwrite the local archive with a cloud archive once per day.</string>																			
  <string name="upload_local_savepoint">Uploading local archive</string>																			
  <string name="use_cloud_savepoint">Use cloud archive</string>																			
  <string name="confirm_upload_local_savepoint_tips">Only one archive can be saved in the cloud. After uploading, the new archive will overwrite the old one, are you sure to upload?</string>																			
  <string name="upload_success">Archive uploaded successfully</string>																			
  <string name="production_capacity">Output</string>																			
  <string name="unlock_success">Unlocked successfully</string>																			
  <string name="made_my_choice">I\'ve chosen</string>																			
  <string name="need">Required</string>																			
  <string name="level">Level</string>																			
  <string name="success_rate">Success Rate</string>																			
  <string name="quality_level_required">Quality required</string>																			
  <string name="ask_for">Require</string>																			
  <string name="select_who_tips">Select the hero for negotiation</string>																			
  <string name="negotiation_success">Negotiation successful</string>																			
  <string name="negotiation_failed">Negotiation Failed</string>																			
  <string name="confirmation_of_override_tips">Each account can only overwrite the local archive with the cloud archive once a day. The local archive will not be retained after overwriting, are you sure to overwrite?</string>																			
  <string name="please_choose">Please select legions to suppress the riot</string>																			
  <string name="error_tips">Treasure chest opening abnormality, please report it to us</string>																			
  <string name="please_login_tips">Please login in before playing the game</string>																			
  <string name="get_tips">Get:</string>																			
  <string name="create_country">Establish a nation</string>																			
  <string name="start_game">Start Game</string>																			
  <string name="choose_person">Select initial Hero:</string>																			
  <string name="choose_tips">Select Initial Tactic:</string>																			
  <string name="choose_tips2">Select Initial Legion:</string>																			
  <string name="random_name">Random nation Name:</string>																			
  <string name="skilltype_1">Legion Tactics</string>																			
  <string name="skilltype_2">TacticS</string>																			
  <string name="skilltype_3">Heroes</string>																			
  <string name="skilltype_4">Event Cards</string>																			
  <string name="skilltype_5">Talent</string>																			
  <string name="skilltype_other">Weather Card</string>																			
  <string name="lasted">Duration</string>																			
  <string name="king">King:</string>																			
  <string name="country_location">Location:</string>																			
  <string name="country_religion">Religion:</string>																			
  <string name="country_kill">Annihilation Legion:</string>																			
  <string name="country_tech">R&amp;D Technology:</string>																			
  <string name="country_policy">Announce policy:</string>																			
  <string name="country_build">Build buildings:</string>																			
  <string name="country_crisis">Crisis response:</string>																			
  <string name="country_disaster">Disaster response:</string>																			
  <string name="country_get_religion">Develop religion:</string>																			
  <string name="no_records_yet">No historical records</string>																			
  <string name="battlepass_not_enough">Insufficient battle pass experience</string>																			
  <string name="battlepass2_not_enough">Insufficient Imperial Seals</string>																			
  <string name="talent1">Political</string>																			
  <string name="talent2">Tech</string>																			
  <string name="talent3">Military</string>																			
  <string name="vip">Privilege</string>																			
  <string name="select_story">Choose Empire Card Pack</string>																			
  <string name="sign_title">Sign Rewards</string>																			
  <string name="no_tcg_toast">No empire loot yet, collect in game</string>																			
  <string name="change_story">Change Empire Pack</string>																			
  <string name="menu1">Legion</string>																			
  <string name="menu2">History</string>																			
  <string name="menu3">Trading Post</string>																			
  <string name="warpass_tips">Go to battlePass Tasks for gaining experience</string>																			
  <string name="unlock_warpass">Unlock battle pass</string>																			
  <string name="unlock_warpass2">Unlock Rise of Empire</string>																			
  <string name="warpass_unlocked">battle pass unlocked</string>																			
  <string name="warpass2_unlocked">Rise of Empire unlocked</string>																			
  <string name="gain_warpass_exp">Gain battle pass Experience</string>																			
  <string name="gain_warpass2_exp">Get Imperial Seal</string>																			
  <string name="warpass_theme_award">Theme rewards</string>																			
  <string name="effecting">Event card in effect</string>																			
  <string name="tcg_award">Collect Rewards</string>																			
  <string name="level_info">Level Information</string>																			
  <string name="statistic">Statistics</string>																			
  <string name="capture">Capture</string>																			
  <string name="ask_join">Recruit</string>																			
  <string name="develop">Development</string>																			
  <string name="stop_war_talk">Peace Talks</string>																			
  <string name="rest">Rest</string>																			
  <string name="kill_all">Destroy</string>																			
  <string name="year_end">End of year</string>																			
  <string name="only_ally_can_adjust">Only your legion can be adjusted in tactics</string>																			
  <string name="in_battle_can_not_adjust">Tactics cannot be adjusted during battle</string>																			
  <string name="today_game_times_out">Today\'s game count has been used up</string>																			
  <string name="today_game_relive_times_out">Today\'s resurrection count has been used up</string>																			
  <string name="today_game_times_out_tips">Do you want to watch ads to break the limit?</string>																			
  <string name="talent_condition1">Previous talent level reaches</string>																			
  <string name="talent_condition2">Unlock after level</string>																			
  <string name="talent_condition3">Reputation reaches</string>																			
  <string name="year_zero">AD 1</string>																			
  <string name="build">Established</string>																			
  <string name="your_country_became">Your nation became</string>																			
  <string name="riot_success">Suppression successful</string>																			
  <string name="riot_failed">Suppression failed</string>																			
  <string name="select_riot_ally">Select legion to suppress the riot</string>																			
  <string name="select_negotiation_heroes">Select hero for negotiation</string>																			
  <string name="heal">Troop Replenishment</string>																			
  <string name="skill_element_1">Technology</string>																			
  <string name="skill_element_2">Policy</string>																			
  <string name="skill_element_3">Religious</string>																			
  <string name="skill_element_4">Building</string>																			
  <string name="skill_element_5">Geography</string>																			
  <string name="skill_element_6">Disaster</string>																			
  <string name="skill_element_7">Crisis</string>																			
  <string name="hero_element_1">Science Heroes</string>																			
  <string name="hero_element_2">Political Heroes</string>																			
  <string name="hero_element_3">Military Heroes</string>																			
  <string name="hero_element_4">Religious Heroes</string>																			
  <string name="hero_element_5">Business Heroes</string>																			
  <string name="hero_element_6">Artistic Heroes</string>																			
  <string name="mercenary_price_down">Hiring prices down</string>																			
  <string name="country_name_prefix">north, south, east, west, center, new, ancient, red stone, blue sea, yellow sand, green forest, black soil, white snow, green peaks, purple sky, golden region, emerald, amber, jade, zhuhai, guilin, pine trees, willows, cypress, locust, peach blossom, plum snow, cherry red, orange sun, bamboo green, tea green, silk, ribbon, tiger eye, dragon scale, phoenix feather, rabbit hair, deer antler, eagle wing, pigeon feather, fish fin, crab shell, tortoise shell, crane, crow, wild goose, pheasant, bird, flowers, clouds, rain, gold, sunrise, moonset, starlight, night, morning, dusk, spring water, flame, golden light</string>																			
  <string name="country_name_infix">eagle eye, dragon heart, tiger soul, wolf soul, bear will, deer leap, snake walk, fish swim, bird fly, sword edge, shield wall, ferry, pass, tower, fortress, city wall, well, windmill, thunder, stone bridge, gold mine, silver spring, academy, theater, museum, book, school, university, opera, palace, temple, church, oasis, canyon, mountains, forest, battlefield, parliament, court, altar, wizard, priest, bishop, knight, monarch</string>																			
  <string name="country_name_suffix">kingdom, state, republic, boundary, domain, country, zone, federation, prefecture, capital, city, continent, island, archipelago, mountain range, plain, grassland, desert, forest, river, lake, bay, sea, ocean, basin, township, tower, fort, fortress, temple, palace, valley, road, way, path, hinterland, front line, barrier, parish, area, zone, circle, ring, gate, pass, canyon, highland, lowland, frontier, border, wall, line of defense, trade point, trade route, trade area, trading post, outpost, mark, point, altar, holy place, gathering place, club, mansion, memorial place, ruins, landmark, marker, milestone</string>																			
  <string name="fatal_sub_text1">Capture and recruit enemy troops. Get a Legion from one of three choices.</string>																			
  <string name="fatal_sub_text2">Accept peace talks and get one of three Heroes</string>																			
  <string name="race_sub_text1">Recruit defeated troops after the war, and get Legions and gold coins.</string>																			
  <string name="race_sub_text2">Rest and recuperate after the battle for a Tactic and gold coins.</string>																			
  <string name="curse_sub_text1">Exploit a city\'s productivity for a Geography Card and a Yield Booster.</string>																			
  <string name="curse_sub_text2">Destroy the city\'s industry for a Geography card and resources.</string>																			
  <string name="choose_your_award">Choose your reward:</string>																			
  <string name="enemy">Enemy:</string>																			
  <string name="select_ally">Select Legion</string>																			
  <string name="select_init_ally">Select Initial Legion</string>																			
  <string name="select_init_hero">Select Initial Heroes</string>																			
  <string name="select_hero">Select Heroes</string>																			
  <string name="select_skill">Select Tactic</string>																			
  <string name="check">Details</string>																			
  <string name="vip_level_not_enough">Insufficient privilege level</string>																			
  <string name="round_tips_prefix">No.</string>																			
  <string name="my_fortune">My Legion</string>																			
  <string name="life_screen_title">Imperial History Book</string>																			
  <string name="share_rewards">Share Rewards</string>																			
  <string name="famous">Celebrities</string>																			
  <string name="resource_desc_1">Gold: for recruitment, diplomacy, mainly from combat and annual production</string>																			
  <string name="resource_desc_2">Wood: for construction, development, mainly from annual production</string>																			
  <string name="resource_desc_3">Stone: for construction, development, mainly from annual production</string>																			
  <string name="resource_desc_4">Iron ore: for construction, development, mainly from annual production</string>																			
  <string name="resource_desc_5">Food: for construction, development, mainly from annual production</string>																			
  <string name="race_desc_1">Infantry: good at close combat, excellent blocking</string>																			
  <string name="race_desc_2">Cavalry: good at close combat, excellent speed</string>																			
  <string name="race_desc_3">Archers: good at long-range, excellent critical damage</string>																			
  <string name="race_desc_4">Instruments: good at siege, excellent crithit</string>																			
  <string name="race_desc_5">Priesthood: good at holy and chaos, excellent absorption</string>																			
  <string name="forever">Permanent</string>																			
  <string name="score_rank">You surpassed</string>																			
  <string name="score_rank2">Players.</string>																			
  <string name="share_item1">Rougelike Nation Management</string>																			
  <string name="share_item2">Choose one of three technology policies</string>																			
  <string name="share_item3">Natural disaster every ten years</string>																			
  <string name="share_item4">Imperial Diplom events</string>																			
  <string name="share_item5">Resist great empire invasion</string>																			
  <string name="share_item6">Epic legion wars</string>																			
  <string name="share_tips">Your Majesty, latest world situation: \nQin Empire implemented [burning books], heading for hegemony\nRome developed [materialism], national strength increased\nEgypt traded on Oasis Road\nArthur assembled Knights to invade!\nSpain defeated at Pavia, stagnation\nSparta rising. Ally or conquer?</string>																			
  <string name="share_award_tips">Share promo code with a friend. Both get rewards:</string>																			
  <string name="already_shared">Friends who have already shared</string>																			
  <string name="share_code">Promo code:</string>																			
  <string name="accepted_share">Accepted Shares</string>																			
  <string name="click_to_copy">Click to copy</string>																			
  <string name="select_ally_tips">Click Militia</string>																			
  <string name="guide_skill_tips">tactics Equipped</string>																			
  <string name="click_skill_guide">Click first tactics to complete guide</string>																			
  <string name="select_ally_guide">Select legion to fight to complete guide</string>																			
  <string name="capture_share">Screenshot and share</string>																			
  <string name="share_and_reward">Share and get rewards</string>																			
  <string name="share_title">My country is being invaded by Qin Empire, help defend!</string>																			
  <string name="peek_tips">please Buy before upgrade star</string>																			
  <string name="setting_hide_electric">Be anonymous in all rankings</string>																			
  <string name="setting_text_color">Fix text color (enable if abnormal)</string>																			
  <string name="top_like_count_one_day">Maximum 5 likes per day</string>																			
  <string name="special_ally">limited edition Legion</string>																			
  <string name="special_skill">limited edition Tactic</string>																			
  <string name="special_hero">limited edition Heroes</string>																			
  <string name="auto_effect">Auto Enable</string>																			
  <string name="effect">Enable</string>																			
  <string name="extra_skill_buff">Gain Attribute Enhancement</string>																			
  <string name="extra_skill_debuff">Gain Attribute Reduction</string>																			
  <string name="cant_get_yet">Sorry, unavailable currently</string>																			
  <string name="switch_war_pass">Switch battlepass</string>																			
  <string name="switch_war_pass_negative_tips">You need to complete Sparta battlepass before you can start Maya battlepass , if you cut to Maya battlepass without completing Sparta battlepass , the experience will be negative.</string>																			
  <string name="tips_300">After 300 years, game difficulty increases.</string>																			
  <string name="unequip_all_tips">Remove all tactics from legion with one click?</string>																			
  <string name="unequip_all">Unload tactics</string>																			
  <string name="luntan_page">Forum Check-in Plan</string>																			
  <string name="touch_ad_tips">Watch ad to get</string>																			
  <string name="filter">Filter</string>																			
  <string name="order">Sort</string>																			
  <string name="random_country_name">Random nation Name</string>																			
  <string name="close">Close Page</string>																			
  <string name="explain">info</string>																			
  <string name="like">Like</string>																			
  <string name="remove">Remove</string>																			
  <string name="empty_slot">Blank column</string>																			
  <string name="select_your_award">Choose your reward:</string>																			
  <string name="day_of">%1$s day</string>																			
  <string name="not_enough_day">Insufficient login days</string>																			
  <string name="is_paying">Payment in progress, please wait</string>																			
  <string name="order_error">Order failed</string>																			
  <string name="pay_shut_down">Payment channel unavailable, please wait</string>																			
  <string name="pay_order">Order</string>																			
  <string name="error_order">Order Error</string>																			
  <string name="order_money">Amount:%1$s</string>																			
  <string name="order_id">Order number: %1$s</string>																			
  <string name="error_order_tips">If paid but no reward, copy order number and contact service %1$s.</string>																			
  <string name="pay_ok">Payment successful</string>																			
  <string name="pay_cancel">Payment cancelled</string>																			
  <string name="pay_error">Payment failed</string>																			
  <string name="pay_blocking_tips">Order confirmation, please wait…\nIf unpaid, click Cancel\nIf paid, don\'t close!!!</string>																			
  <string name="error_order_info">Order error. If paid, please screenshot and contact service\nOrder name: %1$s\nOrder id: %2$s\nUser name: %3$s\nError info: %4$s</string>																			
  <string name="order_confirming">Order confirmation</string>																			
  <string name="copy">Copy</string>																			
  <string name="award_list_version">Welcome to [ ] version: %1$s, happy gaming~</string>																			
  <string name="not_effect">Not active</string>																			
  <string name="effect_multiple_times">Multi-use</string>																			
  <string name="effected">effected</string>																			
  <string name="real_money_unit">Yuan</string>																			
  <string name="real_money_dollar">$</string>																			
  <string name="unlock_all_gain">Unlock rewards after purchase:</string>																			
  <string name="unlock_gain_now">Buy now and collect:</string>																			
  <string name="battle_pass_award">Reward</string>																			
  <string name="battle_pass_quest">Task</string>																			
  <string name="switch_session_tips">Completed season battlepass. Auto-switched to %1$s season.</string>																			
  <string name="ad_sdk_init_failed">Ad SDK init failed, ads can\'t play, please report it</string>																			
  <string name="ad_still_playing">Ad still playing. If abnormal, kill process and re-enter.</string>																			
  <string name="review_result_toast">Thank you for support, happy gaming</string>																			
  <string name="rank_star_title">Do you like this game?</string>																			
  <string name="review_text">Your feedback motivates us to improve. Click stars to rate.</string>																			
  <string name="language_title">Language</string>																			
  <string name="cloud_save_tips">Archives will be uploaded automatically at regular intervals, and each account can only overwrite the local archive with a cloud archive once per day.</string>																			
  <string name="upload_save">Uploading a local archive</string>																			
  <string name="use_cloud_save">Use cloud archive</string>																			
  <string name="no_cloud_save">No cloud archive</string>																			
  <string name="upload_save_time">Upload time:</string>																			
  <string name="cloud_only_one_tips">Only one archive can be saved in the cloud, after uploading, the new archive will overwrite the old one, Confirm upload?</string>																			
  <string name="cloud_tips">Warning! Please click [Uploading a local archive] first before using cloud storage, otherwise you may lose your current game data including rechargeable items! Are you sure you want to use cloud storage?</string>																			
  <string name="not_effected">Not enabled</string>																			
  <string name="ad_for_double_gain">Whether to watch the ad to receive double rewards?</string>																			
  <string name="new_task_tab1">Growth</string>																			
  <string name="new_task_tab2">Collect</string>																			
  <string name="new_task_tab3">Treasure</string>																			
  <string name="new_task_tab4">Consume</string>																			
  <string name="new_task_tab5">Recharge</string>																			
  <string name="new_task_tab5_taptap">Recharge</string>																			
  <string name="time_left">Left:</string>																			
  <string name="time_left_day">Day</string>																			
  <string name="ad_money">Luck Value</string>																			
  <string name="ad_money_desc">Ad Luck Value</string>																			
  <string name="lottery_title1">Legend Roulette</string>																			
  <string name="lottery_title2">Epic Roulette</string>																			
  <string name="lottery_tab1">Roulette</string>																			
  <string name="lottery_tab2">Gift Pack</string>																			
  <string name="reset_lottery">Reset Roulette</string>																			
  <string name="do_lottery">Spin</string>																			
  <string name="reset_lottery_tips">You can reset the roulette up to %1$d times per event</string>																			
  <string name="do_lottery_tips">Items already drawn will not be duplicated, and after 8 spins, you will receive all rewards on the roulette!</string>																			
  <string name="wait">Please wait</string>																			
  <string name="seven_day_title">7-Day Event</string>																			
  <string name="ad_pool_title">Ad Pool(updated regularly)</string>																			
  <string name="google_login">Google Login</string>																			
  <string name="tourist_login">Tourist Mode</string>																			
  <string name="login_mode">Login Mode</string>																			
  <string name="archive_upload_ok">Archive uploaded</string>																			
  <string name="lottery_money">Empire Relic</string>																			
  <string name="lottery_run_out">Running out of Spin chances</string>																			
  <string name="lottery_reset">Lottery rewards have been reset</string>																			
  <string name="lottery_money_not_enough">Insufficient relics</string>																			
  <string name="pvp">Arena</string>																			
  <string name="pvp_shop">Arena Shop</string>																			
  <string name="pvp_rank">Arena ranking</string>																			
  <string name="pvp_rank1">Real-time ranking</string>																			
  <string name="pvp_rank2">Yesterday\'s ranking</string>																			
  <string name="pvp_choose_enemy">Select an opponent</string>																			
  <string name="pvp_quest">Arena Quest</string>																			
  <string name="pvp_score">PVP score:</string>																			
  <string name="challenge">Challenge</string>																			
  <string name="pvp_mock">player_</string>																			
  <string name="today_pvp_num">Number of challenges today: %1$d/%2$d</string>																			
  <string name="duplicated_pk_target_tips">This opponent has been defeated today, please choose another opponent</string>																			
  <string name="pvp_num_limit">Today\'s challenge times have been used up.</string>																			
  <string name="pvp_num_limit2">Today\'s challenge times have been used up, go to VIP screen to gain extra challenge times?</string>																			
  <string name="pvp_error_ally">data error, can\'t start pk</string>																			
  <string name="pvp_upgrade">The opponent has new legion cards. Please update before battling.</string>																			
  <string name="today_pk">Today: %1$dwin %2$dloss</string>																			
  <string name="all_pk">History: %1$dwin %2$dloss</string>																			
  <string name="task_refresh_left_time">refresh</string>																			
  <string name="last_pvp_rank_tips">Ranking refresh at 0:15</string>																			
  <string name="arena_time_tips">23:45 to 0:15, the ranking time is not determined, please enter this page later</string>																			
  <string name="gain_pvp_diamond">Get Crown</string>																			
  <string name="pvp_diamond">Crown</string>																			
  <string name="quit_pvp_title">Confirm to exit Pvp battle? </string>																			
  <string name="quit_pvp_content">Exiting a Pvp battle will be considered a battle failure</string>																			
  <string name="pvp_diamond_not_enough">Not enough Crown</string>																			
  <string name="sell_left_tips">Left：%1$d</string>																			
  <string name="area">Area:</string>																			
  <string name="account_switch_tips">Can\'t switch account, please re-login</string>																			
  <string name="draw_icon">Mongolian Empire</string>																			
  <string name="draw_title">Draw Cards</string>																			
  <string name="draw_task">Draw Task</string>																			
  <string name="ally_coupon">Army card draw</string>																			
  <string name="hero_coupon">Epic character draw</string>																			
  <string name="skill_coupon">Tactical card draw</string>																			
  <string name="draw_card">10 Draws</string>																			
  <string name="saving_tips">Do not close the game immediately after drawing the card, as it takes time to synchronize the save files.</string>																			
  <string name="saving_end_tips">Archive writing completed</string>																			
  <string name="continue_draw">10 Draws</string>																			
  <string name="left_draw_orange">%1$d draws legend card ensure</string>																			
  <string name="left_draw_orange_100">legend card ensure</string>																			
  <string name="pool_title">card pool</string>																			
  <string name="draw_info">Draw Details</string>																			
  <string name="draw_info_content">The current event card pool includes: \n- Orange Quality: Heavy Horse Archer, Dragon Slayer Ballista, Eternal Sky Shaman, Mongolian Mounted Archers, Parthian Tactics, City Massacre and Plunder, Parthian Archery, Grey Wolf and White Deer, Mongolian War Dance, Cangjie, Kublai Khan, Jebe, Muqali, Subutai\n- Blue Quality: Horse Archer, Winged Archers, Flying Knife Soldier, Immortal Cultivator, Huihui Cannon, Mongolian Sabre Soldiers, Counterattack, Wind Arrow, Double Arrow Crossbow, Mobile Strike, Poison Smoke Bomb, Rapid Deployment, Sun Simiao, Scheherazade, Omar, Yelü Chucai\n- Green Quality: Crossbowman, Slinger, Apostle, Pagan, Falconry Team, Resilient Feathers, Awl Arrow, Volley, Zheng He, Marco Polo, Fan Li, Borjigin\nCard pool draw probability explanation: \n The event card pool includes Legion Cards, Tactic Cards, and Epic Characters, with the same quality cards having equal appearance rates.\n Every hundred draws guarantees at least one Orange Quality card.\n - Green Quality draw probability: 58%\n - Blue Quality draw probability: 40%\n - Orange Quality draw probability: 2%</string>																			
  <string name="login_failed_tips">Google account login failed, restart game in 2 seconds.</string>																			
    <string name="holiday_title">CNY</string>																			
    <string name="holiday_tab1">7-Day Sign</string>																			
    <string name="holiday_tab2">Challenges</string>																			
    <string name="holiday_tab3">Roulette</string>																			
    <string name="holiday_tab4">Gifts</string>																			
    <string name="holiday_tab5">Ranking</string>																			
    <string name="holiday_money">CNY coins</string>																			
    <string name="holiday_money_not_enough">Insufficient CNY coins</string>																			
    <string name="holiday_lottery_num">Number of Roulette</string>																			
    <string name="holiday_over_time">The CNY event has ended</string>																			
    <string name="war_pass_tips">Pass experience, obtained to level up the pass.</string>																			
    <string name="holiday_money_tips">CNY Coins, which can be used to activate the CNY Roulette.</string>																			
    <string name="war_pass2_tips">Empire Seals, which can be used to level up Rise of the Empire when acquired.</string>																			
    <string name="diamond_tips">Honour, which can be used to level up talents.</string>																			
    <string name="lottery_money_tips">Relic of Civilisation, can be used to activate the Legendary Spin.</string>																			
    <string name="key_tips">Diamonds, which can be used to purchase Trade Station goods.</string>																			
    <string name="pvp_score_tips">Competitive scores, which can be obtained to improve your competitive ranking.</string>																			
    <string name="pvp_diamond_tips">Crowns, which can be used to purchase items from the Arena Shop.</string>																			
<string name="month_card_immediate">Instant gain</string>																			
<string name="month_card_day">Daily</string>																			
<string name="month_card_tips">Stackable duration</string>																			
<string name="gift">Gift</string>																			
<string name="sell_storage_tips">Limited: %1$d</string>																			
<string name="package_content">Content</string>																			
<string name="tower_mode">Babel</string>																			
<string name="tower_tab1">Climb</string>																			
<string name="tower_tab2">Chest</string>																			
<string name="tower_tab3">Rankings</string>																			
<string name="tower_score">Floor</string>																			
<string name="tower_level">Floor %1$s</string>																			
<string name="tower_type1">Legion</string>																			
<string name="tower_type2">Doom</string>																			
<string name="tower_type3">Arena</string>																			
<string name="tower_type4">Blitz</string>																			
<string name="tower_tips3">Within %1$s</string>																			
<string name="tower_tips4">Within %1$s Rounds</string>																			
<string name="tower_fight_tips">Clear the previous floor first</string>																			
<string name="tower_not_done_tips">Defeat enemies to claim the chest</string>																			
<string name="tower_not_done">Undone</string>																			
<string name="tower_ally_not_match">Deployed heroes do not meet requirements</string>																			
<string name="only_one_master_tips">Only one hero can be deployed</string>																			
    <string name="one_click_select_battle">Deploy</string>																			
    <string name="one_click_unselect_battle">Undeploy</string>																			
</resources>																			